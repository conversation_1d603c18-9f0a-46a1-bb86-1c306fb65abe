/* 主题色 */
$color-primary: #409eff !default;
$color-primary: #ffa000;

$color-success: #67c23a;
$color-warning: #e6a23c;
$color-danger: #f56c6c;
$color-error: #f56c6c;
$color-info: #909399;

$color-white: #ffffff;
$color-black: #000000;

$border-color: #f2f2f2;
$background-color: #fff3e0;

$background-primary: #efefef;

$font-color: #333333;
$font-color-basic: rgba($font-color, 0.8);
$font-color-label: rgba($font-color, 0.6);
$font-color-tips: rgba($font-color, 0.4);
$font-color-salary: #fa635c;

$view-width: 1200px;
$header-height: 68px;

// 栏目header
$page-header-height: 50px;

$logo-width: 150px;
$logo-height: 42px;

$basic-index: 999;
$header-index: 2000;

$logo-icon: '//img.gaoxiaojob.com/uploads/static/image/logo/logo_1.png';
$logo-home: '//img.gaoxiaojob.com/uploads/static/image/logo/logo_home.png';
$logo-text-bottom: '//img.gaoxiaojob.com/uploads/static/image/logo/logo_8.png';
$logo-column: '//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png';

$qrcode-mobile: '//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/11.png';
$qrcode-mpweixin: '//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png';
$qrcode-weibo: '//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/13.png';

// 这里有很多新媒各个页面使用的,定义一些小程序变量
$qrcode-miniapp-sidebar: '//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/index.jpg';
// 这里有很多新媒各个页面使用的,定义一些小程序变量
$qrcode-miniapp-navbar: '//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/index.jpg';

$logo-column-width: 150px;
$logo-column-height: 42px;

@mixin utils-radius {
    border-radius: 4px;
}

@mixin utils-pre-wrap {
    white-space: pre-wrap;
}

@mixin utils-ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

@mixin utils-fixed($index: $basic-index) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: $index;
}

@mixin utils-detail-header-fixed($color: $font-color-tips) {
    box-shadow: 0 1px 10px 4px $color;
}

@mixin utils-detail-header-plain-fixed {
    @include utils-detail-header-fixed(rgba(51, 51, 51, 0.08));
}

/**
    * @description: 全局样式
    * @param {number} $line-size: 行数
    * @param {number} $line-height: 行高
    * @param {number} $font-size: 字体大小
    * @description $font-size default: 14px
*/
@mixin utils-ellipsis-lines($line-size, $line-height, $font-size: 14px) {
    @if (unitless($font-size)) {
        @error '$font-size must be a unit number';
    }

    $line-height: if(unitless($line-height), $font-size * $line-height, $line-height);
    $max-height: $line-height * $line-size;

    display: -webkit-box;
    -webkit-line-clamp: $line-size;
    -webkit-box-orient: vertical;
    max-height: $max-height;
    font-size: $font-size;
    line-height: $line-height;
    word-break: break-word;
    white-space: normal;
    text-overflow: ellipsis;
    overflow: hidden;
}

/* 多行文本溢出
  ------------------------------- */
  @mixin text-ellipsis($line: 2) {
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $line;
    -webkit-box-orient: vertical;
  }

/**
    * @param {string} $inactive-color
    * @param {string} $active-color
    * @param {string} $inactive-opacity
    * @description $inactive-opacity: 0.8
*/
@mixin utils-swiper-bullet($inactive-color: $color-white, $active-color: $color-primary, $inactive-opacity: 0.8) {
    .swiper-pagination-bullet {
        --swiper-pagination-bullet-inactive-opacity: #{$inactive-opacity};
        background-color: $inactive-color;
    }

    .swiper-pagination-bullet-active {
        background-color: $active-color;
    }
}

// 最新公告&简章 列表标题两行展示
@mixin utils-column-title {
    $title-line-size: 2;
    $title-font-size: 14px;
    $title-line-height: 18px;
    $title-max-height: $title-line-height * $title-line-size;

    display: -webkit-box;
    -webkit-line-clamp: $title-line-size;
    -webkit-box-orient: vertical;
    max-height: $title-max-height;
    line-height: $title-line-height;
    word-break: break-word;
    text-overflow: ellipsis;
    overflow: hidden;
}

/**
    * @description $first-opacity: 0.6 $second-opacity: 0.4
*/
@mixin utils-offline-mark($first-opacity: 0.6, $second-opacity: 0.4) {
    .offline-tag {
        display: none !important;
    }
    .offline-mark {
        .offline-tag {
            display: inline-flex !important;
            margin-right: 6px;
            font-size: 14px;
            border: 1px solid #dbdbdb;
            color: #333333;
            padding: 0px 6px;
            height: 22px;
            line-height: 20px;
            align-self: flex-start;
            font-weight: normal;
            @include utils-radius;
        }
        &.offline-gray-first,
        .offline-gray-first {
            opacity: $first-opacity;
        }
        .offline-gray-second {
            opacity: $second-opacity;
        }
    }
}
