@use './variables' as *;

@import './main';

.email-container {
    font-size: 14px;
    .container {
        .content {
            margin: 0;
            white-space: pre-wrap;
            line-height: 32px;
            font-size: 14px;
            span {
                color: $color-primary;
            }
        }
        .job {
            background-color: #f3f8fd;
            border-radius: 8px;
            padding: 0px 20px;
            margin-bottom: 30px;
            .list {
                display: block;
                text-decoration: none;
                color: rgba(51, 51, 51, 0.8);
                padding: 20px 0;
                & + .list {
                    border-top: 1px solid #e7e8eb;
                }
                .row {
                    display: flex;
                    & + div {
                        margin-top: 20px;
                        font-size: 13px;
                    }
                    .col-1 {
                        flex-grow: 1;
                        line-height: 1;
                        .name {
                            color: #333;
                            overflow: hidden;
                            word-break: break-all;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                        }
                    }
                    .col-2 {
                        line-height: 1;
                        width: 120px;
                        text-align: right;
                        flex-shrink: 0;
                    }
                    .salary {
                        color: #fa635c;
                    }
                }
            }
        }
        .company {
            text-align: right;
            margin-top: 16px;
            margin-bottom: 24px;
        }
        .open {
            display: block;
            width: 136px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background: $color-primary;
            border-radius: 4px;
            color: #fff;
            text-decoration: none;
            margin: 0 auto;
        }
    }
}
