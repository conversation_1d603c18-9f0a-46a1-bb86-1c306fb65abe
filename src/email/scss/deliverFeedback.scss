@use './variables' as *;

@import './main';

.email-container {
    font-size: 14px;

    a {
        text-decoration: none !important;
    }

    .email-body {
        line-height: 28px;
        padding-bottom: 18px;

        .hi {
            padding-top: 28px;
            font-size: 15px;
            font-weight: bold;
        }

        .email-content {
            text-indent: 2em;
        }

        a {
            font-weight: bold;
            text-decoration: none !important;
            color: $color-primary !important;
        }

        .resume-tips {
            margin-top: 8px;
            line-height: 24px;

            .warning {
                color: #fa635c;
            }

            .wait-perfect {
                color: $color-primary;
            }

            a {
                position: relative;
                display: inline-block;
                align-items: center;
                border-bottom: 1px solid $color-primary;

                &::after {
                    position: absolute;
                    top: 6px;
                    content: '';
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    border-top: 2px solid $color-primary;
                    border-right: 2px solid $color-primary;
                    transform: translateX(-2px) rotate(45deg) scale(0.7);
                }
            }
        }
    }

    .recommend-wrapper {
        background: #f3f8fd;
        border-radius: 8px;

        .content {
            padding: 20px;
        }

        .recommend-title {
            padding-left: 30px;
            background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/triangle.png') no-repeat left center/22px;
        }

        .big {
            font-weight: bold;
            font-size: 16px;
        }

        .recommend-tips {
            font-size: 13px;
            line-height: 10px;
            color: inherit;
            opacity: 0.8;
        }

        .recommend-content {
            margin-top: 15px;
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;

            .recommend-card {
                box-sizing: border-box;
                margin-bottom: 12px;
                padding: 12px;
                background: #ffffff;
                border-radius: 8px;
                width: calc((100% - 12px) / 2);
                color: inherit;
                text-decoration: none !important;
                line-height: 1.15;

                .job-top {
                    display: flex;

                    .job-name {
                        font-size: 13px;
                        font-weight: bold;
                        flex-grow: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .release-date {
                        font-size: 12px;
                        color: $color-primary;
                        flex-shrink: 0;
                    }
                }

                .tag-content {
                    display: flex;
                    flex-wrap: wrap;
                    padding: 11px 0;

                    .tag {
                        font-size: 12px;
                        line-height: 22px;
                        padding: 0 9px;
                        color: rgba($color: #333, $alpha: 0.8);
                        background-color: #f4f9ff;
                        border-radius: 4px;

                        & + .tag {
                            margin-left: 6px;
                        }
                    }
                }

                .job-bottom {
                    display: flex;
                    justify-content: space-between;
                    font-size: 13px;
                    color: rgba($color: $font-color, $alpha: 0.8);

                    .organizational {
                        width: 158px;
                        flex-shrink: 0;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .address {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        img {
                            width: 12px;
                            margin-bottom: -1px;
                            margin-right: 4px;
                        }
                    }
                }
            }
        }

        .more {
            display: flex;
            justify-content: center;
            margin-top: 8px;

            a {
                width: 152px;
                line-height: 32px;
                text-align: center;
                background-color: $color-primary;
                border-radius: 4px;
                color: #fff;
                text-decoration: none !important;
            }
        }
    }

    .footer {
        padding: 0 !important;
        background-color: #fff !important;

        .qr-code {
            img {
                width: 100%;
                display: block;
                object-fit: contain;
            }
        }

        .cantact-info {
            opacity: 1 !important;
            line-height: 44px;
            text-align: center;

            a {
                color: inherit;
                opacity: 0.6;
            }
        }
    }
}
