@use './variables' as *;

@import './main';
@import './talent-recommend';

.email-wrapper {
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 60px;
    box-sizing: border-box;

    .notification-container {
        color: #f8651b;
        width: 100%;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fbf1e8;
        margin-bottom: 16px;

        .notification-content {
            padding-left: 30px;
            background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/notification.png') no-repeat left center/24px;
        }
    }
}

.email-body {
    padding-top: 28px;
    font-size: 14px;
    padding-bottom: 18px;
    border-bottom: solid 1px #ebebeb;

    .hi {
        font-size: 15px;
        font-weight: bold;
    }

    .position {
        color: $color-primary;
        font-weight: bold;
    }

    .email-content {
        margin-top: 10px;
        text-indent: 2em;
    }

    .name {
        font-weight: bold;
    }
}

.date {
    margin-top: 18px;
    font-size: 12px;
    display: flex;
    justify-content: space-between;

    .recently {
        opacity: 0.6;
    }

    .login {
        color: #486cf5;
        font-weight: bold;
    }
}

.info {
    padding-left: 20px;
    padding-top: 3px;
}

.userinfo {
    display: flex;
    margin: 12px 0 20px;

    .tag-orange {
        font-size: 12px;
        line-height: 18px;
        display: inline-block;
        color: $color-primary;
        background-color: #fff3e0;
        border-radius: 4px;
        padding: 0 10px;
        margin-right: 5px;
    }

    .userphoto {
        width: 96px;
        height: 120px;
        object-fit: contain;
        border: 1px solid #ebebeb;
        border-radius: 8px;
    }

    .user-basic {
        display: flex;
        flex-basis: auto;
        align-items: center;
        min-height: 38px;
        padding: 0;

        .name {
            float: left;
            font-size: 22px;
            font-weight: bold;
            line-height: 1;
            flex-shrink: 0;
        }

        .gender {
            margin-bottom: -3px;
            margin-left: 4px;
            margin-right: 4px;
            width: 20px;
            object-fit: contain;
            flex-shrink: 0;
        }
    }

    .tag-content {
        display: flex;
        flex-grow: 1;
        flex-wrap: wrap;

        span {
            margin-bottom: 3px;
            margin-top: 3px;
        }
    }
}

.education,
.contact {
    font-size: 14px;
    line-height: 15px;
    margin-top: 9px;
    margin-bottom: 10px;

    .contact-email,
    .contact-email a {
        color: #486cf5 !important;
    }
}

.button {
    width: 152px;
    height: 28px;
    background: $color-primary;
    border-radius: 4px;
    display: flex;

    a {
        flex-grow: 1;
        font-size: 14px;
        color: #ffffff;
        text-align: center;
        line-height: 28px;
    }
}

.operate {
    font-size: 14px;
    a {
        display: inline-block;
        text-align: center;
        color: inherit;
        line-height: 26px;
        border: 1px solid #ebebeb;
        height: 28px;
        border-radius: 4px;
        margin-right: 4px;
        transition: all ease 0.1s;

        &.more {
            color: #fff;
            background: $color-primary;
            border-color: $color-primary;
            width: 152px;
            height: 28px;
        }
        &:not(.primary):hover {
            color: $color-primary;
            background-color: #ffeccc;
            border-color: #ffd999;
        }
        &.chat {
            width: 96px;
            background: #ffffff;
            border: 1px solid $color-primary;
            border-radius: 4px;
            color: $color-primary;
            padding-left: 2px;
            background: url(https://img.gaoxiaojob.com/uploads/static/image/resume/chat-primary.png) no-repeat left 8px center / 20px;
            span {
                margin-left: 20px;
            }
        }
    }
}
