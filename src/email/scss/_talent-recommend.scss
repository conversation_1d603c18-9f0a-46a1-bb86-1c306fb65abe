@use './variables' as *;

.recommend-content {
    background: #f3f8fd;
    border-radius: 8px;

    .content {
        padding: 20px;
    }

    .recommend-title {
        padding-left: 30px;
        background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/triangle.png') no-repeat left center/22px;
    }

    .big {
        font-weight: bold;
        font-size: 16px;
    }

    .recommend-tips {
        font-size: 13px;
        color: inherit;
        opacity: 0.8;
    }

    .recommend-content {
        margin-top: 15px;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .recommend-card {
            box-sizing: border-box;
            margin-bottom: 12px;
            padding: 12px;
            background: #ffffff;
            border-radius: 8px;
            width: calc((100% - 12px) / 2);
            display: flex;
            color: inherit;
            text-decoration: none !important;
            line-height: 1.15;

            .avatar {
                width: 50px;
                height: 64px;
                flex-shrink: 0;

                img {
                    width: 100%;
                    border-radius: 8px;
                    object-fit: contain;
                }
            }

            .education {
                margin-top: 10px;
            }

            .want {
                display: block;
                margin-top: 10px;
                width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .detail {
                flex-grow: 1;
                display: block;
                margin-left: 12px;
                font-size: 12px;
                overflow: hidden;
                .top {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                }

                .gender {
                    width: 12px;
                    height: 12px;
                    margin: 0 6px;
                }

                .tag {
                    background: #f6f8ff;
                    line-height: 18px;
                    border-radius: 4px;
                    padding: 0 8px;
                    margin-left: 2px;
                    font-size: 12px;
                    color: #486cf5;
                }
            }

            div {
                display: flex;

                b {
                    background-color: #ffffff;

                    height: 14px;
                    font-size: 14px;
                    font-weight: bold;
                    line-height: 14px;
                }
            }
        }
    }

    .more {
        display: flex;
        justify-content: center;
        margin-top: 8px;

        a {
            display: flex;
            position: relative;
            align-items: center;
            font-size: 14px;
            color: $color-primary;
            text-decoration: none !important;
            font-weight: bold;

            &::after {
                content: '';
                width: 8px;
                height: 8px;
                border-right: 2px solid $color-primary;
                border-bottom: 2px solid $color-primary;
                transform: rotate(-45deg) scale(0.8);
            }
        }
    }
}
