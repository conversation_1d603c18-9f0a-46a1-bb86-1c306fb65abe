@use './variables' as *;

@import './main';
@import './talent-recommend';

.email-wrapper {
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 60px;
    box-sizing: border-box;

    .notification-container {
        color: #f8651b;
        width: 100%;
        height: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fbf1e8;
        margin-bottom: 16px;

        .notification-content {
            padding-left: 30px;
            background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/notification.png') no-repeat left center/24px;
        }
    }
}

.email-container {
    font-size: 14px;
    .container {
        padding-bottom: 60px;

        .bold {
            font-weight: bold;
        }

        .has-dot {
            position: relative;
            padding-left: 11px;

            &::before {
                content: '';
                position: absolute;
                width: 4px;
                left: 0;
                height: 4px;
                background-color: $color-primary;
                border-radius: 50%;
            }
        }

        .common-details {
            margin-top: 7px;
            line-height: 1.15;

            &.has-dot {
                &::before {
                    top: 6px;
                }
            }
        }

        .wrapper-title {
            font-size: 18px;
            font-weight: bold;
            line-height: 1;
            padding-top: 16px;
            padding-bottom: 10px;
        }

        .wrapper-title {
            .underline {
                width: 36px;
                height: 2px;
                background: $color-primary;
                margin-top: 6px;
            }
        }

        .classify-title {
            font-size: 15px;
            font-weight: bold;
            text-align: center;
            line-height: 30px;
            border-radius: 4px;
            color: $color-primary;
            background-color: #ffedd1;
            margin-bottom: 12px;
        }

        .between-content {
            .list {
                margin: 10px 0 5px;
            }
        }

        .between-content {
            .col-1 {
                float: left;
                width: 59%;
            }

            .col-2 {
                float: left;
            }
        }

        .clear::after {
            display: block;
            content: '';
            clear: both;
        }

        .row-content {
            border-top: 1px solid #ebebeb;
        }

        .row-content {
            .row {
                padding-top: 5px;
                padding-bottom: 5px;
                line-height: 1.15;
            }
        }

        .spacing-1 {
            padding-bottom: 10px;
        }

        .spacing-2 {
            padding-bottom: 5px;
        }

        .has-line {
            line-height: 1;
            border-right: 2px solid #666;
        }

        .tag-blue {
            font-size: 12px;
            line-height: 18px;
            display: inline-block;
            color: #486cf5;
            background-color: #f6f8ff;
            border-radius: 4px;
            padding: 0 10px;
            margin-right: 5px;
        }

        .tag-orange {
            font-size: 12px;
            line-height: 18px;
            display: inline-block;
            color: $color-primary;
            background-color: #fff3e0;
            border-radius: 4px;
            padding: 0 10px;
            margin-right: 5px;
        }

        .text-content {
            line-height: 1.6;
            padding: 5px 0 0px;
            margin-top: -2px;
            white-space: pre-line;
            font-weight: inherit;
            word-wrap: break-word;
        }

        .jc-between-title {
            display: flex;
            align-items: baseline;

            .has-dot {
                &::before {
                    top: 8px;
                }
            }

            .title {
                flex-grow: 1;
            }

            .name {
                font-size: 15px;
                font-weight: bold;
                line-height: 1.5;
            }

            .aside {
                flex-shrink: 0;
                margin-left: 15px;
                text-align: right;
                color: $font-color;
            }
        }

        .second-list {
            padding-bottom: 15px;
        }

        .text-description {
            display: flex;

            .label {
                width: 75px;
                line-height: 2;
                flex-shrink: 0;
            }
        }

        /* 正文 start */
        .email-body {
            padding: 26px 0 18px;

            .hi {
                font-size: 15px;
                font-weight: bold;
            }

            .email-content {
                margin-top: 7px;
                text-indent: 2em;
                line-height: 1.6;

                .primary {
                    color: $color-primary;
                    font-weight: bold;
                }
            }
        }
        /* 正文 end */

        /* 个人信息 start */
        .date {
            margin-top: 18px;
            font-size: 12px;
            display: flex;
            justify-content: space-between;

            .recently {
                opacity: 0.6;
            }

            .login {
                color: #486cf5;
                font-weight: bold;
            }
        }

        .info-content {
            padding-top: 12px;
            display: flex;

            .avatar {
                border: 2px solid #ebebeb;
                border-radius: 8px;
                width: 92px;
                height: 116px;
                flex-shrink: 0;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }

            .info {
                flex-grow: 1;
                padding-left: 20px;

                .row {
                    padding: 6px 0;
                }

                .user-basic {
                    display: flex;
                    flex-basis: auto;
                    align-items: center;
                    min-height: 38px;
                    padding: 0;
                }

                .tag-content {
                    display: flex;
                    flex-grow: 1;
                    flex-wrap: wrap;

                    span {
                        margin-bottom: 3px;
                        margin-top: 3px;
                    }
                }
            }

            .name {
                float: left;
                font-size: 22px;
                font-weight: bold;
                line-height: 1;
                flex-shrink: 0;
            }

            .gender {
                margin-bottom: -3px;
                margin-left: 4px;
                margin-right: 4px;
                width: 20px;
                object-fit: contain;
                flex-shrink: 0;
            }

            .operate {
                a {
                    display: inline-block;
                    text-align: center;
                    color: inherit;
                    line-height: 26px;
                    border: 1px solid #ebebeb;
                    width: 96px;
                    height: 28px;
                    border-radius: 4px;
                    margin-right: 4px;
                    transition: all ease 0.1s;

                    &.primary {
                        color: #fff;
                        background: $color-primary;
                        border-color: $color-primary;
                    }
                    &:not(.primary):hover {
                        color: $color-primary;
                        background-color: #ffeccc;
                        border-color: #ffd999;
                    }
                    &.chat {
                        background: #ffffff;
                        border: 1px solid $color-primary;
                        border-radius: 4px;
                        color: $color-primary;
                        padding-left: 2px;
                        background: url(https://img.gaoxiaojob.com/uploads/static/image/resume/chat-primary.png) no-repeat left 8px center / 20px;
                        span {
                            margin-left: 20px;
                        }
                    }
                }
            }

            .advantage-content {
                padding-top: 6px;
            }
        }
        /* 个人信息 end */

        /* 求职意向 start */
        .intention-content {
            .has-dot {
                &::before {
                    top: 11px;
                }
            }
        }
        /* 求职意向 end */

        /* 工作经历 start */
        .work-content {
            .position {
                margin-top: 10px;
                margin-bottom: 6px;

                .name {
                    font-size: 15px;
                    font-weight: bold;
                }
            }
        }
        /* 工作经历 end */

        /* 科研项目 start */
        .project-content {
            .role {
                margin-top: 12px;
                margin-bottom: 8px;
            }
        }

        /* 科研项目 end */

        /* 技能/语言 end */
        .skill-content {
            .second-list {
                padding-bottom: 10px;
            }
        }
        /* 技能/语言 end */

        /* 其他技能 end */
        .other-skill {
            .second-list {
                padding-bottom: 0px;
            }
        }
        /* 其他技能 end */

        /* 附加信息 start */
        .addition-content {
            .name {
                font-size: 15px;
                font-weight: bold;
                line-height: 1.5;

                &::before {
                    top: 8px;
                }
            }
        }
        /* 附加信息 end */

        #showDetail {
            display: none;
        }

        #showDetail:target {
            display: block;

            & + .expand {
                display: none;
            }
        }

        .expand {
            display: flex;
            justify-content: center;
            padding-top: 8px;
            padding-bottom: 20px;

            .button {
                color: $color-primary;
                display: flex;
                align-items: center;
                position: relative;
                padding-right: 15px;

                &::after {
                    content: '';
                    position: absolute;
                    right: 0px;
                    transform: rotate(45deg) scale(0.8);
                    display: block;
                    border-right: 2px solid $color-primary;
                    border-bottom: 2px solid $color-primary;
                    width: 8px;
                    height: 8px;
                }
            }
        }

        .recommend-title {
            padding: 0;
            background: none;
            display: flex;
            flex-direction: column;

            .big {
                text-align: center;

                img {
                    width: 18px;
                }
            }

            .recommend-tips {
                text-align: center;
                margin-top: 13px;
            }
        }

        .recommend-content {
            margin-top: 20px;
        }
    }
}
