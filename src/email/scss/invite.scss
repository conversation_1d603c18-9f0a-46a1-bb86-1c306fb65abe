@use './variables' as *;

@import './main';

.email-container {
    font-size: 14px;
    .container {
        .content {
            margin: 0;
            white-space: pre-wrap;
            line-height: 32px;
            font-size: 14px;
            span {
                color: $color-primary;
            }
        }
        .info {
            background-color: #f3f8fd;
            border-radius: 8px;
            padding: 10px 30px 10px;
            .title {
                display: flex;
                align-items: center;
                line-height: 32px;
                &::before {
                    content: '';
                    margin-right: 5px;
                    display: block;
                    width: 6px;
                    height: 6px;
                    background-color: $color-primary;
                }
            }
            p {
                line-height: 32px;
                margin: 0;
            }
        }
        .company {
            text-align: right;
            margin-top: 16px;
            margin-bottom: 24px;
        }
        .open {
            display: block;
            width: 136px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            background: $color-primary;
            border-radius: 4px;
            color: #fff;
            text-decoration: none;
            margin: 0 auto;
        }
    }
}
