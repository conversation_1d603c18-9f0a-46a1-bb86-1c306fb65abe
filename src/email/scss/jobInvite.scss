@use './variables' as *;

@import './main';

.email-container {
    font-size: 14px;

    a {
        text-decoration: none !important;
    }

    .email-body {
        line-height: 28px;
        padding-bottom: 18px;

        .hi {
            padding-top: 28px;
            font-size: 15px;
            font-weight: bold;
        }

        .email-content {
            margin-bottom: 3px;
            text-indent: 2em;
        }

        a {
            font-weight: bold;
            text-decoration: none !important;
            color: $color-primary !important;
        }

        .email-footer {
            text-align: right;
        }
    }

    .job-invite-content {
        border-radius: 8px;
        padding: 20px;
        background-color: #f3f8fd;

        .job-detail {
            display: block;
            padding: 48px 20px 16px;
            position: relative;
            border-radius: 8px;
            background-color: #fff;
            color: inherit !important;

            .title {
                font-size: 12px;
                color: #6a4b2c;
                padding: 0 14px;
                position: absolute;
                left: -7px;
                top: 17px;
                line-height: 18px;
                background: linear-gradient(90deg, #e5be9a, #f7e4d1);
                border-radius: 0px 8px 8px 0px;

                &::after {
                    content: '';
                    display: block;
                    position: absolute;
                    width: 7px;
                    height: 4px;
                    left: -1px;
                    top: 100%;
                    background: linear-gradient(45deg, transparent 50%, #c5946d 50%);
                }
            }

            .top {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .job-name {
                    font-size: 18px;
                    font-weight: bold;
                }

                .job-date {
                    opacity: 0.8;
                }
            }

            .job-info {
                display: flex;
                padding: 13px 0;

                .job-require {
                    flex-shrink: 0;
                    margin-right: 8px;
                }

                .job-welfare {
                    display: flex;

                    .tag-primary {
                        padding: 2px 8px;
                        background: #fff3e0;
                        border-radius: 4px;
                        color: $color-primary;
                        display: inline-flex;
                        align-self: center;
                        flex-shrink: 0;
                        margin-right: 8px;
                        font-size: 12px;
                    }

                    .tag-collect {
                        position: relative;

                        .hover-area {
                            display: none;
                            position: absolute;
                            z-index: 99;
                            background: #fff;
                            border: 1px solid #e4e7ed;
                            width: 304px;
                            padding: 12px 4px 0px 12px;
                            top: 30px;
                            transform: translateX(-50%);
                            left: 40%;
                            border-radius: 4px;
                            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

                            &::after {
                                position: absolute;
                                background: #fff;
                                border: 1px solid;
                                border-color: transparent #e4e7ed #e4e7ed transparent;
                                width: 10px;
                                height: 10px;
                                content: ' ';
                                transform: translateX(-50%) rotate(-135deg);
                                left: 50%;
                                top: -6px;
                            }

                            .tag-primary {
                                float: left;
                                margin-bottom: 12px;
                            }
                        }

                        &:hover {
                            .hover-area {
                                display: block;
                            }
                        }
                    }
                }
            }

            .work-require {
                border-top: 1px solid #ebebeb;

                .require {
                    font-size: 16px;
                    padding-top: 15px;
                    padding-bottom: 10px;
                    font-weight: bold;
                }

                pre {
                    font-family: inherit;
                    line-height: 28px;
                    margin: 0;

                    display: -webkit-box;
                    -webkit-line-clamp: 4;
                    -webkit-box-orient: vertical;
                    text-overflow: ellipsis;
                    overflow: hidden;

                    white-space: pre-line;
                    word-break: break-all;

                    opacity: 0.8;
                }
            }

            .more {
                font-size: 14px;
                color: $color-primary;
                padding: 10px 0 7px;
                position: relative;

                &::after {
                    content: '';
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    border-top: 2px solid $color-primary;
                    border-right: 2px solid $color-primary;
                    transform: translateX(-2px) rotate(45deg) scale(0.6);
                }
            }
        }

        .accept-btn {
            display: block;
            margin: 20px auto 0;
            width: 96px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            background: #ffa000;
            border-radius: 4px;
            color: #fff;
        }
    }

    .footer {
        padding: 0 !important;
        background-color: #fff !important;

        .qr-code {
            img {
                width: 100%;
                display: block;
                object-fit: contain;
            }
        }

        .cantact-info {
            opacity: 1 !important;
            line-height: 44px;
            text-align: center;

            a {
                color: inherit;
                opacity: 0.6;
            }
        }
    }
}
