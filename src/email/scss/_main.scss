@use './variables' as *;

html,
body {
    height: 100%;
    color: #333;
}
.checkedUrl {
    width: 0;
    height: 0;
}
.email-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100%;
    .email-container {
        width: 680px;
        flex-shrink: 0;
        border: 1px solid #ebebeb;
        border-radius: 16px;
        margin-top: 50px;
        margin-bottom: 50px;
        a {
            text-decoration: none !important;
        }
        .common-title {
            font-size: 16px;
            font-weight: bold;
            background: url('https://img.gaoxiaojob.com/uploads/static/image/resume/triangle.png') no-repeat left center/22px auto;
            line-height: 1;
            padding: 30px 0 30px 30px;
        }
        .header {
            height: 60px;
            background-color: $color-primary;
            position: relative;
            padding-left: 50px;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            display: flex;
            align-items: center;
            .link {
                height: 33px;
                display: flex;
                align-items: center;
                color: #fff;
                text-decoration: none;
                font-size: 14px;
                padding-left: 137px;
                background: url($logo-column) no-repeat left center/122px auto;
            }
            .notes {
                position: absolute;
                right: 20px;
                width: 116px;
                height: 116px;
                display: block;
                bottom: -8px;
            }
        }
        .hideSystemUrl {
            color: #333;
        }
        .container {
            margin: 21px 21px 0;
            border: 1px dashed $color-primary;
            border-bottom: none;
            padding-left: 29px;
            padding-right: 29px;
            padding-bottom: 30px;
            border-radius: 8px 8px 0px 0px;
        }
        .footer {
            color: $font-color !important;
            background-color: #f8f8f8;
            padding: 15px 50px;
            border-bottom-left-radius: 16px;
            border-bottom-right-radius: 16px;

            .qr-code {
                height: 74px;
                background: url($qrcode-mobile) no-repeat right 140px center/66px, url('https://img.gaoxiaojob.com/uploads/static/image/resume/showcase.png') no-repeat left center/100% auto;
            }

            p {
                margin: 0;
                opacity: 0.8;
                font-size: 12px;
                & + p {
                    margin-top: 8px;
                }
                .orange {
                    color: #fa635c !important;
                }

                .blue {
                    color: #486cf5 !important;
                }
                a {
                    text-decoration: none !important;
                }
            }
        }
    }
}
