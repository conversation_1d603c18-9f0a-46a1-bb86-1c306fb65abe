<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>下单成功弹窗组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/paymentSuccessDialog.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script type="text/javascript" src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
    </head>

    <body>
        <div id="paymentSuccessDialog" class="payment-dialog-alert-template" v-cloak>
            <el-dialog v-model="wechatVisible" @close="handelWxClose">
                <div class="wechat-container" v-if="showWechatCode">
                    <div class="paymentTips">
                        <strong>下单成功</strong>
                    </div>
                    <div class="payment-text">
                        <div class="text-mid">
                            <span>{{title}}</span>
                            <span>{{successContent}}</span>
                        </div>
                        <div class="qr-code" v-loading="loading">
                            <img :src="urCodeUrl" alt="" />
                            <img class="logo" src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_square.png" alt="" />
                        </div>
                        <el-button type="primary" @click="removeWechat">我知道了</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <script>
            $(function () {
                const successDialogAlertOptions = {
                    data() {
                        return {
                            successVisible: false,
                            wechatVisible: false,
                            showWechatCode: true,
                            title: '',
                            timer: null,
                            ticket: '',
                            token: '',
                            successContent: '请扫码关注【高校人才网服务号】，回复“求职”，领取VIP专属求职学习资料包！',
                            urCodeUrl: 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQE57zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyZWduUmNqZjlmR0QxZ2JTRE5BY3kAAgTfNGdkAwQsAQAA'
                        }
                    },

                    methods: {
                        paymentSuccessDialogAlert(successData) {
                            const { title, successContent, url, ticket, token } = successData
                            this.title = title
                            this.successContent = successContent
                            this.urCodeUrl = url
                            this.wechatVisible = true
                            if (token !== '' && ticket !== '') {
                                this.ticket = ticket
                                this.token = token
                                this.checkBind()
                            }
                        },
                        checkBind() {
                            this.closeTimer()
                            this.timer = setInterval(async () => {
                                const rs = await httpPost('/api/member/check-bind-qrcode', {
                                    ticket: this.ticket,
                                    token: this.token
                                })
                                switch (rs.status) {
                                    case 1:
                                        this.checkBind()
                                        break
                                    case 2:
                                        this.closeTimer()
                                        this.$message({
                                            message: '绑定成功',
                                            type: 'success'
                                        })
                                        break
                                    default:
                                        // 异常
                                        this.closeTimer()
                                        break
                                }
                            }, 2000)
                        },
                        closeTimer() {
                            if (this.timer) {
                                clearInterval(this.timer)
                                this.timer = null
                            }
                        },
                        removeWechat() {
                            this.wechatVisible = false
                        },
                        handelWxClose() {
                            this.wechatVisible = false
                        }
                    },
                    beforeDestroy() {
                        this.closeTimer()
                    }
                }

                const paymentSuccessDialogAlertComponent = Vue.createApp(successDialogAlertOptions).use(ElementPlus).mount('#paymentSuccessDialog')

                window.globalComponents = { ...window.globalComponents, paymentSuccessDialogAlertComponent }
            })
        </script>
    </body>
</html>
