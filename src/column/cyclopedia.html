<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>百科词</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/cyclopedia.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <!-- <a href="/vip.html" class="nav-link">
                        VIP<span class="gaocai-vip">升级</span>
                    </a> -->
                    <div class="vip">
                        <a href="/vip.html" target="_blank" class="nav-link"
                            >求职VIP<span class="arrow"></span>
                            <span class="artifact">神器</span>
                        </a>
                        <div class="more">
                            <a href="/" target="_blank">简历曝光</a>
                            <a href="/" target="_blank">上岸直通车</a>
                            <a href="/" target="_blank">竞争力分析</a>
                            <a href="/" target="_blank">精选课程</a>
                            <a href="/" target="_blank">高校求职顾问</a>
                        </div>
                    </div>
                    <a class="nav-link join"
                        >小程序
                        <div class="join-code">
                            <div class="news-part">
                                <p class="scan-join">高才优聘小程序上线拉！</p>
                                <!-- <h4 class="scan-join"></h4> -->
                                <p class="explain">随时随地看机会</p>
                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/26.png" />
                            </div>
                        </div>
                    </a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <!-- <el-popover popper-class="header-popover-message" trigger="hover" :width="180">
                        <template #reference>
                            <span class="message">
                                <el-badge :value="total" :hidden="total === 0">
                                    <i class="el-icon-bell"></i>
                                </el-badge>
                            </span>
                        </template>

                        <ul class="message-options">
                            <li v-for="(item, index) in messageOptions" :key="index" class="item pointer" @click="handleClick(item)">
                                <span class="label">{{ item.label }}</span>

                                <span v-if="item.value" class="value"> <span class="num"> {{ item.value }} </span>条未读 </span>
                            </li>
                        </ul>
                    </el-popover>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => (isOpenVip ? openVip('/vip.html') : handleRoute('/job?tab=service'))">
                                    <div class="dropdown-item__card">
                                        <div class="dropdown-item__card-title">
                                            <span>高才</span>
                                            <span>去开通</span>
                                        </div>

                                        <div class="dropdown-item__card-desc">高效求职 尊享11+权益</div>

                                        <div class="dropdown-item__card-tips">•投递置顶 •简历曝光 •竞争力分析</div>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown> -->
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div>
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        {
                                            label: '职位',
                                            value: '1',
                                            path: '/job'
                                        },
                                        {
                                            label: '公告',
                                            value: '2',
                                            path: '/search'
                                        },
                                        {
                                            label: '单位',
                                            value: '3',
                                            path: '/company'
                                        },
                                        {
                                            label: '资讯',
                                            value: '4',
                                            path: '/search',
                                            query: 'type=2'
                                        }
                                    ],

                                    keyword: '',
                                    isVip: true,
                                    vipInfo: {},

                                    total: 100,
                                    messageOptions: [
                                        {
                                            label: '我的直聊',
                                            value: '10',
                                            path: '/chat'
                                        },
                                        {
                                            label: '求职动态',
                                            value: '99+',
                                            path: '/message?read=2&type=0'
                                        },
                                        {
                                            label: '系统通知',
                                            value: '',
                                            path: '/message?read=2&type=3'
                                        }
                                    ]
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                handleClick(data) {
                                    this.handleRoute(data.path)
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div class="cyclopedia-container">
            <div class="job-desc-wrapper">
                <div class="view-content">
                    <h1 class="job-name">
                        博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后博士后
                    </h1>
                    <div class="desc">
                        博士后是指博士研究生毕业后，在高校或科研机构从事短期科研工作的岗位或研究经历。它属于科研能力提升和职业过渡的重要阶段，而非学位或职称。以下从定义、性质、特点等方面展-开说明。以下从定义、性质、特点等方面展开说明。以下从定义、性质、特点等方面展开说明
                    </div>
                </div>
            </div>

            <div class="main-wrapper view-content">
                <div class="main-content">
                    <div class="list-wrapper">
                        <div class="list">
                            <div class="info">
                                <div class="top">
                                    <a href="" class="name" target="_blank">湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北</a>
                                    <a class="company" href="" target="_blank">中国科学院上海微系统与信息技科学院上海技科学院中国科学院上海微系统与信息技科学院上海技科学院</a>
                                </div>
                            </div>

                            <div class="content">
                                <div class="desc down">
                                    岗位职责： 1.遵守中华人民共和国法律法规，具有良好的科学道德，自觉践行新时代科学家精神。 2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。
                                    4.研究方向主要为自然科学、工程技术等。 5.在取得博士学位后至2025年4月15日前，一般应在海外知名高校、科研机构、企业研发机构等获得正式教学或者科...岗位职责：
                                    1.遵守中华人民共和国法律法规，具有良好的科学道德，自觉践行新时代科学家精神。 2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。
                                    4.研究方向主要为自然科学、工程技术等。2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。
                                    4.研究方向主要为自然科学、工程技术等。2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。 4.研究方向主要为自然科学、工程技术等。
                                    4.研究方向主要为自然科学、工程技术等。2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。
                                    4.研究方向主要为自然科学、工程技术等。2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。 4.研究方向主要为自然科学、工程技术等。
                                    4.研究方向主要为自然科学、工程技术等。2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。
                                    4.研究方向主要为自然科学、工程技术等。2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。 4.研究方向主要为自然科学、工程技术等。
                                </div>

                                <div class="trigger"></div>
                            </div>
                        </div>
                        <div class="list">
                            <div class="info">
                                <div class="top">
                                    <a href="" class="name" target="_blank">湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北</a>
                                    <a class="company" href="" target="_blank">中国科学院上海微系统与信息技科学院上海技科学院中国科学院上海微系统与信息技科学院上海技科学院</a>
                                </div>
                                <div class="bottom">
                                    <div class="aside">
                                        <span class="salary">15K-25K/月</span>
                                        <span>江苏-苏州 | 博士研究生 | 招3人</span>
                                    </div>
                                    <div class="type">科研院所 | 公立（国有）</div>
                                </div>
                            </div>

                            <div class="content">
                                <div class="desc">
                                    岗位职责： <br />
                                    1.遵守中华人民共和国法律法规，具有良好的科学道德，自觉践行新时代科学家精神。 <br />
                                    2.出生日期在1985年1月1日（含）以后。 <br />
                                    3.具有博士学位。 <br />
                                    4.研究方向主要为自然科学、工程技术等。 <br />
                                    5.在取得博士学位后至2025年4月15日前，一般应在海外知名高校、科研机构、企业研发机构等获得正式教学或者科... <br />

                                    <div style="margin-top: 20px"></div>

                                    任职要求：<br />
                                    1.遵守中华人民共和国法律法规，具有良好的科学道德，自觉践行新时代科学家精神。 <br />
                                    2.出生日期在1985年1月1日（含）以后。 <br />
                                    3.具有博士学位。 <br />
                                    4.研究方向主要为自然科学、工程技术等。 <br />
                                    5.在取得博士学位后至2025年4月15日前，一般应在海外知名高校、科研机构、企业研发机构等获得正式教学或者科... <br />
                                </div>

                                <div class="trigger"></div>
                            </div>
                        </div>

                        <div class="list">
                            <div class="info">
                                <div class="top">
                                    <a href="" class="name" target="_blank">湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北</a>
                                    <a class="company" href="" target="_blank">中国科学院上海微系统与信息技科学院上海技科学院中国科学院上海微系统与信息技科学院上海技科学院</a>
                                </div>
                                <div class="bottom">
                                    <div class="aside">
                                        <span class="salary">15K-25K/月</span>
                                        <span>江苏-苏州 | 博士研究生 | 招3人</span>
                                    </div>
                                    <div class="type">科研院所 | 公立（国有）</div>
                                </div>
                            </div>

                            <div class="content">
                                <div class="desc">
                                    岗位职责： 1.遵守中华人民共和国法律法规，具有良好的科学道德，自觉践行新时代科学家精神。 2.出生日期在1985年1月1日（含）以后。 3.具有博士学位。
                                    4.研究方向主要为自然科学、工程技术等。 5.在取得博士学位后至2025年4月15日前，一般应在海外知名高校、科研机构、企业研发机构等获得正式教学或者科...岗位职责：
                                </div>

                                <div class="trigger"></div>
                            </div>
                        </div>
                    </div>

                    <div class="more-wrapper">
                        <div class="title-wrapper">
                            <div class="title">相关职位推荐</div>

                            <a class="arrow" href="" target="_blank">查看更多</a>
                        </div>

                        <div class="list-content">
                            <a href="" class="list" target="_blank">
                                <div class="top">
                                    <div class="name">硕士项目办公室研究生教构-硕士项目办公室研究生教构</div>
                                    <div class="salary">面议</div>
                                </div>
                                <div class="tag-content">
                                    <span class="tag">硕士研究生</span>
                                    <span class="tag">博士</span>
                                    <span class="tag">招99人</span>
                                </div>
                                <div class="bottom">
                                    <div class="company">清华大学清华大学清华大学清华大学清华大学清华大学清华大学清华大学</div>
                                    <div class="address">广州广州广州广州广州广州</div>
                                </div>
                            </a>
                            <a href="" class="list" target="_blank">
                                <div class="top">
                                    <div class="name">硕士项目办公室研究生教构-硕士项目办公室研究生教构</div>
                                    <div class="salary">面议</div>
                                </div>
                                <div class="tag-content">
                                    <span class="tag">硕士研究生</span>
                                    <span class="tag">博士</span>
                                    <span class="tag">招99人</span>
                                </div>
                                <div class="bottom">
                                    <div class="company">清华大学</div>
                                    <div class="address">广州广州广州广州广州广州</div>
                                </div>
                            </a>
                            <a href="" class="list" target="_blank">
                                <div class="top">
                                    <div class="name">硕士项目办公室研究生教构-硕士项目办公室研究生教构</div>
                                    <div class="salary">面议</div>
                                </div>
                                <div class="tag-content">
                                    <span class="tag">硕士研究生</span>
                                    <span class="tag">博士</span>
                                    <span class="tag">招99人</span>
                                </div>
                                <div class="bottom">
                                    <div class="company">清华大学清华大学清华大学清华大学清华大学清华大学清华大学清华大学</div>
                                    <div class="address">广州</div>
                                </div>
                            </a>
                            <a href="" class="list" target="_blank">
                                <div class="top">
                                    <div class="name">硕士项目办公室研究生教构-硕士项目办公室研究生教构</div>
                                    <div class="salary">面议</div>
                                </div>
                                <div class="tag-content">
                                    <span class="tag">硕士研究生</span>
                                    <span class="tag">博士</span>
                                    <span class="tag">招99人</span>
                                </div>
                                <div class="bottom">
                                    <div class="company">清华大学</div>
                                    <div class="address">广州</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="aside-content">
                    <!-- 登录组件---挂件 -->

                    <div class="more-wrapper">
                        <div class="title">更多词条推荐</div>

                        <div class="content">
                            <a class="item" href="" target="_blank">博士后是干什么的</a>
                            <a class="item" href="" target="_blank">英语教师的职业前景怎么样？待遇怎么样呢待遇怎么样呢</a>
                            <a class="item" href="" target="_blank">辅导员工资水平怎么样？</a>
                            <a class="item" href="" target="_blank">辅导员工资水平怎么样？</a>
                            <a class="item" href="" target="_blank">超市运营就业前景</a>
                            <a class="item" href="" target="_blank">人力行政总监工资水平，工作内容是什么</a>
                            <a class="item" href="" target="_blank">程序员就业环境怎么样</a>
                            <a class="item" href="" target="_blank">海外优青招聘要求有哪些</a>
                            <a class="item" href="" target="_blank">PHP开发工程师是干什么的</a>
                            <a class="item" href="" target="_blank">工程设备管理总经理就业前景</a>
                        </div>
                    </div>
                </div>
            </div>

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">人才招聘</a>| <a href="/" target="_blank">广告服务</a>|
                    <a href="/" target="_blank">免责声明</a>| <a href="/" target="_blank">网站导航</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>
        </div>

        <script>
            $(function () {
                $('.main-wrapper .list-wrapper .list .desc:not(.down)').each(function () {
                    const $this = $(this)
                    // 六行高度 行高24px
                    const packUpHeight = 144
                    const height = $this.height()
                    const isHide = height <= packUpHeight

                    if (isHide) {
                        $this.siblings('.trigger').remove()
                    } else {
                        $this.siblings('.trigger').show()
                        $this.addClass('up')
                    }
                })

                $('.main-wrapper .list-wrapper .trigger').click(function () {
                    const $this = $(this)
                    const $desc = $this.siblings('.desc')
                    const isUp = $desc.hasClass('up')

                    if (isUp) {
                        $desc.removeClass('up')
                        $desc.addClass('down')
                    } else {
                        $desc.removeClass('down')
                        $desc.addClass('up')
                    }
                })
            })
        </script>
    </body>
</html>
