<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>博后公告&职位</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/commonPostdoctor.css" />
        <link rel="stylesheet" href="./css/postdoctorJob.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/filterDialog.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./js/postdoctor/common.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <div class="postdoctor-container">
            <header class="postdoctor-header-container">
                <div class="postdoctor-header-content">
                    <div class="postdoctor-menu-content">
                        <a href="/" class="logo">
                            <span class="logo-text">高才博士后</span>

                            <div class="collect-tips"></div>
                        </a>
                        <a href="/" class="postdoctor-menu-item">首页</a>
                        <a href="/" class="postdoctor-menu-item active">博后公告&职位</a>
                        <a href="/" class="postdoctor-menu-item">招收单位& PI大厅</a>
                        <a href="/" class="postdoctor-menu-item">博后活动</a>
                    </div>
                    <div class="postdoctor-nav-content">
                        <a href="" class="postdoctor-nav-item">需求发布</a>
                        <a href="" class="postdoctor-nav-item" target="_blank">高才官网</a>
                        <a href="" class="postdoctor-nav-item" target="_blank">单位登录</a>

                        <button class="postdoctor-login-btn open-login-dialog">人才登录</button>
                        <!-- <a class="person-avatar" href="" target="_blank"></a>
                            <img src="https://picsum.photos/seed/picsum/36/36" alt="" />
                        </a> -->
                    </div>
                </div>

                <script>
                    $(function () {
                        var $header = $('.postdoctor-header-container')
                        var $logo = $header.find('.logo')
                        var $logoTips = $header.find('.collect-tips')
                        var isCollectShow = false

                        function showCollect() {
                            if (isCollectShow) return

                            isCollectShow = true
                            var time = new Date().getTime()

                            $logoTips.fadeIn(0).css('background', `url(./assets/postdoctor/common/collect-tips.gif?v=${time}) no-repeat center/100% auto`)

                            setTimeout(() => {
                                $logoTips.fadeOut(1000)

                                setTimeout(() => {
                                    isCollectShow = false
                                }, 1000)
                            }, 6000)
                        }

                        var envHrefMap = [
                            'https://boshihou.gaoxiaojob.com/',
                            'https://boshihou.test.gcjob.jugaocai.com/',
                            'https://boshihou.dev.gcjob.jugaocai.com/',
                            'https://boshihou.pre.gcjob.jugaocai.com/',
                            'https://boshihou.gray.gcjob.jugaocai.com/'
                        ]
                        var href = window.location.href

                        var isPostdoctorHome = envHrefMap.includes(href)

                        if (isPostdoctorHome) {
                            showCollect()
                        }

                        $logo.mouseenter(showCollect)
                    })
                </script>
            </header>

            <div class="main-container">
                <div class="showcase-wrapper">
                    <div class="showcase-top">
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/590/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/590/90" alt="" />
                        </a>
                    </div>
                    <div class="showcase-bottom">
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                    </div>
                </div>

                <div class="filter-wrapper" id="filter-wrapper">
                    <div class="filter-row kw-row">
                        <div class="filter-label">关键词</div>
                        <div class="filter-value">
                            <div class="search" id="keywordComponent">
                                <div class="search-input">
                                    <el-input class="kw" v-model="keyword" @keyup.enter="search" @clear="handleKwClear" placeholder="请输入关键词搜索" clearable></el-input>
                                </div>
                                <button class="search-btn" @click="search">搜索</button>
                            </div>
                            <div class="search-hot">
                                <div class="search-hot-label">热门搜索:</div>
                                <div class="search-hot-value">
                                    <a class="item" href="">王大伟课题组</a>
                                    <a class="item active" href="">东莞理工学院</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                    <a class="item" href="">关键词</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">热门地区</div>
                        <div class="filter-value">
                            <a class="filter-item" href="">不限</a>
                            <a class="filter-item active">北京</a>
                            <a class="filter-item" href="">上海</a>
                            <a class="filter-item" href="">天津</a>
                            <a class="filter-item" href="">重庆</a>
                            <a class="filter-item" href="">广州</a>
                            <a class="filter-item" href="">深圳</a>
                            <a class="filter-item" href="">武汉</a>
                            <a class="filter-item" href="">南京</a>
                            <a class="filter-item" href="">西安</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">安徽</a>
                            <a class="filter-item" href="">福建</a>
                            <a class="filter-item" href="">广东</a>
                            <a class="filter-item" href="">江苏</a>
                            <a class="filter-item" href="">黑龙江</a>
                            <a class="filter-item" href="">山东</a>
                            <a class="filter-item" href="">湖北</a>
                            <a class="filter-item" href="">四川</a>
                            <a class="filter-item" href="">浙江</a>
                            <a class="filter-item" href="">陕西</a>
                            <a class="filter-item" href="">江西</a>
                            <a class="filter-item" href="">吉林</a>
                            <a class="filter-item" href="">河南</a>
                            <a class="filter-item" href="">湖南</a>
                            <a class="filter-item" href="">辽宁</a>
                            <a class="filter-item" href="">河北</a>
                            <a class="filter-item" href="">山西</a>
                            <a class="filter-item" href="">海南</a>
                            <a class="filter-item" href="">贵州</a>
                            <a class="filter-item" href="">广西</a>
                            <a class="filter-item" href="">甘肃</a>
                            <a class="filter-item" href="">内蒙古</a>
                            <a class="filter-item" href="">云南</a>
                            <a class="filter-item" href="">宁夏</a>
                            <a class="filter-item" href="">青海</a>
                            <a class="filter-item" href="">新疆</a>
                        </div>
                        <div class="filter-more dialog-target" data-target="#areaDialog">更多地区</div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">学科领域</div>
                        <div class="filter-value">
                            <button class="open-login open-login-dialog">登录查看我的专业</button>

                            <a class="filter-item" href="">不限</a>
                            <a class="filter-item" href="">生命科学与医学</a>
                            <a class="filter-item" href="">计算机软件与人工智能</a>
                            <a class="filter-item" href="">机械制造控制与动力电气</a>
                            <a class="filter-item" href="">数理力学光电与地球科学</a>
                            <a class="filter-item" href="">电子信息半导体与通信</a>
                            <a class="filter-item" href="">人文艺术与社会科学</a>
                            <a class="filter-item" href="">经管金融统计与数据</a>
                            <a class="filter-item" href="">材料能源矿业与环境</a>
                            <a class="filter-item" href="">农林水牧与生物</a>
                            <a class="filter-item" href="">化学化工纺织与食品</a>
                            <a class="filter-item" href="">土木建筑与园林规划</a>
                            <a class="filter-item" href="">交通船舶与空天深海</a>
                            <a class="filter-item" href="">跨学科与综合领域 </a>
                        </div>
                        <div class="filter-more dialog-target" data-target="#majorDialog">更多学科</div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">需求专业</div>
                        <div class="filter-value">
                            <a class="filter-item" href="">不限</a>
                            <a class="filter-item" href="">外国语言文学</a>
                            <a class="filter-item" href="">新闻传播学</a>
                            <a class="filter-item" href="">中国语言文学</a>
                            <a class="filter-item" href="">哲学</a>
                            <a class="filter-item" href="">艺术学理论</a>
                            <a class="filter-item" href="">音乐与舞蹈学</a>
                            <a class="filter-item" href="">戏剧与影视学</a>
                            <a class="filter-item" href="">美术学</a>
                            <a class="filter-item" href="">设计学</a>
                            <a class="filter-item" href="">考古学</a>
                            <a class="filter-item" href="">中国史</a>
                            <a class="filter-item" href="">世界史</a>
                            <a class="filter-item" href="">教育学</a>
                            <a class="filter-item" href="">心理学</a>
                            <a class="filter-item" href="">体育学</a>
                            <a class="filter-item" href="">法学</a>
                            <a class="filter-item" href="">政治学</a>
                            <a class="filter-item" href="">社会学</a>
                            <a class="filter-item" href="">民族学</a>
                            <a class="filter-item" href="">马克思主义理论</a>
                            <a class="filter-item" href="">公安学</a>
                            <a class="filter-item" href="">军事学</a>
                        </div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">
                            单位类型
                            <div class="tips">（多选）</div>
                        </div>
                        <div class="filter-value">
                            <a class="filter-item" href="">不限</a>
                            <a class="filter-item" href="">双一流院校</a>
                            <div class="filter-item filter-item--limit">超出5个</div>
                            <a class="filter-item" href="">普通本科院校</a>
                            <a class="filter-item" href="">中国科学院系统</a>
                            <a class="filter-item active" href="">自然与应用科研机构（事业单位类型）</a>
                            <a class="filter-item" href="">企业研发机构</a>
                            <a class="filter-item" href="">人文社科研机构（事业单位类型）</a>
                            <a class="filter-item" href="">医院</a>
                            <a class="filter-item" href="">事业单位</a>
                            <a class="filter-item" href="">政府国有企业</a>
                            <a class="filter-item" href="">知名企业</a>
                            <a class="filter-item active" href="">中小成长型企业</a>
                            <a class="filter-item" href="">高职高专院校</a>
                            <a class="filter-item" href="">机关单位</a>
                            <a class="filter-item" href="">党校与行政学院</a>
                            <a class="filter-item active" href="">银行、信用社等金融机构</a>
                        </div>
                    </div>

                    <div class="filter-row more-row">
                        <div class="filter-label">更多筛选</div>
                        <div class="filter-value">
                            <!-- active 展开下拉框， is-select有选择值 -->
                            <div class="release custom-select">
                                <div class="el-select">
                                    <div class="select-trigger">
                                        <div class="el-input el-input--mini el-input--suffix">
                                            <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="发布时间" />
                                            <span class="el-input__suffix">
                                                <span class="el-input__suffix-inner">
                                                    <i class="down"></i>
                                                    <a class="el-select__caret el-input__icon el-icon-circle-close" href=""></a>
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="el-select__popper el-popper is-light is-pure">
                                    <div class="el-select-dropdown">
                                        <div class="el-scrollbar">
                                            <div class="el-select-dropdown__wrap el-scrollbar__wrap el-scrollbar__wrap--hidden-default">
                                                <div class="el-scrollbar__view el-select-dropdown__list">
                                                    <a class="el-select-dropdown__item" href="">
                                                        <span>近三天</span>
                                                    </a>
                                                    <a class="el-select-dropdown__item" href="">
                                                        <span>近七天</span>
                                                    </a>
                                                    <li class="el-select-dropdown__item selected">
                                                        <span>近一个月</span>
                                                    </li>
                                                    <a class="el-select-dropdown__item" href="">
                                                        <span>近三个月</span>
                                                    </a>
                                                    <a class="el-select-dropdown__item" href="">
                                                        <span>近一年</span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="el-popper__arrow"></div>
                                </div>
                            </div>
                            <!-- is-checked 已勾选 -->
                            <a href="" class="checkbox is-checked"> 只看PI团队直招 </a>
                        </div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">已选条件</div>
                        <div class="filter-value">
                            <div class="select-item">
                                北京
                                <a class="filter-clear" href=""></a>
                            </div>
                            <div class="select-item">
                                人文艺术与社会科学-中国语言文学
                                <a class="filter-clear" href=""></a>
                            </div>
                            <div class="select-item">
                                中国科学院系统
                                <a class="filter-clear" href=""></a>
                            </div>
                            <div class="select-item">
                                中小型成长企业
                                <a class="filter-clear" href=""></a>
                            </div>
                            <div class="select-item">
                                高职高专院校
                                <a class="filter-clear" href=""></a>
                            </div>
                            <div class="select-item">
                                银行、信用社等金融机构
                                <a class="filter-clear" href=""></a>
                            </div>
                            <div class="select-item">
                                党校与行政学院
                                <a class="filter-clear" href=""></a>
                            </div>
                        </div>
                        <a class="filter-clear-all" href="">清空筛选条件</a>
                    </div>
                </div>

                <div class="content-wrapper">
                    <div class="left-content">
                        <div class="tabs-content">
                            <a href="" class="tabs">
                                <div class="name">招收简章</div>
                            </a>
                            <a href="" class="tabs active">
                                <div class="english">POSTDOCTORAL JOB LIST</div>
                                <div class="name">博后职位</div>
                            </a>
                        </div>

                        <div>
                            <div class="list-content">
                                <!-- is-recommend 推荐，is-offline 已下线 -->
                                <div class="list is-recommend">
                                    <div class="top">
                                        <a class="title" href="" title="" target="_blank">
                                            <div class="tag-content">
                                                <span class="tag offline">已线下</span>

                                                <span class="tag urgent">急</span>
                                                <span class="tag recommend">荐</span>
                                                <span class="tag pi">PI直招</span>
                                            </div>
                                            名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公-告名称公告名称公-告名称公告名称，完整展示，自动换行
                                        </a>
                                        <div class="salary">100-200万/年</div>
                                    </div>

                                    <div class="detail">
                                        <div class="tag">乌鲁木齐</div>
                                        <div class="tag">招若干人</div>
                                        <div class="tag">计算机科学与技术,哲学,超12计算机科学与技术,哲学,超12字…</div>

                                        <div class="bright-spot">
                                            提供年薪80-100万、事业编制、提供250万科研经费、购房补贴、提供250万科研经提供年薪80-100万、事业编制、提供250万科研经费、购房补贴、提供250万科研经费
                                        </div>
                                    </div>

                                    <a class="announcement" href="" title="" target="_blank">
                                        公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称超1行公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称超1行…
                                    </a>

                                    <div class="bottom">
                                        <a class="company" href="" title="" target="_blank">
                                            <img src="https://picsum.photos/id/237/24/27" alt="" class="logo" />
                                            <div class="name">单位名称单位名称单位名称单位名称</div>
                                            <span class="type">| 自然与应用科研机构（事业单位类型）</span>
                                        </a>
                                        <div class="release-time">2024.05.20发布</div>
                                    </div>

                                    <a class="view" href="" title="" target="_blank">查看详情</a>
                                </div>

                                <div class="list is-offline">
                                    <div class="top">
                                        <a class="title" href="" title="" target="_blank">
                                            <div class="tag-content">
                                                <span class="tag offline">已线下</span>

                                                <span class="tag urgent">急</span>
                                                <span class="tag recommend">荐</span>
                                                <span class="tag pi">PI直招</span>
                                            </div>
                                            名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公-告名称公告名称公-告名称公告名称，完整展示，自动换行
                                        </a>
                                        <div class="salary">100-200万/年</div>
                                    </div>

                                    <div class="detail">
                                        <div class="tag">乌鲁木齐</div>
                                        <div class="tag">招若干人</div>
                                        <div class="tag">计算机科学与技术,哲学,超12计算机科学与技术,哲学,超12字…</div>

                                        <div class="bright-spot">
                                            提供年薪80-100万、事业编制、提供250万科研经费、购房补贴、提供250万科研经提供年薪80-100万、事业编制、提供250万科研经费、购房补贴、提供250万科研经费
                                        </div>
                                    </div>

                                    <a class="announcement" href="" title="" target="_blank">
                                        公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称超1行公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称公告名称超1行…
                                    </a>

                                    <div class="bottom">
                                        <a class="company" href="" title="" target="_blank">
                                            <img src="https://picsum.photos/id/237/24/27" alt="" class="logo" />
                                            <div class="name">单位名称单位名称单位名称单位名称</div>
                                            <span class="type">| 自然与应用科研机构（事业单位类型）</span>
                                        </a>
                                        <div class="release-time">2024.05.20发布</div>
                                    </div>

                                    <a class="view" href="" title="" target="_blank">查看详情</a>
                                </div>
                            </div>

                            <div id="paginationComponent" class="pagination">
                                <el-pagination background layout="prev, pager, next" @current-change="handlePageChange" :default-page-size="pageSize" :total="total" v-model:current-page="page" />
                            </div>
                        </div>

                        <div class="empty-container">暂无相关职位，请修改筛选条件试试</div>
                    </div>

                    <div class="aside">
                        <div class="login-guide open-login-dialog"></div>

                        <div class="ranking">
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">乌鲁木齐乌鲁木齐</div>
                                    </div>
                                </a>
                            </div>
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">乌鲁木齐乌鲁木齐</div>
                                    </div>
                                </a>
                            </div>
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">乌鲁木齐乌鲁木齐</div>
                                    </div>
                                </a>
                            </div>
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">北京</div>
                                    </div>
                                </a>
                            </div>
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">北京</div>
                                    </div>
                                </a>
                            </div>
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">北京</div>
                                    </div>
                                </a>
                            </div>
                            <div class="list">
                                <a href="" title="" target="_blank">
                                    <div class="name">职位名称职位名称职位名超1行职位名称职位名称职位名超1行...</div>
                                    <div class="info">
                                        <span class="salary">90-300万/年</span>
                                        | 计算机科学与技术等-...（事业单位类型）
                                    </div>
                                    <div class="bottom">
                                        <div class="company-name">单位名称单位名称超1行...</div>
                                        <div class="address">北京</div>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <div class="qr-code">
                            <div class="title">[高才博士后]公众号</div>
                            <div class="desc">扫码关注，第一时间获取优质的博士后招聘信息</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选弹出层 -->
            <div class="filter-dialog-template">
                <!-- 地区 -->
                <div class="el-overlay" id="areaDialog" style="z-index: 2003">
                    <div class="el-overlay-dialog">
                        <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
                            <div class="el-dialog__header">
                                <div class="filter-header">
                                    <div class="title">请选地区</div>
                                    <div class="search" id="areaSearchTemplate">
                                        <el-select
                                            v-model="filterHref"
                                            :remote-method="handleSearch"
                                            filterable
                                            remote
                                            reserve-keyword
                                            :size="size"
                                            placeholder="请输入关键词"
                                            @change="handleSearchChange"
                                        >
                                            <el-option v-for="item in filterOptions" :key="item.k" :label="item.v" :value="item.href" />
                                        </el-select>
                                    </div>
                                </div>
                                <button class="el-dialog__headerbtn close-dialog" type="button">
                                    <i class="el-dialog__close el-icon el-icon-close"></i>
                                </button>
                            </div>
                            <div class="el-dialog__body">
                                <div class="filter-content">
                                    <div class="data-content">
                                        <div class="left-content">
                                            <span class="list active">北京</span>
                                            <span class="list">天津</span>
                                            <span class="list">河北省</span>
                                        </div>
                                        <div class="right-content">
                                            <div class="second-content column-4 show">
                                                <div class="second-item">
                                                    <a class="active" href="">北京</a>
                                                </div>
                                            </div>
                                            <div class="second-content column-4">
                                                <div class="second-item">
                                                    <a class="" href="">天津</a>
                                                </div>
                                            </div>
                                            <div class="second-content column-4">
                                                <div class="second-item">
                                                    <a class="" href="">河北省</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">石家庄</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">唐山</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">秦皇岛</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">邯郸</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">邢台</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">保定</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">张家口</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--v-if-->
                        </div>
                    </div>
                    <script>
                        const areaSearchOptions = {
                            data() {
                                return {
                                    size: 'small',
                                    filterHref: '',
                                    options: [
                                        {
                                            k: '1',
                                            v: '北京',
                                            parentId: '0',
                                            level: '1',
                                            fullName: '北京',
                                            spell: 'beijings',
                                            children: [
                                                {
                                                    k: '2',
                                                    v: '北京',
                                                    parentId: '1',
                                                    level: '2',
                                                    fullName: '北京,北京市',
                                                    spell: 'beijing',
                                                    topParentId: '1'
                                                }
                                            ]
                                        },
                                        {
                                            k: '37',
                                            v: '河北',
                                            parentId: '0',
                                            level: '1',
                                            fullName: '河北省',
                                            spell: 'hebei'
                                        },
                                        {
                                            k: '37',
                                            v: '河北省',
                                            parentId: '0',
                                            level: '1',
                                            spell: 'hebei',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '38',
                                            v: '石家庄',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,石家庄市',
                                            spell: 'shijiazhuang',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '61',
                                            v: '唐山',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,唐山市',
                                            spell: 'tangshan',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '76',
                                            v: '秦皇岛',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,秦皇岛市',
                                            spell: 'qinhuangdao',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '84',
                                            v: '邯郸',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,邯郸市',
                                            spell: 'handan',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '104',
                                            v: '邢台',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,邢台市',
                                            spell: 'xingtai',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '124',
                                            v: '保定',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,保定市',
                                            spell: 'baoding',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '150',
                                            v: '张家口',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,张家口市',
                                            spell: 'zhangjiakou',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '168',
                                            v: '承德',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,承德市',
                                            spell: 'chengde',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '180',
                                            v: '沧州',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,沧州市',
                                            spell: 'cangzhou',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '197',
                                            v: '廊坊',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,廊坊市',
                                            spell: 'langfang',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '208',
                                            v: '衡水',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,衡水市',
                                            spell: 'hengshui',
                                            topParentId: '37'
                                        }
                                    ],
                                    filterOptions: []
                                }
                            },

                            mounted() {},

                            methods: {
                                handleSearch(query) {
                                    if (query) {
                                        const { options } = this
                                        setTimeout(() => {
                                            const objMap = new Map()
                                            this.filterOptions = options.filter((item) => {
                                                const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                                const { v } = item
                                                const flag = isIncludes && !objMap.has(v)
                                                if (flag) objMap.set(v, 1)
                                                return flag
                                            })
                                        }, 200)
                                    } else {
                                        this.filterOptions = []
                                    }
                                },

                                handleSearchChange() {
                                    window.location.href = this.filterHref
                                }
                            }
                        }

                        Vue.createApp(areaSearchOptions).use(ElementPlus).mount('#areaSearchTemplate')
                    </script>
                </div>

                <div class="el-overlay" id="majorDialog" style="z-index: 2003">
                    <div class="el-overlay-dialog">
                        <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
                            <div class="el-dialog__header">
                                <div class="filter-header">
                                    <div class="title">请选地区</div>
                                    <div class="search" id="majorSearchTemplate">
                                        <el-select
                                            v-model="filterHref"
                                            :remote-method="handleSearch"
                                            filterable
                                            remote
                                            reserve-keyword
                                            :size="size"
                                            placeholder="请输入关键词"
                                            @change="handleSearchChange"
                                        >
                                            <el-option v-for="item in filterOptions" :key="item.k" :label="item.v" :value="item.href" />
                                        </el-select>
                                    </div>
                                </div>
                                <button class="el-dialog__headerbtn close-dialog" type="button">
                                    <i class="el-dialog__close el-icon el-icon-close"></i>
                                </button>
                            </div>
                            <div class="el-dialog__body">
                                <div class="filter-content">
                                    <div class="data-content">
                                        <div class="left-content">
                                            <span class="list active">哲学</span>
                                            <span class="list">经济学</span>
                                            <span class="list">法学</span>
                                            <span class="list">教育学</span>
                                            <span class="list">文学</span>
                                        </div>
                                        <div class="right-content">
                                            <div class="second-content column-4 show">
                                                <div class="second-item">
                                                    <a class="active" href="">哲学</a>
                                                </div>
                                            </div>
                                            <div class="second-content column-4">
                                                <div class="second-item">
                                                    <a class="" href="">理论经济学</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">应用经济学</a>
                                                </div>
                                            </div>
                                            <div class="second-content column-4">
                                                <div class="second-item">
                                                    <a class="" href="">法学与法律</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">政治学</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">社会学</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">民族学</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">马克思主义理论</a>
                                                </div>
                                            </div>
                                            <div class="second-content column-4">
                                                <div class="second-item">
                                                    <a class="" href="">心理学</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href="">体育学</a>
                                                </div>
                                            </div>
                                            <div class="second-content column-4">
                                                <div class="second-item">
                                                    <a class="" href="">中国语言文学</a>
                                                </div>
                                                <div class="second-item">
                                                    <a class="" href=""></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--v-if-->
                        </div>
                    </div>
                    <script>
                        const majorSearchOptions = {
                            data() {
                                return {
                                    size: 'small',
                                    filterHref: '',
                                    options: [
                                        {
                                            k: '1',
                                            v: '哲学',
                                            parentId: '0'
                                        },
                                        {
                                            k: '1',
                                            v: '哲学（全部）',
                                            parentId: '0',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '16',
                                            v: '哲学',
                                            parentId: '1',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '2',
                                            v: '经济学',
                                            parentId: '0'
                                        },
                                        {
                                            k: '2',
                                            v: '经济学（全部）',
                                            parentId: '0',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '17',
                                            v: '理论经济学',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '18',
                                            v: '应用经济学',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '748',
                                            v: '数字经济',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '802',
                                            v: '金融学',
                                            parentId: '2',
                                            topParentId: '2'
                                        }
                                    ],
                                    filterOptions: []
                                }
                            },

                            mounted() {},

                            methods: {
                                handleSearch(query) {
                                    if (query) {
                                        const { options } = this
                                        setTimeout(() => {
                                            const objMap = new Map()
                                            this.filterOptions = options.filter((item) => {
                                                const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                                const { v } = item
                                                const flag = isIncludes && !objMap.has(v)
                                                if (flag) objMap.set(v, 1)
                                                return flag
                                            })
                                        }, 200)
                                    } else {
                                        this.filterOptions = []
                                    }
                                },
                                handleSearchChange() {
                                    window.location.href = this.filterHref
                                }
                            }
                        }

                        Vue.createApp(majorSearchOptions).use(ElementPlus).mount('#majorSearchTemplate')
                    </script>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar-container">
                <div class="guild-part">
                    <a href="/member/person/home" target="_blank" class="tools-link is-home"> 主页 </a>

                    <a href="/member/person/resume" target="_blank" class="tools-link is-resume">简历</a>

                    <a href="/member/person/invite" target="_blank" class="tools-link is-invite">
                        <span>邀约</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/delivery" target="_blank" class="tools-link is-delivery">
                        <span>投递</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/chat" target="_blank" class="tools-link is-chat">
                        <span>直聊</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <!-- <a href="/member/person/collection" target="_blank"
                                        class="tools-link is-collect">收藏</a> -->

                    <!-- <a href="/member/person/message" target="_blank" class="tools-link is-news">消息</a> -->
                </div>

                <div class="status-part" id="statusTemplate">
                    <a href="javascript:;" class="link-icon miniapp">
                        <span>小程序</span>
                        <span class="miniapp-hover">扫码进入小程序</span>
                    </a>

                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高才博士后公众号</span>
                    </a>

                    <a href="javascript:;" class="link-icon mobile">
                        <span>移动端</span>
                        <span class="mobile-hover">查看高校人才网移动端</span>
                    </a>

                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <a href="javascript:;" class="link-icon feedback-link">
                                <span>
                                    咨询<br />
                                    反馈
                                </span>
                                <span class="mobile-hover">查看高校人才网移动端</span>
                            </a>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>平台功能体验等问题请联系：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                        <p>求职会员服务问题请咨询：</p>
                                        <p><strong>微信：</strong>gzgxrcw14</p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>

                    <!-- <a href="//weibo.com/jobgd" target="_blank" class="link-icon weibo">
                        <span>微博</span>
                        <span class="weibo-hover">关注高校人才网官方微博</span>
                    </a> -->
                </div>
            </div>

            <!-- 返回顶部 start -->
            <div id="backtopTemplate">
                <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                    <span class="backtop-button"></span>
                    <div class="top">TOP</div>
                </el-backtop>
            </div>
            <script>
                $(function () {
                    const backtopOptions = {
                        computed: {
                            viewportHeight() {
                                return window.innerHeight / 2
                            }
                        }
                    }

                    Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
                    Vue.createApp({}).use(ElementPlus).mount('#statusTemplate')
                })
            </script>
            <!-- 返回顶部 end -->

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                    <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>
        </div>

        <script src="./js/postdoctor/announcementAndJob.js"></script>
        <script>
            $(function () {
                const keywordComponent = {
                    data() {
                        return {
                            keyword: ''
                        }
                    },
                    methods: {
                        search() {
                            updateHref({ keyword: this.keyword, page: '' })
                        },

                        handleKwClear() {
                            updateHref({ keyword: '', page: '' })
                        }
                    },
                    mounted() {}
                }

                Vue.createApp(keywordComponent).use(ElementPlus).mount('#keywordComponent')

                const paginationComponent = {
                    data() {
                        return {
                            page: 1,
                            pageSize: 15,
                            total: 100
                        }
                    },
                    methods: {
                        handlePageChange(page) {
                            updateHref({ page: page })
                        }
                    },
                    mounted() {}
                }

                const paginationVue = Vue.createApp(paginationComponent).use(ElementPlus).mount('#paginationComponent')

                function scrollToDataContent() {
                    const page = getHrefQuery('page')
                    if (page) {
                        scrollToTarget('filter-wrapper')
                    }
                }
                scrollToDataContent()

                var isToast = false
                $('.filter-wrapper').on('click', '.filter-item--limit', function () {
                    if (!isToast) {
                        isToast = true
                        ElementPlus.ElMessage.warning('最多可选择5项')
                        setTimeout(() => {
                            isToast = false
                        }, 3000)
                    }
                })
            })
        </script>
    </body>
</html>
