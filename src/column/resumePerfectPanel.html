<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>简历完善度组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/resumePerfectPanel.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
    </head>

    <body>
        <div id="resumePerfectPanelTemplate" class="resume-perfect-panel-template">
            <div class="resume-perfect-panel-container" v-if="visible">
                <div class="resume-perfect-tips">您当前简历完善度不足<spen class="percentage">75%</spen>，请完善简历，提升您的求职成功率！</div>

                <el-button class="resume-perfect-btn" @click="handlePerfect"> 去完善 </el-button>

                <el-button class="close-button" @click="visible = false"><i class="el-icon el-icon-close"></i> </el-button>
            </div>
        </div>

        <script>
            $(function () {
                const resumePerfectPanelOptions = {
                    data() {
                        return {
                            visible: true
                        }
                    },

                    methods: {
                        handlePerfect() {
                            location.href = '/member/person/resume'
                        }
                    },

                    mounted() {}
                }
                Vue.createApp(resumePerfectPanelOptions).use(ElementPlus).mount('#resumePerfectPanelTemplate')
            })
        </script>
    </body>
</html>
