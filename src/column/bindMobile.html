<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>绑定手机号</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/bindMobile.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <div class="bind-mobile-container">
            <header class="header-container">
                <nav>
                    <a href="/" class="header-logo"></a>
                    <div class="header-slogan">高校人才网：一站式高层次人才求职招聘服务平台</div>
                </nav>
            </header>

            <main class="bind-container" id="bindMobileTemplate" v-cloak>
                <div class="bind-main">
                    <div class="title">绑定手机号</div>
                    <a class="back" href="javascript:;" onclick="history.back(-1)">返回</a>
                    <div class="tips">根据相关法律法规要求，须完善手机号。完善后可以使用手机号登录</div>
                    <el-form class="bind-form" ref="bindMobileRef" :model="bindMobileForm" :rules="bindMobileRules">
                        <el-form-item prop="mobile">
                            <el-input
                                class="mobile"
                                v-model="bindMobileForm.mobile"
                                oninput="value=value.replace(/[^0-9]/g, '')"
                                name="gaoxiaojobMobile"
                                autocomplete="on"
                                placeholder="请输入常用手机号码"
                                :maxlength="mobileMaxLength"
                                clearable
                            >
                                <!-- 暂时不要前置icon -->
                                <!-- <template #prefix></template> -->

                                <!-- 号段 -->
                                <template #prefix>
                                    <el-select v-model="bindMobileForm.mobileCode" popper-class="bind-mobile-prefix-popper" class="mobile-prefix-select">
                                        <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
                                            <el-option v-for="{ country, code } in list" :key="code" :value="code">
                                                <span style="float: left">{{ country }}</span>
                                                <span style="float: right"> {{ code }} </span>
                                            </el-option>
                                        </el-option-group>
                                    </el-select>
                                </template>
                            </el-input>
                        </el-form-item>

                        <el-form-item prop="code">
                            <el-input class="sms-code" v-model="bindMobileForm.code" placeholder="请输入验证码" clearable>
                                <!-- 暂时不要前置icon -->
                                <!-- <template #prefix></template> -->
                                <template #suffix>
                                    <el-button :disabled="codeDisabled" @click="handleSendCode" style="background: initial" v-html="codeText"> </el-button>
                                </template>
                            </el-input>
                        </el-form-item>

                        <el-button class="bind-mobile-confirm" type="primary" :loading="loading" @click="handleBind"> 登录 </el-button>
                    </el-form>
                </div>
            </main>

            <footer class="footer-container">
                <div class="footer-main"></div>
            </footer>
        </div>

        <script>
            $(function () {
                const loginDialogOptions = {
                    data() {
                        function validMobile(mobile) {
                            const reg = /^1[3-9]\d{9}$/
                            return reg.test(mobile)
                        }

                        function validNumber(number) {
                            return /^\d+$/.test(number)
                        }

                        const validateMobile = (rule, value, callback) => {
                            const {
                                bindMobileForm: { mobileCode }
                            } = this
                            if (mobileCode === '+86') {
                                if (validMobile(value)) {
                                    callback()
                                } else {
                                    callback('请输入正确的手机号码')
                                }
                            } else if (validNumber(value)) {
                                callback()
                            } else {
                                callback('请输入正确的手机号码')
                            }
                        }

                        return {
                            prefixOptions: [],

                            bindMobileForm: {
                                token: '',
                                mobileCode: '+86',
                                mobile: '',
                                code: ''
                            },

                            codeDisabled: false,
                            codeText: '获取验证码',
                            codeTime: 60,
                            codeTimer: null,

                            loading: false,

                            bindMobileRules: {
                                mobile: [
                                    { required: true, message: '请输入手机号码', trigger: 'blur' },
                                    { validator: validateMobile, trigger: 'blur' }
                                ],
                                code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
                            }
                        }
                    },

                    computed: {
                        redirectQuery() {
                            return `?redirect=${window.location.href}`
                        },
                        isNeedStep() {
                            var referrer = document.referrer
                            console.log(referrer)
                            return /\/a\//.test(referrer) ? 2 : ''
                        },
                        mobileMaxLength: function () {
                            return this.bindMobileForm.mobileCode === '+86' ? 11 : 20
                        }
                    },

                    methods: {
                        /* 海外号段 */
                        async getMobilePrefix() {
                            const data = await httpGet('/api/config/load-country-mobile-code')
                            this.prefixOptions = data
                        },

                        getParams(key) {
                            const search = window.location.search
                            const [mark, ...queryArr] = search.split('?')
                            const query = queryArr.join('?')
                            if (!query) return ''
                            if (new RegExp(`${key}=([^&]*)`).test(query)) {
                                const value = decodeURIComponent(RegExp.$1)
                                return /^-?\d+$/.test(value) ? value * 1 : value
                            }
                        },

                        getToken() {
                            this.bindMobileForm.token = this.getParams('token')
                        },

                        async getMobileCode() {
                            const {
                                bindMobileForm: { mobileCode, mobile, token }
                            } = this
                            await httpPost('/api/member/send-wx-bind-mobile-code', {
                                mobileCode,
                                mobile,
                                token,
                                type: 1
                            })
                        },

                        countDown() {
                            this.codeDisabled = true
                            this.codeTimer = setInterval(() => {
                                if (this.codeTime === 1) {
                                    clearInterval(this.codeTimer)
                                    this.codeTime = 60
                                    this.codeText = '重新发送'
                                    this.codeDisabled = false
                                } else {
                                    this.codeTime -= 1
                                    this.codeText = `${this.codeTime}S<span>重新获取</span>`
                                }
                            }, 1000)
                        },

                        handleSendCode() {
                            this.$refs.bindMobileRef.validateField('mobile', (errMsg) => {
                                if (errMsg.length === 0) {
                                    this.getMobileCode()
                                    this.countDown()
                                }
                            })
                        },

                        // handleNavigateTo(resumeStep) {
                        //     let redirectUrl = this.getParams('redirect')

                        //     const stepViewUrls = [
                        //         '/member/person/required',
                        //         '/member/person/required/basic',
                        //         '/member/person/required/education',
                        //         '/member/person/required/intention',
                        //     ]

                        //     let href = ''

                        //     if (redirectUrl) {
                        //         href = redirectUrl
                        //     } else {
                        //         href = resumeStep > 3 ? '/job' : stepViewUrls[resumeStep]
                        //     }
                        //     window.location.href = href
                        // },

                        handleBind() {
                            const formRef = this.$refs['bindMobileRef']
                            const bindApi = '/api/member/validate-wx-bind-mobile-code'
                            let redirectUrl = this.getParams('redirect') || '/job'

                            formRef.validate(async (valid) => {
                                if (valid) {
                                    this.loading = true

                                    const {
                                        bindMobileForm: { mobileCode, mobile, code, token }
                                    } = this
                                    let formData = {
                                        mobileCode,
                                        mobile,
                                        code,
                                        token,
                                        redirect: redirectUrl,
                                        isNeedStep: this.isNeedStep
                                    }

                                    try {
                                        const { token, expireTime, redirectUrl } = await httpPost(bindApi, formData)
                                        setToken(token, expireTime)
                                        window.location.href = redirectUrl
                                    } catch (err) {
                                        this.loading = false
                                    }
                                }
                            })
                        }
                    },

                    mounted() {
                        /* 海外号段 */
                        this.getMobilePrefix()
                        this.getToken()
                    }
                }
                const loginDialogComponent = Vue.createApp(loginDialogOptions).use(ElementPlus).mount('#bindMobileTemplate')
            })
        </script>
    </body>
</html>
