<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>博士后单位大厅</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/commonPostdoctor.css" />
        <link rel="stylesheet" href="./css/postdoctorCompany.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./js/postdoctor/common.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <div class="postdoctor-container">
            <header class="postdoctor-header-container">
                <div class="postdoctor-header-content">
                    <div class="postdoctor-menu-content">
                        <a href="/" class="logo">
                            <span class="logo-text">高才博士后</span>

                            <div class="collect-tips"></div>
                        </a>
                        <a href="/" class="postdoctor-menu-item">首页</a>
                        <a href="/" class="postdoctor-menu-item">博后公告&职位</a>
                        <a href="/" class="postdoctor-menu-item active">招收单位& PI大厅</a>
                        <a href="/" class="postdoctor-menu-item">博后活动</a>
                    </div>
                    <div class="postdoctor-nav-content">
                        <a href="" class="postdoctor-nav-item">需求发布</a>
                        <a href="" class="postdoctor-nav-item" target="_blank">高才官网</a>
                        <a href="" class="postdoctor-nav-item" target="_blank">单位登录</a>

                        <button class="postdoctor-login-btn open-login-dialog">人才登录</button>
                        <!-- <a class="person-avatar" href="" target="_blank"></a>
                            <img src="https://picsum.photos/seed/picsum/36/36" alt="" />
                        </a> -->
                    </div>
                </div>

                <script>
                    $(function () {
                        var $header = $('.postdoctor-header-container')
                        var $logo = $header.find('.logo')
                        var $logoTips = $header.find('.collect-tips')
                        var isCollectShow = false

                        function showCollect() {
                            if (isCollectShow) return

                            isCollectShow = true
                            var time = new Date().getTime()

                            $logoTips.fadeIn(0).css('background', `url(./assets/postdoctor/common/collect-tips.gif?v=${time}) no-repeat center/100% auto`)

                            setTimeout(() => {
                                $logoTips.fadeOut(1000)

                                setTimeout(() => {
                                    isCollectShow = false
                                }, 1000)
                            }, 6000)
                        }

                        var envHrefMap = [
                            'https://boshihou.gaoxiaojob.com/',
                            'https://boshihou.test.gcjob.jugaocai.com/',
                            'https://boshihou.dev.gcjob.jugaocai.com/',
                            'https://boshihou.pre.gcjob.jugaocai.com/',
                            'https://boshihou.gray.gcjob.jugaocai.com/'
                        ]
                        var href = window.location.href

                        var isPostdoctorHome = envHrefMap.includes(href)

                        if (isPostdoctorHome) {
                            showCollect()
                        }

                        $logo.mouseenter(showCollect)
                    })
                </script>
            </header>

            <div class="main-container">
                <div class="showcase-wrapper">
                    <div class="showcase-top">
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/590/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/590/90" alt="" />
                        </a>
                    </div>
                    <div class="showcase-bottom">
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                        <a class="item animation-mouseover" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/290/90" alt="" />
                        </a>
                    </div>
                </div>

                <div id="filterComponent" class="filter-wrapper" v-cloak>
                    <div class="filter-row kw-row">
                        <div class="filter-label">关键词</div>
                        <div class="filter-value">
                            <div class="search">
                                <el-input class="kw" v-model="formData.keyword" @keyup.enter="fetchList" @clear="handleKwClear" placeholder="请输入关键词搜索" clearable></el-input>
                                <button @click="fetchList()" class="search-btn">搜索</button>
                            </div>
                            <div v-if="companyHostSearch.length > 0" class="search-hot">
                                <div class="search-hot-label">热门搜索:</div>
                                <div class="search-hot-value">
                                    <a
                                        href="javascript:;"
                                        class="item showcase-browse"
                                        :data-showcase-number="item.number"
                                        :data-showcase-id="item.id"
                                        :class="{active: item.id===companyHostSearchId}"
                                        v-for="item in companyHostSearch"
                                        @click="handleCompanyHotSearch(item)"
                                    >
                                        {{item.title}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">所在地区</div>
                        <div class="filter-value">
                            <button class="filter-item" :class="{active: formData.areaId.length === 0}" @click="handleFilter('areaId', '')">全部</button>
                            <button class="filter-item" :class="{active: formData.areaId.includes(item.value) }" @click="handleFilter('areaId', item.value)" v-for="item in hotAreaList">
                                {{item.label}}
                                <span class="filter-clear" @click="(e) => handleClearFilter('areaId', item.value, e)"></span>
                            </button>
                        </div>
                        <div class="filter-more" @click="openSelectDialog">更多地区</div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">单位类型</div>
                        <div class="filter-value">
                            <button class="filter-item" :class="{active: formData.companyType.length === 0}" @click="handleFilter('companyType', '')">全部</button>
                            <button class="filter-item" :class="{active: formData.companyType.includes(item.value)}" @click="handleFilter('companyType', item.value)" v-for="item in companyTypeList">
                                {{item.label}}
                                <span class="filter-clear" @click="(e) => handleClearFilter('companyType',item.value, e)"></span>
                            </button>
                        </div>
                    </div>

                    <div class="filter-row">
                        <div class="filter-label">更多筛选</div>
                        <div class="filter-value">
                            <el-checkbox size="small" v-model="formData.isPi" @change="fetchList()" />
                            只看PI团队直招
                        </div>
                        <div class="filter-clear-all" @click="handleClearAll">清空筛选条件<span v-if="queryTotal">（{{queryTotal}}）</span></div>
                    </div>

                    <select-dialog v-model="formData.areaId" :list="areaList" multiple :multiple-limit="5" @update="fetchList()" ref="selectDialog"></select-dialog>
                </div>

                <div class="company-wrapper" id="company-wrapper">
                    <div class="list-content">
                        <div class="list animation-mouseover">
                            <div class="tag">PI直招</div>
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <!-- <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a> -->

                                <div class="empty">
                                    <div class="text">更多招聘信息请前往<br />单位中心查看</div>
                                </div>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                        <div class="list animation-mouseover">
                            <a href="" class="company-info" target="_blank">
                                <div class="logo">
                                    <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                </div>
                                <div class="info">
                                    <div class="title">广东工业大学广东工业大学广东广东广东工业大学广东工业大学广东广东慌</div>
                                    <div class="type">普通本科院校 | 公立（国有）</div>
                                </div>
                            </a>
                            <div class="announcement">
                                <a href="" class="item" target="_blank">
                                    <div class="name">广东工业大学诚邀海外英才申报2024年国广东工业大学诚邀海外英才申报2024年国</div>
                                    <span class="time">05.20</span>
                                </a>
                            </div>
                            <a href="" class="bottom" target="_blank">
                                <div class="aside">
                                    <div class="total">在招公告：<span>50</span></div>
                                    <div class="total">在招职位：<span>502</span></div>
                                </div>

                                <div class="address">呼和浩特特呼和浩特特...</div>
                                <div class="more">查看更多</div>
                            </a>
                        </div>
                    </div>

                    <div id="paginationComponent" class="pagination">
                        <el-pagination background layout="prev, pager, next" @current-change="handlePageChange" :default-page-size="pageSize" :total="total" v-model:current-page="page" />
                    </div>

                    <div class="empty-container">暂无相关单位，请修改筛选条件试试</div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar-container">
                <div class="guild-part">
                    <a href="/member/person/home" target="_blank" class="tools-link is-home"> 主页 </a>

                    <a href="/member/person/resume" target="_blank" class="tools-link is-resume">简历</a>

                    <a href="/member/person/invite" target="_blank" class="tools-link is-invite">
                        <span>邀约</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/delivery" target="_blank" class="tools-link is-delivery">
                        <span>投递</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/chat" target="_blank" class="tools-link is-chat">
                        <span>直聊</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <!-- <a href="/member/person/collection" target="_blank"
                                        class="tools-link is-collect">收藏</a> -->

                    <!-- <a href="/member/person/message" target="_blank" class="tools-link is-news">消息</a> -->
                </div>

                <div class="status-part" id="statusTemplate">
                    <a href="javascript:;" class="link-icon miniapp">
                        <span>小程序</span>
                        <span class="miniapp-hover">扫码进入小程序</span>
                    </a>

                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高才博士后公众号</span>
                    </a>

                    <a href="javascript:;" class="link-icon mobile">
                        <span>移动端</span>
                        <span class="mobile-hover">查看高校人才网移动端</span>
                    </a>

                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <a href="javascript:;" class="link-icon feedback-link">
                                <span>
                                    咨询<br />
                                    反馈
                                </span>
                                <span class="mobile-hover">查看高校人才网移动端</span>
                            </a>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>平台功能体验等问题请联系：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                        <p>求职会员服务问题请咨询：</p>
                                        <p><strong>微信：</strong>gzgxrcw14</p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>

                    <!-- <a href="//weibo.com/jobgd" target="_blank" class="link-icon weibo">
                        <span>微博</span>
                        <span class="weibo-hover">关注高校人才网官方微博</span>
                    </a> -->
                </div>
            </div>

            <!-- 返回顶部 start -->
            <div id="backtopTemplate">
                <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                    <span class="backtop-button"></span>
                    <div class="top">TOP</div>
                </el-backtop>
            </div>
            <script>
                $(function () {
                    const backtopOptions = {
                        computed: {
                            viewportHeight() {
                                return window.innerHeight / 2
                            }
                        }
                    }

                    Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
                    Vue.createApp({}).use(ElementPlus).mount('#statusTemplate')
                })
            </script>
            <!-- 返回顶部 end -->

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                    <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>
        </div>

        <script>
            $(function () {
                function getList(query) {
                    var params = {}
                    Object.keys(query).forEach((key) => {
                        const value = query[key]
                        const isArray = Array.isArray(value)
                        params[key] = isArray ? value.join() : value

                        if (key == 'isPi') {
                            params[key] = value ? '1' : ''
                        }
                    })
                    httpGet('/company/get-search-list', params).then((resp) => {
                        const { list, count } = resp
                        paginationVue._.data.total = count
                        if (count) {
                            $('.company-wrapper .list-content').html(list)
                            $('.empty-container').hide()
                            $('#paginationComponent').show()
                        } else {
                            $('.company-wrapper .list-content').html('')
                            $('.empty-container').show()
                            $('#paginationComponent').hide()
                        }
                    })
                }

                const filterComponent = {
                    data() {
                        return {
                            hotAreaList: [],
                            areaList: [
                                {
                                    k: '0',
                                    v: '热门城市',
                                    parentId: '-1',
                                    level: '0',
                                    children: [
                                        {
                                            k: '2',
                                            v: '北京',
                                            parentId: '1',
                                            level: '2',
                                            spell: 'beijing',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '20',
                                            v: '天津',
                                            parentId: '19',
                                            level: '2',
                                            spell: 'tianjin',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '656',
                                            v: '哈尔滨',
                                            parentId: '655',
                                            level: '2',
                                            spell: 'haerbin',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '802',
                                            v: '上海',
                                            parentId: '801',
                                            level: '2',
                                            spell: 'shanghai',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '821',
                                            v: '南京',
                                            parentId: '820',
                                            level: '2',
                                            spell: 'nanjing',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '934',
                                            v: '杭州',
                                            parentId: '933',
                                            level: '2',
                                            spell: 'hangzhou',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1047',
                                            v: '合肥',
                                            parentId: '1046',
                                            level: '2',
                                            spell: 'hefei',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1169',
                                            v: '福州',
                                            parentId: '1168',
                                            level: '2',
                                            spell: 'fuzhou',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1376',
                                            v: '济南',
                                            parentId: '1375',
                                            level: '2',
                                            spell: 'jinan',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1533',
                                            v: '郑州',
                                            parentId: '1532',
                                            level: '2',
                                            spell: 'zhengzhou',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1710',
                                            v: '武汉',
                                            parentId: '1709',
                                            level: '2',
                                            spell: 'wuhan',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1828',
                                            v: '长沙',
                                            parentId: '1827',
                                            level: '2',
                                            spell: 'changsha',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1965',
                                            v: '广州',
                                            parentId: '1964',
                                            level: '2',
                                            spell: 'guangzhou',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1988',
                                            v: '深圳',
                                            parentId: '1964',
                                            level: '2',
                                            spell: 'shenzhen',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '1999',
                                            v: '珠海',
                                            parentId: '1964',
                                            level: '2',
                                            spell: 'zhuhai',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '2324',
                                            v: '重庆',
                                            parentId: '2323',
                                            level: '2',
                                            spell: 'chongqing',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '2368',
                                            v: '成都',
                                            parentId: '2367',
                                            level: '2',
                                            spell: 'chengdu',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '2899',
                                            v: '西安',
                                            parentId: '2898',
                                            level: '2',
                                            spell: 'xian',
                                            topParentId: '0'
                                        }
                                    ],
                                    spell: ''
                                },
                                {
                                    k: '1',
                                    v: '北京',
                                    parentId: '0',
                                    level: '1',
                                    fullName: '北京',
                                    spell: 'beijings',
                                    children: [
                                        {
                                            k: '2',
                                            v: '北京',
                                            parentId: '1',
                                            level: '2',
                                            fullName: '北京,北京市',
                                            spell: 'beijing',
                                            topParentId: '1'
                                        }
                                    ]
                                },
                                {
                                    k: '19',
                                    v: '天津',
                                    parentId: '0',
                                    level: '1',
                                    fullName: '天津',
                                    spell: 'tianjins',
                                    children: [
                                        {
                                            k: '20',
                                            v: '天津',
                                            parentId: '19',
                                            level: '2',
                                            fullName: '天津,天津市',
                                            spell: 'tianjin',
                                            topParentId: '19'
                                        }
                                    ]
                                },
                                {
                                    k: '37',
                                    v: '河北',
                                    parentId: '0',
                                    level: '1',
                                    fullName: '河北省',
                                    spell: 'hebei',
                                    children: [
                                        {
                                            k: '37',
                                            v: '河北省',
                                            parentId: '0',
                                            level: '1',
                                            spell: 'hebei',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '38',
                                            v: '石家庄',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,石家庄市',
                                            spell: 'shijiazhuang',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '61',
                                            v: '唐山',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,唐山市',
                                            spell: 'tangshan',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '76',
                                            v: '秦皇岛',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,秦皇岛市',
                                            spell: 'qinhuangdao',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '84',
                                            v: '邯郸',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,邯郸市',
                                            spell: 'handan',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '104',
                                            v: '邢台',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,邢台市',
                                            spell: 'xingtai',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '124',
                                            v: '保定',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,保定市',
                                            spell: 'baoding',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '150',
                                            v: '张家口',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,张家口市',
                                            spell: 'zhangjiakou',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '168',
                                            v: '承德',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,承德市',
                                            spell: 'chengde',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '180',
                                            v: '沧州',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,沧州市',
                                            spell: 'cangzhou',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '197',
                                            v: '廊坊',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,廊坊市',
                                            spell: 'langfang',
                                            topParentId: '37'
                                        },
                                        {
                                            k: '208',
                                            v: '衡水',
                                            parentId: '37',
                                            level: '2',
                                            fullName: '河北省,衡水市',
                                            spell: 'hengshui',
                                            topParentId: '37'
                                        }
                                    ]
                                },
                                {
                                    k: '220',
                                    v: '山西',
                                    parentId: '0',
                                    level: '1',
                                    fullName: '山西省',
                                    spell: 'shanxi',
                                    children: [
                                        {
                                            k: '220',
                                            v: '山西省',
                                            parentId: '0',
                                            level: '1',
                                            spell: 'shanxi',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '221',
                                            v: '太原',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,太原市',
                                            spell: 'taiyuan',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '232',
                                            v: '大同',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,大同市',
                                            spell: 'datong',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '244',
                                            v: '阳泉',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,阳泉市',
                                            spell: 'yangquan',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '250',
                                            v: '长治',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,长治市',
                                            spell: 'changzhi',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '264',
                                            v: '晋城',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,晋城市',
                                            spell: 'jincheng',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '271',
                                            v: '朔州',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,朔州市',
                                            spell: 'shuozhou',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '278',
                                            v: '晋中',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,晋中市',
                                            spell: 'jinzhong',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '290',
                                            v: '运城',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,运城市',
                                            spell: 'yuncheng',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '304',
                                            v: '忻州',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,忻州市',
                                            spell: 'xinzhou',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '319',
                                            v: '临汾',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,临汾市',
                                            spell: 'linfen',
                                            topParentId: '220'
                                        },
                                        {
                                            k: '337',
                                            v: '吕梁',
                                            parentId: '220',
                                            level: '2',
                                            fullName: '山西省,吕梁市',
                                            spell: 'lvliang',
                                            topParentId: '220'
                                        }
                                    ]
                                },
                                {
                                    k: '351',
                                    v: '内蒙古',
                                    parentId: '0',
                                    level: '1',
                                    fullName: '内蒙古自治区',
                                    spell: 'neimenggu',
                                    children: [
                                        {
                                            k: '351',
                                            v: '内蒙古自治区',
                                            parentId: '0',
                                            level: '1',
                                            spell: 'neimenggu',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '352',
                                            v: '呼和浩特',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,呼和浩特市',
                                            spell: 'huhehaote',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '362',
                                            v: '包头',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,包头市',
                                            spell: 'baotou',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '372',
                                            v: '乌海',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,乌海市',
                                            spell: 'wuhai',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '376',
                                            v: '赤峰',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,赤峰市',
                                            spell: 'chifeng',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '389',
                                            v: '通辽',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,通辽市',
                                            spell: 'tongliao',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '398',
                                            v: '鄂尔多斯',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,鄂尔多斯市',
                                            spell: 'eerduosi',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '407',
                                            v: '呼伦贝尔',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,呼伦贝尔市',
                                            spell: 'hulunbeier',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '422',
                                            v: '巴彦淖尔',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,巴彦淖尔市',
                                            spell: 'bayannaoer',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '430',
                                            v: '乌兰察布',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,乌兰察布市',
                                            spell: 'wulanchabu',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '442',
                                            v: '兴安盟',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,兴安盟',
                                            spell: 'xinganmeng',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '449',
                                            v: '锡林郭勒盟',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,锡林郭勒盟',
                                            spell: 'xilinguolemeng',
                                            topParentId: '351'
                                        },
                                        {
                                            k: '462',
                                            v: '阿拉善盟',
                                            parentId: '351',
                                            level: '2',
                                            fullName: '内蒙古自治区,阿拉善盟',
                                            spell: 'alashanmeng',
                                            topParentId: '351'
                                        }
                                    ]
                                },
                                {
                                    k: '466',
                                    v: '辽宁',
                                    parentId: '0',
                                    level: '1',
                                    fullName: '辽宁省',
                                    spell: 'liaoning',
                                    children: [
                                        {
                                            k: '466',
                                            v: '辽宁省',
                                            parentId: '0',
                                            level: '1',
                                            spell: 'liaoning',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '467',
                                            v: '沈阳',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,沈阳市',
                                            spell: 'shenyang',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '481',
                                            v: '大连',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,大连市',
                                            spell: 'dalian',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '492',
                                            v: '鞍山',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,鞍山市',
                                            spell: 'anshan',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '500',
                                            v: '抚顺',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,抚顺市',
                                            spell: 'fushun',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '508',
                                            v: '本溪',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,本溪市',
                                            spell: 'benxi',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '515',
                                            v: '丹东',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,丹东市',
                                            spell: 'dandong',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '522',
                                            v: '锦州',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,锦州市',
                                            spell: 'jinzhou',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '530',
                                            v: '营口',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,营口市',
                                            spell: 'yingkou',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '537',
                                            v: '阜新',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,阜新市',
                                            spell: 'fuxin',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '545',
                                            v: '辽阳',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,辽阳市',
                                            spell: 'liaoyang',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '553',
                                            v: '盘锦',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,盘锦市',
                                            spell: 'panjin',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '558',
                                            v: '铁岭',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,铁岭市',
                                            spell: 'tieling',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '566',
                                            v: '朝阳',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,朝阳市',
                                            spell: 'chaoyang',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '574',
                                            v: '葫芦岛',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,葫芦岛市',
                                            spell: 'huludao',
                                            topParentId: '466'
                                        },
                                        {
                                            k: '581',
                                            v: '金普新区',
                                            parentId: '466',
                                            level: '2',
                                            fullName: '辽宁省,金普新区',
                                            spell: 'jinpuxinqu',
                                            topParentId: '466'
                                        }
                                    ]
                                }
                            ],
                            companyTypeList: [],
                            companyHostSearchId: '',
                            companyHostSearch: [
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                },
                                {
                                    image: '',
                                    url: '',
                                    number: 'bh_danweidating_remensousuo',
                                    id: '18486',
                                    title: '我是搜索',
                                    subTitle: '',
                                    companyId: '',
                                    secondTitle: '',
                                    otherImageUrl: ''
                                }
                            ],

                            formData: {
                                keyword: '',
                                areaId: [],
                                companyType: [],
                                isPi: false
                            }
                        }
                    },
                    computed: {
                        queryTotal() {
                            const { formData } = this
                            let total = 0
                            const map = ['keyword', 'areaId', 'companyType', 'isPi']
                            map.forEach((key) => {
                                const value = formData[key]
                                const isArray = value instanceof Array
                                if (value) {
                                    total = isArray ? total + value.length : total + 1
                                }
                            })
                            return total
                        }
                    },
                    methods: {
                        getFilter() {
                            httpGet('/company/get-search-params').then((resp) => {
                                const { companyHostSearch, hotAreaList, areaList, companyTypeList } = resp
                                this.companyHostSearch = companyHostSearch
                                this.hotAreaList = hotAreaList
                                this.areaList = areaList
                                this.companyTypeList = companyTypeList
                            })
                        },

                        handleKwClear() {
                            const { companyHostSearchId } = this
                            if (companyHostSearchId) {
                                this.companyHostSearchId = ''
                            }
                            this.fetchList()
                        },

                        openSelectDialog() {
                            this.$refs.selectDialog?.handleOpen()
                        },

                        handleFilter(type, id) {
                            const current = this.formData[type]
                            const isArray = current instanceof Array

                            if (!id) {
                                this.formData[type] = isArray ? [] : ''
                                this.fetchList()
                                return
                            }

                            const isInclude = isArray ? current.includes(id) : current === id

                            if (isInclude) {
                                this.formData[type] = isArray ? current.filter((item) => item !== id) : id
                            } else {
                                if (isArray && current.length >= 5) {
                                    return ElementPlus.ElMessage.warning('最多可选择5项')
                                }
                                isArray ? this.formData[type].push(id) : (this.formData[type] = id)
                            }

                            this.fetchList()
                        },

                        handleCompanyHotSearch(row) {
                            const { id, title } = row
                            this.companyHostSearchId = id
                            this.formData['keyword'] = title
                            this.fetchList()
                        },

                        handleClearFilter(type, id, e) {
                            const current = this.formData[type]
                            const isArray = current instanceof Array
                            this.formData[type] = isArray ? current.filter((item) => item !== id) : ''
                            this.fetchList()
                            e.stopPropagation()
                        },

                        handleClearAll() {
                            const { formData } = this
                            Object.keys(formData).forEach((key) => {
                                const value = formData[key]
                                const isArray = value instanceof Array
                                this.formData[key] = isArray ? [] : ''
                                if (key === 'isPi') {
                                    this.formData[key] = false
                                }
                            })
                            this.fetchList()
                        },

                        fetchList(page) {
                            const { formData } = this
                            if (!page) {
                                paginationVue._.data.page = 1
                            }
                            const query = { ...formData, page: page ? page : 1 }
                            getList(query)
                        }
                    },
                    mounted() {
                        this.getFilter()
                    }
                }
                const filterVue = Vue.createApp(filterComponent).component('select-dialog', selectDialogComponent).use(ElementPlus).mount('#filterComponent')

                const paginationComponent = {
                    data() {
                        return {
                            page: 1,
                            pageSize: 15,
                            total: 100
                        }
                    },
                    methods: {
                        handlePageChange(page) {
                            filterVue.fetchList(page)
                            scrollToTarget('company-wrapper')
                        }
                    },
                    mounted() {}
                }

                const paginationVue = Vue.createApp(paginationComponent).use(ElementPlus).mount('#paginationComponent')
            })
        </script>
    </body>
</html>
