<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>活动汇总页</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/commonActivity.css" />
        <link rel="stylesheet" href="./css/activityGather.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <link rel="stylesheet" href="./lib/animate/animate.min.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/element-plus/zh-cn.js "></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <div class="activity-container">
            <header class="activity-header-container">
                <div class="activity-header-content">
                    <div class="activity-menu-content">
                        <a href="/" class="logo"></a>
                        <a href="/" class="activity-menu-item active">招聘会首页</a>
                        <a href="/" class="activity-menu-item">全国巡回现场招聘会</a>
                        <a href="/" class="activity-menu-item">2025RPO线上面试会</a>
                        <a href="/" class="activity-menu-item">出海引才</a>
                        <a href="/" class="activity-menu-item">归国活动</a>
                    </div>
                    <div class="activity-nav-content">
                        <a href="" class="activity-nav-item">合作申请</a>

                        <button class="activity-login-btn open-login-dialog">人才登录</button>
                        <!-- <a class="person-avatar" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/36/36" alt="" />
                        </a> -->
                    </div>
                </div>
            </header>

            <div class="banner-wrapper">
                <div class="swiper banner-swiper">
                    <div class="swiper-wrapper">
                        <a class="swiper-slide company-list" title="" href="" target="_blank">
                            <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                        </a>
                        <a class="swiper-slide company-list" title="" href="" target="_blank">
                            <img class="cover" src="https://picsum.photos/id/870/364/140" alt="" />
                        </a>
                        <a class="swiper-slide company-list" title="" href="" target="_blank">
                            <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                        </a>
                    </div>

                    <div class="swiper-pagination"></div>
                </div>
            </div>

            <div class="main-container" id="main-container">
                <div class="data-wrapper">
                    <ul>
                        <li>
                            <div class="left">
                                <img src="./assets/activity/activityGather/data-icon1.png" alt="" />
                            </div>
                            <div class="right">
                                <div class="count countUp">800</div>
                                <p>累计举办</p>
                            </div>
                        </li>
                        <li>
                            <div class="left">
                                <img src="./assets/activity/activityGather/data-icon2.png" alt="" />
                            </div>
                            <div class="right">
                                <div class="count countUp">800</div>
                                <p>参与单位</p>
                            </div>
                        </li>
                        <li>
                            <div class="left">
                                <img src="./assets/activity/activityGather/data-icon3.png" alt="" />
                            </div>
                            <div class="right">
                                <div class="count countUp">10.05<span>w</span></div>
                                <p>对接人选</p>
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- 热门活动速达 -->
                <div class="module hot-session-wrapper" module-name="热门活动速达">
                    <div class="view-content">
                        <div class="hd">
                            <div class="title">
                                <img src="./assets/activity/activityGather/session-title.png" alt="" />
                            </div>
                            <p class="text">这里是当下最受高学历、高层次人才关注的引才活动</p>
                        </div>
                        <div class="bd">
                            <div class="swiper hot-session-swiper">
                                <div class="swiper-wrapper">
                                    <a class="swiper-slide session-list" title="" href="" target="_blank">
                                        <div class="img-box">
                                            <div class="tag">全国巡回现场招聘会</div>
                                            <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                            <div class="swiper swiper-v">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">1.1 w</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="content">
                                            <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                            <div class="ft">
                                                <div class="time">2024.03.16 14:00-16:00</div>
                                                <div class="address">南阳理工大学站，新…</div>
                                            </div>
                                        </div>
                                        <div class="button disabled" data-link="">立即报名</div>
                                    </a>
                                    <a class="swiper-slide session-list" title="" href="" target="_blank">
                                        <div class="img-box">
                                            <div class="tag">全国巡回现场招聘会</div>
                                            <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                            <div class="swiper swiper-v">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">南阳理工大学站南阳理工大学站南阳理工大学站南阳理工大学站</div>
                                                    </div>
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">1.2 w</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="content">
                                            <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                            <div class="ft">
                                                <div class="time">2024.03.16 14:00-16:00</div>
                                                <div class="address">南阳理工大学站，新…</div>
                                            </div>
                                        </div>
                                        <div class="button" data-link="http://www.baidu.com">立即报名</div>
                                    </a>
                                    <a class="swiper-slide session-list" title="" href="" target="_blank">
                                        <div class="img-box">
                                            <div class="tag">全国巡回现场招聘会</div>
                                            <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                            <div class="swiper swiper-v">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">火热报名中</div>
                                                    </div>
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">1.2 w</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="content">
                                            <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                            <div class="ft">
                                                <div class="time">2024.03.16 14:00-16:00</div>
                                                <div class="address">南阳理工大学站，新…</div>
                                            </div>
                                        </div>
                                        <div class="button" data-link="http://www.baidu.com">立即报名</div>
                                    </a>
                                    <a class="swiper-slide session-list" title="" href="" target="_blank">
                                        <div class="img-box">
                                            <div class="tag">全国巡回现场招聘会</div>
                                            <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                            <div class="swiper swiper-v">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">1.1 w</div>
                                                    </div>
                                                    <div class="swiper-slide">
                                                        <div class="hot-tag">火热报名中</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="content">
                                            <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                            <div class="ft">
                                                <div class="time">2024.03.16 14:00-16:00</div>
                                                <div class="address">南阳理工大学站，新…</div>
                                            </div>
                                        </div>
                                        <div class="button" data-link="http://www.baidu.com">立即报名</div>
                                    </a>
                                </div>
                            </div>
                            <div class="swiper-button-prev"></div>
                            <div class="swiper-button-next"></div>
                        </div>
                    </div>
                </div>

                <!-- 引才活动总览 -->
                <div class="module activity-total-wrapper" module-name="引才活动总览">
                    <div class="view-content">
                        <div class="hd">
                            <div class="title">
                                <img src="./assets/activity/activityGather/activity-title.png" alt="" />
                            </div>
                            <p class="text">线上+现场，境内+海外，专场+组团，贯穿全年，总有一场活动适合你</p>
                        </div>

                        <div class="bd">
                            <div class="filter-wrapper" id="filter-wrapper">
                                <div class="filter-list ts">
                                    <div class="label">特色专场</div>
                                    <div class="value">
                                        <div class="swiper session-swiper">
                                            <div class="swiper-wrapper">
                                                <a class="swiper-slide session-list" title="" href="" target="_blank">
                                                    <div class="img-box">
                                                        <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                    </div>
                                                    <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                                </a>
                                                <a class="swiper-slide session-list" title="" href="" target="_blank">
                                                    <div class="img-box">
                                                        <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                    </div>
                                                    <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                                </a>
                                                <a class="swiper-slide session-list" title="" href="" target="_blank">
                                                    <div class="img-box">
                                                        <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                    </div>
                                                    <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                                </a>
                                                <a class="swiper-slide session-list" title="" href="" target="_blank">
                                                    <div class="img-box">
                                                        <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                    </div>
                                                    <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                                </a>
                                                <a class="swiper-slide session-list" title="" href="" target="_blank">
                                                    <div class="img-box">
                                                        <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                    </div>
                                                    <div class="tit">广州站-2025年春季博士现场巡回招聘会...</div>
                                                </a>
                                            </div>
                                            <div class="swiper-button-next"></div>
                                            <div class="swiper-button-prev"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="filter-list js-filter-list" id="activityType">
                                    <div class="label">活动系列</div>
                                    <div class="value">
                                        <a href="" target="_blank" title="" checked="true" class="item is-special">不限</a>
                                        <a href="" target="_blank" title="" class="item">全国巡回现场招聘会</a>
                                    </div>
                                </div>
                                <div class="filter-list js-filter-list" id="areaId">
                                    <div class="label">热门地区</div>
                                    <div class="value">
                                        <a href="" target="_blank" title="" class="item">不限</a>
                                        <a href="https://zhaopinhui.test.gcjob.jugaocai.com/yincai/beijing" target="_blank" title="" class="item" checked="true">北京</a>
                                        <a href="" target="_blank" title="" class="item">上海</a>
                                    </div>
                                </div>
                                <div class="filter-list js-filter-list" id="featureTag">
                                    <div class="label">特色标签</div>
                                    <div class="value">
                                        <a href="" target="_blank" title="" class="item">不限</a>
                                        <a href="" target="_blank" title="" class="item" checked="true">北京</a>
                                    </div>
                                </div>
                                <div class="filter-list js-filter-list" id="activityTime">
                                    <div class="label">活动时间</div>
                                    <div class="value">
                                        <a href="" target="_blank" title="" class="item">不限</a>
                                        <a href="" target="_blank" title="" class="item" checked="true">接下来3个月</a>
                                        <a href="" target="_blank" title="" class="item">本月</a>
                                        <a href="" target="_blank" title="" class="item">过去3个月</a>
                                        <a href="" target="_blank" title="" class="item">本年度</a>
                                        <el-date-picker
                                            :class="time && time.length ? 'is-selected' : ''"
                                            @change="handleMonthrange"
                                            v-model="time"
                                            size="small"
                                            type="monthrange"
                                            range-separator="至"
                                            start-placeholder="开始月份"
                                            end-placeholder="结束月份"
                                            format="YYYY/MM"
                                            value-format="YYYY-MM"
                                        />
                                    </div>
                                </div>
                                <div class="filter-list js-filter-list" id="activityStatus">
                                    <div class="label">活动状态</div>
                                    <div class="value">
                                        <a href="" target="_blank" title="" class="item">不限</a>
                                        <a href="" target="_blank" title="" class="item" checked="true">待举办</a>
                                    </div>
                                </div>
                                <div class="filter-list js-filter-list" id="activityToHold">
                                    <div class="label">举办方式</div>
                                    <div class="value">
                                        <a href="" target="_blank" title="" class="item">不限</a>
                                        <a href="" target="_blank" title="" class="item" checked="true">线下</a>
                                    </div>
                                    <div class="filter-aside">
                                        <a class="clear-all" href="">清空筛选条件</a>
                                    </div>
                                </div>
                            </div>

                            <div class="activity-wrapper">
                                <div class="company-content">
                                    <div class="item">
                                        <a class="hd" href="http://" target="_blank" title="">
                                            <div class="tag tag1">出海引才</div>
                                            <div class="img-box">
                                                <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                <div class="tags">
                                                    <span>校园场</span>
                                                    <span>校园场</span>
                                                </div>
                                            </div>

                                            <div class="title" title="国际差旅报销！哈尔滨工程大学第九届海内外青年学者兴海论坛诚邀全球英才参加！">
                                                <div class="inline-tag up">线上</div>
                                                国际差旅报销！哈尔滨工程大学第九届海内外青年学者兴海论坛诚邀全球英才参加！
                                            </div>
                                        </a>

                                        <div class="bd">
                                            <div class="time">活动时间：2025年04月15日~2025年04月16日</div>
                                            <div class="session" id="popoverComponent1">
                                                <el-popover placement="right" :width="350" trigger="hover">
                                                    <template #reference>
                                                        <p>活动地点：香港科技大学站,香港中文大学站,香港大…</p>
                                                    </template>
                                                    <div class="popover">
                                                        <div class="scroll-box">
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>

                                        <div class="ft">
                                            <div class="left">
                                                <div class="label">参会单位：</div>
                                                <div class="value"><span>316&nbsp;</span>家</div>
                                            </div>
                                            <div class="right">
                                                <div class="button disabled" data-link="">已结束</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <a class="hd" href="http://" target="_blank" title="">
                                            <div class="tag tag2">出海引才</div>
                                            <div class="img-box">
                                                <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                <div class="tags">
                                                    <span>校园场</span>
                                                    <span>校园场</span>
                                                </div>
                                            </div>

                                            <div class="title" title="">
                                                <div class="inline-tag down">线下</div>
                                                国际差旅报销！哈尔滨工程大学第九届海内外青年学者兴海论坛诚邀全球英才参加！
                                            </div>
                                        </a>

                                        <div class="bd">
                                            <div class="time">活动时间：2025年04月15日~2025年04月16日</div>
                                            <div class="session" id="popoverComponent2">
                                                <el-popover placement="right" :width="350" trigger="hover">
                                                    <template #reference>
                                                        <p>活动地点：香港科技大学站,香港中文大学站,香港大…</p>
                                                    </template>
                                                    <div class="popover">
                                                        <div class="scroll-box">
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>

                                        <div class="ft">
                                            <div class="left">
                                                <div class="label">参会单位：</div>
                                                <div class="value">国际差旅报销！哈尔滨工程大学第九届海内外青年学者兴海论坛诚邀全球英才参加！</div>
                                            </div>
                                            <div class="right">
                                                <div class="button" data-link="http://www.baidu.com">立即报名</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <a class="hd" href="http://" target="_blank" title="">
                                            <div class="tag tag3">出海引才</div>
                                            <div class="img-box">
                                                <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                <div class="tags">
                                                    <span>校园场</span>
                                                    <span>校园场</span>
                                                </div>
                                            </div>

                                            <div class="title" title="">
                                                <div class="inline-tag up">线上</div>
                                                国际差旅报销！哈尔滨工程大学第九届海内外青年学者兴海论坛诚邀全球英才参加！
                                            </div>
                                        </a>

                                        <div class="bd">
                                            <div class="time">活动时间：2025年04月15日~2025年04月16日</div>
                                            <div class="session" id="popoverComponent3">
                                                <el-popover placement="right" :width="350" trigger="hover">
                                                    <template #reference>
                                                        <p>活动地点：香港科技大学站,香港中文大学站,香港大…</p>
                                                    </template>
                                                    <div class="popover">
                                                        <div class="scroll-box">
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>

                                        <div class="ft">
                                            <div class="left">
                                                <div class="label">参会单位：</div>
                                                <div class="value"><span>更新中</span></div>
                                            </div>
                                            <div class="right">
                                                <div class="button" data-link="http://www.baidu.com">立即报名</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <a class="hd" href="http://" target="_blank" title="">
                                            <div class="tag tag4">出海引才</div>
                                            <div class="img-box">
                                                <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                <div class="tags">
                                                    <span>校园场</span>
                                                    <span>校园场</span>
                                                </div>
                                            </div>

                                            <div class="title" title="">
                                                <div class="inline-tag down">线上+线下</div>
                                                国际差旅报销！哈尔滨工程大学第九届海内外青年学者兴海论坛诚邀全球英才参加！
                                            </div>
                                        </a>

                                        <div class="bd">
                                            <div class="time">活动时间：2025年04月15日~2025年04月16日</div>
                                            <div class="session" id="popoverComponent4">
                                                <el-popover placement="right" :width="350" trigger="hover">
                                                    <template #reference>
                                                        <p>活动地点：香港科技大学站,香港中文大学站,香港大…</p>
                                                    </template>
                                                    <div class="popover">
                                                        <div class="scroll-box">
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                            <div class="pop-item">
                                                                <div class="pop-tit">广东专场广东专场广东专场广东专场广东专场广东专场</div>
                                                                <div class="pop-time">
                                                                    <div class="lebel">时间：</div>
                                                                    <div class="value">7月上旬</div>
                                                                </div>
                                                                <div class="pop-address">
                                                                    <div class="lebel">地点：</div>
                                                                    <div class="value">广州，深圳，珠海</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </el-popover>
                                            </div>
                                        </div>

                                        <div class="ft">
                                            <div class="left">
                                                <div class="label">参会单位：</div>
                                                <div class="value"><span>316&nbsp;</span>家</div>
                                            </div>
                                            <div class="right">
                                                <div class="button" data-link="http://www.baidu.com">立即报名</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="paginationComponent">
                                    <div v-show="count>0" class="pagination-cotnent">
                                        <el-pagination background layout="prev, pager, next" :page-size="12" @current-change="change" :total="count" v-model:current-page="page" />
                                    </div>
                                </div>
                                <div class="empty">暂无相关活动，请修改筛选条件试试</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 引才现场直击 -->
                <div class="module information-wrapper" module-name="引才现场直击">
                    <div class="view-content">
                        <div class="hd">
                            <div class="title">
                                <img src="./assets/activity/activityGather/information-title.png" alt="" />
                            </div>
                            <p class="text">速览一手新鲜的引才活动资讯，了解各地引才生动景象</p>
                        </div>
                        <div class="bd">
                            <div class="content-l">
                                <a href="http://" target="_blank" title="" nofollow>
                                    <div class="img">
                                        <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                    </div>
                                    <div class="content">
                                        <div class="time">2024.07.15 发布</div>
                                        <div class="title">澳门科技大学2024/2025学年招聘创新学院-计算机科学与计算机科学科学科学科学科学科学科学科学学科学学...学...</div>
                                        <div class="desc">澳门科技大学2024/2025学年招聘创新学院-计算机学科计算机...</div>
                                    </div>
                                </a>
                            </div>
                            <div class="content-r">
                                <div class="swiper information-swiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <a class="information-list" title="" href="" target="_blank" nofollow>
                                                <div class="list-l">
                                                    <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                </div>
                                                <div class="list-r">
                                                    <div class="title">文社理工医全覆盖！复旦即将推出至少100门AI课程</div>
                                                    <div class="desc">
                                                        求职最煎熬的莫过于无尽的等待。找工作本来就着急，偏偏用人单位迟迟不回复。在不知何时到头的等待中，很多求职者会开始怀疑自己。然而，高校不回复超字...
                                                    </div>
                                                    <div class="time">2024.07.15 发布</div>
                                                </div>
                                            </a>
                                            <a class="information-list" title="" href="" target="_blank" nofollow>
                                                <div class="list-l">
                                                    <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                </div>
                                                <div class="list-r">
                                                    <div class="title">文社理工医全覆盖！复旦即将推出至少100门AI课程</div>
                                                    <div class="desc">
                                                        求职最煎熬的莫过于无尽的等待。找工作本来就着急，偏偏用人单位迟迟不回复。在不知何时到头的等待中，很多求职者会开始怀疑自己。然而，高校不回复超字...
                                                    </div>
                                                    <div class="time">2024.07.15 发布</div>
                                                </div>
                                            </a>
                                            <a class="information-list" title="" href="" target="_blank" nofollow>
                                                <div class="list-l">
                                                    <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                </div>
                                                <div class="list-r">
                                                    <div class="title">文社理工医全覆盖！复旦即将推出至少100门AI课程</div>
                                                    <div class="desc">
                                                        求职最煎熬的莫过于无尽的等待。找工作本来就着急，偏偏用人单位迟迟不回复。在不知何时到头的等待中，很多求职者会开始怀疑自己。然而，高校不回复超字...
                                                    </div>
                                                    <div class="time">2024.07.15 发布</div>
                                                </div>
                                            </a>
                                        </div>

                                        <div class="swiper-slide">
                                            <a class="information-list" title="" href="" target="_blank" nofollow>
                                                <div class="list-l">
                                                    <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                </div>
                                                <div class="list-r">
                                                    <div class="title">文社理工医全覆盖！复旦即将推出至少100门AI课程</div>
                                                    <div class="desc">
                                                        求职最煎熬的莫过于无尽的等待。找工作本来就着急，偏偏用人单位迟迟不回复。在不知何时到头的等待中，很多求职者会开始怀疑自己。然而，高校不回复超字...
                                                    </div>
                                                    <div class="time">2024.07.15 发布</div>
                                                </div>
                                            </a>
                                            <a class="information-list" title="" href="" target="_blank" nofollow>
                                                <div class="list-l">
                                                    <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                </div>
                                                <div class="list-r">
                                                    <div class="title">文社理工医全覆盖！复旦即将推出至少100门AI课程</div>
                                                    <div class="desc">
                                                        求职最煎熬的莫过于无尽的等待。找工作本来就着急，偏偏用人单位迟迟不回复。在不知何时到头的等待中，很多求职者会开始怀疑自己。然而，高校不回复超字...
                                                    </div>
                                                    <div class="time">2024.07.15 发布</div>
                                                </div>
                                            </a>
                                            <a class="information-list" title="" href="" target="_blank" nofollow>
                                                <div class="list-l">
                                                    <img class="cover" src="./assets/activity/singleDetail/banner.jpg" alt="" />
                                                </div>
                                                <div class="list-r">
                                                    <div class="title">文社理工医全覆盖！复旦即将推出至少100门AI课程</div>
                                                    <div class="desc">
                                                        求职最煎熬的莫过于无尽的等待。找工作本来就着急，偏偏用人单位迟迟不回复。在不知何时到头的等待中，很多求职者会开始怀疑自己。然而，高校不回复超字...
                                                    </div>
                                                    <div class="time">2024.07.15 发布</div>
                                                </div>
                                            </a>
                                        </div>
                                    </div>

                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高才服务概览 -->
                <div class="module server-wrapper" module-name="高才服务概览">
                    <div class="view-content">
                        <div class="hd">
                            <div class="title">
                                <img src="./assets/activity/activityGather/server-titlt.png" alt="" />
                            </div>
                            <p class="text">高才科技——打造全球引智活动专家</p>
                        </div>
                        <div class="bd">
                            <div class="server-top">
                                <div class="server-top-tit">广州高才信息科技有限公司</div>
                                <div class="server-top-desc">
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;高才科技致力于打造海内外高层次人才求职招聘综合服务平台，旗下运营<span>“高校人才网”</span>、<span>“高才海外HiTalentGlobal”</span>和<span>“高才博士后”</span>三大核心子品牌。公司通过构建全方位的媒体矩阵，汇聚了超百万硕博人才资源，形成全球化、全场景的高层次人才服务生态。<br />
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;依托全球化资源整合能力与全流程服务体系，高才科技已发展成为国内领先的“跨境+全场景”高层次人才引进活动服务运营商。公司秉持创新理念，打造“线上+线下”双轨活动引才模式，提供从方案策划、精准宣传、人才邀约到活动执行等全链路服务，主要服务品类包括：“高才·海外行”海外引才活动、“高才·优博汇”全国博士巡回招聘会活动、“高才·优青汇”海外优秀青年人才归国对接活动等，近三年来公司已成功举办了数百场高层次人才招聘活动，轨迹遍布全球30多个国家、地区的百余座城市，其专业实力与显著成效获得政府及众多高校、科研机构、企事业单位客户的高度认可。
                                </div>
                            </div>
                            <div class="server-bottom">
                                <div class="text">
                                    <p>多元化活动</p>
                                </div>
                                <div class="tabs">
                                    <ul class="tab-hd">
                                        <li data-id="01" class="list list-n1 active">
                                            <p>国内线下活动</p>
                                            <span>包括全国巡回招聘会、政府引才活动</span>
                                        </li>
                                        <li data-id="02" class="list list-n2">
                                            <p>海（境）外线下活动</p>
                                            <span>包括出海引才、归国活动</span>
                                        </li>
                                        <li data-id="03" class="list list-n3">
                                            <p>线上引才活动</p>
                                            <span>包括RPO线上面试会、高层次人才引才直播交流会、 海外优青线上论坛</span>
                                        </li>
                                    </ul>
                                    <div class="tab-bd">
                                        <div class="tab-con-wrap tab-con-wrap-n1">
                                            <div class="swiper">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide swiper-slide-n1">
                                                        <div class="slider-hd">
                                                            <p class="p1">全国巡回招聘会</p>
                                                            <p class="p2">
                                                                设立城市酒店场和985高校场，<br />
                                                                已覆盖北京、上海、广州、南京、武汉、成都、西安、深圳、长春、哈尔滨、大连、天津、长沙、杭州、合肥等城市。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider1-1.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider1-2.png" alt="" />
                                                                <p>*&nbsp;数据更新于2025年2月</p>
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd"><span>6</span>年品牌效应</li>
                                                                    <li class="even">多元渠道发动</li>
                                                                    <li class="odd">优质会务保障</li>
                                                                    <li class="even"><span>70+</span>高质量活动</li>
                                                                    <li class="odd"><span>3380+</span>参会单位</li>
                                                                    <li class="even"><span>20000+</span>参会人才</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide swiper-slide-n2">
                                                        <div class="slider-hd">
                                                            <p class="p1">政府引才活动</p>
                                                            <p class="p2">
                                                                是由高校人才网联动政府机构及其他权威平台举办的特色引才活动，<br />
                                                                根据单位类型、人才层次、专业学科、所在区域等属性打造活动特色，并定向邀约用人单位组团参会。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider1-3.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider1-4.png" alt="" />
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd">特色鲜明，针对性强</li>
                                                                    <li></li>
                                                                    <li class="odd">强强联合，多方助阵</li>
                                                                    <li></li>
                                                                    <li class="odd">规模更大，吸引力强</li>
                                                                    <li></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="page-box">
                                                    <div class="swiper-pagination"></div>
                                                    <div class="swiper-button-next"></div>
                                                    <div class="swiper-button-prev"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-con-wrap tab-con-wrap-n2">
                                            <div class="swiper">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide swiper-slide-n1">
                                                        <div class="slider-hd">
                                                            <p class="p1">出海引才</p>
                                                            <p class="p2">
                                                                根据用人单位的引才专业、人才层次、意向地区等需求，为用人单位提供定制化海（境）外专场招聘宣讲会，帮助用人单位精准引才海外智力。<br />
                                                                当前已覆盖中国香港、日本、韩国、新加坡、俄罗斯、英国、法国、德国、瑞典、瑞士、美国、澳大利亚、新西兰、荷兰等国家及地区。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider2-1.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider2-2.png" alt="" />
                                                                <p>*&nbsp;数据更新于2025年2月</p>
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd">百所境外名校精选落地</li>
                                                                    <li class="even"><span>80+</span>场海境外活动</li>
                                                                    <li class="odd">精美雇主品牌宣传设计</li>
                                                                    <li class="even"><span>4400+</span>参会博士</li>
                                                                    <li class="odd">专业境外出行服务护航</li>
                                                                    <li class="even"><span>14+</span>国家及地区</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide swiper-slide-n2">
                                                        <div class="slider-hd">
                                                            <p class="p1">归国活动</p>
                                                            <p class="p2">
                                                                组织国际青年学者论坛活动，通过需求对接、宣传发动、人才寻访、简历收集&筛选、人才推荐等，<br />
                                                                为用人单位提供符合要求的参会学者匹配及邀约。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider2-3.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider2-4.png" alt="" />
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd">多渠发动</li>
                                                                    <li class="even">人才邀约</li>
                                                                    <li class="odd">人才筛选</li>
                                                                    <li class="even">人选对接</li>
                                                                    <li class="odd">精准引聚</li>
                                                                    <li class="even">效果保证</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="page-box">
                                                    <div class="swiper-pagination"></div>
                                                    <div class="swiper-button-next"></div>
                                                    <div class="swiper-button-prev"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-con-wrap tab-con-wrap-n3">
                                            <div class="swiper">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide swiper-slide-n1">
                                                        <div class="slider-hd">
                                                            <p class="p1">RPO线上面试会</p>
                                                            <p class="p2">
                                                                以RPO招聘+招聘会/宣讲会两种模式结合的线上组团面试会，<br />
                                                                通过精细化服务助力单位快速聚集及直面目标候选人。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider3-1.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider3-2.png" alt="" />
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd">结合RPO模式，人才更精准</li>
                                                                    <!-- <li></li> -->
                                                                    <li class="odd">全流程跟进，面试更高效</li>
                                                                    <!-- <li></li> -->
                                                                    <li class="odd">精细化服务，省时又省力</li>
                                                                    <!-- <li></li> -->
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide swiper-slide-n2">
                                                        <div class="slider-hd">
                                                            <p class="p1">高层次人才线上引才直播交流会</p>
                                                            <p class="p2">
                                                                根据用人单位的引才需求，策划并执行高层次人才线上引才交流会，<br />
                                                                助力用人单位在线实时对接全球英才。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider3-3.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider3-4.png" alt="" />
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd">定制直播流程</li>
                                                                    <li class="even">专项人员指导</li>
                                                                    <li class="odd">专业物料设计</li>
                                                                    <li class="even">主流媒体宣传</li>
                                                                    <li class="odd">多方入口引流</li>
                                                                    <li class="even">简历筛选推荐</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="swiper-slide swiper-slide-n3">
                                                        <div class="slider-hd">
                                                            <p class="p1">海外优青线上论坛</p>
                                                            <p class="p2">
                                                                为有海外优青引才需求的用人单位策划并执行海外优青线上论坛，<br />
                                                                助力单位加速储备海优人才。
                                                            </p>
                                                        </div>
                                                        <div class="slider-bd">
                                                            <div class="bd-l">
                                                                <img src="./assets/activity/activityGather/server-slider3-5.png" alt="" />
                                                                <img src="./assets/activity/activityGather/server-slider3-6.png" alt="" />
                                                            </div>
                                                            <div class="bd-r">
                                                                <ul>
                                                                    <li class="odd">定制论坛方案</li>
                                                                    <li class="even">搭建专属直播间</li>
                                                                    <li class="odd">内部多渠并推</li>
                                                                    <li class="even">海外学联助推</li>
                                                                    <li class="odd">海优简历推荐</li>
                                                                    <li class="even">人才画像报告</li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="page-box">
                                                    <div class="swiper-pagination"></div>
                                                    <div class="swiper-button-next"></div>
                                                    <div class="swiper-button-prev"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 往期精彩集锦 -->
                <div class="module cooperation-case-wrapper" module-name="往期精彩集锦">
                    <div class="view-content">
                        <div class="hd">
                            <div class="title">
                                <img src="./assets/activity/activityGather/cooperation-case-title.png" alt="" />
                            </div>
                            <p class="text">全方位高层次人才服务，助力全球精英寻猎</p>
                        </div>

                        <div class="list-content">
                            <div class="swiper-box">
                                <div class="swiper swiper-t1">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="default">
                                                <div class="header">
                                                    <div class="tag">全国巡回现场招聘会</div>
                                                    <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                </div>
                                                <div class="content-box">
                                                    <div class="content">
                                                        <div class="hd">
                                                            <p>广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题超2行...</p>
                                                        </div>
                                                        <div class="bd">
                                                            <p>
                                                                副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副超3行...
                                                            </p>
                                                        </div>
                                                        <div class="ft">
                                                            <img src="https://picsum.photos/id/1/642/170" alt="" />
                                                            <p>次标题次标题次标题次标题</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mask">
                                                <div class="hd">案例详情</div>
                                                <div class="bd">
                                                    <p>
                                                        高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势
                                                    </p>
                                                </div>
                                                <div class="ft">
                                                    <div class="button">滚动查看更多</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="default">
                                                <div class="header">
                                                    <div class="tag">全国巡回现场招聘会</div>
                                                    <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                </div>
                                                <div class="content-box">
                                                    <div class="content">
                                                        <div class="hd">
                                                            <p>广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题超2行...</p>
                                                        </div>
                                                        <div class="bd">
                                                            <p>
                                                                副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副超3行...
                                                            </p>
                                                        </div>
                                                        <div class="ft">
                                                            <img src="https://picsum.photos/id/1/642/170" alt="" />
                                                            <p>次标题次标题次标题次标题</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mask">
                                                <div class="hd">案例详情</div>
                                                <div class="bd">
                                                    <p>
                                                        高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势
                                                    </p>
                                                </div>
                                                <div class="ft">
                                                    <div class="button">滚动查看更多</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="default">
                                                <div class="header">
                                                    <div class="tag">全国巡回现场招聘会</div>
                                                    <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                </div>
                                                <div class="content-box">
                                                    <div class="content">
                                                        <div class="hd">
                                                            <p>广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题超2行...</p>
                                                        </div>
                                                        <div class="bd">
                                                            <p>
                                                                副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副超3行...
                                                            </p>
                                                        </div>
                                                        <div class="ft">
                                                            <img src="https://picsum.photos/id/1/642/170" alt="" />
                                                            <p>次标题次标题次标题次标题</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mask">
                                                <div class="hd">案例详情</div>
                                                <div class="bd">
                                                    <p>
                                                        高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势
                                                    </p>
                                                </div>
                                                <div class="ft">
                                                    <div class="button">滚动查看更多</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="default">
                                                <div class="header">
                                                    <div class="tag">全国巡回现场招聘会</div>
                                                    <img src="./assets/postdoctor/publish/advantage-n3.png" alt="" />
                                                </div>
                                                <div class="content-box">
                                                    <div class="content">
                                                        <div class="hd">
                                                            <p>广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题广告标题超2行...</p>
                                                        </div>
                                                        <div class="bd">
                                                            <p>
                                                                副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副标题副标副标题副标题副标题副标题副标题副标题副超3行...
                                                            </p>
                                                        </div>
                                                        <div class="ft">
                                                            <img src="https://picsum.photos/id/1/642/170" alt="" />
                                                            <p>次标题次标题次标题次标题</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mask">
                                                <div class="hd">案例详情</div>
                                                <div class="bd">
                                                    <p>
                                                        高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势高校人才网深耕注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势，在官网、微信公众号、社群及海外渠道等发力，助力用人单位多维度触达海外优质人才，增强曝光。高校人才网深耕行业17年，已打造了全面精准、高层次人才关注度高的自有媒体矩阵，同时已与海（境）外超百家学联、研究生会渠道建立了密切合作，是国内用人单位引进海内外高层次人才的重要宣传平台。本次服务将充分发挥渠道宣传优势
                                                    </p>
                                                </div>
                                                <div class="ft">
                                                    <div class="button">滚动查看更多</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                </div>

                <!-- 引才合作伙伴 -->
                <div class="module department-wrap" module-name="引才合作伙伴">
                    <div class="hd">
                        <div class="title">
                            <img src="./assets/activity/activityGather/department-title.png" alt="" />
                        </div>
                        <p class="text">18年专业沉淀，与合作伙伴同前行，伴高层次人才共成长</p>
                    </div>
                    <ul>
                        <li>
                            <div class="list-wrap list-n1">
                                <div class="list">
                                    <div class="list-l">
                                        <p>高校单位</p>
                                    </div>
                                    <div class="list-r">
                                        <img src="./assets/activity/activityGather/department-n1.png" alt="" />
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="list-wrap list-n2">
                                <div class="list">
                                    <div class="list-l">
                                        <p>科研机构</p>
                                    </div>
                                    <div class="list-r">
                                        <img src="./assets/activity/activityGather/department-n2.png" alt="" />
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="list-wrap list-n3">
                                <div class="list">
                                    <div class="list-l">
                                        <p>企事业单位</p>
                                    </div>
                                    <div class="list-r">
                                        <img src="./assets/activity/activityGather/department-n3.png" alt="" />
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li>
                            <div class="list-wrap list-n4">
                                <div class="list">
                                    <div class="list-l">
                                        <p>医疗单位</p>
                                    </div>
                                    <div class="list-r">
                                        <img src="./assets/activity/activityGather/department-n4.png" alt="" />
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <div class="cooperation-wrapper">
                    <div class="top"></div>
                    <div class="title">聚焦高端人才 专业就选高才</div>
                    <div class="bottom">
                        <a class="cooperation" href="https://www.gaoxiaojob.com/member/company/applyCooperation" target="_blank" rel="nofollow">合作申请</a>
                        <a class="tel" href="tel:020-85611139" rel="nofollow">020-85611139</a>
                    </div>
                </div>
            </div>

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                    <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>

            <!-- 左侧楼层锚点 -->
            <div class="fixed-tool"></div>
        </div>

        <script src="./js/activity/common.js"></script>
        <script src="./lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
        <script>
            $(function () {
                //分页
                const paginationComponent = {
                    data() {
                        return {
                            page: 1,
                            count: 100
                        }
                    },
                    mounted() {},
                    methods: {
                        initPage() {
                            this.page = 1
                        },
                        change(page) {
                            const res = updateHref({ page: page }, 1)

                            history.pushState({}, '', `${res}` || location.pathname)

                            this.handleSearchAgain({ page })
                        },
                        handleSearchAgain(query = {}) {
                            const { page, ...other } = query

                            if (!page) {
                                this.initPage()
                            }

                            const param = {
                                // ...other,
                                page: page ? page : 1
                            }

                            const url = location.pathname === '/' ? '/yincai' : location.pathname + location.search
                            const updatedUrl = url.replace(/yincai/, 'yincaipage')

                            httpPost(updatedUrl, {}).then((resp) => {
                                const {
                                    activityList: { html, count, limit, page },
                                    activitySearch,
                                    activitySearch: { areaId, featureTag, activityStatus, activityTime, activityToHold, activityType, searchParams }
                                } = resp

                                Object.keys(activitySearch).forEach((key) => {
                                    // console.log(key, activitySearch[key]) // 输出每个键和对应的值
                                    if (key === 'searchParams') return
                                    let html = ''
                                    activitySearch[key].forEach((k, i) => {
                                        html += `<a href="${k.url}" title="${k.name}" class="item ${i === 0 ? 'is-special' : ''}" checked="${k.active}">${k.name}</a>`
                                    })
                                    $(`#${key} .value a`).remove()
                                    $(`#${key} .value`).prepend(html)
                                })

                                const $companyContent = $('.activity-wrapper .company-content')
                                $companyContent.html(`${html}`)

                                if (html == '') {
                                    $companyContent.hide()
                                    $('.activity-wrapper .empty').show()
                                } else {
                                    $companyContent.show()
                                    handleMousemovePop()
                                    $('.activity-wrapper .empty').hide()
                                }

                                this.count = Number(count)
                            })
                        }
                    }
                }

                const paginationComponentVue = Vue.createApp(paginationComponent)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#paginationComponent')

                // 监听前进后退
                const popStateHandler = () => {
                    window.location.reload()
                }

                window.addEventListener('popstate', popStateHandler)

                $('.js-filter-list').on('click', 'a', function (e) {
                    e.preventDefault()
                    const link = $(this).attr('href')

                    if (link) {
                        history.pushState({}, '', `${link}` || location.pathname)

                        paginationComponentVue.handleSearchAgain()

                        scrollToTarget('filter-wrapper')
                    }
                })

                // 时间选择器
                const activityTimeComponent = {
                    data() {
                        return {
                            time: [] // 回显的时候需要处理成 ['2020-01', '2020-05']
                        }
                    },
                    mounted() {},
                    methods: {
                        handleActivityTime() {
                            this.time = []
                        },
                        handleMonthrange(value) {
                            let res = ''
                            if (value) {
                                res = updateHref({ activityCustomTime: value[0] + ',' + value[1], activityTime: 5 }, 1)
                            } else {
                                res = updateHref({ activityCustomTime: '', activityTime: '' }, 1)
                            }
                            history.pushState({}, '', `${res}` || location.pathname)
                            paginationComponentVue.handleSearchAgain()
                        }
                    }
                }

                const activityTimeComponentVue = Vue.createApp(activityTimeComponent)
                    .use(ElementPlus, {
                        locale: ElementPlusLocaleZhCn
                    })
                    .mount('#activityTime')

                $('#activityTime').on('click', 'a', function (e) {
                    e.preventDefault()
                    activityTimeComponentVue.handleActivityTime()
                })

                // 清除全部筛选
                $('#activityToHold').on('click', '.clear-all', function (e) {
                    e.preventDefault()
                    activityTimeComponentVue.handleActivityTime()
                })

                // 列表移入气泡
                function handleMousemovePop() {
                    const domArr = $('.activity-wrapper .bd .session')

                    for (let index = 0; index < domArr.length; index++) {
                        const element = domArr[index]
                        const popoverComponent = {
                            data() {
                                return {}
                            },
                            mounted() {},
                            methods: {}
                        }

                        const popoverComponentVue = Vue.createApp(popoverComponent)
                            .use(ElementPlus)
                            .mount(`#popoverComponent${index + 1}`)
                    }
                }

                handleMousemovePop()

                // banner swiper
                new Swiper('.banner-swiper', {
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false
                    },
                    loopFillGroupWithBlank: true,
                    pagination: {
                        el: '.banner-swiper .swiper-pagination',
                        clickable: true
                    }
                })

                // 咨询 swiper
                new Swiper('.information-swiper', {
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false
                    },
                    loopFillGroupWithBlank: true,
                    pagination: {
                        el: '.information-swiper .swiper-pagination',
                        clickable: true
                    }
                })

                // tab swiper Satrt

                const swiperInstances = {} // 用来存储 Swiper 实例

                const swiperConfigs = ['.tab-con-wrap-n1 .swiper', '.tab-con-wrap-n2 .swiper', '.tab-con-wrap-n3 .swiper']

                swiperConfigs.forEach((config, index) => {
                    const tabSwiper = new Swiper(config, {
                        loop: true,
                        autoplay: {
                            delay: 5000,
                            disableOnInteraction: true
                        },
                        pagination: {
                            el: `${config} .swiper-pagination`,
                            type: 'fraction'
                        },
                        navigation: {
                            nextEl: `${config} .swiper-button-next`,
                            prevEl: `${config} .swiper-button-prev`
                        }
                    })

                    // 动态创建属性名称 tabSwiper1, tabSwiper2, ...
                    swiperInstances[`tabSwiper${index + 1}`] = tabSwiper
                })

                // tab swiper End

                //热门场次 swiper start
                new Swiper('.hot-session-swiper', {
                    spaceBetween: 14,
                    slidesPerView: 'auto',
                    allowTouchMove: false,
                    slidesPerGroup: 3,
                    navigation: {
                        nextEl: '.hot-session-wrapper .swiper-button-next',
                        prevEl: '.hot-session-wrapper .swiper-button-prev'
                    }
                })

                const sessionListArr = $('.hot-session-swiper .session-list .swiper-v')

                Array.from(sessionListArr).forEach((config, index) => {
                    new Swiper(config, {
                        direction: 'vertical',
                        allowTouchMove: false,
                        autoplay: true,
                        loop: sessionListArr.eq(index).find('.swiper-slide').length > 1 ? true : false
                    })
                })

                //热门场次 swiper end

                //特色专场 swiper start
                new Swiper('.session-swiper', {
                    slidesPerView: 'auto',
                    spaceBetween: 20,
                    allowTouchMove: false,
                    slidesPerGroup: 3,
                    navigation: {
                        nextEl: '.session-swiper .swiper-button-next',
                        prevEl: '.session-swiper .swiper-button-prev'
                    }
                })
                //特色专场 swiper end

                //精彩集锦 swiper start
                new Swiper('.swiper-t1', {
                    slidesPerView: 3,
                    slidesPerGroup: 3,
                    spaceBetween: 20,
                    // loop: true,
                    // autoplay: {
                    //     delay: 5000,
                    //     disableOnInteraction: false
                    // },
                    navigation: {
                        nextEl: '.cooperation-case-wrapper .swiper-button-next',
                        prevEl: '.cooperation-case-wrapper .swiper-button-prev'
                    }
                })
                //精彩集锦 swiper end

                // 热门场次button点击
                $('.hot-session-wrapper').on('click', '.button', function (e) {
                    e.preventDefault()
                    const link = $(this).attr('data-link')
                    if (link) {
                        window.open(link, '_blank')
                    }
                })

                // 活动列表button点击
                $('.activity-wrapper').on('click', '.button', function (e) {
                    e.preventDefault()
                    const link = $(this).attr('data-link')
                    if (link) {
                        window.open(link, '_blank')
                    }
                })

                // 切换tab
                $('.server-wrapper .tab-hd .list').click(function () {
                    let index = $(this).index()
                    $('.server-wrapper .tab-hd .list').removeClass('active')
                    $(this).addClass('active')
                    $('.server-wrapper .tab-bd .tab-con-wrap').css('display', 'none')
                    $('.server-wrapper .tab-bd .tab-con-wrap').eq(index).css('display', 'block')
                    // swiperInstances[`tabSwiper${index + 1}`].destroy(true, true) // 销毁旧的实例
                    // swiperInstances[`tabSwiper${index + 1}`].update()
                })

                // 判断是否展示“滚动查看更多”按钮
                $('.swiper-wrapper .mask .bd p').each(function (index) {
                    let pHeight = $(this).height()
                    let prentHeight = $(this).parent().height()
                    // console.log(pHeight,prentHeight);
                    if (pHeight < prentHeight) {
                        $('.swiper-wrapper .mask .ft').eq(index).hide()
                    }
                })

                // 筛选后滚动到固定位置
                function scrollToDataContent() {
                    const page = getHrefQuery('page')
                    if (page) {
                        scrollToTarget('filter-wrapper')
                    }
                }

                scrollToDataContent()

                const observerOptions = {
                    root: null,
                    rootMargin: '0px',
                    threshold: 0.1
                }

                const observer = new IntersectionObserver((entries, observer) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            const element = entry.target
                            const span = element.querySelector('span')
                            // 正确提取带小数的目标数字
                            const targetNumber = parseFloat(element.textContent.replace(/[^0-9.]/g, ''))
                            let currentNumber = 0
                            const increment = targetNumber / 100
                            const duration = 1000 // 2 seconds
                            const interval = duration / 100

                            const counter = setInterval(() => {
                                currentNumber += increment
                                // 四舍五入到2位小数
                                currentNumber = Math.round(currentNumber * 100) / 100

                                if (currentNumber >= targetNumber) {
                                    element.childNodes[0].textContent = targetNumber // 保持原始目标数字
                                    clearInterval(counter)
                                } else {
                                    element.childNodes[0].textContent = currentNumber // 保持当前小数
                                }
                            }, interval)

                            observer.unobserve(element)
                        }
                    })
                }, observerOptions)

                document.querySelectorAll('.countUp').forEach((element) => {
                    observer.observe(element)
                })
            })
        </script>
    </body>
</html>
