<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>公告详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/announcement.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/public.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main" id="seeHeat">
                <div class="detail-container announcement">
                    <div class="detail-header">
                        <div class="detail-header-container">
                            <div class="breadcrumb">
                                位置：
                                <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞ 北京大学珠海分校2021年度招聘公告
                            </div>

                            <div class="main">
                                <section>
                                    <div class="title">
                                        <h1>中国人民解放军空军特色医学中心2022年博士后研究人员招收简章招收简章招收简章</h1>
                                    </div>

                                    <div class="info">
                                        <span class="establishment-tag">部分编制</span>
                                        共计<span class="color-primary">12</span>个岗位，招 <span class="color-primary">29</span>人

                                        <a class="view-relation" href="">查看此公告的职位列表</a>
                                    </div>

                                    <div id="boonsTemplate" class="boons">
                                        <span class="establishment-tag">部分编制</span>
                                        <span class="boon">全勤奖</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">五险一金</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">绩效奖金</span>

                                        <el-popover placement="bottom" :width="330" trigger="hover" v-cloak>
                                            <template #reference>
                                                <i class="el-icon boon-more">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                        <path
                                                            fill="currentColor"
                                                            d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                        ></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <!-- <span class="boon">全勤奖</span> -->
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">五险一金</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">绩效奖金</span>
                                        </el-popover>
                                    </div>
                                </section>

                                <aside>
                                    <div class="announcement emit">
                                        <div class="detail-button announcement-button">
                                            <div class="share-mini-code-container">
                                                <div class="share-mini-code-trigger share-mini-code-trigger--primary">分享</div>

                                                <div class="share-mini-code-popup">
                                                    <div class="share-mini-code">
                                                        <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                    </div>
                                                    <div class="share-mini-code-title">微信扫一扫</div>
                                                    <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                                </div>
                                            </div>

                                            <div class="el-button--collect">收藏</div>
                                            <div class="el-button--analyse see-heat">公告热度</div>
                                        </div>
                                        <!-- <button class="announcement el-button el-button--primary el-button--collect collected">
                                        <span>已收藏</span>
                                    </button>
                                    <button class="el-button el-button--info is-plain is-disabled" disabled>
                                        <span>已下线</span>
                                    </button> -->
                                        <button class="announcement el-button el-button--primary el-button--apply">
                                            <span>立即投递</span>
                                        </button>
                                    </div>
                                </aside>

                                <a class="el-button el-button--primary view-button" href="">
                                    <span>查看此公告的职位列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <section>
                            <div class="section-main">
                                <div class="detail-subtitle is-basic">基本信息</div>

                                <div class="detail-list">
                                    <div class="detail-item">
                                        <ul class="detail-content el-row announcement">
                                            <li class="el-col">发布时间：2021-07-06</li>
                                            <li class="el-col">截止日期：2021-08-06</li>
                                            <li class="el-col">学历要求：硕士及以上</li>

                                            <li class="el-row">
                                                <div class="el-col el-col-8 show-complete is-complete">
                                                    <span class="label">所属省份：</span>
                                                    <div class="value">
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                    </div>
                                                </div>

                                                <div class="el-col el-col-16 show-complete">
                                                    <span class="label">工作地点：</span>
                                                    <div class="value">
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                        <a href="" class="color-primary">北京</a>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="el-row">
                                                <div class="el-col el-col-8 show-complete is-complete">
                                                    <span class="label">报名方式：</span>
                                                    <div class="value">电子邮件，网上系统，现场报名，电话报名，传真，邮寄，其他</div>
                                                </div>

                                                <div class="el-col el-col-16 show-complete">
                                                    <span class="label">栏目分类：</span>
                                                    <div class="value">
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                        <a href="" class="color-primary">高校招聘</a>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="el-row">
                                                <div class="el-col show-complete">
                                                    <span class="label">需求学科（供参考）：</span>
                                                    <div class="value">
                                                        <a href="" class="color-primary">哲学</a>
                                                        <a href="" class="color-primary">理学</a>
                                                        <a href="" class="color-primary">工学</a>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="detail-subtitle is-detail">公告详情</div>

                                <div class="detail-list">
                                    <div class="detail-main-content">
                                        <u><span></span></u>
                                        <p>
                                            <u><span></span></u>
                                        </p>
                                        <p>
                                            <span>根据学校事业发展需要及国家相关规定，经四川省教育厅核准，我校面向社会公开招聘事业编制工作人员21名。现将公开招聘有关事项公告如下：</span><span></span>
                                        </p>
                                        <p><span>一、考核招聘单位基本情况</span><span></span></p>
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <p><span>单位名称</span><span></span></p>
                                                    </td>
                                                    <td>
                                                        <p><span>单位性质</span><span></span></p>
                                                    </td>
                                                    <td>
                                                        <p><span>单位地址</span><span></span></p>
                                                    </td>
                                                    <td>
                                                        <p><span>主要职能</span><span></span></p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <p><span>四川民族学院</span><span></span></p>
                                                    </td>
                                                    <td>
                                                        <p><span>核定收支、定额补助事业单位</span><span></span></p>
                                                    </td>
                                                    <td>
                                                        <p><span>四川省康定市姑咱镇</span><span></span></p>
                                                    </td>
                                                    <td>
                                                        <p><span>人才培养、科学研究、社会服务</span><span></span></p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <p>
                                            <span
                                                >四川民族学院是四川省人民政府主办的全日制普通本科高校，面向社会招生。学校位于“溜溜的山、洁白的云、奔腾的河、迅疾的风”之间的四川省康定市姑咱镇，西靠世界情歌故里康定，东望高耸入云的二郎山，南邻大渡桥横铁索寒的泸定，北接丹巴美人谷和仙境般的甲居藏寨，共和国最美景观大道318线从校园旁边迤逦而过，汹涌澎湃的大渡河从这里不舍昼夜奔流入海。学校环境优美，空气清新，阳光明媚，气候宜人，冬不冷夏不热，年平均气温18℃，是省级“园林式校园”。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >学校现有29个本科专业、11个专科专业，涵盖文学、理学、工学、法学等10个学科门类；现有各类学生13000人，其中全日制近万人。校园面积625亩，在建泸定校区1000亩，馆藏纸质图书100万册，电子图书30万册。学校教职员工800余人，其中高级专业技术职务170人，具有博士、硕士学位教师300余人。学校现有16个二级学院、4个省部级重点学科，4个省级一流本科专业，28个特色研究中心（所），建有各类专业实习实训基地133个，设有研究生招生考试、中小学教师资格考试等考点。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span>学校坚持以“人才强校”为抓手，深化教育教学改革，加大内涵建设，促进转型发展，努力把学校建设成充满活力的现代化综合性应用型民族本科高校。</span
                                            ><span></span>
                                        </p>
                                        <p><span>二、招聘对象、范围及基本条件</span><span></span></p>
                                        <p><span>（一）招聘的对象和范围</span><span></span></p>
                                        <p>
                                            <span
                                                >1.招聘对象：面向全国招聘2021年12月31日前取得普通高等教育硕士研究生学历学位（含持有国家教育部留学服务中心认证学历、学位的境外留学人员和赴台生），自愿到我校工作5年及以上的人员。且符合《四川民族学院2021年11月考核招聘事业编制工作人员岗位和条件要求一览表》(详见附件1，以下简称<a
                                                ></a><a></a>《岗位和条件要求一览表》)相关岗位条件要求和本公告其他要求的人员。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span>2.招聘对象年龄条件（以身份证日期为准）：</span><span>博士研究生1971年11月1日及以后出生；</span
                                            ><span>硕士研究生1986年11月1日及以后出生；有五年及以上相关工作经历的硕士研究生,年龄放宽放到1981年11月1日及以后出生。</span><span></span>
                                        </p>
                                        <p><span>报名时本单位的在编工作人员，不属本单位本次招聘对象范围。</span><span></span></p>
                                        <p><span>（二）基本条件</span><span></span></p>
                                        <p><span>1.应聘者应同时具备以下条件：</span><span></span></p>
                                        <p>
                                            <span
                                                >(1)具有中华人民共和国国籍，热爱社会主义祖国，拥护中华人民共和国宪法，拥护中国共产党，遵纪守法，品行端正，有良好的职业道德，爱岗敬业，事业心和责任感强。</span
                                            ><span></span>
                                        </p>
                                        <p><span>(2)身体健康，体检合格，能正常履行招聘岗位职责。</span><span></span></p>
                                        <p>
                                            <span
                                                >(3)具备本公告具体招聘岗位要求的条件和资格（详见《岗位和条件要求一览表》）。其中，应聘者本人有效的毕业证所载学历和所获专业名称，应与拟应聘岗位的“学历”和“专业条件要求”两栏分别相符。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >(4)取得海外学历的须经国家教育部留学服务中心进行学历学位认证和第三方专业认证，认证学历学位、专业名称与所报考岗位专业要求相符，不符者取消报考资格。</span
                                            ><span></span>
                                        </p>
                                        <p><span>(5)委培、定向毕业生，须征得原委培、定向单位书面同意。</span><span></span></p>
                                        <p><span>特别提示：不符应聘条件者，请勿报考，否则取消报考或聘用资格，责任由报考者自负。</span><span></span></p>
                                        <p><span>2.有下列情况之一者，不得报考：</span><span></span></p>
                                        <p><span>(1)曾受过各类刑事处罚的。</span><span></span></p>
                                        <p><span>(2)曾被开除公职的。</span><span></span></p>
                                        <p><span>(3)有违法、违纪行为正在接受审查的。</span><span></span></p>
                                        <p><span>(4)尚未解除党纪、政纪处分的。</span><span></span></p>
                                        <p><span>(5)按照《事业单位公开招聘人员暂行规定》和《四川省省属事业单位公开招聘工作人员实施细则（试行）》的相关规定应当回避的。</span><span></span></p>
                                        <p><span>(6)尚处于试用期内的新录用公务员。</span><span></span></p>
                                        <p>
                                            <span
                                                >(7)按照中共中央办公厅、国务院办公厅《关于加快推进失信被执行人信用监督、警示和惩戒机制建设的意见》规定，由人民法院通过司法程序认定的失信被执行人。</span
                                            ><span></span>
                                        </p>
                                        <p><span>(8)法律法规等规定的其他不能报考事业单位的情形。</span><span></span></p>
                                        <p><span>三、考核招聘岗位和名额</span><span></span></p>
                                        <p>
                                            <span>本次考核招聘均在事业单位经批准设置的岗位内招聘，招聘名额为21名，具体岗位详见</span><span>《岗位和条件要求一览表</span><span>》（附件1）</span
                                            ><span></span>
                                        </p>
                                        <p><span>四、报名</span><span></span></p>
                                        <p><span>本次考核招聘仅采用网络报名方式进行。</span><span></span></p>
                                        <p><span>（一）报名网址和时间</span><span></span></p>
                                        <p><span>1.报名网址：甘孜州人力资源和社会保障局门户网站“人事考试网”栏目（http://rsks.gzz.gov.cn）。</span><span></span></p>
                                        <p><span>2.报名时间：2021年11月15日09:00至2021年11月24日18:00止。报名截止后，报名系统自动关闭，不能再提交报名信息。</span><span></span></p>
                                        <p><span>（二）报名程序</span><span></span></p>
                                        <p>
                                            <span
                                                >1.选择岗位。应聘者登录报名网站后，应认真阅读本公告，详细了解招聘对象、范围、条件以及有关政策规定和有关注意事项等内容，根据自身情况选择完全符合报考条件的一个岗位报名，每个应聘者在本次公招中限报一个岗位。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >2.填报信息。应聘人员按照网络提示要求通过用户注册（请务必记住个人账号和密码）、用户登录后，如实、准确填写《四川民族学院考核招聘工作人员报考信息表》（以下简称《报考信息表》）的各项内容。应聘人员所学专业和学历均以毕业证书原件所标注的专业和学历为准，必须与招聘岗位资格条件要求的专业和学历完全相符，必须完全按照标注的专业和学历如实填写。应聘人员不能用新、旧两个身份证号同时报名，报名与考试时使用的身份证必须一致。应聘人员如隐瞒有关情况或填报虚假信息骗取报考资格的，一经查实，取消其应聘资格，所造成的后果由应聘人员本人承担。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >3.上传照片。应聘人员在网上提交《报考信息表》时，必须按网络提示上传本人近期免冠彩色数码证件电子照片（JPG格式、16-32KB），并在网络报名成功后的二个工作日内登陆报名网站查询是否通过资格初审和照片质量审查。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >4.资格初审。四川民族学院各用人部门根据报考人员提交的《报考信息表》和上传的照片，严格按照《公告》规定的招聘范围、对象、资格条件等要求进行资格初审，并在考生报名后的二个工作日内提出审查意见。对审查不合格的，应说明理由，对照片质量不符合要求的，应提示应聘人员重新上传照片。资格初审合格者按网络提示打印《报考信息表》两份，供资格复审时使用。网上资格初审的结果仅作为报考人员参加资格复审的依据,网络报名资格初审合格不代表最终资格审查合格。</span
                                            ><span></span>
                                        </p>
                                        <p><span>应聘者最终是否符合所报岗位条件要求由四川民族学院负责的资格复审和报送省教育厅前我单位审核确认的结果最终确定。</span><span></span></p>
                                        <p><span>在招聘的任何环节发现应聘者不符合报考条件、弄虚作假、违反回避制度或同时多岗位报考的，取消应聘或聘用资格，且责任自负。</span><span></span></p>
                                        <p>
                                            <span
                                                >在国（境）外取得学历学位的应聘者须于2021年11月25日之前向我校提供：在国（境）外高校所学课程及成绩单原件及复印件1份、国（境）外取得学历学位证书原件和复印件1份、国家教育部留学服务中心认证的学历学位认证书原件和复印件1份。按《关于进一步规范省属事业单位公开招聘工作人员工作的通知》（川人办发[2009]457号）要求，由四川民族学院统一组织进行第三方的专业认证，认定结果与招聘专业为相同专业的拟视为专业资格条件合格。认证费用由报考人员自行缴纳，我校代收。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span>5.《报考信息表》填写。</span><span>为避免网络出现故障，报考者还须</span
                                            ><span
                                                >填写《四川民族学院考核招聘工作人员报考信息表》（见附件2）后发送至四川民族学院人事处邮箱
                                                <span class="detail-hidden-email-address">（点击查看）</span>
                                                ，邮件题目格式：姓名+学历学位+专业，应聘者所提供信息必须真实无误。</span
                                            ><span></span>
                                        </p>
                                        <p><span>6.缴费确认</span><span></span></p>
                                        <p>
                                            <span
                                                >资格初审合格者，必须于2021年11月16日8:00至11月26日18:00前登录报名网站，按网络提示完成网上缴费。按照《四川省发展和改革委员会、四川省财政厅关于重新公布全省人力资源社会保障部门行政事业性收费的通知》（川发改价格[2017]472号）规定，每人需缴纳50元。未在规定时间内缴费者，视为自动放弃。</span
                                            ><span></span>
                                        </p>
                                        <p><span>五、资格复审</span><span></span></p>
                                        <p>
                                            <span>资格初审合格者，</span><span>于2021年12月2日到四川民族学院</span><span>B校区图书馆一楼</span><span>参加资格复审。进行资格复审，</span
                                            ><span>资格复审应现场提交以下材料：</span><span></span>
                                        </p>
                                        <p><span>1.《四川民族学院考核招聘工作人员报考信息表》1份（附件2）；</span><span></span></p>
                                        <p><span>2.身份证，有效的毕业证、学位证原件和复印件1份；</span><span></span></p>
                                        <p>
                                            <span
                                                >其中，2021年应届毕业生参加资格复审时，若尚未取得学位证、毕业证的，需提供学生证原件及复印件1份、硕士研究生培养单位主管毕业生就业工作部门或研究生院开具的就读专业情况的证明原件（</span
                                            ><span>模板见附件3</span><span>）。其最终是否符合应聘岗位学位、学历资格条件，以本人毕业时取得的有效学位证及毕业证所载学历和学位为准。</span><span></span>
                                        </p>
                                        <p><span>3.从中国高等教育学生信息网（</span><span>http://www.chsi.com.cn</span><span>）彩色打印的教育部学籍在线验证报告（带二维码）。</span><span></span></p>
                                        <p><span>4.有政治面貌要求的岗位的应聘人员需提供由组织关系所在党组织出具的政治面貌证明（模板见附件4）。</span><span></span></p>
                                        <p><span>5.在职人员应提供原单位出具的同意报考书面材料（模板见附件5）或者生效的劳动（人事）争议仲裁裁决书等有效书面证明材料。</span><span></span></p>
                                        <p><span>6.其他材料（如职称证书、各类获奖证书、科研成果证明材料原件等）。</span><span></span></p>
                                        <p><span>不能按要求提供上述证件和证明以及未按时参加资格审查的，视为自动放弃其应聘资格。</span><span></span></p>
                                        <p><span>六、考核</span><span></span></p>
                                        <p><span>（一）考核的组织实施、方式及内容</span><span></span></p>
                                        <p>
                                            <span>本次招聘的所有岗位均进行心理素质测试。</span
                                            ><span>心理素质测试在通过资格复审后进行，测试地点：四川民族学院B校区图书馆三楼电子阅览室。心理素质测试结束后领取面试通知书</span><span></span>
                                        </p>
                                        <p><span>考核采取专业素质测试+综合面试的考核方式进行。</span><span></span></p>
                                        <p><span>考核于2021年12月4日在四川民族学院进行。时间、地点及面试要求详见《面试通知书》。</span><span></span></p>
                                        <p><span>1.专业素质测试：</span><span></span></p>
                                        <p>
                                            <span
                                                >采用试讲和业绩情况汇报的方式进行。应聘者在试讲时向主考官提供时间为45分钟的所授课程教案（纸质及多媒体课件）一套（内容按照《岗位和条件要求一览表》专业要求栏要求），重点从实现教学目的的能力、掌握教材内容的能力、组织课堂教学的能力、教学基本素养、运用现代教育技术和教具的能力、教学效果六个方面进行考核。业绩情况汇报，由考核组对应聘者思想政治素质、职业道德、学术水平进行全面考核。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >2.综合面试：由学校统一组织实施。采取现场答辩的方式，主要考查应聘者事业心责任感、逻辑思维能力、语言表达能力、协调沟通能力、知识应用及创新能力、举止仪表及岗位适应能力等方面。</span
                                            ><span></span>
                                        </p>
                                        <p><span>（二）考核评分及成绩计算方法</span><span></span></p>
                                        <p>
                                            <span>1.专业素质测试和综合面试满分均为100分，采取去掉</span><span>一个最高分和一个最低分</span><span>，以有效分的平均值为应考者的实际得分</span
                                            ><span>的办法，</span><span>分数保留一位小数，小数点后第二位数字按“四舍五入”予以取舍。</span><span></span>
                                        </p>
                                        <p><span>2.考生的考核成绩=专业素质测试成绩（×70%）+综合面试成绩（×30%）。</span><span></span></p>
                                        <p><span>被考核人员的专业素质测试成绩或综合面试成绩任意一项低于60分，不予录用。</span><span></span></p>
                                        <p><span>（三）考核成绩公布</span><span></span></p>
                                        <p><span>考核工作结束后，2个工作日内在四川民族学院网站公布考核成绩和体检人员名单。</span><span></span></p>
                                        <p><span>七、体检</span><span></span></p>
                                        <p><span>（一）确定体检人员</span><span></span></p>
                                        <p><span>按应聘人员的考核成绩及招聘岗位的计划数1：1确定体检人选，若体检不合格，按从高分到低分依次递补。</span><span></span></p>
                                        <p><span>（二）体检标准</span><span></span></p>
                                        <p>
                                            <span>体检的项目和标准参照修订后的《公务员录用体检通用标准（试行）》和</span><u><span>《公务员录用体检操作手册（试行）》</span></u
                                            ><span>执行。其中，乙肝检测项目按国家人社部、教育部和卫生部《关于进一步规范入学和就业体检项目维护乙肝表面抗原携带者入学和就业权利的通知》的要求执行。</span
                                            ><span></span>
                                        </p>
                                        <p><span>（三）体检时间和地点</span><span></span></p>
                                        <p><span>体检由四川民族学院组织在指定的二级甲等以上综合性医院进行。体检时间、集合地点及体检人员名单一并公布。</span><span></span></p>
                                        <p><span>未按规定时间到指定地点参加体检，以及未在规定的期限内完成规定项目体检的考生，视为自动弃权。</span><span></span></p>
                                        <p><span>（四）复检及递补</span><span></span></p>
                                        <p>
                                            <span
                                                >初次体检不合格的，本人可在接到体检结果通知三日内申请复检一次。复检由四川民族学院指定到除原体检医院以外的二级甲等以上综合性医院进行。申请复检人员的体检结果以复检结果为准。</span
                                            ><span></span>
                                        </p>
                                        <p><span>某岗位因体检人员自动弃权或体检不合格而出现的空额，按照参加考核考生的考试总成绩，从高分到低分依次等额递补体检人员。</span><span></span></p>
                                        <p><span>八、综合审查</span><span></span></p>
                                        <p>
                                            <span
                                                >四川民族学院对体检合格人员的思想政治素质、遵纪守法情况、道德品质修养、心理调适能力等方面进行综合考核，并对其与应聘相关的人事档案等材料的真实有效性和应聘资格进行核实确认。</span
                                            ><span></span>
                                        </p>
                                        <p><span>九、公示</span><span></span></p>
                                        <p>
                                            <span
                                                >考核、体检、综合审查合格者由四川民族学院确定为拟聘用人员，并在并在省人社厅官网“人事考试”专栏、省教育官网、四川民族学院网站上公示7个工作日。公示期内及以后因举报查实取消资格或弃权等原因出现的招聘名额空缺，不再递补。</span
                                            ><span></span>
                                        </p>
                                        <p><span>十、审核确认和办理聘用手续</span><span></span></p>
                                        <p><span>公示无异议人员由四川民族学院通知报送以下材料的时间和地点。</span><span></span></p>
                                        <p>
                                            <span
                                                >1.在职人员应提供原单位出具的同意其到新单位应聘的书面材料(通过省教育厅审核确认后，在职人员再按招聘单位要求提供正式同意解除聘用(劳动)合同或者同意流(调)动的书面材料)或者生效的劳动（人事）争议仲裁裁决书等有效书面证明材料以及有效的毕业证书和学位证书（有学位要求的，下同）。</span
                                            ><span></span>
                                        </p>
                                        <p><span>2.毕业两年来尚未就业的考生应提供教育行政部门出具的有效毕业证书和学位证书以及未就业证明。</span><span></span></p>
                                        <p>
                                            <span
                                                >3.下岗、失业人员应提供户口所在地县级劳动保障部门发放的《再就业优惠证》或失业证明、有效的毕业证书和学位证书以及有效的人事档案管理机构出具的档案管理证明。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >四川民族学院负责对报考人员所提供材料的真实性进行审查，并负责对拟聘人员报考资格条件进行最终审核确认。审查审核合格并经我单位党委常委会研究同意后，将汇总拟聘用人员的相关材料报省教育厅。省教育厅将根据有关规定和我单位报送的人员材料，对拟聘用人员进行确认。</span
                                            ><span></span>
                                        </p>
                                        <p><span>通过省教育厅确认的人员，取得聘用资格。</span><span></span></p>
                                        <p>
                                            <span>我校凭确认复函，通知被聘用人员在规定的时间内（具体时间以四川民族学院通知为准）到单位报到，签订事业单位人事聘用合同，实行事业单位聘用制管理。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >逾期不能按要求提供上述材料的以及通过省教育厅审核确认后，未按规定时间报到的，视为自动放弃，取消其拟聘（聘用）人员资格。因以上原因产生的空额，不再递补。</span
                                            ><span></span>
                                        </p>
                                        <p><span>十一、纪律与监督</span><span></span></p>
                                        <p>
                                            <span
                                                >我单位在公开招聘中，将确保信息、过程、结果公开，接受社会及有关部门的监督。对违反规定、弄虚作假聘用的人员一经查实，取消其聘用资格，并对相关人员按照有关规定进行严肃处理。凡违反人事部令第６号第三十条和考风考纪规定，特别是不按公告进行资格审查的，按</span
                                            ><span>《事业单位公开招聘违纪违规行为处理规定》等规定</span><span>严肃处理，情节严重的给予政纪党纪处分，构成犯罪的，依法追究刑事责任。</span><span></span>
                                        </p>
                                        <p><span>十二、特别提示</span><span></span></p>
                                        <p>
                                            <span
                                                >1.考核招聘过程中如有调整、补充等事项，由四川民族学院在四川民族学院网站上公告。因报考者不主动、不按要求登录相关网站查阅相关信息，导致本人未能按要求参加考核、体检、考察、递补、聘用的，责任自负。</span
                                            ><span></span>
                                        </p>
                                        <p><span>2.请应聘人员确保联系方式正确、畅通。否则因无法与报考者取得联系所造成的后果，由报考者自行负责。</span><span></span></p>
                                        <p><span>3.本单位不举办也不委托任何机构举办考试辅导培训班。</span><span></span></p>
                                        <p><span>十三、疫情防控要求</span><span></span></p>
                                        <p><span>因新型冠状病毒肺炎疫情防控需要，根据四川省、康定市疫情防控相关要求，请到校参加考核人员遵照以下要求执行：</span><span></span></p>
                                        <p>
                                            <span
                                                >（一）根据新冠肺炎疫情防控要求，若本次考核招聘需要调整考核日程，调整情况将提前在四川民族学院官网予以发布，请报考人员随时关注变化情况并做好相应安排。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >（二）请按要求如实填写《<a></a
                                                ><a></a
                                                >四川民族学院来校人员登记表》（以下简称《登记表》，见附6），与四川天府健康通截图（截图日期为11月30日及之后），国务院通信大数据行程卡截图此两样截图，于12月1日16:00前发送至人事处邮箱
                                                <span class="detail-hidden-email-address">（点击查看）</span>
                                                。邮件题目格式：职位编码+姓名，并及时与人事处电话联系（联系人：林老师，联系电话：0836-2856177）。川外来校人员还须提供48小时内核酸检测证明截图（检测日期为11月30日及之后）。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span
                                                >（三）根据《甘孜州2021年新冠肺炎疫情防控工作指南（第四版）》规定，四川天府健康通为“红码”、“黄码”、14天内有中高风险地区旅居史、有中高风险地区所在县（市、区）和直辖市、省会城市所在街道旅居史、有中高风险地区所在地级市（直辖市、省会城市所在区）旅居史的应聘人员，不能来校参加考核。</span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <span>（四）应聘人员在校门口使用微信小程序“四川天府健康通”扫场所码，持有显示“绿码”的人员，接受体温检测并登记，确认身体无异常状况后，方可进校参加考核。</span
                                            ><span></span>
                                        </p>
                                        <p><span>（五）体温超过37.3℃的应聘人员，不能参加考核。</span><span></span></p>
                                        <p>
                                            <span
                                                >（六）请应聘人员来校途中做好旅途防护，乘坐公共交通工具的，旅途中应全程佩戴有效防护口罩。应避免用手直接接触公共物品，保持手部清洁。请妥善保管旅行票据信息，记录好乘车时间和地点，并积极配合交通防控部门的相关要求。</span
                                            ><span></span>
                                        </p>
                                        <p><span>（七）应聘人员自行准备口罩，未佩戴口罩者不允许进入校园、不接受资格复审，考核过程原则全程佩戴口罩。</span><span></span></p>
                                        <p><span>（八）考核期间应聘人员不扎堆、不聚集，避免人群拥挤。</span><span></span></p>
                                        <p><span>十四、有关咨询和监督电话</span><span></span></p>
                                        <p><span>咨询电话：0836-2855978（四川民族学院人事处，来电时请说明是在高校人才网看到的信息）</span><span></span></p>
                                        <p><span>监督电话：0836-2856171（四川民族学院纪委办公室）</span><span></span></p>
                                        <p><span>028-86110025（四川省教育厅人事与教师工作处）</span><span></span></p>
                                        <p><span>四川民族学院</span><span></span></p>
                                        <p><span>2021年11月8日</span><span></span></p>
                                        <p>
                                            <b><span>附件1：四川民族学院2021年11月考核招聘事业编制工作人员岗位和条件一览表.xlsx</span></b
                                            ><a href="http://rsks.gzz.gov.cn/tools/file.ashx?id=975895c0168129d38170238ec352f8e2"
                                                ><b
                                                    ><u><span>附件下载</span></u></b
                                                ></a
                                            ><b><span></span></b>
                                        </p>
                                        <p>
                                            <b><span>附件2、3、4、5、6.rar</span></b
                                            ><a href="http://rsks.gzz.gov.cn/tools/file.ashx?id=d20bfeb2a36684cd0c2b1923bb5b5238"
                                                ><b
                                                    ><u><span>附件下载</span></u></b
                                                ></a
                                            ><b><span></span></b>
                                        </p>
                                        <p><span>信息来源于网络，如有变更请以原发布者为准。</span><span></span></p>
                                        <p><span>来源链接：</span><span></span></p>
                                        <p><span></span></p>
                                        <p>
                                            <span
                                                ><a href="http://rsks.gzz.gov.cn/newsDetail/26026.html"
                                                    ><u><span>http://rsks.gzz.gov.cn/newsDetail/26026.html</span></u></a
                                                ></span
                                            ><span></span>
                                        </p>
                                        <p>
                                            <img src="http://test.static.gaoxiaojob.com/uploads/image/20220225/20220225101448_89207.png" contenteditable="false" />
                                        </p>
                                    </div>

                                    <div class="feedback-link">
                                        <div class="feedback-source-tips">
                                            <a class="color-primary" href="javascript:;">附件/来源打开异常提示</a>
                                            <p>附件下载异常：因平台URL协议兼容性原因，公告正文附件材料可能存在无法下载情况，届时您可复制附件链接并新建页面打开查看。</p>
                                            <p>来源链接打开异常：若文中“来源链接XXX”无法正常打开，属内容来源网站异常情况，本平台无法监控处理，如有需求，可持续关注相应链接修复情况。</p>
                                        </div>

                                        <a class="color-primary" href="https://wj.qq.com/s2/10430873/e75f" target="_blank">[我要纠错]</a>

                                        <script>
                                            $(function () {
                                                $('.feedback-source-tips').on('click', 'a', function () {
                                                    $(this).parent().toggleClass('is-extend')
                                                })
                                            })
                                        </script>
                                    </div>
                                </div>

                                <div class="detail-subtitle is-file">附件下载</div>

                                <div class="file-list">
                                    <el-popover placement="bottom" :width="300" trigger="hover" content="word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx">
                                        <template #reference>
                                            <a href="" download="" class="file" target="_blank">
                                                <div class="word"></div>
                                                <div class="file-name">word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx</div>
                                            </a>
                                        </template>
                                    </el-popover>
                                    <el-popover placement="bottom" :width="300" trigger="hover" content="excel文件名称.xlsx">
                                        <template #reference>
                                            <a href="" download="" class="file" target="_blank">
                                                <div class="excel"></div>
                                                <div class="file-name">excel文件名称.xlsx</div>
                                            </a>
                                        </template>
                                    </el-popover>
                                    <a href="" download="" class="file" target="_blank">
                                        <div class="pdf"></div>
                                        <div class="file-name">pdf文件名称.pdf</div>
                                    </a>

                                    <a href="" download="" class="file" target="_blank">
                                        <div class="common"></div>
                                        <div class="file-name">txt文件名称.txt</div>
                                    </a>
                                </div>

                                <!-- 未登录 start -->
                                <div class="use-Heat see-heat">
                                    <div class="detail-subtitle is-heat heat">
                                        <div>公告热度</div>
                                        <div class="open-heat">解锁详细分析</div>
                                    </div>

                                    <div class="power-heat">
                                        <div class="text-heat">该公告在同类公告中的热度为 <span class="active">火爆</span>，目前已有<span class="active">1000人</span>对其非常感兴趣</div>
                                    </div>
                                </div>

                                <!-- 未登录 end -->

                                <!-- 已登录 start -->
                                <div class="detail-subtitle is-heat heat">
                                    <div>公告热度</div>
                                    <div class="open-heat">查看详细分析</div>
                                </div>

                                <div class="power-heat">
                                    <div class="text-heat">该公告在同类公告中的热度为 <span>火爆</span>，目前已有<span>1000人</span>对其非常感兴趣</div>
                                </div>
                                <!-- 已登录 end -->

                                <div class="detail-emit">
                                    <a class="el-button el-button--primary" href="">
                                        <span>查看此公告的职位列表</span>
                                    </a>

                                    <button class="announcement el-button el-button--primary el-button--apply">
                                        <span>立即投递</span>
                                    </button>
                                </div>

                                <div class="qr-code">
                                    <div class="qr-tips">
                                        欢迎扫描下方二维码关注高校人才网官方微信（硕博QQ交流群：1015865503 ，进微信群<a href="http://qr61.cn/oaZcT1/qac5BaF" style="text-decoration: underline"
                                            >请点击添加官方客服号</a
                                        >）
                                    </div>
                                    <img class="code" src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" />
                                </div>

                                <div class="detail-tips">
                                    重要风险提示：如招聘单位在招聘过程中向求职者提出收取押金、保证金、体检费、材料费、成本费，或指定医院体检等，求职者有权要求招聘单位出具物价部门批准的收费许可证明材料，若无法提供相关证明，请求职者提高警惕，有可能属于诈骗或违规行为。
                                </div>

                                <!-- <div class="share-custom">
                                    <div class="sina-weibo">
                                        <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24"> </wb:follow-button>
                                    </div>
                                    <div>
                                        <div class="bshare-custom">
                                            <a title="分享到微信" class="bshare-weixin"></a>
                                            <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>
                                            <a title="分享到QQ空间" class="bshare-qzone"></a>
                                            <a title="分享到Facebook" class="bshare-facebook"></a>
                                            <a title="分享到Twitter" class="bshare-twitter"></a>
                                            <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn"> </a>
                                            <span class="BSHARE_COUNT bshare-share-count">0</span>
                                        </div>
                                        <script src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>
                                        <script src="http://static.bshare.cn/b/bshareC0.js"></script>
                                    </div>
                                </div> -->
                            </div>

                            <div class="footer-link-for-spider">
                                <div class="tab-nav">
                                    <span class="is-active">热门地区</span>
                                    <span>热门学科</span>
                                    <span>热门单位</span>
                                </div>

                                <div class="tab-pane">
                                    <div class="pane is-active">
                                        <a href="" target="_blank">热门地区</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                    </div>

                                    <div class="pane">
                                        <a href="" target="_blank">热门学科</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                    </div>

                                    <div class="pane">
                                        <a href="" target="_blank">热门单位</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">广州高校人才网</a>
                                        <a href="" target="_blank">深圳高校人才网</a>
                                        <a href="" target="_blank">呼和浩特高校人才网</a>
                                        <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                        <a href="" target="_blank">上海高校人才网</a>
                                    </div>
                                </div>
                            </div>

                            <script>
                                $(function () {
                                    $linkForSpider = $('.footer-link-for-spider')
                                    $nav = $linkForSpider.find('.tab-nav span')
                                    $pane = $linkForSpider.find('.tab-pane .pane')
                                    activeClassName = 'is-active'

                                    $nav.on('click', function () {
                                        $(this).addClass(activeClassName).siblings().removeClass(activeClassName)
                                        $pane.eq($(this).index()).addClass(activeClassName).siblings().removeClass(activeClassName)
                                    })
                                })
                            </script>
                        </section>

                        <aside>
                            <div class="unit">
                                <a class="unit-name" href="" target="_blank">
                                    <img class="logo" src="http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png" alt="" />

                                    <div class="name">
                                        <h3>湖南师范大学</h3>
                                        <h5>Hunan Normal University</h5>
                                    </div>
                                </a>

                                <div class="unit-tips category">教育/培训/院校</div>
                                <div class="unit-tips number">1000人以上</div>
                                <div class="unit-tips type">科研院所</div>

                                <div class="unit-data el-row">
                                    <a class="el-col el-col-8" href="" target="_blank">
                                        <strong class="color-primary">100</strong>
                                        <span>在招公告</span>
                                    </a>
                                    <a class="el-col el-col-8" href="" target="_blank">
                                        <strong class="color-primary">66</strong>
                                        <span>在招职位</span>
                                    </a>
                                    <span class="el-col el-col-8 to-home">
                                        <a href="" target="_blank">
                                            <span>单位主页</span>
                                        </a>
                                    </span>
                                </div>
                            </div>

                            <div id="recommendContainer">
                                <!-- <div class="recommend is-notice">
                                <div class="title">
                                    <h5>专享推荐</h5>
                                </div>

                                <div id="detailRecommend" class="swiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="recommend-list">
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="recommend-list">
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                            <script>
                                $(function () {
                                    new Swiper("#detailRecommend", {
                                        autoplay: {
                                            delay: 7000,
                                            pauseOnMouseEnter: true,
                                            disableOnInteraction: false
                                        },
                                        delay: 7000,
                                        loop: true,
                                        pagination: {
                                            el: ".swiper-pagination",
                                            clickable: true
                                        }
                                    })
                                })
                            </script> -->
                            </div>

                            <div class="new-media-group">
                                <link rel="stylesheet" href="./css/newMediaGroup.css" />

                                <div class="group">
                                    <div class="group-title">
                                        <div class="title">
                                            <h5>精选社群</h5>
                                        </div>

                                        <h4>
                                            <strong>每天1000+人才加入</strong>
                                            <span class="more-btn color-primary">查看更多&gt;&gt;</span>
                                        </h4>
                                    </div>
                                    <div class="group-list">
                                        <ul>
                                            <li class="ligong active">
                                                <div class="group-item">理工农医交流群</div>
                                                <div class="group-codeimg">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" />
                                                    <p><strong>微信扫码进群</strong></p>
                                                    <p>添加请备注学历</p>
                                                </div>
                                            </li>
                                            <li class="renwen">
                                                <div class="group-item">人文社科交流群</div>
                                                <div class="group-codeimg">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" />
                                                    <p><strong>微信扫码进群</strong></p>
                                                    <p>添加请备注学历</p>
                                                </div>
                                            </li>
                                            <li class="jingguan">
                                                <div class="group-item">经管求职交流群</div>
                                                <div class="group-codeimg">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" />
                                                    <p><strong>微信扫码进群</strong></p>
                                                    <p>添加请备注学历</p>
                                                </div>
                                            </li>
                                            <li class="beishangguangshen">
                                                <div class="group-item">北上广深求职交流群</div>
                                                <div class="group-codeimg">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" />
                                                    <p><strong>微信扫码进群</strong></p>
                                                    <p>添加请备注学历</p>
                                                </div>
                                            </li>
                                            <li class="youzhidanwei">
                                                <div class="group-item">优质单位博硕直推群</div>
                                                <div class="group-codeimg">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" />
                                                    <p><strong>微信扫码进群</strong></p>
                                                    <p>添加请备注学历</p>
                                                </div>
                                            </li>
                                            <li class="shuoborencai">
                                                <div class="group-item">硕博人才求职学习平台</div>
                                                <div class="group-codeimg">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/18.png" />
                                                    <p><strong>微信扫码即可学习</strong></p>
                                                    <p></p>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="group-dialog">
                                        <div class="dialog">
                                            <div class="dialog-top">精选社群（每天有1000+博硕人才加入）<i class="closeBtn"></i></div>
                                            <div class="dialog-body">
                                                <div class="dialog-nav"><span class="active">学科领域专场</span><span>地区专场</span></div>
                                                <div class="group-box active">
                                                    <div class="group-content">
                                                        <div>
                                                            工学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            理学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            经济学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            管理学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            哲学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            法学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            文学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            艺术学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            教育学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            历史学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            医学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            农林水产学
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            交叉学科
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            其他
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/101.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="group-box">
                                                    <div class="group-content">
                                                        <div>
                                                            东北地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            华北地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            西北地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            华中地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            华东地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            西南地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            华南地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            海外地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            港澳台地区
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div class="group-content border-top">
                                                        <div>
                                                            北京
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            上海
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            广州
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            深圳
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            武汉
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            南京
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            哈尔滨
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            长春
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            西安
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                        <div>
                                                            成都
                                                            <p>
                                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/16.png" /><br /><strong>微信扫码，即刻进群</strong
                                                                ><br />添加请备注"学历"
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <script>
                                    $(function () {
                                        var $moreBtn = $('.more-btn')
                                        var $dialog = $('.group-dialog')
                                        var $groupItems = $('.group-list li')
                                        var $closeBtn = $('.closeBtn')
                                        var $dialogTab = $('.dialog-nav span')
                                        var $groupBox = $('.group-box')

                                        $groupItems.on('click', function () {
                                            $(this).addClass('active').siblings().removeClass('active')
                                        })

                                        $moreBtn.on('click', function () {
                                            $dialog.show()
                                        })

                                        $closeBtn.on('click', function () {
                                            $dialog.hide()
                                        })

                                        $dialogTab.on('click', function () {
                                            var index = $(this).index()
                                            $(this).addClass('active').siblings().removeClass('active')
                                            $groupBox.eq(index).addClass('active').siblings().removeClass('active')
                                        })
                                    })
                                </script>
                            </div>
                        </aside>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                <a href="/" target="_blank">资质证明</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                    <a href="/" target="_blank">粤ICP备13048400号-2</a>
                    粤公网安备：
                    <a href="/" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="/" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <script src="http://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
        <script src="./js/detailService.js"></script>
        <script>
            $(function () {
                Vue.createApp({}).use(ElementPlus).mount('#boonsTemplate')
                Vue.createApp({}).use(ElementPlus).mount('.file-list')

                var id = '<?=$info["id"]?>'
                var $collectButton = $('.el-button--collect')
                var $jobApplyButton = $('.el-button--apply')
                var $recommendContainer = $('#recommendContainer')

                httpPost('/api/person/announcement/get-recommend-list', { id: id }).then(function (data) {
                    if (data.html) {
                        $recommendContainer.html(data.html)
                    }
                })

                $collectButton.on('click', function () {
                    var $this = $(this)
                    var isCollected = $this.hasClass('collected')

                    httpPost('/api/person/announcement/collect', { id }).then(function () {
                        $this
                            .toggleClass('collected')
                            .find('span')
                            .text(isCollected ? '收藏' : '已收藏')
                    })
                })

                $jobApplyButton.on('click', function () {
                    window.globalComponents.applyDialogComponent.announcementApply(id)
                })

                var seeHeat = $('.see-heat')
                var params = {
                    apiPull: '/api/person/announcement/check-generate-report',
                    apiCreate: '/api/person/announcement/create-report',
                    param: { announcementId: '<?=$info["id"]?>' }
                }
                seeHeat.on('click', function () {
                    window.globalComponents.PromptDialogComponent.pull(params)
                })
            })
        </script>
    </body>
</html>
