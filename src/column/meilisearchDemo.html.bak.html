<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>搜索示例</title>
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <script src="https://unpkg.com/element-plus"></script>
        <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
        <style>
            .search-container {
                padding: 20px;
                max-width: 1200px;
                margin: 0 auto;
            }
            .search-header {
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
                align-items: center;
            }
            .search-input {
                flex: 1;
            }
            .table-container {
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div id="app">
            <div class="search-container">
                <div class="search-header">
                    <el-input v-model="searchKeyword" placeholder="请输入关键字" class="search-input" clearable @keyup.enter="handleSearch"> </el-input>
                    <el-input-number v-model="limit" :min="1" :max="2000" placeholder="数量"></el-input-number>
                    <el-button type="primary" @click="handleSearch" :loading="loading"> 搜索 </el-button>
                    <el-button type="success" @click="handleExport" :disabled="!tableData.length"> 导出表格 </el-button>
                </div>

                <div class="table-container">
                    <el-table :data="tableData" border style="width: 100%" v-loading="loading">
                        <el-table-column prop="id" label="ID" width="180"> </el-table-column>
                        <el-table-column prop="title" label="职位名称"> </el-table-column>
                        <el-table-column label="匹配度" width="100">
                            <template #default="scope"> {{ formatMatchRate(scope.row.salary) }} </template>
                        </el-table-column>
                        <el-table-column prop="status" label="职位是否在线" width="120"> </el-table-column>
                        <el-table-column label="跳转链接" width="100">
                            <template #default="scope">
                                <el-link type="primary" :href="scope.row.url" target="_blank"> 查看详情 </el-link>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>

        <script>
            const { createApp, ref } = Vue
            const app = createApp({
                setup() {
                    const searchKeyword = ref('')
                    const limit = ref(10)
                    const tableData = ref([])
                    const loading = ref(false)

                    const formatMatchRate = (rate) => {
                        return `${(rate * 100).toFixed(1)}%`
                    }

                    const handleSearch = async () => {
                        if (!searchKeyword.value.trim()) {
                            ElMessage.warning('请输入搜索关键词')
                            return
                        }

                        loading.value = true
                        try {
                            const params = new URLSearchParams({
                                keyword: searchKeyword.value,
                                limit: limit.value.toString()
                            })

                            const response = await fetch(`https://api.gaoxiaojob.com/meilisearch/job-demo?${params}`)

                            if (!response.ok) {
                                throw new Error('网络请求失败')
                            }

                            const responseData = await response.json()

                            if (responseData.result !== 1) {
                                throw new Error(responseData.msg || '请求失败')
                            }

                            tableData.value = responseData.data || []

                            if (tableData.value.length === 0) {
                                ElMessage.info('未找到相关数据')
                            }
                        } catch (error) {
                            console.error('搜索失败:', error)
                            ElMessage.error(error.message || '搜索失败，请稍后重试')
                            tableData.value = []
                        } finally {
                            loading.value = false
                        }
                    }

                    const handleExport = () => {
                        if (!tableData.value.length) {
                            ElMessage.warning('没有可导出的数据')
                            return
                        }

                        // 定义CSV的表头
                        const headers = ['ID', '职位名称', '匹配度', '职位是否在线', '链接']

                        // 将数据转换为CSV格式
                        const csvContent = [
                            // 添加表头
                            headers.join(','),
                            // 添加数据行
                            ...tableData.value.map((row) =>
                                [
                                    row.id,
                                    `"${row.title}"`, // 使用双引号包裹，避免逗号等特殊字符问题
                                    formatMatchRate(row.salary),
                                    row.status,
                                    row.url
                                ].join(',')
                            )
                        ].join('\n')

                        // 创建Blob对象
                        const blob = new Blob(['\ufeff' + csvContent], {
                            // 添加BOM标记，解决中文乱码
                            type: 'text/csv;charset=utf-8'
                        })

                        // 创建下载链接
                        const link = document.createElement('a')
                        link.href = URL.createObjectURL(blob)
                        link.download = `职位数据_${new Date().toLocaleDateString()}.csv`

                        // 触发下载
                        document.body.appendChild(link)
                        link.click()
                        document.body.removeChild(link)

                        // 释放URL对象
                        URL.revokeObjectURL(link.href)

                        ElMessage.success('导出成功')
                    }

                    return {
                        searchKeyword,
                        limit,
                        tableData,
                        loading,
                        handleSearch,
                        handleExport,
                        formatMatchRate
                    }
                }
            })

            app.use(ElementPlus)
            app.mount('#app')
        </script>
    </body>
</html>
