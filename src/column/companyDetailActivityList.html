<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>单位详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/companyDetail.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main auth">
                <div class="detail-container">
                    <div class="detail-header">
                        <div class="modal">
                            <div class="main">
                                <div class="info">
                                    <div class="left">
                                        <div class="logo">
                                            <img src="http://test.static.gaoxiaojob.com/uploads/company_logo/20220312095428_64084.png" alt="" />
                                        </div>
                                        <button class="el-button el-button--primary company-collect-button">
                                            <span>立即关注</span>
                                        </button>
                                        <!-- <button class="el-button el-button--default company-collect-button">
                                        <span>已关注</span>
                                    </button> -->
                                    </div>
                                    <div class="right">
                                        <h1>湖南师范大学</h1>
                                        <h2>Northwestern Polytechnical University</h2>
                                        <div class="tags" id="tagsTemplate">
                                            <span>985</span>
                                            <span>211</span>
                                            <span>一流大学</span>

                                            <el-popover placement="bottom" :width="430" trigger="hover" v-cloak>
                                                <template #reference>
                                                    <span>
                                                        <i class="el-icon boon-more" style="--font-size: 12px">
                                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                                <path
                                                                    fill="currentColor"
                                                                    d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                                ></path>
                                                            </svg>
                                                        </i>
                                                    </span>
                                                </template>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">五险一金</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">绩效奖金</span>
                                            </el-popover>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bottom">
                                <div class="tabs">
                                    <a class="tab-pane-common introduce" href="./companyDetail.html">单位介绍</a>
                                    <a class="tab-pane-common announcemen" href="./companyDetailNoticeList.html">招聘公告（99）</a>
                                    <a class="tab-pane-common job" href="./companyDetailJobList.html">招聘职位（99+）</a>
                                    <a class="tab-pane-common activity active" href="./companyDetailActivityList.html">引才活动（99+）</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <section id="activityTemplate" class="section">
                            <div class="tabs-common detail-cotent-auth">
                                <div class="tab-content company-activity js-company-activity active">
                                    <div class="wrapper">
                                        <div class="top">
                                            <div class="title">引才活动</div>
                                        </div>

                                        <ul class="result">
                                            <li class="item">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才
                                                    </a>
                                                    <div class="tag tag1">学者论坛</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step-1">即将开始</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                                <div class="activity-desc" title="...">
                                                    <a href="http://" target="_blank">中国科学院分子细胞科学卓越创新中心招聘公告</a>； <a href="http://" target="_blank">中心招聘公告中心中国科学</a>；
                                                    <a href="http://" target="_blank">院分子细胞科学卓越创新中心招聘公告招聘公告</a>
                                                </div>
                                            </li>

                                            <li class="item">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...
                                                    </a>
                                                    <div class="tag tag2">出海引才</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step1">进行中</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="item">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...
                                                    </a>
                                                    <div class="tag tag3">其他活动</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step2">进行中</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="item">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...
                                                    </a>
                                                    <div class="tag tag4">全国巡回现场招聘会</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step3">进行中</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="item">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...
                                                    </a>
                                                    <div class="tag tag5">RPO线上面试会</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step2">进行中</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="item">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...
                                                    </a>
                                                    <div class="tag tag6">RPO线上面试会</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step2">进行中</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                            </li>

                                            <li class="item offline-mark">
                                                <div class="activity-top">
                                                    <a class="offline-gray-first title" href="http://" target="_blank" rel="nofollow">
                                                        诚邀海内外英才参加2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛人才...
                                                    </a>
                                                    <div class="tag tag1">学者论坛</div>
                                                </div>
                                                <div class="activity-info">
                                                    <div class="step step3">已结束</div>
                                                    <div class="date">
                                                        <span class="text">活动时间:2025.12.16~2025.1.18</span>
                                                    </div>
                                                    <div class="address">
                                                        <span class="text">活动地点:北京</span>
                                                    </div>
                                                    <div class="session">
                                                        <span class="text">活动场次:德国•慕尼黑站,德国•柏林站,荷兰•代尔夫特站,荷兰...</span>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>

                                        <div class="empty">
                                            <el-empty description="暂无数据"></el-empty>
                                        </div>

                                        <el-pagination
                                            background
                                            :layout="'total, sizes, prev, pager, next, jumper'"
                                            v-model:current-page="noticePagination.page"
                                            v-model:page-size="noticePagination.size"
                                            :total="noticePagination.total"
                                        >
                                        </el-pagination>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <aside class="aside">
                            <div class="detail-basic">
                                <div class="basic-data">
                                    <img src="http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png" alt="" class="logo" />
                                    <div class="name">
                                        <h5>湖南师范大学</h5>
                                        <h6>Hunan Normal University</h6>
                                    </div>
                                </div>

                                <div class="basic-info">
                                    <span class="category">教育/培训/院校</span>
                                    <span class="nature"> <i>高等院校</i><i>公立</i> </span>
                                    <span class="scale">1000人以上</span>
                                    <span class="site"><a href="http://www.gaoxiaojob.com/" target="_blank" rel="nofollow">http://www.gaoxiaojob.com/</a></span>
                                </div>
                            </div>

                            <div class="company-style">
                                <div class="title">单位风采</div>
                                <div class="swiper company-style-swiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <a href="" target="_blank">
                                                <img src="http://img.gaoxiaojob.com/xbwyHF_04.jpg" alt="" />
                                            </a>
                                        </div>
                                        <div class="swiper-slide">
                                            <a href="" target="_blank">
                                                <img src="http://img.gaoxiaojob.com/xbwyHF_03.jpg" alt="" />
                                            </a>
                                        </div>
                                        <div class="swiper-slide">
                                            <a href="" target="_blank">
                                                <img src="http://img.gaoxiaojob.com/xbwyHF_04.jpg" alt="" />
                                            </a>
                                        </div>
                                    </div>
                                    <div class="swiper-button swiper-button-next company-style-next"></div>
                                    <div class="swiper-button swiper-button-prev company-style-prev"></div>
                                    <div class="swiper-pagination company-style-pagination"></div>
                                </div>
                            </div>

                            <div class="company-activity">
                                <div class="title">单位近期引才活动预告</div>
                                <div class="content">
                                    <div class="activity">
                                        <div class="activity-title">
                                            <a href="http://" target="_blank" rel="noopener noreferrer">2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛</a>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-tag tag tag1">全国巡回招聘会</div>
                                            <div class="activity-con">2024.11.23~2024.12.12&nbsp;丨&nbsp;广州巴黎广州巴黎广州巴黎</div>
                                        </div>
                                    </div>
                                    <div class="activity">
                                        <div class="activity-title">
                                            <a href="http://" target="_blank" rel="noopener noreferrer">2025年北京大学环境学科（北京&深圳）优秀青年人才国际论坛</a>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-tag tag tag2">出海引才</div>
                                            <div class="activity-con">2024.11.23~2024.12.12 丨广州巴黎广州巴黎广州巴黎</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="view-all">
                                    <a href="http://" target="_blank" rel="noopener noreferrer">查看全部活动&nbsp;&nbsp;></a>
                                </div>
                            </div>

                            <div class="company-address">
                                <div class="title">单位地址</div>
                                <div class="address">广州市天河区建工路12号301房（广州高才信息科技有限公司）</div>
                            </div>

                            <div class="to-miniprogram"></div>
                        </aside>
                    </div>
                </div>
            </div>
        </div>

        <script src="./lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./lib/popper/popper.min.js"></script>
        <script>
            $(function () {
                Vue.createApp({}).use(ElementPlus).mount('#tagsTemplate')

                const companyOptions = {
                    data() {
                        return {
                            companyId: '',
                            announcementAreaOptions: [
                                { label: '广州', value: '1' },
                                { label: '深圳', value: '2' }
                            ],

                            announceJobCategoryList: [{ label: '技术', value: '1' }],

                            majorOptions: [
                                { label: '毛泽东理论', value: '1' },
                                { label: '计算机应用', value: '2' }
                            ],

                            educationOptions: [
                                { label: '不限', value: '1' },
                                { label: '大专', value: '2' },
                                { label: '本科', value: '3' },
                                { label: '博士', value: '4' }
                            ],

                            noticeApi: '/api/person/company/get-activity-list',

                            noticePagination: {
                                page: 1,
                                size: 20,
                                total: 100
                            }
                        }
                    },

                    watch: {
                        noticePagination: {
                            handler() {
                                this.handleFilter()
                            },
                            deep: true
                        }
                    },

                    mounted() {
                        this.initSwiper()
                    },

                    methods: {
                        initSwiper() {
                            var swiper = new Swiper('.company-style-swiper', {
                                loop: true,
                                autoplay: {
                                    pauseOnMouseEnter: true,
                                    disableOnInteraction: false,
                                    delay: 3000
                                },
                                navigation: {
                                    nextEl: '.company-style-next',
                                    prevEl: '.company-style-prev'
                                },
                                preventClicks: false,
                                pagination: {
                                    el: '.swiper-pagination'
                                },
                                allowTouchMove: false
                            })
                        },

                        handleFilter() {
                            const { page, size: pageSize } = this.noticePagination
                            const { companyId, noticeApi } = this
                            const api = noticeApi
                            const query = { page, pageSize, companyId }

                            httpGet(api, query).then((res) => {
                                const { list, total } = res

                                $('.js-company-activity').find('.result').html(list)
                                this.noticePagination.total = total
                            })
                        }
                    }
                }

                Vue.createApp(companyOptions)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#activityTemplate')

                var id = "<?= $info['companyId']?>"
                var $collectButtons = $('.company-collect-button')

                // get miniprogram qrcode
                var miniprogramSlot = $('.to-miniprogram')

                httpGet('/company/get-detail-mini-code?id=' + id).then(function (data) {
                    miniprogramSlot.append($('<img src="' + data.url + '" />'))
                })

                $collectButtons.on('click', function () {
                    var $this = $(this)
                    var isCollected = $this.hasClass('el-button--default')

                    httpPost('/api/person/company/collect', { companyId: id }).then(function () {
                        if (isCollected) {
                            $this.removeClass('el-button--default').addClass('el-button--primary').find('span').text('立即关注')
                        } else {
                            $this.removeClass('el-button--primary').addClass('el-button--default').find('span').text('已关注')
                        }
                    })
                })
            })
        </script>
    </body>
</html>
