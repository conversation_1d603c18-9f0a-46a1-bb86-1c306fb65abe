<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>公共弹窗组件</title>
    <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
    <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
    <link rel="stylesheet" href="./css/common.css" />
    <link rel="stylesheet" href="./css/checkGenerateReportDialog.css" />
    <script src="./lib/vue/vue.min.js"></script>
    <script src="./lib/element-plus/index.min.js"></script>
    <script src="./lib/axios/axios.min.js"></script>
    <script src="./lib/qs/qs.min.js"></script>
    <script src="./lib/jquery/jquery.min.js"></script>
    <script src="./js/config.js"></script>
    <script src="./js/request.js"></script>
    <script type="text/javascript" src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
</head>

<body>
    <div id="checkGenerateReportDialog" class="success-dialog-alert-template" v-cloak>
        <el-dialog v-model="dialogVisible" :close-on-click-modal="false">
            <div class="wechat-container" v-if="jumpType == 1">
                <div class="checktips">{{ title }}</div>
                <div class="tips-text">
                    <div class="text-right">
                        <span>{{tips1}}</span>
                        <div class="text-second">{{tips2}}</div>
                    </div>
                </div>
                <div class="el-message-box__btns">
                    <button class="el-button el-button--default el-button--large change" type="button"
                        @click="handelDialogClose">
                        <span>{{cancelBtnTxt}}</span>
                    </button>
                    <button class="el-button el-button--default el-button--large el-button--primary" type="button"
                        @click="createReport(isConfirm)">
                        <span>{{confirmBtnTxt}}</span>
                    </button>
                </div>
            </div>
            <div class="wechat-container" v-else-if="jumpType == 9">
                <div class="checktips">{{ title }}</div>
                <div class="tips-text">
                    <div class="text-right">
                        <span>{{tips1}}</span>
                        <div class="text-second">{{tips2}}</div>
                    </div>
                </div>
                <div class="el-message-box__btns">
                    <button class="el-button el-button--default el-button--large change" type="button"
                        @click="handelDialogClose">
                        <span>{{cancelBtnTxt}}</span>
                    </button>
                    <button class="el-button el-button--default el-button--large el-button--primary" type="button"
                        @click="finishResume">
                        <span>{{confirmBtnTxt}}</span>
                    </button>
                </div>
            </div>
            <div class="wechat-container" v-else>
                <div class="checktips">{{ title }}</div>
                <div class="tips-text">
                    <div class="text-right">
                        <span>{{tips1}}</span>
                        <div class="text-second">{{tips2}}</div>
                    </div>
                </div>
                <div class="el-message-box__btns">
                    <button class="el-button el-button--default el-button--large change" type="button"
                        @click="handelDialogClose">
                        <span>{{cancelBtnTxt}}</span>
                    </button>
                </div>
            </div>
        </el-dialog>
    </div>

    <script>
        $(function () {
            const PromptDialogOptions = {
                data() {
                    return {
                        dialogVisible: false,
                        title: '',
                        tips1: '',
                        tips2: '',
                        jumpUrl: '',
                        jumpType: '',
                        confirmBtnTxt: '',
                        cancelBtnTxt: '',
                        params: '',
                        isConfirm: ''
                    }
                },

                methods: {
                    pull(params, callback = () => {}) {
                        var _this = this
                        _this.params = params
                        httpGet(params.apiPull, params.param).then(function (data) {
                            const {
                                title,
                                jumpType,
                                jumpUrl,
                                confirmBtnTxt,
                                cancelBtnTxt,
                                tips1,
                                tips2,
                                isConfirm
                            } = data
                            if (jumpType == 2) {
                                window.open(jumpUrl, '_blank')
                                return
                            }
                            _this.dialogVisible = true
                            _this.title = title
                            _this.tips1 = tips1
                            _this.tips2 = tips2
                            _this.jumpUrl = jumpUrl
                            _this.jumpType = jumpType
                            _this.confirmBtnTxt = confirmBtnTxt
                            _this.cancelBtnTxt = cancelBtnTxt
                            _this.isConfirm = isConfirm
                        })
                    },
                    createReport(id) {
                        var _this = this
                        var params = _this.params
                        var isConfirm = {
                            isConfirm: id
                        }
                        var param = {
                            ...params.param,
                            ...isConfirm
                        }
                        if (id == '1') {
                            httpGet(params.apiPull, param).then(function (data) {
                                const {
                                    title,
                                    jumpType,
                                    jumpUrl,
                                    confirmBtnTxt,
                                    cancelBtnTxt,
                                    tips1,
                                    tips2,
                                    isConfirm
                                } = data
                                _this.title = title
                                _this.tips1 = tips1
                                _this.tips2 = tips2
                                _this.jumpUrl = jumpUrl
                                _this.jumpType = jumpType
                                _this.confirmBtnTxt = confirmBtnTxt
                                _this.cancelBtnTxt = cancelBtnTxt
                                _this.isConfirm = isConfirm
                            })
                        } else {
                            httpGet(params.apiCreate, params.param).then(function (data) {
                                const {
                                    jumpUrl
                                } = data
                                _this.handelDialogClose()
                                window.open(jumpUrl)
                            })
                        }
                    },
                    finishResume() {
                        window.location.href = this.jumpUrl
                    },
                    handelDialogClose() {
                        this.dialogVisible = false
                    }
                },
                mounted() {}
            }

            const PromptDialogComponent = Vue.createApp(PromptDialogOptions).use(ElementPlus).mount(
                '#checkGenerateReportDialog')

            window.globalComponents = {
                ...window.globalComponents,
                PromptDialogComponent
            }


            var params = {
                apiPull: '/api/person/job/check-generate-report',
                apiCreate: '/api/person/job/create-report',
                param: {
                    jobId: '482868'
                }
            }
            PromptDialogComponent.pull(params)
        })

    </script>
</body>

</html>
