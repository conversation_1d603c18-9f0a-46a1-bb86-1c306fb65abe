<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>职位中心</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/rczhaopin.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div class="el-main">
            <div class="search-container">
                <div class="kw-content">
                    <input type="text" class="search-kw" placeholder="请输入职位/公告/单位等关键词搜索" />
                    <button class="search-button">搜索</button>
                </div>
                <div class="filter-content">
                    <div class="filter-pane area">
                        <h6 class="filter-label">工作地点</h6>
                        <div class="filter-value">
                            <a class="filter-item select" href="javascript:;">全部</a>
                            <a class="filter-item" href="">广州</a>
                            <a class="filter-item" href="">深圳</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                            <a class="filter-item" href="">成都</a>
                        </div>
                        <span class="filter-more area-more">更多</span>
                    </div>
                    <div class="filter-pane only-line">
                        <h6 class="filter-label">职位类型</h6>
                        <div class="filter-value">
                            <div class="filter-list">
                                <a class="filter-item" href="">全部</a>
                                <a class="filter-item active" sub-key="1" href="javascript:;">教学岗（高等院校）</a>
                                <a class="filter-item" sub-key="2" href="javascript:;">教学支撑岗（高等院校）</a>
                                <a class="filter-item" sub-key="3" href="javascript:;">教学岗（中小学及幼儿园）</a>
                                <a class="filter-item" sub-key="4" href="javascript:;">科学研究岗（教育/科研/卫生单位）</a>
                                <a class="filter-item" sub-key="5" href="javascript:;">医疗卫生专业岗</a>
                                <a class="filter-item" sub-key="6" href="javascript:;">博士后</a>

                                <div class="filter-sub-content">
                                    <div class="filter-sub active" sup-key="1">
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item select" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                    </div>
                                    <div class="filter-sub" sup-key="2">
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                        <a class="filter-sub-item" href="">教学支撑岗二级</a>
                                    </div>
                                    <div class="filter-sub" sup-key="3">
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                        <a class="filter-sub-item" href="">教学岗二级</a>
                                    </div>
                                    <div class="filter-sub" sup-key="4">
                                        <a class="filter-sub-item" href="">科学研究岗二级</a>
                                        <a class="filter-sub-item" href="">科学研究岗二级</a>
                                        <a class="filter-sub-item" href="">科学研究岗二级</a>
                                        <a class="filter-sub-item" href="">科学研究岗二级</a>
                                    </div>
                                    <div class="filter-sub" sup-key="5">
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                        <a class="filter-sub-item" href="">医疗卫生专业岗二级</a>
                                    </div>
                                    <div class="filter-sub" sup-key="6">
                                        <a class="filter-sub-item" href="">博士后二级</a>
                                        <a class="filter-sub-item" href="">博士后二级</a>
                                        <a class="filter-sub-item" href="">博士后二级</a>
                                        <a class="filter-sub-item" href="">博士后二级</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="filter-more">更多</span>
                    </div>
                    <div class="filter-pane">
                        <h6 class="filter-label">更多筛选</h6>
                        <div class="filter-value">
                            <div class="filter-select">
                                <div class="current-select">学历要求</div>
                                <div class="filter-select-dropdown">
                                    <div class="filter-select-tree">
                                        <ul>
                                            <li><a class="filter-item" href="">博士研究生</a></li>
                                            <li><a class="filter-item" href="">硕士研究生</a></li>
                                            <li><a class="filter-item" href="">本科</a></li>
                                            <li><a class="filter-item" href="">大专</a></li>
                                            <li><a class="filter-item" href="">其他</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-cascade is-select">
                                <div class="current-select">单位类型</div>
                                <div class="filter-select-dropdown">
                                    <div class="filter-select-tree">
                                        <ul>
                                            <li><a class="filter-item" href="">双一流院校</a></li>
                                            <li><a class="filter-item select" href="">普通本科院校</a></li>
                                            <li><a class="filter-item" href="">高职高专院校</a></li>
                                            <li><a class="filter-item" href="">党校与行政学院</a></li>
                                            <li><a class="filter-item" href="">机关单位</a></li>
                                            <li><a class="filter-item" href="">军队武警</a></li>
                                            <li><a class="filter-item" href="">事业单位</a></li>
                                            <li><a class="filter-item" href="">政府国有企业</a></li>
                                            <li><a class="filter-item" href="">知名企业</a></li>
                                            <li><a class="filter-item" href="">中小成长型企业</a></li>
                                            <li><a class="filter-item" href="">中小学</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="filter-cascade">
                                <div class="current-select">学科分类</div>
                                <div class="filter-select-dropdown">
                                    <div class="filter-select-tree">
                                        <ul>
                                            <li class="has-children">
                                                <a class="filter-item" href="javascript:;">哲学</a>
                                                <div class="filter-select-tree">
                                                    <ul>
                                                        <li class="has-children">
                                                            <a class="filter-item" href="javascript:;">哲学</a>
                                                            <div class="filter-select-tree">
                                                                <ul>
                                                                    <li><a class="filter-item" href="">哲学3333</a></li>
                                                                    <li><a class="filter-item" href="">哲学3333</a></li>
                                                                    <li><a class="filter-item" href="">哲学3333</a></li>
                                                                    <li><a class="filter-item" href="">哲学3333</a></li>
                                                                    <li><a class="filter-item" href="">哲学3333</a></li>
                                                                    <li><a class="filter-item" href="">哲学3333</a></li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            <a class="filter-item" href="javascript:;">哲学测试三级-请勿用！！！！！！</a>
                                                            <div class="filter-select-tree">
                                                                <ul>
                                                                    <li>
                                                                        <a class="filter-item" href="javascript:;">哲学三级--测试4级</a>
                                                                        <div class="filter-select-tree">
                                                                            <ul>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                                <li><a class="filter-item" href="">哲学四级</a></li>
                                                                            </ul>
                                                                        </div>
                                                                    </li>
                                                                    <li><a class="filter-item" href="">哲学三级</a></li>
                                                                    <li><a class="filter-item" href="">哲学三级</a></li>
                                                                    <li><a class="filter-item" href="">哲学三级</a></li>
                                                                    <li><a class="filter-item" href="">哲学三级</a></li>
                                                                    <li><a class="filter-item" href="">哲学三级</a></li>
                                                                    <li><a class="filter-item" href="">哲学三级</a></li>
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li>
                                                <a class="filter-item select" href="javascript:;">经济学</a>
                                                <div class="filter-select-tree">
                                                    <ul>
                                                        <li><a href="">理论经济学</a></li>
                                                        <li><a href="">应用经济学</a></li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li>
                                                <a class="filter-item" href="javascript:;">法学</a>
                                                <div class="filter-select-tree">
                                                    <ul>
                                                        <li><a href="">法学</a></li>
                                                        <li><a href="">政治学</a></li>
                                                        <li><a href="">社会学</a></li>
                                                        <li><a href="">民族学</a></li>
                                                        <li><a href="">马克思主义理论</a></li>
                                                        <li><a href="">公安学</a></li>
                                                        <li><a href="">法学</a></li>
                                                        <li><a href="">政治学</a></li>
                                                        <li><a href="">社会学</a></li>
                                                        <li><a href="">民族学</a></li>
                                                        <li><a href="">马克思主义理论</a></li>
                                                        <li><a href="">公安学</a></li>
                                                    </ul>
                                                </div>
                                            </li>
                                            <li><a class="filter-item" href="javascript:;">教育学</a></li>
                                            <li><a class="filter-item" href="javascript:;">文学</a></li>
                                            <li><a class="filter-item" href="javascript:;">历史学</a></li>
                                            <li><a class="filter-item" href="javascript:;">理学</a></li>
                                            <li><a class="filter-item" href="javascript:;">工学</a></li>
                                            <li><a class="filter-item" href="javascript:;">农学</a></li>
                                            <li><a class="filter-item" href="javascript:;">医学</a></li>
                                            <li><a class="filter-item" href="javascript:;">军事学</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="filter-pane filter-selected">
                        <h6 class="filter-label">已选条件</h6>
                        <div class="filter-value">
                            <span class="selected"
                                >北京
                                <a href="" class="close"></a>
                            </span>
                            <span class="selected"
                                >学术领军人才
                                <a href="" class="close"></a>
                            </span>
                            <span class="selected"
                                >博士研究生
                                <a href="" class="close"></a>
                            </span>
                            <span class="selected"
                                >双一流院校
                                <a href="" class="close"></a>
                            </span>
                            <span class="selected"
                                >北京
                                <a href="" class="close"></a>
                            </span>
                            <span class="selected"
                                >学术领军人才
                                <a href="" class="close"></a>
                            </span>
                            <span class="selected"
                                >博士研究生
                                <a href="" class="close"></a>
                            </span>
                        </div>
                        <a href="" class="filter-empty">清空筛选条件</a>
                    </div>
                </div>
            </div>

            <div class="job-container">
                <div class="job-content">
                    <div class="job-empty">
                        <div class="empty">暂无相关职位，请修改筛选条件试试</div>
                        <div class="empty-bottom">
                            <div class="recommend-title">更多职位推荐</div>
                            <a class="more" href="/job">查看更多></a>
                        </div>
                    </div>

                    <ul class="job-list">
                        <li class="job-card">
                            <div class="job-body">
                                <div class="job-detail">
                                    <a class="job-name" href="/job.html" target="_blank">
                                        湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院---湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院
                                    </a>
                                    <div class="job-basic">
                                        <span class="job-salary">15K-25K/月</span>
                                        <span class="job-info">江苏-苏州 | 博士研究生 | 招3人</span>
                                        <div class="job-welfare">
                                            <span class="tag">带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假</span>
                                            <span class="tag">五险一金</span>
                                            <span class="tag">节日福利</span>
                                        </div>
                                    </div>
                                    <a class="job-announcement" target="_blank" href="">
                                        中国科学院分子细胞科学卓越创新中心邹卫国研究组2022年招聘博士后/副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副</a
                                    >
                                </div>
                                <button class="job-button job-apply" data-id="12">申请职位</button>
                                <!-- <button class="job-button job-applied" disabled>已申请</button>
                            <button class="job-button job-offline" disabled>已下线</button> -->
                            </div>
                            <div class="job-footer">
                                <div class="company-basic">
                                    <a class="company-name" href="" target="_blank">中国科学院上海微系统与信息技科学院上海信息技科学院中国科学院上海微系统与信息技科学院上海信息技科学院</a>
                                    <div class="company-info">科研院所 | 公立（国有）</div>
                                </div>
                                <div class="release-date">09-07发布</div>
                            </div>
                        </li>
                        <li class="job-card">
                            <div class="job-body">
                                <div class="job-detail">
                                    <a class="job-name" href="" target="_blank">
                                        湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院---湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院
                                    </a>
                                    <div class="job-basic">
                                        <span class="job-salary">15K-25K/月</span>
                                        <span class="job-info">江苏-苏州 | 博士研究生 | 招3人</span>
                                        <div class="job-welfare">
                                            <span class="tag">带薪年假</span>
                                            <span class="tag">五险一金</span>
                                            <span class="tag">节日福利</span>
                                        </div>
                                    </div>
                                    <!-- <a class="job-announcement" target="_blank" href="">
                                    中国科学院分子细胞科学卓越创新中心邹卫国研究组2022年招聘博士后/副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副</a> -->
                                </div>
                                <button class="job-button job-apply" data-id="12">申请职位</button>
                            </div>
                            <div class="job-footer">
                                <div class="company-basic">
                                    <a class="company-name" href="" target="_blank">中国科学院上海微系统与信息技科学院上海信息技科学院中国科学院上海微系统与信息技科学院上海信息技科学院</a>
                                    <div class="company-info">科研院所 | 公立（国有）</div>
                                </div>
                                <div class="release-date">09-07发布</div>
                            </div>
                        </li>
                        <li class="job-card">
                            <div class="job-body">
                                <div class="job-detail">
                                    <a class="job-name" href="" target="_blank">
                                        湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院---湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院
                                    </a>
                                    <div class="job-basic">
                                        <span class="job-salary">15K-25K/月</span>
                                        <span class="job-info">江苏-苏州 | 博士研究生 | 招3人</span>
                                        <div class="job-welfare">
                                            <span class="tag">带薪年假</span>
                                            <span class="tag">五险一金</span>
                                            <span class="tag">节日福利</span>
                                        </div>
                                    </div>
                                    <a class="job-announcement" target="_blank" href="">
                                        中国科学院分子细胞科学卓越创新中心邹卫国研究组2022年招聘博士后/副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副</a
                                    >
                                </div>
                                <button class="job-button job-apply" data-id="12">申请职位</button>
                            </div>
                            <div class="job-footer">
                                <div class="company-basic">
                                    <a class="company-name" href="" target="_blank">中国科学院上海微系统与信息技科学院上海信息技科学院中国科学院上海微系统与信息技科学院上海信息技科学院</a>
                                    <div class="company-info">科研院所 | 公立（国有）</div>
                                </div>
                                <div class="release-date">09-07发布</div>
                            </div>
                        </li>
                    </ul>

                    <div class="pagination">
                        <a href="" class="btn-prev"></a>
                        <a href="" class="number">1</a>
                        <a class="el-icon more btn-quickprev el-icon-more" href=""></a>
                        <a href="" class="number">4</a><a href="" class="number">5</a>
                        <a href="javascript:;" class="number current">6</a>
                        <a href="" class="number">7</a><a href="" class="number">8</a>
                        <a class="el-icon more btn-quicknext el-icon-more" href=""></a>
                        <a href="" class="number">19</a>
                        <a href="" class="btn-next disabled"></a>
                    </div>
                    <a class="view-more-job" href="/job">查看更多</a>
                </div>

                <aside class="aside-content">
                    <!-- 登录及登录引导模块 start -->
                    <!-- guideCard.html -->
                    <!-- loginAside.html -->
                    <!-- 登录及登录引导模块 end -->

                    <div class="qr-code-content">
                        <img class="qr-code" src="//img.gaoxiaojob.com/uploads/static/image/logo/wx/service_qrcode.jpg" alt="" />
                        <div class="tips">
                            <div class="tips-title">实时接收求职反馈</div>
                            <div class="scan">请使用微信【扫一扫】<span>关注服务号绑定</span></div>
                        </div>
                    </div>

                    <div class="advert-vip" v-if="!isLogin || !isVip" @click="openVip">
                        <img src="assets/vip/advert-vip.png" alt="" />
                    </div>

                    <a class="collaborate-content" href="/member/company/applyCooperation" target="_blank">
                        <div>
                            <div class="apply-title">单位合作申请</div>
                            <div class="tips">享受更多定制化招聘服务</div>
                        </div>
                        <span class="apply-btn">去提交</span>
                    </a>

                    <div class="ad-content">
                        <a href="" target="_blank">
                            <img src="http://img.gaoxiaojob.com/xbwyC1_18.jpg" alt="" />
                        </a>
                        <a href="" target="_blank">
                            <img src="http://img.gaoxiaojob.com/xbwyC1_18.jpg" alt="" />
                        </a>
                    </div>
                    <div class="job-recommendation">
                        <div class="title">
                            <h5>相似职位推荐</h5>
                        </div>
                        <div>
                            <div class="element">
                                <a href="/" target="_blank">
                                    <div class="position">
                                        <span class="job-name" title="职位名称*********">职位名称职位名称职位名称职位名称</span>
                                    </div>
                                    <div class="required"><span>本科</span><span class="tag">|</span><span>10人</span><span class="tag">|</span><span class="time">02-11发布</span></div>
                                    <div class="introduce">
                                        <span class="company" title="单位名称">单位名称单位名称</span>
                                        <span class="area">呼和浩特</span>
                                    </div>
                                </a>
                            </div>
                            <div class="element">
                                <a href="/" target="_blank">
                                    <div class="position">
                                        <span class="job-name" title="职位名称*********">职位名称职位名称职位名名称职位名名称职位名称职位名称</span>
                                    </div>
                                    <div class="required"><span>本科</span><span class="tag">|</span><span>10人</span><span class="tag">|</span><span class="time">02-11发布</span></div>
                                    <div class="introduce">
                                        <span class="company" title="单位名称">单位名称单位名称</span>
                                        <span class="area">呼和浩特</span>
                                    </div>
                                </a>
                            </div>
                            <div class="element">
                                <a href="/" target="_blank">
                                    <div class="position">
                                        <span class="job-name" title="职位名称*********">职位名称职位名称职位名称职位名称</span>
                                    </div>
                                    <div class="required"><span>本科</span><span class="tag">|</span><span>10人</span><span class="tag">|</span><span class="time">02-11发布</span></div>
                                    <div class="introduce">
                                        <span class="company" title="单位名称">单位名称位名位名位名单位名称</span>
                                        <span class="area">呼和浩特</span>
                                    </div>
                                </a>
                            </div>
                            <div class="element">
                                <a href="/" target="_blank">
                                    <div class="position">
                                        <span class="job-name" title="职位名称*********">职位名称职位名称职位名称职位名称</span>
                                    </div>
                                    <div class="required"><span>本科</span><span class="tag">|</span><span>10人</span><span class="tag">|</span><span class="time">02-11发布</span></div>
                                    <div class="introduce">
                                        <span class="company" title="单位名称">单位名称单名称单名称单名称单名称单位名称</span>
                                        <span class="area">呼和浩特</span>
                                    </div>
                                </a>
                            </div>
                        </div>
                        <div class="more-job">更多<a href="/" target="_blank" title="1212121">职位名称职位名称职位名称职位名称</a>职位推荐</div>
                    </div>
                </aside>
            </div>

            <div class="position-container">
                <div class="position-content">
                    当前位置：
                    <a href="">高校人才网</a> >
                    <span>职位中心</span>
                </div>
            </div>

            <div class="footer-link-container">
                <div class="footer-link-content">
                    <div class="footer-link-for-spider">
                        <div class="tab-nav">
                            <span class="is-active">热门岗位</span>
                            <span>附近职位</span>
                            <span>相似职位</span>
                            <span>热门搜索</span>
                            <span>职位百科</span>
                        </div>

                        <div class="tab-pane">
                            <div class="pane is-active">
                                <a href="" target="_blank">热门地区</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">热门学科</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">相似职位</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">热门搜索</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">职位百科</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>
                        </div>
                    </div>

                    <script>
                        $(function () {
                            $linkForSpider = $('.footer-link-for-spider')
                            $nav = $linkForSpider.find('.tab-nav span')
                            $pane = $linkForSpider.find('.tab-pane .pane')
                            activeClassName = 'is-active'

                            $nav.on('click', function () {
                                $(this).addClass(activeClassName).siblings().removeClass(activeClassName)
                                $pane.eq($(this).index()).addClass(activeClassName).siblings().removeClass(activeClassName)
                            })
                        })
                    </script>
                </div>
            </div>
        </div>

        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                <a href="/" target="_blank">资质证明</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                    <a href="/" target="_blank">粤ICP备13048400号-2</a>
                    粤公网安备：
                    <a href="/" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="/" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <!-- <script src="./js/pagination.js"></script> -->
        <script>
            $(function () {
                var searchParams = {
                    areaId: 1,
                    majorId: 256
                }
                var $filterTag = $('.filter-pane .filter-item[sub-key]')

                $filterTag.click(function () {
                    $(this).addClass('active').siblings().removeClass('active')
                    var key = $(this).attr('sub-key')
                    var $filterSub = $(this).parents('.filter-pane').find(`.filter-sub[sup-key=${key}]`)
                    $filterSub.addClass('active').siblings().removeClass('active')
                })

                /**
                 * select选择器、cascader选择器  start
                 */
                var $filterSelect = $('.filter-select, .filter-cascade')
                $filterSelect
                    .mouseenter(function () {
                        $(this).find('.filter-select-dropdown > .filter-select-tree').nextAll().remove()
                        $(this).addClass('open')
                    })
                    .mouseleave(function () {
                        $(this).removeClass('open')
                    })

                $('html body').on('click', '.filter-cascade li', function (e) {
                    e.stopPropagation()
                    var subHTML = $(this).find('.filter-select-tree').html()
                    var $targetParent = $(this).parents('.filter-select-tree')

                    $targetParent.nextAll().remove()
                    if (subHTML) {
                        $(this).addClass('active').siblings().removeClass('active')
                        $targetParent.after(`<div class="filter-select-tree">${subHTML}</div>`)
                    }
                })
                /**
                 * select选择器、cascader选择器  end
                 */

                // 分页
                var currentPage = 6
                $('.pagination').createPage({
                    total: 380,
                    pageSize: 20,
                    current: parseInt(currentPage)
                })

                // 工作地点折叠/展开
                $('html body').on('click', '.area-more', function () {
                    const $this = $(this)
                    const $parent = $this.parent()

                    $this.toggleClass('is-reverse')
                    $parent.toggleClass('is-show')
                })

                // 搜索
                $('html body').on('click', '.search-button', function () {
                    const kw = $('.search-kw').val()
                    let params = Object.keys(searchParams).reduce((pre, cur) => {
                        return `${pre}&${cur}=${searchParams[cur]}`
                    }, '')
                    window.location.href = `/job?keyword=${kw}${params}`
                })

                // 申请职位
                $('html body').on('click', '.job-apply', function () {
                    var $this = $(this)
                    var jobId = $this.attr('data-id')
                    window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                        $this.prop('disabled', true).addClass('job-applied').text('已申请')
                    })
                })

                function handleShowLogin() {
                    window.globalComponents.loginDialogComponent.showLoginDialog()
                }

                function openVip() {
                    const { isLogin, isVip } = this
                    console.log(isLogin, 'isLogin', isVip)
                    if (!isLogin) {
                        this.handleShowLogin()
                        return false
                    } else {
                        window.open('/vip.html', '_blank')
                    }
                }
            })
        </script>
    </body>
</html>
