<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>级联选择组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/cascaderDialog.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
    </head>

    <body>
        <div id="cascaderDialogTemplate" class="cascader-dialog-template" v-cloak>
            <el-button class="el-button" type="primary">确定</el-button>

            <el-dialog v-model="visible" :close-on-click-modal="false" custom-class="dialog-center" @close="handleClose">
                <template #title>
                    <div class="cascader-header">
                        <div class="title">{{ title }}</div>
                        <div class="tips">(最多选择<span>{{ multipleLimit }}</span>项)</div>
                        <div class="search">
                            <el-select v-model="searchValue" :remote-method="handleSearch" filterable remote reserve-keyword :size="size" placeholder="请输入关键词" @change="handleSearchChange">
                                <el-option v-for="item in searchOptions" :key="item.k" :label="item.v" :value="item.k" />
                            </el-select>
                        </div>
                    </div>
                </template>
                <div class="cascader-content">
                    <div v-show="!!valueItems.length" class="select">
                        <div class="select-label">已选：</div>
                        <div class="select-value">
                            <el-tag v-for="item in valueItems" :key="item.k" @close="handleRemove(item)" class="tag" closable>
                                <span class="el-tag__content">{{ item.v }}</span>
                            </el-tag>
                        </div>
                    </div>
                    <div class="data-content">
                        <div class="left-content">
                            <a
                                @click="handleClickFirst(index)"
                                v-for="(item, index) in options"
                                :key="item.k"
                                href="JavaScript:;"
                                class="list"
                                :class="{
                active: selectFirstIndex === index,
                'has-select': handleFirstClass(item.k)
              }"
                                >{{ item.v }}</a
                            >
                        </div>
                        <div class="right-content">
                            <div v-for="(first, first_index) in options" :key="first_index" class="second-content" :class="{ show: selectFirstIndex == first_index }">
                                <div class="second-list-content column-4">
                                    <div v-for="second in first.children" :key="second.k" class="second-item only">
                                        <label>
                                            <input
                                                @input="
                    (e) => {
                        handleSelect(e, second)
                    }
                    "
                                                class="btn-select"
                                                name="value"
                                                :value="second.k"
                                                v-model="value"
                                                type="checkbox"
                                            />
                                            <span>{{ second.v }}</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="multiple" class="cascader-footer">
                        <el-button @click="submit" class="submit" type="primary" :size="size">确定</el-button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <script>
            $(function () {
                const cascaderDialogOptions = {
                    data() {
                        return {
                            multiple: true,
                            multipleLimit: 2,
                            title: '请选择职位类型',

                            size: 'small',

                            visible: false,

                            realValue: '',

                            // 选中的值
                            value: [],
                            valueItems: [],
                            valueText: '',

                            searchValue: '',

                            // 一级索引
                            selectFirstIndex: 0,

                            // 真实一级标识
                            firstMarkKey: [],

                            // 真实二级标识
                            secondMarkKey: [],

                            // 数据
                            options: [
                                {
                                    k: '1',
                                    code: '1',
                                    v: '正高级职称',
                                    parentId: '0',
                                    level: '1',
                                    children: [
                                        {
                                            k: '5',
                                            code: '5',
                                            v: '教授',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '6',
                                            code: '6',
                                            v: '正高级讲师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '7',
                                            code: '7',
                                            v: '正高级实习指导教师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '8',
                                            code: '8',
                                            v: '正高级教师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '9',
                                            code: '9',
                                            v: '研究员',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '10',
                                            code: '10',
                                            v: '正高级工程师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '11',
                                            code: '11',
                                            v: '正高级实验师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '12',
                                            code: '12',
                                            v: '教授级高级农艺师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '13',
                                            code: '13',
                                            v: '教授级高级兽医师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '14',
                                            code: '14',
                                            v: '教授级高级畜牧师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '15',
                                            code: '15',
                                            v: '主任医师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '16',
                                            code: '16',
                                            v: '主任药师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '17',
                                            code: '17',
                                            v: '主任护师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '18',
                                            code: '18',
                                            v: '主任技师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '19',
                                            code: '19',
                                            v: '正高级经济师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '20',
                                            code: '20',
                                            v: '正高级会计师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '21',
                                            code: '21',
                                            v: '正高级审计师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '22',
                                            code: '22',
                                            v: '正高级统计师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '23',
                                            code: '23',
                                            v: '高级记者',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '24',
                                            code: '24',
                                            v: '高级编辑',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '25',
                                            code: '25',
                                            v: '编审',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '26',
                                            code: '26',
                                            v: '研究馆员',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '27',
                                            code: '27',
                                            v: '播音指导',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '28',
                                            code: '28',
                                            v: '译审',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '29',
                                            code: '29',
                                            v: '一级律师',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '30',
                                            code: '30',
                                            v: '一级公证员',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '31',
                                            code: '31',
                                            v: '其他正高级职称',
                                            parentId: '268',
                                            level: '2',
                                            topParentId: '1'
                                        }
                                    ]
                                },
                                {
                                    k: '2',
                                    code: '2',
                                    v: '副高级职称',
                                    parentId: '0',
                                    level: '1',
                                    children: [
                                        {
                                            k: '32',
                                            code: '32',
                                            v: '副教授',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '33',
                                            code: '33',
                                            v: '高级讲师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '34',
                                            code: '34',
                                            v: '高级实习指导教师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '35',
                                            code: '35',
                                            v: '高级教师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '36',
                                            code: '36',
                                            v: '副研究员',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '37',
                                            code: '37',
                                            v: '高级工程师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '38',
                                            code: '38',
                                            v: '高级实验师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '39',
                                            code: '39',
                                            v: '高级农艺师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '40',
                                            code: '40',
                                            v: '高级兽医师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '41',
                                            code: '41',
                                            v: '高级畜牧师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '42',
                                            code: '42',
                                            v: '副主任医师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '43',
                                            code: '43',
                                            v: '副主任药师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '44',
                                            code: '44',
                                            v: '副主任护师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '45',
                                            code: '45',
                                            v: '副主任技师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '46',
                                            code: '46',
                                            v: '高级经济师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '47',
                                            code: '47',
                                            v: '高级会计师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '48',
                                            code: '48',
                                            v: '高级审计师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '49',
                                            code: '49',
                                            v: '高级统计师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '50',
                                            code: '50',
                                            v: '主任记者',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '51',
                                            code: '51',
                                            v: '主任编辑',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '52',
                                            code: '52',
                                            v: '副编审',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '53',
                                            code: '53',
                                            v: '副研究馆员',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '54',
                                            code: '54',
                                            v: '主任播音员',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '55',
                                            code: '55',
                                            v: '副译审',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '56',
                                            code: '56',
                                            v: '二级律师',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '57',
                                            code: '57',
                                            v: '二级公证员',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '58',
                                            code: '58',
                                            v: '其他副高级职称',
                                            parentId: '269',
                                            level: '2',
                                            topParentId: '2'
                                        }
                                    ]
                                },
                                {
                                    k: '3',
                                    code: '3',
                                    v: '中级职称',
                                    parentId: '0',
                                    level: '1',
                                    children: [
                                        {
                                            k: '59',
                                            code: '59',
                                            v: '讲师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '60',
                                            code: '60',
                                            v: '一级实习指导教师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '61',
                                            code: '61',
                                            v: '一级教师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '62',
                                            code: '62',
                                            v: '助理研究员',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '63',
                                            code: '63',
                                            v: '工程师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '64',
                                            code: '64',
                                            v: '实验师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '65',
                                            code: '65',
                                            v: '农艺师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '66',
                                            code: '66',
                                            v: '兽医师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '67',
                                            code: '67',
                                            v: '畜牧师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '68',
                                            code: '68',
                                            v: '主治医师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '69',
                                            code: '69',
                                            v: '主管药师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '70',
                                            code: '70',
                                            v: '主管护师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '71',
                                            code: '71',
                                            v: '主管技师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '72',
                                            code: '72',
                                            v: '经济师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '73',
                                            code: '73',
                                            v: '中级会计师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '74',
                                            code: '74',
                                            v: '审计师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '75',
                                            code: '75',
                                            v: '统计师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '76',
                                            code: '76',
                                            v: '记者',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '77',
                                            code: '77',
                                            v: '编辑',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '78',
                                            code: '78',
                                            v: '技术编辑',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '79',
                                            code: '79',
                                            v: '一级校对',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '80',
                                            code: '80',
                                            v: '馆员',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '81',
                                            code: '81',
                                            v: '一级播音员',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '82',
                                            code: '82',
                                            v: '翻译',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '83',
                                            code: '83',
                                            v: '三级律师',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '84',
                                            code: '84',
                                            v: '三级公证员',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '85',
                                            code: '85',
                                            v: '其他中级职称',
                                            parentId: '270',
                                            level: '2',
                                            topParentId: '3'
                                        }
                                    ]
                                },
                                {
                                    k: '4',
                                    code: '4',
                                    v: '初级职称',
                                    parentId: '0',
                                    level: '1',
                                    children: [
                                        {
                                            k: '86',
                                            code: '86',
                                            v: '助教',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '87',
                                            code: '87',
                                            v: '助理讲师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '88',
                                            code: '88',
                                            v: '二级实习指导教师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '89',
                                            code: '89',
                                            v: '二级教师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '90',
                                            code: '90',
                                            v: '研究实习员',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '91',
                                            code: '91',
                                            v: '助理工程师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '92',
                                            code: '92',
                                            v: '助理实验师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '93',
                                            code: '93',
                                            v: '助理农艺师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '94',
                                            code: '94',
                                            v: '助理兽医师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '95',
                                            code: '95',
                                            v: '助理畜牧师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '96',
                                            code: '96',
                                            v: '住院医师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '97',
                                            code: '97',
                                            v: '药师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '98',
                                            code: '98',
                                            v: '护师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '99',
                                            code: '99',
                                            v: '技师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '100',
                                            code: '100',
                                            v: '助理经济师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '101',
                                            code: '101',
                                            v: '助理会计师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '102',
                                            code: '102',
                                            v: '助理审计师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '103',
                                            code: '103',
                                            v: '助理统计师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '104',
                                            code: '104',
                                            v: '助理记者',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '105',
                                            code: '105',
                                            v: '助理编辑',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '106',
                                            code: '106',
                                            v: '助理技术编辑',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '107',
                                            code: '107',
                                            v: '二级校对',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '108',
                                            code: '108',
                                            v: '助理馆员',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '109',
                                            code: '109',
                                            v: '二级播音员',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '110',
                                            code: '110',
                                            v: '助理翻译',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '111',
                                            code: '111',
                                            v: '四级律师',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '112',
                                            code: '112',
                                            v: '四级公证员',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '113',
                                            code: '113',
                                            v: '其他初级职称',
                                            parentId: '271',
                                            level: '2',
                                            topParentId: '4'
                                        }
                                    ]
                                }
                            ],
                            // 最后子级，用于搜索
                            lastChildrenOptions: [],

                            // 搜索数据
                            searchLoading: false,
                            searchOptions: [],

                            successCallback: null
                        }
                    },

                    computed: {},

                    watch: {
                        value: {
                            handler(newValue) {
                                this.recursive()
                            },
                            deep: true
                        }
                    },

                    mounted() {
                        this.getLastChildren()
                    },

                    methods: {
                        async open(value, callback = () => {}) {
                            let newValue
                            if (Array.isArray(value)) {
                                newValue = value
                            } else {
                                newValue = value ? String(value).split(',') : []
                            }
                            this.realValue = newValue
                            this.value = newValue
                            this.recursive('init')
                            this.successCallback = callback
                            this.visible = true
                        },
                        getValueText() {
                            this.valueText = this.valueItems.map((item) => item.v).join()
                        },
                        initOpen(firstActiveIndex, secondOpenkey) {
                            this.selectFirstIndex = firstActiveIndex
                            this.secondOpenkey = secondOpenkey
                        },

                        // 获取父级标识
                        getParentMark(data) {
                            const { parentId, topParentId } = data
                            this.secondMarkKey.push(parentId)
                            this.firstMarkKey.push(topParentId)
                        },

                        // 清除父级标识
                        clearParentMark() {
                            this.firstMarkKey = []
                            this.secondMarkKey = []
                        },

                        handleSecondClass(k) {
                            const has = this.secondMarkKey.includes(k)
                            return has
                        },

                        handleFirstClass(k) {
                            const has = this.firstMarkKey.includes(k)
                            return has
                        },
                        // 已选去重, 例如城市-热门城市包括城市北京，北京-北京
                        getValueItems(arr) {
                            const newArr = []
                            const obj = {}
                            const { length } = arr
                            for (let i = 0; i < length; i += 1) {
                                const item = arr[i]
                                const { k } = item
                                if (!obj[k]) {
                                    newArr.push(item)
                                    obj[k] = true
                                }
                            }
                            this.valueItems = newArr
                        },

                        // 递归
                        recursive(type) {
                            const { length: optionsLength } = this.options
                            if (!optionsLength) return

                            const valueItems = []
                            const { value, options } = this

                            this.clearParentMark()

                            let isFirst = false
                            options.map((data, index) => {
                                const arrayMap = (array, parent) => {
                                    const { length } = array
                                    for (let i = 0; i < length; i += 1) {
                                        const item = array[i]
                                        const { k, children } = item
                                        if (value.includes(k)) {
                                            valueItems.push(item)
                                            if (!isFirst && type === 'init') {
                                                isFirst = true
                                                this.initOpen(index, parent.k)
                                            }
                                            this.getParentMark(item)
                                        }
                                        if (Array.isArray(children) && children.length) {
                                            arrayMap(children, item)
                                        }
                                    }
                                }
                                const { length: childrenLength = 0 } = data.children
                                if (childrenLength) {
                                    arrayMap(data.children, data)
                                }
                            })

                            this.getValueItems(valueItems)
                            this.getValueText()
                        },

                        getLastChildren() {
                            const {
                                options,
                                options: { length: optionsLength }
                            } = this
                            if (!optionsLength) return

                            const lastChildren = []
                            const arrayMap = (array) => {
                                const { length } = array
                                for (let i = 0; i < length; i += 1) {
                                    const item = array[i]
                                    const { children } = item

                                    if (Array.isArray(children) && children.length) {
                                        arrayMap(children)
                                    } else {
                                        lastChildren.push(item)
                                    }
                                }
                            }
                            arrayMap(options)
                            this.lastChildrenOptions = lastChildren
                        },

                        // 点击一级
                        handleClickFirst(index) {
                            this.selectFirstIndex = index
                        },

                        handleRemove(data) {
                            const { valueItems, value } = this
                            const { k } = data
                            this.valueItems = valueItems.filter((item) => item.k !== k)
                            this.value = value.filter((item) => item !== k)
                        },

                        handleChangeModelValue(value) {
                            const { multiple } = this
                            if (multiple) {
                                this.successCallback(value, this.valueItems)
                            } else {
                                const valueItems = this.lastChildrenOptions.filter((item) => item.k === value)
                                this.successCallback(value, valueItems)
                            }
                            this.visible = false
                        },

                        // 处理筛选数据
                        handleSearch(query) {
                            if (query) {
                                this.searchLoading = true
                                const { lastChildrenOptions } = this
                                setTimeout(() => {
                                    const objMap = new Map()
                                    this.searchOptions = lastChildrenOptions.filter((item) => {
                                        const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                        const { v } = item
                                        const flag = isIncludes && !objMap.has(v)
                                        if (flag) objMap.set(v, 1)
                                        return flag
                                    })
                                    this.searchLoading = false
                                }, 200)
                            } else {
                                this.searchOptions = []
                            }
                        },

                        handleSearchChange(k) {
                            const { multiple } = this

                            this.searchOptions = []
                            this.searchValue = ''

                            if (!multiple) {
                                this.handleChangeModelValue(k)
                            } else {
                                const {
                                    value,
                                    value: { length }
                                } = this
                                const { multipleLimit } = this
                                if (length >= multipleLimit) {
                                    this.$message.error(`最多选择${multipleLimit}项`)
                                    return
                                }
                                const contain = value.includes(k)
                                if (!contain) {
                                    this.value.push(k)
                                }
                            }
                        },

                        // 选中最后一级
                        handleSelect(e) {
                            const {
                                target: { value, checked }
                            } = e
                            const { multiple, multipleLimit } = this

                            // 单选
                            if (!multiple) {
                                this.handleChangeModelValue(value)
                            } else {
                                const { length } = this.value
                                if (checked && multipleLimit <= length) {
                                    this.$message.error(`最多选择${multipleLimit}项`)
                                    this.value = this.value.filter((k) => k !== value)
                                }
                            }
                        },

                        submit() {
                            const { value } = this
                            this.handleChangeModelValue(value)
                        },

                        // 关闭弹窗，还原真实值
                        handleClose() {
                            this.value = this.realValue
                        },

                        // 清空
                        handleClear() {
                            const { multiple } = this.props
                            const val = multiple ? [] : ''
                            this.handleChangeModelValue(val)
                        }
                    }
                }
                const cascaderDialog = Vue.createApp(cascaderDialogOptions).use(ElementPlus).mount('#cascaderDialogTemplate')

                window.globalComponents = { ...window.globalComponents, cascaderDialog }
            })
        </script>

        <script>
            $(function () {
                var realValue = ''
                $('.el-button').on('click', function () {
                    var $btn = $(this)
                    window.globalComponents.cascaderDialog.open(realValue, function (value, array) {
                        realValue = value
                        console.log('array:', array)
                    })
                })
            })
        </script>
    </body>
</html>
