<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>公共弹窗组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/checkGenerateChatDialog.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script type="text/javascript" src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
    </head>

    <body>
        <div id="checkGenerateChatDialog" class="chat-dialog-alert-template" v-cloak>
            <el-dialog v-model="dialogVisible" :close-on-click-modal="false">
                <div class="wechat-container">
                    <div class="checktips">{{ title }}</div>
                    <div class="tips-text">
                        <div class="text-right">
                            <div class="tips-1" v-html="tips1"></div>
                            <div class="tips-2" v-html="tips2"></div>
                        </div>
                    </div>
                    <div class="el-message-box__btns">
                        <button class="el-button el-button--default el-button--large change" type="button" @click="handelDialogClose">
                            <span>{{cancelBtnTxt}}</span>
                        </button>
                        <button v-if="confirmBtnTxt" class="el-button el-button--default el-button--large el-button--primary" type="button" @click="confirm()">
                            <span>{{confirmBtnTxt}}</span>
                        </button>
                    </div>
                </div>
            </el-dialog>
        </div>

        <script>
            $(function () {
                const PromptDialogOptions = {
                    data() {
                        return {
                            dialogVisible: false,
                            title: '',
                            tips1: '<span class="font-basic">已向对方发送消息！可至【我的直聊】继续沟通～</span>',
                            tips2: '<span>如需修改打招呼语，请在【我的直聊 - 打招呼语设置】修改</span>',
                            // tips1: '<span>已向对方发送消息！可至【我的直聊】继续沟通～</span>',
                            // tips2: '<span class="font-basic">如需修改打招呼语，请在【我的直聊 - 打招呼语设置】修改</span>',
                            jumpUrl: '',
                            openTarget: '_self',
                            confirmBtnTxt: '',
                            cancelBtnTxt: '',
                            params: ''
                        }
                    },

                    methods: {
                        create(jobId, callback = () => {}) {
                            var _this = this
                            httpPost('/api/person/chat/create-room', { jobId }).then(function (data) {
                                const { title, jumpUrl, confirmBtnTxt, cancelBtnTxt, tips1, tips2, chatId, isTips, target = '_self' } = data

                                _this.jumpUrl = jumpUrl
                                _this.openTarget = target

                                callback(data)

                                if (isTips) {
                                    _this.title = title
                                    _this.tips1 = tips1
                                    _this.tips2 = tips2
                                    _this.confirmBtnTxt = confirmBtnTxt
                                    _this.cancelBtnTxt = cancelBtnTxt
                                    _this.dialogVisible = true
                                    return
                                }

                                window.open(jumpUrl, target)
                            })
                        },
                        confirm() {
                            window.open(this.jumpUrl, this.openTarget)
                            this.dialogVisible = false
                        },
                        handelDialogClose() {
                            this.dialogVisible = false
                        }
                    },
                    mounted() {}
                }

                const ChatDialogComponent = Vue.createApp(PromptDialogOptions).use(ElementPlus).mount('#checkGenerateChatDialog')

                window.globalComponents = {
                    ...window.globalComponents,
                    ChatDialogComponent
                }

                ChatDialogComponent.create(314536)
            })
        </script>
    </body>
</html>
