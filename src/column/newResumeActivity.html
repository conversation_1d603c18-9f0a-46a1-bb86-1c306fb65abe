<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>拉新活动</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <div id="newResumeActivityTemplate" class="new-resume-activity-template" v-cloak>
            <div class="nav-free-vip-enter" @click="fetchShareInfo">
                <div class="nav-free-vip-header">
                    <div class="title">免费薅羊毛</div>
                    <div class="regulate" @click.stop="showRegulate">活动规则</div>
                </div>
                <div class="nav-free-vip-footer">点击<span class="invite">立即邀请>></span>,扫码获取专属分享海报</div>
            </div>

            <el-dialog custom-class="free-vip-share-dialog" append-to-body v-model="shareDialogVisible">
                <div class="main-container">
                    <div class="title">扫码获取专属分享海报</div>
                    <img class="qr-code" :src="qrCodeUrl" alt="" />
                    <div class="step">
                        <div class="item scan">扫描二维码</div>
                        <div class="item save">保存海报</div>
                        <div class="item send">发送给好友</div>
                    </div>
                </div>
            </el-dialog>

            <el-dialog custom-class="free-vip-regulate-dialog" append-to-body v-model="regulateDialogVisible">
                <div class="main-container">
                    <div class="title">规则说明</div>

                    <div class="wrapper">
                        <div class="name">活动内容</div>
                        <div class="content">向好友发送分享海报/分享链接，邀请好友注册并完善简历，双方均可获得黄金VIP会员 * 3天。</div>
                    </div>

                    <div class="wrapper">
                        <div class="name">邀请流程</div>
                        <div class="content">
                            1、点击“<span class="special">立即邀请>></span>”，扫描二维码获得专属邀请海报，分享给硕博好友。 <br />
                            2、好友在分享页面正确填写手机号完成注册，并于注册后<span class="special">7日内</span>登录该账号，完善在线简历至65%或以上，即视为邀请成功。<br />
                            *
                            若好友注册后，未于7日内将简历完整度完善至65%或以上，则视为邀请不成功。用户可在手机移动端网页&高才优聘小程序：【我的】-【免费领VIP会员】-【我的成就】模块查看好友邀请进度。<br />
                            3、每成功邀请一位好友，邀请方及被邀请方均可获得高校人才网黄金VIP会员*3天权益。<br />
                            4、邀请成功，奖励会于当天24:00前发放。奖励发放情况可在手机移动端网页&高才优聘小程序：【我的】-【免费领VIP会员】-【我的成就】模块查看。<br />
                            5、若发放奖励时，用户有生效中的钻石VIP套餐，奖励将发放失败，请务必知悉。<br />
                            6、同一用户最多可获取15次奖励，累计45天。超过上限后，邀请成功不再发放相应奖励。<br />
                            <span class="bold">7、本活动最终解释权归高校人才网所有。</span>
                        </div>
                    </div>
                </div>
            </el-dialog>
        </div>

        <script>
            $(function () {
                const newResumeActivityOptions = {
                    data() {
                        return {
                            shareDialogVisible: false,
                            regulateDialogVisible: false,
                            qrCodeUrl: ''
                        }
                    },

                    methods: {
                        showRegulate() {
                            this.regulateDialogVisible = true
                        },

                        async fetchShareInfo() {
                            const { codeUrl } = await httpGet('/api/person/new-resume/create')

                            this.qrCodeUrl = codeUrl
                            this.shareDialogVisible = true
                        }
                    }
                }

                Vue.createApp(newResumeActivityOptions).use(ElementPlus).mount('#newResumeActivityTemplate')
            })
        </script>
    </body>
</html>
