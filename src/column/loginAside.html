<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>登录组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/loginAside.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script type="text/javascript" src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
        <style>
            /* 效果演示 */
            .login-aside-template {
                width: 320px;
            }
        </style>
    </head>

    <body>
        <!-- 圆角: class="login-aside-template" 直角: class="login-aside-template is-plain"-->
        <div id="loginAsideTemplate" class="login-aside-template" v-cloak>
            <el-form ref="mobileLoginRef" :model="mobileLoginForm" :rules="mobileLoginRules">
                <el-form-item prop="mobile">
                    <el-input
                        class="mobile"
                        v-model="mobileLoginForm.mobile"
                        oninput="value=value.replace(/[^0-9]/g, '')"
                        name="gaoxiaojobMobile"
                        autocomplete="on"
                        placeholder="请输入手机号码"
                        :maxlength="mobileMaxLength"
                    >
                        <template #prefix>
                            <div class="input-prepend">
                                <i class="aside-login-mobile"></i>
                                <el-select v-model="mobileLoginForm.mobileCode" popper-class="aside-mobile-prefix-popper" class="mobile-prefix-select">
                                    <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
                                        <el-option v-for="{ country, code } in list" :key="code" :value="code">
                                            <span style="float: left">{{ country }}</span>
                                            <span style="float: right"> {{ code }} </span>
                                        </el-option>
                                    </el-option-group>
                                </el-select>
                            </div>
                        </template>
                    </el-input>
                </el-form-item>

                <el-form-item prop="code">
                    <el-input v-model="mobileLoginForm.code" class="aside-login-code" placeholder="请输入验证码">
                        <template #prefix>
                            <i class="aside-login-msg"></i>
                        </template>

                        <template #suffix>
                            <el-button type="text" :disabled="codeDisabled" @click="handleSendCode"> {{ codeText }} </el-button>
                        </template>
                    </el-input>
                </el-form-item>

                <el-button class="mobile-login-confirm" type="primary" :loading="mobileLogining" @click="handleLogin"> 登录/注册 </el-button>
            </el-form>
        </div>

        <script>
            $(function () {
                const loginAsideOptions = {
                    data() {
                        function validMobile(mobile) {
                            const reg = /^1[3-9]\d{9}$/
                            return reg.test(mobile)
                        }

                        function validNumber(number) {
                            return /^\d+$/.test(number)
                        }

                        const validateMobile = (rule, value, callback) => {
                            const {
                                mobileLoginForm: { mobileCode }
                            } = this
                            if (mobileCode === '+86') {
                                if (validMobile(value)) {
                                    callback()
                                } else {
                                    callback('请输入正确的手机号码')
                                }
                            } else if (validNumber(value)) {
                                callback()
                            } else {
                                callback('请输入正确的手机号码')
                            }
                        }

                        return {
                            captcha: null,
                            captchaAppId: '',

                            prefixOptions: [],

                            mobileLoginForm: {
                                mobileCode: '+86',
                                mobile: '',
                                code: ''
                            },

                            codeDisabled: false,
                            codeText: '获取验证码',
                            codeTime: 60,
                            codeTimer: null,

                            mobileLogining: false,

                            mobileLoginRules: {
                                mobile: [
                                    { required: true, message: '请输入手机号码', trigger: 'blur' },
                                    { validator: validateMobile, trigger: 'blur' }
                                ],
                                code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
                            }
                        }
                    },

                    computed: {
                        mobileMaxLength: function () {
                            return this.mobileLoginForm.mobileCode === '+86' ? 11 : 20
                        }
                    },

                    methods: {
                        /* 号段 */
                        async getMobilePrefix() {
                            const data = await httpGet('/api/config/load-country-mobile-code')
                            this.prefixOptions = data
                        },

                        async getMobileCode(data) {
                            const {
                                mobileLoginForm: { mobileCode, mobile }
                            } = this
                            await httpPost('/api/member/send-mobile-login-code', {
                                mobileCode,
                                mobile,
                                type: 1,
                                ...data
                            })
                        },

                        async getCaptchaConfig() {
                            const { captchaAppId: appId } = await httpGet('/api/member/get-captcha-config')
                            this.captchaAppId = appId
                        },

                        handleCaptcha(callback) {
                            try {
                                this.captcha = new TencentCaptcha(this.captchaAppId, (res) => {
                                    const { ret, ticket, randstr } = res
                                    if (ret === 0) {
                                        callback && callback({ ticket, randstr })
                                    }
                                })
                                this.captcha.show()
                            } catch (err) {
                                console.log(err)
                            }
                        },

                        countDown() {
                            const handler = () => {
                                if (this.codeTime === 1) {
                                    clearInterval(this.codeTimer)
                                    this.codeTime = 60
                                    this.codeText = '重新发送'
                                    this.codeDisabled = false
                                } else {
                                    this.codeTime -= 1
                                    this.codeText = `${this.codeTime}秒`
                                }
                            }

                            this.codeDisabled = true
                            handler()
                            this.codeTimer = setInterval(() => {
                                handler()
                            }, 1000)
                        },

                        handleSendCode() {
                            //---------------点击发送验证码，数据埋点-------------------
                            let params = {
                                actionType: 3,
                                actionModule: 13
                            }
                            //---------------点击发送验证码，数据埋点-------------------

                            buriedPoint(params)
                            this.$refs.mobileLoginRef.validateField('mobile', (errMsg) => {
                                if (errMsg.length === 0) {
                                    this.handleCaptcha((data) => {
                                        this.getMobileCode(data)
                                        this.countDown()
                                    })
                                }
                            })
                        },

                        async handleSign() {
                            const loginApi = '/api/member/validate-mobile-login-code'
                            const loading = 'mobileLogining'

                            this[loading] = true

                            let formData = { type: 1 }

                            const {
                                mobileLoginForm: { mobileCode, mobile, code }
                            } = this
                            formData = { ...formData, mobileCode, mobile, code }

                            try {
                                //---------------点击提交，数据埋点-------------------
                                let params = {
                                    actionType: 2,
                                    actionModule: 13
                                }
                                buriedPoint(params)
                                //---------------点击发送验证码，数据埋点-------------------

                                const { token, expireTime } = await httpPost(loginApi, formData)
                                setToken(token, expireTime)
                                window.location.reload()
                            } catch (err) {
                                this[loading] = false
                            }
                        },

                        handleLogin() {
                            const formRef = this.$refs['mobileLoginRef']

                            formRef.validate((valid) => {
                                if (valid) {
                                    this.handleSign()
                                }
                            })
                        }
                    },

                    mounted() {
                        /* 海外号段 */
                        this.getMobilePrefix()
                        this.getCaptchaConfig()
                    }
                }
                const loginAsideComponent = Vue.createApp(loginAsideOptions).use(ElementPlus).mount('#loginAsideTemplate')
            })
        </script>
    </body>
</html>
