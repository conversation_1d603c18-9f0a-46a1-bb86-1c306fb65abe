<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>公告详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/noticeTwo.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/public.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main">
                <div class="detail-container">
                    <div class="detail-header">
                        <div class="detail-header-container">
                            <div class="breadcrumb">
                                位置：
                                <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞
                                <a>北京大学珠海分校2021年度招聘公告</a>
                            </div>

                            <div class="main">
                                <div class="detail">
                                    <h1
                                        title="北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学"
                                        class="title"
                                    >
                                        诚邀优秀青年人才依托首都师范大学申报海外优青2024年文本限制最长2行，超出申报海外优青申报海外优青海外优青海...就...
                                    </h1>
                                    <div class="detail-bottom">
                                        <a class="el-button view-button" href=""> 查看此公告的职位列表 </a>
                                        <div class="share-mini-code-container">
                                            <div class="share-mini-code-trigger">分享</div>

                                            <div class="share-mini-code-popup">
                                                <div class="share-mini-code">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                </div>
                                                <div class="share-mini-code-title">微信扫一扫</div>
                                                <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                            </div>
                                        </div>
                                        <div class="el-button--collect">收藏</div>
                                        <div class="el-button--analyse see-heat">公告热度</div>
                                    </div>
                                </div>

                                <a class="company-home" href="" target="_blank">单位主页</a>
                            </div>
                        </div>

                        <div class="wave"></div>

                        <div class="detail-header-container--fixed">
                            <div class="detail">
                                <div class="title" title="北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学">
                                    北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学
                                </div>
                                <div class="bottom">
                                    <span class="tag">部分有编</span>
                                    <div>招<span class="bold">29</span>人，共计<span class="bold">212</span>个岗位</div>
                                    <a class="el-button view-button" href="">查看此公告的职位列表</a>
                                </div>
                            </div>
                            <div class="aside">
                                <div class="detail-button">
                                    <div class="share-mini-code-container">
                                        <div class="share-mini-code-trigger">分享</div>

                                        <div class="share-mini-code-popup">
                                            <div class="share-mini-code">
                                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                            </div>
                                            <div class="share-mini-code-title">微信扫一扫</div>
                                            <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                        </div>
                                    </div>
                                    <div class="el-button--collect">收藏</div>
                                    <div class="el-button--analyse see-heat">公告热度</div>
                                </div>
                                <div class="bottom">
                                    <button class="offline">已下线</button>
                                    <a class="company-home" href="" target="_blank">单位主页</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <div class="info-wrapper">
                            <div class="info-wrapper-content">
                                <div class="item info">
                                    共计<span class="primary bold">&nbsp;4&nbsp;</span>个岗位 | 招<span class="primary bold">&nbsp;29&nbsp;</span>人&nbsp;&nbsp;发布时间：2021-06-25 |
                                    截止时间：详见正文 | 工作地点：深圳、上海
                                </div>

                                <div class="item major">
                                    <div class="major-content">
                                        需求学科（供参考）：
                                        <a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>, <a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>，<a href="/" target="_blank">机械工程</a>
                                    </div>

                                    <div class="major-popup">
                                    机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、
                                    机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、
                                    机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、机械工程、
                                    机械工程、机械工程、
                                    </div>
                                </div>

                                <div class="item welfare">
                                    职位福利：<span class="primary bold">全部有编、</span
                                    >全勤奖、带薪年假、五险一金、绩效奖金、年底双薪、下午茶福利、班车接送、加班补贴、全勤奖、带薪年假、五险一金、绩效奖金、全勤奖、带薪年假、五险一金、绩效奖金、年底双薪、下午茶福利、班车接送、加班补贴、全勤奖、带薪年假、五险一金、绩效奖金、全勤奖、带薪年假、五险一金、绩效奖金、年底双薪、下午茶福利、班车接送、加班补贴、全勤奖、带薪年假、五险一金、绩效奖金、全勤奖、带薪年假、五险一金、绩效奖金、年底双薪、下午茶福利、班车接送、加班补贴、全勤奖、带薪年假、五险一金、绩效奖金
                                </div>
                            </div>
                        </div>

                        <div class="common-wrapper">
                            <div class="common-title">
                                <h2>
                                    学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介学校简介
                                </h2>
                            </div>

                            <div class="common-content">
                                <p>
                                    <span>义乌工商职业技术学院地处国际商贸名城义乌，前身是创办于</span><span>1993</span><span>年的杭州大学义乌分校，占地面积</span><span>1000</span
                                    ><span>余亩，建筑面积</span><span>31.39</span><span>万平方米，自然风光秀丽，建筑风格独特，全日制在校生近</span><span>13000</span
                                    ><span
                                        >名。学校是浙江省高职高水平学校、浙江省优质高职院校、首批浙江省创业型大学建设试点院校，荣登全国创新创业典型经验高校，获评全国高校实践育人创新创业基地、国家级创新创业教育实践基地。现因学校发展需要，面向社会招聘一批博士研究生或具有高级专业技术职称、在行业企业具有重大影响力的高层次人才。根据《浙江省事业单位公开招聘人员暂行办法》</span
                                    ><span>(</span><span>浙人才</span><span>[2007]184</span><span>号</span><span>)</span><span>，现将有关事项公告如下：</span><span></span>
                                </p>
                                <p>
                                    <span
                                        ><img class="rich-img" src="http://img.gaoxiaojob.com/uploads/image/20230120/20230120095052_44466.png" style="max-width: 100%" contenteditable="false"
                                    /></span>
                                </p>
                            </div>
                        </div>

                        <div class="common-wrapper">
                            <div class="common-title">
                                <h2>应聘条件</h2>
                            </div>

                            <div class="common-content">
                                <p>
                                    1.硕士研究生及以上学历，毕业于国内外知名高校，具有留学经历者优先。<br />
                                    2.遵纪守法，具有良好的政治素养和职业道德。<br />
                                    3.热爱教育事业，认同民办高等教育事业。<br />
                                    4.身体健康，具有高度的事业心、责任感及团队合作精神。<br />
                                    5.具有扎实的专业理论知识和创新能力，具有良好的文字和口头表达能力。硕士研究生及以上学历，毕业于国内外知名高校，具有留学经历者优先。硕士研究生及以上学历，毕业于国内外知名高校，具有留学经历者优先。
                                </p>
                            </div>
                        </div>

                        <div class="common-wrapper">
                            <div class="common-title">
                                <h2>招聘专业</h2>
                            </div>

                            <div class="common-content">
                                <p>学校诚邀各类高层次人才加入，主要面向上述11个教学单位相关专业领域，具体岗位及要求详见下方表格。</p>

                                <table>
                                    <tr>
                                        <th>序号</th>
                                        <th>招聘部门</th>
                                        <th>岗位名称</th>
                                        <th>专业要求</th>
                                        <th>学历或职位要求</th>
                                        <th>联系人</th>
                                    </tr>
                                    <tr>
                                        <td>1</td>
                                        <td>机械技术学院</td>
                                        <td>专任教师</td>
                                        <td>机械工程、机电控制类</td>
                                        <td>博士研究生</td>
                                        <td>
                                            王老师<br /><br />
                                            0510-81838711<br /><br />
                                            <EMAIL>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>控制技术学院</td>
                                        <td>专任教师</td>
                                        <td>机电控制、控制工程、软件工程类、电子信息（大类）类</td>
                                        <td>博士研究生、硕士研究生且具有副<br /><br />高职称</td>
                                        <td>
                                            刘老师<br /><br />
                                            0510-81838726<br /><br />
                                            <EMAIL>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>物联网技术学院</td>
                                        <td>专任教师</td>
                                        <td>计算机（打类）类、软件工程类、计算机（网络管理）、物联网工程</td>
                                        <td>博士研究生、硕士研究生且具有副<br /><br />高职称</td>
                                        <td>
                                            蔡老师<br /><br />
                                            0510-81838751<br /><br />
                                            <EMAIL>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>汽车与交通学院</td>
                                        <td>专任教师</td>
                                        <td>车辆工程、交通运输工程、载运工具运通工程、机械工程类、动力工程及工程热<br /><br />物理、物体立学、航空工程、航空宇航科学与技术</td>
                                        <td>博士研究生、硕士研究生且具有副<br /><br />高职称</td>
                                        <td>
                                            邹老师<br /><br />
                                            <EMAIL>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>管理学院</td>
                                        <td>专任老师</td>
                                        <td>工商管理（市场营销、电子商务及相关专业方向）或管理科学与工程（物流管理<br /><br />及相关专业方向）</td>
                                        <td>博士研究生</td>
                                        <td>
                                            俞老师<br /><br />
                                            0510-81838501<br /><br />
                                            <EMAIL>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="common-wrapper">
                            <div class="common-title">
                                <h2>请以以上内容为准</h2>
                            </div>
                        </div>

                        <div class="common-wrapper file-wrapper">
                            <div class="common-title">
                                <h2>附件下载</h2>
                            </div>

                            <div class="file-list">
                                <el-popover placement="bottom" :width="300" trigger="hover" content="word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx">
                                    <template #reference>
                                        <a href="" download="" class="file" target="_blank">
                                            <div class="word"></div>
                                            <div class="file-name">word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx</div>
                                        </a>
                                    </template>
                                </el-popover>
                                <el-popover placement="bottom" :width="300" trigger="hover" content="excel文件名称.xlsx">
                                    <template #reference>
                                        <a href="" download="" class="file" target="_blank">
                                            <div class="excel"></div>
                                            <div class="file-name">excel文件名称.xlsx</div>
                                        </a>
                                    </template>
                                </el-popover>
                                <a href="" download="" class="file" target="_blank">
                                    <div class="pdf"></div>
                                    <div class="file-name">pdf文件名称.pdf</div>
                                </a>

                                <a href="" download="" class="file" target="_blank">
                                    <div class="common"></div>
                                    <div class="file-name">txt文件名称.txt</div>
                                </a>
                            </div>
                        </div>

                        <!-- 未登录 start -->
                        <div>
                            <div class="notice-heat see-heat">
                                <div class="top">
                                    <div class="left">公告热度</div>
                                    <div class="right">解锁详细分析</div>
                                </div>
                                <div class="text-heat">该公告在同类公告中的热度为 <span class="active">火爆</span> ，目前已有 <span class="active">1000人</span> 对其非常感兴趣</div>
                            </div>
                        </div>

                        <!-- 未登录 start -->

                        <!-- 已登录 start -->
                        <div class="notice-heat">
                            <div class="top">
                                <div class="left">公告热度</div>
                                <div class="right">查看详细分析</div>
                            </div>
                            <div class="text-heat">该公告在同类公告中的热度为 <span>火爆</span> ，目前已有 <span>1000人</span> 对其非常感兴趣</div>
                        </div>
                        <!-- 已登录 start -->

                        <div class="detail-emit">
                            <a class="view-job" href=""> 查看此公告的职位列表 </a>
                            <button class="el-button el-button--apply">立即投递</button>

                            <button class="el-button offline" disabled>已下线</button>
                        </div>

                        <!-- <div class="share-custom">
                            <div class="sina-weibo">
                                <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24"> </wb:follow-button>
                            </div>
                            <div>
                                <div class="bshare-custom">
                                    <a title="分享到微信" class="bshare-weixin"></a>
                                    <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>
                                    <a title="分享到QQ空间" class="bshare-qzone"></a>
                                    <a title="分享到Facebook" class="bshare-facebook"></a>
                                    <a title="分享到Twitter" class="bshare-twitter"></a>
                                    <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn"> </a>
                                    <span class="BSHARE_COUNT bshare-share-count">0</span>
                                </div>
                                <script src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>
                                <script src="http://static.bshare.cn/b/bshareC0.js"></script>
                            </div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                <a href="/" target="_blank">资质证明</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                    <a href="/" target="_blank">粤ICP备13048400号-2</a>
                    粤公网安备：
                    <a href="/" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="/" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <script src="http://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
        <script src="./js/detailService.js"></script>
        <script>
            $(function () {
                Vue.createApp({}).use(ElementPlus).mount('.file-list')

                var id = '<?=$info["id"]?>'
                var $collectButton = $('.el-button--collect')
                var $jobApplyButton = $('.el-button--apply')

                $collectButton.on('click', function () {
                    var isCollected = $collectButton.hasClass('collected')

                    httpPost('/api/person/announcement/collect', { id }).then(function () {
                        $collectButton.toggleClass('collected').text(isCollected ? '收藏' : '已收藏')
                    })
                })

                $jobApplyButton.on('click', function () {
                    window.globalComponents.applyDialogComponent.announcementApply(id)
                })

                var seeHeat = $('.see-heat')
                var params = {
                    apiPull: '/api/person/announcement/check-generate-report',
                    apiCreate: '/api/person/announcement/create-report',
                    param: { announcementId: '<?=$info["id"]?>' }
                }
                seeHeat.on('click', function () {
                    window.globalComponents.PromptDialogComponent.pull(params)
                })
            })
        </script>
    </body>
</html>
