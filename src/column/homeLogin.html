<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>登录组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/home.css" />
        <link rel="stylesheet" href="./css/homeLogin.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script type="text/javascript" src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
    </head>

    <body>
        <div class="banner-container">
            <div class="banner-login">
                <div id="homeLoginTemplate" class="home-login-template" v-cloak>
                    <div class="login-switch">
                        <div @click="handleSwitchToScan" v-if="loginSwitchType=='account'" class="account-tips" :class="{'show-tips':isShowLoginTips}">
                            <div class="login-tips">微信扫码1秒登录</div>
                        </div>
                        <div @click="handleSwitchToAccount" v-else class="scan-tips">
                            <div class="login-tips">短信/密码登录</div>
                        </div>
                    </div>

                    <div v-if="loginSwitchType=='account'" class="home-account-login">
                        <el-tabs v-model="loginType">
                            <el-tab-pane label="登录/注册" name="mobile">
                                <el-form ref="mobileLoginRef" :model="mobileLoginForm" :rules="mobileLoginRules" :show-message="false">
                                    <el-form-item prop="mobile">
                                        <el-input
                                            class="mobile"
                                            v-model="mobileLoginForm.mobile"
                                            oninput="value=value.replace(/[^0-9]/g, '')"
                                            name="gaoxiaojobMobile"
                                            autocomplete="on"
                                            placeholder="常用手机号码"
                                            :maxlength="mobileMaxLength"
                                        >
                                            <!-- 暂时不要前置icon -->
                                            <!-- <template #prefix></template> -->

                                            <!-- 号段 -->
                                            <template #prefix>
                                                <div class="input-prepend">
                                                    <el-select v-model="mobileLoginForm.mobileCode" popper-class="home-mobile-prefix-popper" class="mobile-prefix-select">
                                                        <el-option-group v-for="{ type, list } in prefixOptions" :key="type" :label="type">
                                                            <el-option v-for="{ country, code } in list" :key="code" :value="code">
                                                                <span style="float: left">{{ country }}</span>
                                                                <span style="float: right"> {{ code }} </span>
                                                            </el-option>
                                                        </el-option-group>
                                                    </el-select>
                                                </div>
                                            </template>
                                        </el-input>
                                    </el-form-item>

                                    <el-form-item prop="code">
                                        <el-input class="sms-code" v-model="mobileLoginForm.code" placeholder="验证码">
                                            <!-- 暂时不要前置icon -->
                                            <!-- <template #prefix></template> -->

                                            <template #suffix>
                                                <el-button :disabled="codeDisabled" @click="handleSendCode" class="send-code-button"> {{ codeText }} </el-button>
                                            </template>
                                        </el-input>
                                    </el-form-item>

                                    <el-button class="mobile-login-confirm" type="primary" :loading="mobileLogining" @click="handleLogin"> 登录/注册 </el-button>
                                </el-form>
                            </el-tab-pane>

                            <el-tab-pane label="密码登录" name="account">
                                <el-form ref="accountLoginRef" :model="accountLoginForm" :rules="accountLoginRules" :show-message="false">
                                    <el-form-item prop="account">
                                        <el-input v-model="accountLoginForm.account" name="gaoxiaojobAccount" autocomplete="on" placeholder="请输入手机号/用户名/邮箱"></el-input>
                                    </el-form-item>

                                    <el-form-item prop="password">
                                        <el-input v-model="accountLoginForm.password" type="password" show-password placeholder="请输入密码"> </el-input>
                                    </el-form-item>

                                    <div class="account-login-tips">
                                        <a class="ft12 color-primary" :href="resetPasswordUrl">忘记密码</a>
                                        <a class="ft12 color-primary" href="/member/company/login">单位登录</a>
                                    </div>

                                    <el-button class="account-login-confirm" type="primary" :loading="accountLogining" @click="handleLogin"> 求职者登录 </el-button>
                                </el-form>
                            </el-tab-pane>
                        </el-tabs>

                        <div class="signin-tips color-label ft12" v-show="isMobileLogin">
                            首次登录将自动注册，即代表同意<br />
                            <a class="color-primary" href="">《用户协议》</a>
                            <a class="color-primary" href="">《隐私条款》</a>
                        </div>
                    </div>

                    <div v-else class="home-scan-login">
                        <div class="scan-login-title">微信扫码登录</div>
                        <div class="qr-code" v-loading="qrCodeLoading">
                            <div v-if="!isScanSuccess" class="code" :style="`background-image:url(${qrCodeInfo.url})`">
                                <div v-if="!qrCodeLoading" class="scan-animation"></div>
                            </div>

                            <div class="code-refresh" v-show="isQRcodePast" @click="getQRCode"></div>

                            <div v-if="isScanSuccess" class="scan-success">扫描成功</div>
                        </div>
                        <div class="scan-code-tips">
                            <template v-if="isQRcodePast"> 二维码失效 点击重试 </template>
                            <template v-else-if="isScanSuccess">
                                <div class="success">请在手机上确认登录</div>
                            </template>
                            <template v-else> 打开微信扫一扫，快速登录 </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const homeLoginOptions = {
                    data() {
                        function validMobile(mobile) {
                            const reg = /^1[3-9]\d{9}$/
                            return reg.test(mobile)
                        }

                        function validNumber(number) {
                            return /^\d+$/.test(number)
                        }

                        function validPassword(password) {
                            const reg = /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)[A-Za-z\d]{6,26}$/
                            return reg.test(password)
                        }

                        const validateMobile = (rule, value, callback) => {
                            const {
                                mobileLoginForm: { mobileCode }
                            } = this
                            if (mobileCode === '+86') {
                                if (validMobile(value)) {
                                    callback()
                                } else {
                                    callback('请输入正确的手机号码')
                                }
                            } else if (validNumber(value)) {
                                callback()
                            } else {
                                callback('请输入正确的手机号码')
                            }
                        }

                        const validatePassword = (rule, value, callback) => {
                            if (validPassword(value)) {
                                callback()
                            } else {
                                callback('6-26位大小写字母、数字组合密码')
                            }
                        }

                        return {
                            loginType: 'mobile',

                            loginSwitchType: 'account',
                            isShowLoginTips: true,
                            qrCodeTimer: null,
                            qrCodeInfo: {
                                url: '',
                                scene: '',
                                status: ''
                            },
                            qrCodeLoading: false,
                            redirectUrl: window.location.href,

                            captcha: null,
                            captchaAppId: '',

                            prefixOptions: [],

                            mobileLoginForm: {
                                mobileCode: '+86',
                                mobile: '',
                                code: ''
                            },

                            codeDisabled: false,
                            codeText: '获取验证码',
                            codeTime: 60,
                            codeTimer: null,

                            mobileLogining: false,

                            mobileLoginRules: {
                                mobile: [
                                    { required: true, message: '请输入手机号码', trigger: 'blur' },
                                    { validator: validateMobile, trigger: 'blur' }
                                ],
                                code: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
                            },

                            accountLoginForm: {
                                account: '',
                                password: ''
                            },

                            accountLogining: false,

                            accountLoginRules: {
                                account: [{ required: true, message: '请输入手机号/用户名/邮箱', trigger: 'blur' }],
                                password: [
                                    { required: true, message: '请输入密码', trigger: 'blur' },
                                    { validator: validatePassword, trigger: 'blur' }
                                ]
                            }
                        }
                    },

                    computed: {
                        isMobileLogin() {
                            return this.loginType === 'mobile'
                        },

                        redirectQuery() {
                            return `?redirect=${window.location.href}`
                        },

                        signinUrl() {
                            return `/member/person/registry${this.redirectQuery}`
                        },

                        companySigninUrl() {
                            return `/member/company/login`
                        },

                        resetPasswordUrl() {
                            return `/member/person/reset${this.redirectQuery}`
                        },
                        mobileMaxLength() {
                            return this.mobileLoginForm.mobileCode === '+86' ? 11 : 20
                        },
                        isQRcodePast() {
                            return this.qrCodeInfo.status === -1
                        },
                        isScanSuccess() {
                            return this.qrCodeInfo.status === 2
                        }
                    },

                    methods: {
                        async getMobilePrefix() {
                            const data = await httpGet('/api/config/load-country-mobile-code')
                            this.prefixOptions = data
                        },

                        async getMobileCode(data) {
                            const {
                                mobileLoginForm: { mobileCode, mobile }
                            } = this
                            await httpPost('/api/member/send-mobile-login-code', {
                                mobileCode,
                                mobile,
                                type: 1,
                                ...data
                            })
                        },

                        async getCaptchaConfig() {
                            const { captchaAppId: appId } = await httpGet('/api/member/get-captcha-config')
                            this.captchaAppId = appId
                        },

                        handleCaptcha(callback) {
                            try {
                                this.captcha = new TencentCaptcha(this.captchaAppId, (res) => {
                                    const { ret, ticket, randstr } = res
                                    if (ret === 0) {
                                        callback && callback({ ticket, randstr })
                                    }
                                })
                                this.captcha.show()
                            } catch (err) {
                                console.log(err)
                            }
                        },

                        countDown() {
                            this.codeDisabled = true
                            this.codeTimer = setInterval(() => {
                                if (this.codeTime === 1) {
                                    clearInterval(this.codeTimer)
                                    this.codeTime = 60
                                    this.codeText = '重新发送'
                                    this.codeDisabled = false
                                } else {
                                    this.codeTime -= 1
                                    this.codeText = `${this.codeTime}秒`
                                }
                            }, 1000)
                        },

                        handleSendCode() {
                            this.$refs.mobileLoginRef.validateField('mobile', (errMsg) => {
                                if (errMsg.length === 0) {
                                    this.handleCaptcha((data) => {
                                        this.getMobileCode(data)
                                        this.countDown()
                                    })
                                } else {
                                    this.$message.error(errMsg)
                                }
                            })
                        },

                        async handleSign(data) {
                            const { isMobileLogin } = this
                            const loginApi = '/api/member/' + (isMobileLogin ? 'validate-mobile-login-code' : 'account-login')
                            const loading = isMobileLogin ? 'mobileLogining' : 'accountLogining'

                            this[loading] = true

                            let formData = { type: 1 }

                            if (isMobileLogin) {
                                const {
                                    mobileLoginForm: { mobileCode, mobile, code }
                                } = this
                                formData = { ...formData, mobileCode, mobile, code }
                            } else {
                                formData = { ...formData, ...this.accountLoginForm }
                            }

                            try {
                                const { token, expireTime, redirectUrl } = await httpPost(loginApi, {
                                    ...formData,
                                    ...data,
                                    redirect: this.redirectUrl
                                })
                                setToken(token, expireTime)
                                window.location.href = redirectUrl
                            } catch (err) {
                                this[loading] = false
                            }
                        },

                        handleLogin() {
                            const { isMobileLogin } = this
                            const formRef = this.$refs[isMobileLogin ? 'mobileLoginRef' : 'accountLoginRef']
                            const fields = isMobileLogin ? ['mobile', 'code'] : ['account', 'password']
                            const errMsgs = []

                            formRef.validateField(fields, (errMsg) => {
                                if (errMsg.length) {
                                    errMsgs.push(errMsg)
                                }
                            })

                            if (errMsgs.length) {
                                this.$message.error(errMsgs[0])
                                return
                            }

                            if (isMobileLogin) {
                                this.handleSign()
                            } else {
                                this.handleCaptcha((data) => {
                                    this.handleSign(data)
                                })
                            }
                        },

                        countDownTips() {
                            setTimeout(() => {
                                this.isShowLoginTips = false
                            }, 5000)
                        },

                        clearQRCodeTimer() {
                            clearTimeout(this.qrCodeTimer)
                        },

                        checkQRCode() {
                            this.qrCodeTimer = setTimeout(async () => {
                                const { scene } = this.qrCodeInfo
                                const data = await httpPost('/api/member/check-mini-login-qrcode', {
                                    scene,
                                    redirect: this.redirectUrl
                                })

                                const { status, userInfo } = data

                                // 1等待扫码、2已经扫码、3登录成功、-1错误
                                this.qrCodeInfo.status = status
                                switch (status) {
                                    case 1:
                                        if (this.loginSwitchType != 'scan') {
                                            this.clearQRCodeTimer()
                                            return
                                        }
                                        this.checkQRCode()
                                        break
                                    case 2:
                                        this.checkQRCode()
                                        break
                                    case 3:
                                        setToken(userInfo.token, userInfo.expireTime)
                                        window.location.href = userInfo.redirectUrl
                                        break
                                    default:
                                        this.clearQRCodeTimer()
                                        break
                                }
                            }, 2000)
                        },

                        async getQRCode() {
                            this.qrCodeLoading = true
                            const data = await httpGet('/api/member/get-mini-login-qrcode')
                            this.qrCodeInfo = data
                            this.qrCodeLoading = false
                            this.clearQRCodeTimer()
                            this.checkQRCode()
                        },

                        handleSwitchToScan() {
                            this.loginSwitchType = 'scan'
                            this.qrCodeInfo.status = ''
                            this.getQRCode()
                        },

                        handleSwitchToAccount() {
                            this.loginSwitchType = 'account'
                            this.clearQRCodeTimer()
                        }
                    },

                    mounted() {
                        /* 海外号段 */
                        this.getMobilePrefix()
                        this.getCaptchaConfig()
                        this.countDownTips()
                        // 按照不同的情况逻辑处理
                        if (this.loginSwitchType === 'scan') {
                            this.getQRCode()
                        }
                        if (this.loginSwitchType === 'account') {
                            this.clearQRCodeTimer()
                        }
                    }
                }
                Vue.createApp(homeLoginOptions).use(ElementPlus).mount('#homeLoginTemplate')
            })
        </script>
    </body>
</html>
