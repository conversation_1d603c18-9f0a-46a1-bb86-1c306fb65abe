<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>竞争力洞察</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/competitivePower.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message">
                        <!-- 有消息 -->
                        <el-badge :value="100" class="item"><i class="el-icon-bell"></i></el-badge>
                        <!-- 无消息 -->
                        <!-- <i class="el-icon-bell"></i> -->
                    </a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        {
                                            label: '职位',
                                            value: '1',
                                            path: '/job'
                                        },
                                        {
                                            label: '公告',
                                            value: '2',
                                            path: '/search'
                                        },
                                        {
                                            label: '单位',
                                            value: '3',
                                            path: '/company'
                                        },
                                        {
                                            label: '资讯',
                                            value: '4',
                                            path: '/search',
                                            query: 'type=2'
                                        }
                                    ],

                                    keyword: ''
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <main class="el-main">
                <div class="search-header">
                    <div class="recommend-container">
                        <div class="title">竞争力<span>洞察</span></div>
                        <div class="subheading">职位竞争力分析+公告热度查询，知己知彼，提升求职胜算</div>
                        <div class="set-meal">
                            <div class="list" :class="{active: index === packageIndex}" v-for="(item, index) in orderList" :key="item.equityPackageId" @mouseenter="handlePackageEnter(index)">
                                <div class="top" v-if="item.buyTypeTxt">{{item.buyTypeTxt}}</div>
                                <div class="content">
                                    <div class="day">{{item.days}}天</div>
                                    <div class="del">¥{{item.originalAmount}}</div>
                                    <div class="buy-price">
                                        <div class="allprice"><span>¥</span>{{item.realAmount}}</div>
                                        <div class="amortized">
                                            <div class="discount">{{item.discountTxt}}</div>
                                            <div class="single">(单次低至¥{{item.timesAmount}})</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="buy-btn" @click="openpayDialog(item.equityPackageCategoryId,index)">立即购买</div>
                            </div>
                        </div>
                        <div class="service-agreement">
                            * 购买即表示同意<a href="#" target="_blank"><span>《高校人才网增值服务协议》</span></a>
                        </div>
                    </div>
                </div>
                <div class="job-analysis">
                    <div class="analysis-content">
                        <div class="analysis-title">职位竞争力分析</div>
                        <div class="analysis-subheading">人岗智能匹配，多维解析简历，挖掘你的竞争优势！</div>
                        <div class="analysis-description">
                            <div class="left-content">
                                <div class="left-catalogue" :class="index === jobIndex ? 'active' : ''" v-for="(item, index) in jobList" :key="item.Id" @click="jobTabClick(index)">
                                    <img :src="index === jobIndex ? item.img : item.imgblack" alt="" />
                                    <div class="text">
                                        {{item.text}}
                                        <span>{{item.subheading}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="right-content">
                                <div class="item" :class="index === jobIndex ? 'active' : ''" v-for="(item, index) in jobImgList">
                                    <img :src="item.img" alt="" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="announcement-heat">
                    <div class="announcement-content">
                        <div class="announcement-title">公告热度</div>
                        <div class="announcement-subheading">了解公告热度和求职者的画像分布，掌握竞争情报</div>
                        <div class="announcement-description">
                            <div class="left-content">
                                <div class="item" :class="index === announcementIndex ? 'active' : ''" v-for="(item, index) in announcementImgList">
                                    <img :src="item.img" alt="" />
                                </div>
                            </div>
                            <div class="right-content">
                                <div
                                    class="right-catalogue"
                                    :class="index === announcementIndex ? 'active' : ''"
                                    v-for="(item, index) in announcementList"
                                    :key="item.Id"
                                    @click="announcementTabClick(index)"
                                >
                                    <img :src="index === announcementIndex ? item.img : item.imgblack" alt="" />
                                    <div class="text">
                                        {{item.text}}
                                        <span>{{item.subheading}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="evaluate-wrapper">
                    <div class="main-wrapper">
                        <div class="wrapper-title">用户评价</div>
                        <div class="wrapper-desc">1.3w+会员正在使用，好评如潮</div>
                        <div class="evaluate-content">
                            <div class="list" v-for="item in evaluateList">
                                <div class="avatar">
                                    <img :src="item.avatar" alt="" />
                                </div>
                                <div class="name">{{item.name}}</div>
                                <div class="ask">{{item.ask}}</div>
                                <div class="content">{{item.content}}</div>
                                <div class="tag">
                                    <span v-for="tag in item.tag">{{tag}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="search-footer">
                    <div class="search-content">
                        <div class="search-title">服务说明</div>
                        <div class="text-content">
                            <div class="left-text">
                                <p>服务将于付款成功后自动开通，服务过期则所有权益失效。购买后请合理安排时间并尽快使用</p>
                                <p>* “竞争力洞察”包含 职位竞争力分析&公告热度 2项服务。套餐购买后立即生效。</p>
                                <p>* 服务期内，按自然日，每项服务可使用的次数上限为10次/天，不累计；过期未使用则权益失效。</p>
                                <p>* 本报告结合高校人才网大数据生成，内容可能随求职动态而产生变化，结果仅供参考。</p>
                                <p>* 本产品为线上虚拟服务，不支持退款。</p>
                                <p>
                                    * 购买即表示同意<a href="#" target="_blank"><span>《高校人才网增值服务协议》</span></a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="showFooterFixed" class="fixed-wrapper">
                    <div class="main-wrapper">
                        <div class="buy-info">
                            竞争力洞察
                            <div class="single-price">单次低至<span>{{currentPackage.timesAmount}}</span>元，即刻解锁极速求职特权</div>
                        </div>
                        <div class="aside">
                            <div class="detail">
                                <div class="price">{{currentPackage.realAmount}}</div>
                                元/{{currentPackage.days}}天
                                <div class="original-price">{{currentPackage.originalAmount}}元</div>
                            </div>
                            <button class="pay" @click="openBuyDialog">立即开通</button>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            packageIndex: 0,
                            currentPackage: {},
                            jobIndex: 0,
                            announcementIndex: 0,
                            orderList: [],
                            jobTimer: null,
                            announcementTimer: null,
                            orderLoading: false,
                            jobList: [
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/resume.png',
                                    imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/resume-black.png',
                                    id: '1',
                                    text: '简历匹配度分析',
                                    subheading: '结合求职意向，一键解析你与岗位的匹配度'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/jobicon.png',
                                    imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/jobicon-black.png',
                                    id: '2',
                                    text: '职位热度分析',
                                    subheading: '火爆or冷门，求职热度看得见，职位“捡漏”也能轻松上岸'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/person.png',
                                    imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/person-black.png',
                                    id: '3',
                                    text: ' 竞争者能力分布 ',
                                    subheading: '直观展示你的竞争优劣形势，让投递更有底气'
                                }
                            ],
                            announcementList: [
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/heat-data.png',
                                    imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/heatdata-black.png',
                                    id: '1',
                                    text: '综合热度',
                                    subheading: '基于感兴趣人群分析，定位同类公告热度对比，助你求职决策'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/compete-active.png',
                                    imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/compete.png',
                                    id: '2',
                                    text: '竞争态势&热度趋势',
                                    subheading: '随时掌握关注热度趋势，洞悉投递竞争情况，让求职更有把握'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-analysis.png',
                                    imgblack: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-black.png',
                                    id: '3',
                                    text: '求职者画像分析',
                                    subheading: '是竞争对手也是圈内同行，速览未来从业者画像'
                                }
                            ],
                            jobImgList: [
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/mate.png'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-heat.png'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/ability.png'
                                }
                            ],
                            announcementImgList: [
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/heat.png'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/trend.png'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/job-draw.png'
                                }
                            ],

                            evaluateList: [
                                {
                                    avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-1.png',
                                    name: '林**',
                                    ask: '教务管理丨博士丨工作5年',
                                    content: '竞争力分析报告真不错，可以看到自己的学历、专业、意向城市跟申请的职位匹不匹配，适合拿来检查简历投的对不对，自己的核心竞争力怎么样。',
                                    tag: ['真不错']
                                },
                                {
                                    avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-2.png',
                                    name: '宋**',
                                    ask: '互联网后端开发丨硕士丨工作7年',
                                    content: '现在投递简历前，都习惯查一下。可以看到热度趋势还有投递竞争比啥的，大概能了解自己有没有机会，有些我以为热度不高，结果还是很多人投。',
                                    tag: ['性价比高', '分析维度多']
                                },
                                {
                                    avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-3.png',
                                    name: '欧阳**',
                                    ask: '学科教师丨博士丨工作2年',
                                    content: '每天都能查10次，看到喜欢的公告就查一下，看看竞争情况怎么样，现在投简历会不会扎堆，如果能再根据报告结果加上一些求职建议就更好了。',
                                    tag: ['性价比高', '体验好']
                                },
                                {
                                    avatar: '//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/avatar-4.png',
                                    name: '刘**',
                                    ask: '科研助理丨硕士丨应届生',
                                    content: '买了半个月的试了一下，里面的信息还蛮有用的，一个是可以看到自己和岗位的匹配情况，还有就是自己在人群中的位置，可以知道自己的优势和短板。',
                                    tag: ['直观', '有参考价值']
                                }
                            ],
                            showFooterFixed: false
                        }
                    },
                    methods: {
                        jobTabClick(index) {
                            this.jobIndex = index
                            // 清空定时器
                            clearInterval(this.jobTimer)
                            // 重新设置定时器
                            this.setJobCarousel()
                        },
                        announcementTabClick(index) {
                            this.announcementIndex = index
                            // 清空定时器
                            clearInterval(this.announcementTimer)
                            // 重新设置定时器
                            this.setAnnouncementCarousel()
                        },
                        openpayDialog(id, index) {
                            //-------------用户付费转化数据埋点-开始---------------
                            // let productName = $('.list:eq('+index+') .day').text()
                            // //用户付费转化数据埋点
                            // let logData = {
                            //     params : { productId:id,productName:productName },
                            //     actionType : '1',
                            //     actionId : '10020002'
                            // }
                            // this.payBuriedPoint(logData)
                            let uuid = "<?=$data['uuid']?>"
                            //-------------用户付费转化数据埋点-结束---------------

                            let api = '/api/person/resume-equity-package/get-popup-package-list'
                            window.globalComponents.PayDialogAlertComponent.show(api, id, index, uuid, position)
                        },
                        show(id) {
                            this.orderLoading = true
                            // 请求数据
                            httpGet('/api/person/resume-equity-package/get-buy-package-list?equityPackageCategoryId=' + id).then((r) => {
                                this.orderList = r.list
                                this.currentPackage = r.list[0]
                                this.orderLoading = false
                            })
                        },
                        setJobCarousel() {
                            // 设置轮播
                            let _this = this
                            this.jobTimer = setInterval(function () {
                                _this.jobIndex++
                                if (_this.jobIndex > 2) {
                                    _this.jobIndex = 0
                                }
                            }, 3000)
                        },
                        setAnnouncementCarousel() {
                            // 设置轮播
                            let _this = this
                            this.announcementTimer = setInterval(function () {
                                _this.announcementIndex++
                                if (_this.announcementIndex > 2) {
                                    _this.announcementIndex = 0
                                }
                            }, 3000)
                        },
                        //-------------用户付费转化数据埋点-开始---------------
                        // payBuriedPoint(data) {
                        //     const jsonString = JSON.stringify(data)
                        //
                        //     let configKey = '123abcgaoxiaorencaiwang'
                        //     let code = CryptoJS.MD5(configKey).toString()
                        //     let iv = CryptoJS.enc.Utf8.parse(code.substring(0, 16))
                        //     let key = CryptoJS.enc.Utf8.parse(code.substring(16))
                        //
                        //     let encryptString = CryptoJS.AES.encrypt(jsonString, key, { mode: CryptoJS.mode.ECB, iv: iv })
                        //     let img = new Image()
                        //     img.src = `3.gif?data=` + encodeURIComponent(encryptString)
                        // },
                        //-------------用户付费转化数据埋点-结束---------------

                        scroll() {
                            const _this = this
                            window.addEventListener('scroll', function () {
                                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                                const showHeight = document.body.clientHeight
                                _this.showFooterFixed = scrollTop > showHeight * 1
                            })
                        },

                        handlePackageEnter(index) {
                            this.packageIndex = index
                            this.currentPackage = this.orderList[index]
                        },

                        openBuyDialog() {
                            const index = this.packageIndex
                            const id = this.currentPackage.equityPackageCategoryId
                            this.openpayDialog(id, index)
                        }
                    },
                    mounted() {
                        this.show(2)
                        this.setJobCarousel()
                        this.setAnnouncementCarousel()

                        this.scroll()

                        //-------------用户付费转化数据埋点-开始---------------
                        window.onbeforeunload = function () {
                            httpGet("/showcase-browse-log/update-competitive-power-view-point-log?uuid=<?=$data['uuid']?>")
                        }
                        //-------------用户付费转化数据埋点-结束---------------
                    }
                }

                Vue.createApp(component).use(ElementPlus).mount('#component')
            })
        </script>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <!-- 底部测试滚动使用 -->
        <!-- <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/index.html" target="_blank">关于我们</a>| <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/contactUs.html">联系我们</a>|
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/joinUs.html" target="_blank">人才招聘</a>| <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html">产品服务</a>|
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/disclaimers.html" target="_blank">免责声明</a>| <a href="/data/sitemap.html" target="_blank">网站导航</a>|
                <a href="//www.gaoxiaojob.com/zhaopin/zhuanti/zzzm2021/index.html" target="_blank">资质证明</a>|
                <a href="//gaocai.gaoxiaojob.com" target="_blank">高才科技官网</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2023 高校人才网 版权所有 网站备案信息：
                    <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13048400号</a>
                    粤公网安备：
                    <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010602004138" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="//zt.gaoxiaojob.com/zzdxywjyxkz.jpg" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer> -->
    </body>
</html>
