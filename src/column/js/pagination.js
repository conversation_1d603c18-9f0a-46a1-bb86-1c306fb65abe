;(function ($) {
    var ms = {
        init: function (el, args) {
            return (function () {
                const { href, total, pageSize } = args
                // 总页数
                args.pageCount = Math.ceil(total / pageSize)

                if (args.pageCount) {
                    ms.fillHtml(el, args)
                }

                if (!href) {
                    ms.bindEvent(el, args)
                }
            })()
        },

        // 生成a链接
        createHref: function ({ href }, current, isDisable) {
            var link
            if (!href || isDisable) {
                link = 'javascript:;'
            } else if (href.search(/\/p\d+/) != -1) {
                link = href.replace(/\/p\d+/, `/p${current}`)
            } else if (href.search(/\/$/) != -1) {
                link = href.replace(/\/$/, `/p${current}`)
            } else {
                link = `${href}/p${current}`
            }
            return link
        },

        //填充html
        fillHtml: function (el, args) {
            return (function () {
                el.empty()
                var childrenEl = ''

                var { current, pagerCount, pageCount } = args

                if (current > pageCount) {
                    current = 1
                }

                // 判断pagerCount是否大于4、小于22并且为奇数
                if (pagerCount < 5 || pagerCount > 21 || pagerCount % 2 == 0) {
                    pagerCount = 5
                }

                // 设置最大页码按钮数中间差值
                var spaceNumber = Math.floor((pagerCount - 2) / 2)

                // 前面是否出现隐藏
                var isShowPrevMore = pageCount > pagerCount && current >= (pagerCount + 3) / 2
                // 后面是否出现隐藏
                var isShowNextMore = pageCount > pagerCount && current < pageCount - spaceNumber - 1

                // 生成上一页
                childrenEl += `<a href="${ms.createHref(args, current - 1, current == 1)}" class="btn-prev ${current == 1 ? 'disabled' : ''}"></a>`

                // 生成中间部分
                var start = 1,
                    end = pageCount > pagerCount ? pagerCount : pageCount
                end = isShowNextMore ? end - 1 : end

                // 前面出现隐藏时
                if (isShowPrevMore) {
                    const morePrevPage = current - (pagerCount - 2)
                    childrenEl += `
                    <a href="${ms.createHref(args, 1)}" class="number">${1}</a>
                    <a class="el-icon more btn-quickprev el-icon-more" href="${ms.createHref(args, morePrevPage)}"></a>
                `
                }

                // 后面出现隐藏时
                var nextMoreEl = ''
                if (isShowNextMore) {
                    const moreNextPage = current + (pagerCount - 2)
                    nextMoreEl = `
                    <a class="el-icon more btn-quicknext el-icon-more" href="${ms.createHref(args, moreNextPage)}"></a>
                    <a href="${ms.createHref(args, pageCount)}" class="number">${pageCount}</a>
                `
                }

                if (isShowPrevMore && isShowNextMore) {
                    // 前后都出现隐藏时
                    start = current - spaceNumber
                    end = current + spaceNumber
                } else if (isShowPrevMore) {
                    // 只有前面出现隐藏时
                    start = pageCount - pagerCount + 2
                    end = pageCount
                } else if (isShowNextMore) {
                    // 只有后面出现隐藏时
                    start = 1
                    end = pagerCount - 1
                }

                for (let i = start; i <= end; i++) {
                    childrenEl += `<a href="${ms.createHref(args, i, i == current)}" class="number ${i == current ? 'current' : ''}">${i}</a>`
                }

                childrenEl += nextMoreEl

                // 生成下一页
                childrenEl += `
                <a href="${ms.createHref(args, current + 1, current == pageCount)}" class="btn-next ${current == pageCount ? 'disabled' : ''}"></a>
            `
                el.append(childrenEl)
            })()
        },

        bindEvent: function (el, args) {
            const { callback } = args
            return (function () {
                el.on('click', 'a.number', function () {
                    var current = parseInt($(this).text())
                    args.current = current
                    ms.fillHtml(el, args)
                    if (typeof args.callback == 'function') {
                        callback(current)
                    }
                })
                //上一页
                el.on('click', 'a.btn-prev', function (e) {
                    const isDisable = $(this).hasClass('disabled')
                    if (isDisable) return
                    args.current = args.current - 1
                    ms.fillHtml(el, args)

                    if (typeof callback == 'function') {
                        callback(args.current)
                    }
                })
                //下一页
                el.on('click', 'a.btn-next', function () {
                    const isDisable = $(this).hasClass('disabled')
                    if (isDisable) return
                    args.current = args.current + 1
                    ms.fillHtml(el, args)

                    if (typeof callback == 'function') {
                        callback(args.current)
                    }
                })
            })()
        }
    }
    $.fn.createPage = function (options) {
        var args = $.extend(
            {
                total: 0,
                pageSize: 10,
                pagerCount: 7, // 设置最大页码按钮数。 页码按钮的数量，当总页数超过该值时会折叠  (介于 5 和 21 之间的奇数)
                current: 1,
                href: window.location.href, //
                callback: function () {}
            },
            options
        )
        ms.init(this, args)
    }
})(jQuery)
