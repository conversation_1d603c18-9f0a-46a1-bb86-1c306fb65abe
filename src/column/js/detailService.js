$(function () {
    // 页面滚动 start
    var $body = $('body')
    var $header = $('.el-header')
    var $detailHeader = $('.detail-container .detail-header')
    var bodyPaddingTop = $body.css('padding-top')
    var headerHeight = $header.height()
    var detailHeaderHeight = $detailHeader.height()

    $(window).on('scroll', function () {
        var scrollTop = $(window).scrollTop()

        if (scrollTop > headerHeight + detailHeaderHeight) {
            $body.css('padding-top', headerHeight + detailHeaderHeight)
            $header.addClass('is-fixed')
            $detailHeader.addClass('is-fixed')
        } else {
            $body.css('padding-top', bodyPaddingTop)
            $header.removeClass('is-fixed')
            $detailHeader.removeClass('is-fixed')
        }
    })
    // 页面滚动 end
})
