$(function () {
    var template = `
    <div class="header-login-guide-popup">
        <div class="title">登录高校人才网，您可以享受以下权益：</div>

        <ul class="list">
            <li class="item">更懂你的优质信息</li>
            <li class="item">30w+岗位一键直投</li>
            <li class="item">与用人方互动交流</li>
            <li class="item">收藏/关注/订阅/分享</li>
        </ul>

        <button class="el-button el-button--small el-button--primary">立即登录</button>
    </div>
    `

    var timer

    var loginRegisterContainer = '.login-register-container'
    var headerLoginGuidePopup = '.header-login-guide-popup'
    var headerLoginGuidePopupButton = '.header-login-guide-popup .el-button'

    var $body = $('body')
    var $popup = $(template)

    function fadeInPopup() {
        $popup.fadeIn()

        setTimeout(() => {
            $popup.addClass('animation-guide-jump')
        }, 100)
    }

    function fadeOutPopup() {
        clearTimeout(timer)
        $popup.fadeOut()
        $popup.removeClass('animation-guide-jump')
    }

    function delayFadeOutPopup() {
        timer = setTimeout(function () {
            fadeOutPopup()
        }, 300)
    }

    $body.append($popup)

    $body.on('mouseenter', loginRegisterContainer, function () {
        var $this = $(this)

        var width = $this.outerWidth()
        var height = $this.outerHeight()
        var offset = $this.offset()
        var top = offset.top
        var left = offset.left

        var popupWidth = $popup.outerWidth()

        clearTimeout(timer)

        $popup.css({ top: top + height, left: width + left - popupWidth })
        fadeInPopup()
    })

    $body.on('mouseleave', loginRegisterContainer, function () {
        delayFadeOutPopup()
    })

    $body.on('mouseenter', headerLoginGuidePopup, function () {
        clearTimeout(timer)
    })

    $body.on('mouseleave', headerLoginGuidePopup, function () {
        delayFadeOutPopup()
    })

    $body.on('click', headerLoginGuidePopup, function (event) {
        event.stopPropagation()
    })

    $body.on('click', headerLoginGuidePopupButton, function () {
        window.globalComponents.loginDialogComponent.showLoginDialog()
    })

    $body.on('click', function () {
        fadeOutPopup()
    })

    $(window).on('scroll', function () {
        if (timer) return
        fadeOutPopup()
    })

    window.headerLoginGuideAutoShow = function () {
        $body.find(loginRegisterContainer).trigger('mouseenter')
        httpGet('/api/person/resume/add-login-pop-tips-amount')
    }
})
