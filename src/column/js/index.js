// 首页js文件

$(function () {
    var defaultDelay = 5000
    var swiperArray = [
        {
            // banner区域
            swiper: '.slide-picture-display',
            pagination: '.slide-picture-pagination'
        },
        {
            // a1区域
            swiper: '.my-swiper',
            pagination: '.my-pagination'
        },
        {
            // 左侧a4区域
            swiper: '.left-sidebar',
            pagination: '.swiper-pagination'
        },
        {
            // 右侧a5区域
            swiper: '.right-sidebar',
            pagination: '.swiper-pagination'
        },
        {
            // d1,b4区域
            swiper: '.cardlist',
            pagination: '.cardlist-pagination'
        },
        {
            // c1区域
            swiper: '.c1-banner',
            pagination: '.c1-pagination'
        }
    ]

    swiperArray.forEach(function (item) {
        var delay = item.delay ? item.delay : defaultDelay
        new Swiper(item.swiper, {
            pagination: {
                el: item.pagination,
                clickable: true
            },
            autoplay: {
                pauseOnMouseEnter: true,
                disableOnInteraction: false,
                delay: delay
            },
            loop: true,
            simulateTouch: false
        })
    })

    // 页面滑动后固定导航栏
    !(function () {
        isNav($(document).scrollTop(0))
        $(window).on('scroll', function () {
            var boxTop = $('.main').offset().top
            isNav(boxTop)
        })
    })()

    function isNav(boxTop) {
        if ($(window).scrollTop() > boxTop) {
            $('.nav').addClass('fixed')
            $('body').css('padding-top', '44px')
        } else {
            $('.nav').removeClass('fixed')
            $('body').css('padding-top', '0')
        }
    }

    // c4区 列表切换
    $('.main-headline li').on('mouseenter', function () {
        $(this).addClass('self').siblings().removeClass('self')
        var index = $(this).index()
        $('.main-headline-container .tab-content').eq(index).show().siblings().hide()
    })

    // d1区域 列表切换
    $('.navbox li').on('click', function () {
        $(this).addClass('list-change').siblings().removeClass('list-change')
        var index = $(this).index()
        $('.cardcon .t-card').eq(index).show().siblings().hide()
    })

    // h2区域 热门信息列表切换
    $('.hb li').on('mouseenter', function () {
        $(this).addClass('on').siblings().removeClass('on')
        var index = $(this).index()
        $('.dwm-ranking .item').eq(index).show().siblings().hide()
    })

    // 精选职位区域列表切换
    $('.switch li').on('mouseenter', function () {
        $(this).addClass('change').siblings().removeClass('change')
        var index = $(this).index()
        $('.tab-con .con').eq(index).show().siblings().hide()
    })

    // 精选职位区域内部(热门职位,推荐职位)列表切换
    $('.option li').on('mouseenter', function () {
        $(this).addClass('active').siblings().removeClass('active')
        var index = $(this).index()
        $(this).parents('.con').find('.job-board .list').eq(index).show().siblings().hide()
    })

    // 热门单位区域单位列表切换
    $('.list li').on('mouseenter', function () {
        $(this).addClass('current').siblings().removeClass('current')
        var index = $(this).index()
        $('.product-list .prodect-detail').eq(index).show().siblings().hide()
    })

    // 热门单位区域热门职位内部列表切换
    $('.college-change li').on('mouseenter', function () {
        $(this).addClass('play-1').siblings().removeClass('play-1')
        var index = $(this).index()
        $('.tab-list .college-region').eq(index).show().siblings().hide()
    })

    // 热门单位区域热门职位内部列表切换
    $('.job-change li').on('mouseenter', function () {
        $(this).addClass('play-2').siblings().removeClass('play-2')
        var index = $(this).index()
        $('.tab-list .college-job').eq(index).show().siblings().hide()
    })

    // 圈子列表切换
    $('.exchange .inner li').on('click', function () {
        $(this).addClass('is-active').siblings().removeClass('is-active')
        var index = $(this).index()
        $('.dialogue .widget-content').eq(index).show().siblings().hide()
    })

    // 侧边导航栏
    !(function () {
        var nav = $('.nav').height()
        var flag = true
        $(window).on(
            'scroll',
            $.throttle(300, function () {
                if (flag) {
                    $('.main .w').each(function (i, ele) {
                        var offsetTop = parseInt($(ele).offset().top)
                        if ($(document).scrollTop() >= offsetTop - nav) {
                            $('.fixed-tool li').eq(i).addClass('current').siblings().removeClass('current')
                        }
                    })
                }
            })
        )

        $('.fixed-tool li').on('click', function () {
            flag = false
            var index = $(this).index()
            var current = $('.main .w').eq(index).offset().top
            $('body, html')
                .stop()
                .animate(
                    {
                        scrollTop: current - nav
                    },
                    function () {
                        flag = true
                    }
                )
            $(this).addClass('current').siblings().removeClass('current')
        })
    })()

    // a3区 走马灯
    !(function () {
        var speed = 60
        var timer = null
        var distance = 2
        var $scrollBegin = $('#scrollBegin')
        var $scrollEnd = $('#scrollEnd')
        var $scrollSlide = $('#scrollDiv')

        function fnScroll() {
            var scrollLeft = Math.floor($scrollEnd.width() - $scrollSlide.scrollLeft())
            if (scrollLeft <= speed) {
                $scrollSlide.scrollLeft(0)
            } else {
                $scrollSlide.scrollLeft($scrollSlide.scrollLeft() + distance)
            }
        }

        $scrollEnd.html($scrollBegin.html())
        timer = setInterval(fnScroll, speed)

        $scrollSlide.on('mouseenter', function () {
            clearInterval(timer)
        })
        $scrollSlide.on('mouseleave', function () {
            timer = setInterval(fnScroll, speed)
        })
    })()

    // l区域 官方微博
    !(function () {
        var boxHeight = $('.weibo-show-content').height()
        var scrollTop = boxHeight
        var totalHeight = $('.scroll-content').height()
        var isDown = true
        var timer = null

        var durationFn = function () {
            $('.down-scroll').css('opacity', 1)
            $('.up-scroll').css('opacity', 1)

            if (scrollTop >= totalHeight) {
                isDown = false
                scrollTop = totalHeight
                $('.down-scroll').css('opacity', 0)
            }
            if (scrollTop <= boxHeight) {
                $('.up-scroll').css('opacity', 0)
                scrollTop = boxHeight
                isDown = true
            }
        }

        var timerFn = function () {
            timer = setInterval(function () {
                if (isDown) {
                    scrollTop += 5
                } else {
                    scrollTop -= 5
                }
                durationFn()
                $('.weibo-show-content').scrollTop(scrollTop - boxHeight)
            }, 500)
        }

        $('#part').hover(
            function () {
                clearInterval(timer)
            },
            function () {
                timerFn()
            }
        )

        timerFn()

        $('.up-scroll').on('click', function () {
            scrollTop -= 150
            durationFn()
            $('.weibo-show-content').scrollTop(scrollTop - boxHeight)
        })
        $('.down-scroll').on('click', function () {
            scrollTop += 150
            durationFn()
            $('.weibo-show-content').scrollTop(scrollTop - boxHeight)
        })
    })()

    // h2区域 焦点信息
    var $partArea = $('#scrollText')
    var $scroll1 = $('#scroll1')
    var $scroll2 = $('#scroll2')

    $scroll2.html($scroll1.html())

    function roll() {
        if ($partArea.scrollTop() >= $scroll1.height()) {
            $partArea.scrollTop(0)
        }
        $partArea.animate({
            scrollTop: $partArea.scrollTop() + 36
        })
    }

    var stopRoll = setInterval(roll, 5000)

    $partArea.on('mouseover', function () {
        clearInterval(stopRoll)
    })

    $partArea.on('mouseout', function () {
        stopRoll = setInterval(roll, 5000)
    })

    var boxTop = $('.main').offset().top
    var $slideSwiperContainer = $('.slide-swiper-container')
    $(window).on('scroll', function () {
        // 页面滚到main区块两侧轮播图，侧边导航栏往上
        if ($(document).scrollTop() >= boxTop) {
            $slideSwiperContainer.css('top', '100px')
        } else {
            $slideSwiperContainer.css('top', '340px')
        }

        // 页面滚到main区块侧边导航栏往上
        if ($(document).scrollTop() >= boxTop) {
            $('.fixed-tool').css('top', '290px')
        } else {
            $('.fixed-tool').css('top', '530px')
        }
    })

    // 侧边栏
    !(function () {
        var headerHeight = $('.page-header-container').height() + $('.banner-container').height()
        isFlag($(document).scrollTop(), headerHeight)
        $(window).on('scroll', function () {
            var scrollTop = $(document).scrollTop()
            isFlag(scrollTop, headerHeight)
        })
    })()

    // function isFlag(scrollTop, headerHeight) {
    //     var navHeight = $('.nav').height()
    //     var sidebarHeight = $('.sidebar-container').css('height', '100%')
    //     if (scrollTop >= headerHeight) {
    //         if (/calc/g.test($('.sidebar-container').attr('style'))) {
    //             return
    //         }
    //         $('.sidebar-container').css({
    //             top: navHeight,
    //             height: sidebarHeight - navHeight
    //         })
    //     } else {
    //         var top = headerHeight - scrollTop
    //         var height = $(window).height()
    //         $('.sidebar-container').css({
    //             top: top,
    //             height: height - top
    //         })
    //     }
    // }
})
