;(function (global) {
    var primaryColor = '#FFA000'
    var secondColor = '#FFD08B'

    var labelColor = '#333333'
    var labelBasicColor = '#5C5C5C'
    var labelMinFontSize = 12
    var labelFontSize = 14

    var linearColor = [
        { offset: 0, color: secondColor },
        { offset: 1, color: primaryColor }
    ]
    var pieColorStops = [
        { offset: 0, color: primaryColor },
        { offset: 1, color: secondColor }
    ]

    var activeColor = new echarts.graphic.LinearGradient(0, 0, 0, 1, linearColor)
    var axisActiveColor = new echarts.graphic.LinearGradient(0, 1, 0, 0, linearColor)

    var colors = ['#FC8452', '#F9C552', '#91CC75', '#56B689', '#73C0DE', '#B178CA', '#EE6666', '#FF9494', '#7387DE']

    var inactiveColor = '#EDEDED'

    var richMarkOptions = {
        padding: [4, 6, 8],
        backgroundColor: {
            image: '//img.gaoxiaojob.com/uploads/static/echarts/mark.png'
        },
        color: 'white',
        fontSize: labelMinFontSize,
        fontWeight: 400,
        align: 'center',
        verticalAlign: 'middle'
    }

    /**
     *
     * @param {{mark?: boolean; value: number; status?: boolean; breakSymbol?: string; suffixSymbol?: string}} data
     * @description data.status 是否显示换行 - true
     * @description data.breakSymbol 几个换行符 - '\n'
     * @description data.suffixSymbol 后缀符号 - '%'
     * @returns
     */
    function formatterMark(data) {
        var mark = data.mark || false
        var value = data.value
        var status = data.status || false
        var breakText = data.breakSymbol || '\n'
        var suffixText = data.suffixSymbol || '%'

        return (mark ? '{mark|您在这里}' : '') + '{value|' + (mark || status ? breakText : '') + value + suffixText + '}'
    }

    /**
     * 单色渐变柱状图
     * @param {{target: string; data?: Array<{name: string; value: number; mark?: boolean}>}} options
     * @returns
     */
    function barPrimaryChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}

        var data = params.data || []
        var label = []

        var option = {
            grid: {
                top: 60,
                left: 20,
                right: 20,
                bottom: 24
            },
            xAxis: {
                type: 'category',
                data: label,
                axisLabel: {
                    color: labelColor,
                    fontSize: labelFontSize
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: inactiveColor
                    }
                }
            },
            yAxis: {
                show: false,
                type: 'value',
                max: 100
            },
            series: [
                {
                    type: 'bar',
                    data: data,
                    showBackground: true,
                    backgroundStyle: {
                        color: inactiveColor
                    },
                    barWidth: 40,
                    label: {
                        show: true,
                        position: 'top',
                        formatter: function (params) {
                            return formatterMark({ mark: params.data.mark, value: params.value, breakSymbol: '\n\n' })
                        },
                        textStyle: {
                            color: labelColor,
                            fontSize: labelMinFontSize
                        },
                        rich: { mark: richMarkOptions, value: { align: 'center' } }
                    },
                    itemStyle: {
                        color: activeColor
                    }
                }
            ]
        }

        data.forEach(function (item) {
            label.push(item.name)
        })

        echarts.init(dom).setOption(option)
    }

    /**
     * 单色渐变柱状图柱状图（横向）
     * @param {{target: string; data?: Array<{name: string; value: {name: string; value: number; mark?: boolean}}>; active: string}} options
     * @returns
     */
    function barPrimaryAxisChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}
        var data = params.data || ''
        var active = params.active || ''

        var label = []
        var series = []
        var seriesData = {}

        var option = {
            grid: {
                top: 70,
                bottom: 0,
                containLabel: true
            },
            legend: {
                top: 20
            },
            xAxis: {
                show: false,
                type: 'value'
            },
            yAxis: {
                type: 'category',
                data: label,
                axisLabel: {
                    color: labelColor,
                    fontSize: labelFontSize
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: false,
                    lineStyle: {
                        color: inactiveColor
                    }
                }
            },
            series: series
        }

        data.forEach(function (item) {
            var name = item.name
            var value = item.value

            label.push(name)

            value.forEach(function (val) {
                var key = val.name
                var data = { value: val.value, mark: !!val.mark }

                if (seriesData[key]) {
                    seriesData[key].push(data)
                } else {
                    seriesData[key] = [data]
                }
            })
        })

        for (var key in seriesData) {
            series.push({
                name: key,
                type: 'bar',
                data: seriesData[key],
                stack: 'total',
                barWidth: 28,
                itemStyle: {
                    color: key === active ? axisActiveColor : inactiveColor
                },
                label: {
                    show: true,
                    align: 'center',
                    verticalAlign: 'middle',
                    color: labelColor,
                    fontSize: labelFontSize,
                    formatter: function (params) {
                        var value = params.value

                        return formatterMark({ mark: params.data.mark, status: true, value: value, breakSymbol: '\n\n' })
                    },
                    rich: {
                        mark: richMarkOptions,
                        value: {
                            padding: [10, 0]
                        }
                    }
                }
            })
        }

        echarts.init(dom).setOption(option)
    }

    /**
     * 柱状图柱状图（横向）
     * @param {{target: string; data?: Array<{name: string; value: number}>}} options
     * @returns
     */
    function barNormalAxisChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}
        var value = params.data || []

        var option = {
            grid: {
                top: 30,
                left: 30,
                right: 50,
                bottom: 0,
                containLabel: true
            },
            xAxis: {
                show: false,
                type: 'value'
            },
            yAxis: {
                type: 'category',
                barWidth: 20,
                data: value.reverse().map(function (item) {
                    return item.name
                }),
                axisLabel: {
                    color: labelColor,
                    fontSize: labelFontSize
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        color: inactiveColor
                    }
                }
            },
            series: [
                {
                    type: 'bar',
                    data: value.map(function (item, index) {
                        return {
                            name: item.name,
                            value: item.value,
                            itemStyle: {
                                color: colors[Math.abs(index + 1 - value.length)] || primaryColor
                            }
                        }
                    }),
                    barWidth: 20,
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{c}%',
                        valueAnimation: true
                    }
                }
            ]
        }

        echarts.init(dom).setOption(option)
    }

    /**
     * 单色渐变饼状图
     * @param {{target: string; data?: Array<{name: string; value: number; mark?: boolean}>; active: string}} options
     * @returns
     */
    function piePrimaryChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}
        var data = params.data || []
        var active = params.active || ''

        var option = {
            grid: {
                top: 0,
                bottom: 0
            },
            series: [
                {
                    type: 'pie',
                    data: data,
                    radius: [45, 75],
                    left: 'center',
                    width: 400,
                    label: {
                        alignTo: 'edge',
                        formatter: function (params) {
                            var name = params.name
                            var value = params.value

                            return formatterMark({ mark: params.data.mark, value: name + value })
                        },
                        minMargin: 10,
                        edgeDistance: 10,
                        rich: {
                            mark: richMarkOptions,
                            value: { padding: [10, 0], fontSize: labelFontSize, align: 'center' }
                        }
                    },
                    labelLine: {
                        show: true,
                        lineStyle: {
                            color: primaryColor
                        }
                    },
                    itemStyle: {
                        normal: {
                            color: inactiveColor,
                            borderRadius: 4
                        }
                    }
                }
            ]
        }

        data.forEach(function (item, index) {
            var valid = item.name === active

            if (valid) {
                item.label = { textStyle: { color: primaryColor } }

                item.itemStyle = {
                    normal: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 1,
                            y2: 1,
                            colorStops: pieColorStops,
                            globalCoord: false
                        }
                    }
                }
            }

            data.splice(index, 1, item)
        })

        echarts.init(dom).setOption(option)
    }

    /**
     * 饼状图
     * @param {{target: string; data?: Array<{name: string; value: number}>}} options
     * @returns
     */
    function pieNormalChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}
        var value = params.data || []

        var option = {
            grid: {
                top: 50,
                bottom: 20
            },
            series: [
                {
                    type: 'pie',
                    radius: [45, 85],
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 1
                    },
                    label: {
                        alignTo: 'edge',
                        formatter: '{name|{b}}\n{value|{c}%}',
                        minMargin: 5,
                        edgeDistance: 10,
                        lineHeight: 15,
                        rich: {
                            name: {
                                color: labelColor,
                                fontSize: labelFontSize
                            },
                            value: {
                                color: labelBasicColor,
                                fontSize: labelMinFontSize
                            }
                        }
                    },
                    labelLine: {
                        length: 15,
                        length2: 0,
                        maxSurfaceAngle: 80
                    },

                    data: value.map(function (item, index) {
                        return { name: item.name, value: item.value, itemStyle: { color: colors[index] || primaryColor } }
                    })
                }
            ]
        }

        echarts.init(dom).setOption(option)
    }

    /**
     * 仪表盘
     * @param {{target: string; label: string; value: number}} options
     * @returns
     */
    function gaugePrimaryChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}

        var label = params.label || ''
        var value = params.value || 0

        var level = {
            0.125: '一般',
            0.375: '中等',
            0.625: '激烈',
            0.875: '火爆'
        }

        var option = {
            grid: {
                top: 50,
                bottom: 20
            },
            series: [
                {
                    type: 'gauge',
                    startAngle: 180,
                    endAngle: 0,
                    center: ['50%', '95%'],
                    radius: 150,
                    min: 0,
                    max: 1,
                    splitNumber: 8,
                    axisLine: {
                        lineStyle: {
                            width: 10,
                            color: [
                                [0.25, '#7CFFB2'],
                                [0.5, '#58D9F9'],
                                [0.75, '#FDDD60'],
                                [1, '#FF6E76']
                            ]
                        }
                    },
                    pointer: {
                        icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                        length: '12%',
                        width: 12,
                        offsetCenter: [0, '-60%'],
                        itemStyle: {
                            color: 'auto'
                        }
                    },
                    axisTick: {
                        length: 12,
                        lineStyle: {
                            color: 'auto',
                            width: 1
                        }
                    },
                    splitLine: {
                        length: 20,
                        lineStyle: {
                            color: 'auto',
                            width: 2
                        }
                    },
                    axisLabel: {
                        color: labelColor,
                        fontSize: labelFontSize,
                        distance: -50,
                        rotate: 'tangential',
                        formatter: function (value) {
                            return level[value] || ''
                        }
                    },
                    title: {
                        offsetCenter: [0, '-10%'],
                        color: labelColor,
                        fontSize: labelFontSize,
                        lineHeight: 18
                    },
                    detail: {
                        fontSize: 18,
                        offsetCenter: [0, '-35%'],
                        valueAnimation: true,
                        formatter: function (value) {
                            var data = []

                            for (var key in level) {
                                if (key >= value) {
                                    data.push(level[key])
                                }
                            }

                            return data[0] || level[0]
                        },
                        color: 'white',
                        padding: [0, 0],
                        backgroundColor: 'inherit',
                        borderRadius: 20
                    },
                    data: [
                        {
                            value: value,
                            name: '在{title|“' + label + '”}类\n公告中的热度情况',
                            title: {
                                rich: {
                                    title: {
                                        color: primaryColor,
                                        fontSize: labelFontSize,
                                        fontWeight: 'bold'
                                    }
                                }
                            }
                        }
                    ]
                }
            ]
        }

        echarts.init(dom).setOption(option)
    }

    /**
     *  折线图
     * @param {{target: string; data: Array<{name: string; value: number; text?: string}>}} options
     * @returns
     */
    function linePrimaryChart(options) {
        var dom = document.getElementById(options.target)

        if (!dom) return

        var params = options || {}
        var value = params.data || []

        var option = {
            color: ['#FFA000', '#486CF5'],
            grid: {
                left: 35,
                right: 35,
                top: 40,
                bottom: 30
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                triggerEvent: true,
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#EBEBEB'
                    }
                },
                axisLabel: {
                    color: labelBasicColor,
                    fontSize: labelMinFontSize,
                    fontWeight: 'bold'
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#D8D8D8',
                        type: 'dashed'
                    }
                },
                data: value.map(function (item) {
                    return item.name
                })
            },
            yAxis: {
                type: 'value',
                show: false
            },
            series: [
                {
                    type: 'line',
                    smooth: true,
                    label: {
                        show: true,
                        position: 'top',
                        formatter: function (params) {
                            return params.data.text ? '{text| }' + '{value|' + params.data.text + '}' : ''
                        },
                        textStyle: {},
                        rich: {
                            text: {
                                backgroundColor: {
                                    image: '//img.gaoxiaojob.com/uploads/static/echarts/symbol.png'
                                },
                                fontSize: 16,
                                fontWeight: 400,
                                align: 'center',
                                verticalAlign: 'middle'
                            },
                            value: {
                                padding: [0, 0, 0, 4],
                                color: '#FA635C',
                                fontSize: 16,
                                fontWeight: 'bold'
                            }
                        }
                    },

                    data: value
                }
            ]
        }

        echarts.init(dom).setOption(option)
    }

    global.Charts = {
        barPrimaryChart: barPrimaryChart,
        barPrimaryAxisChart: barPrimaryAxisChart,
        barNormalAxisChart: barNormalAxisChart,
        piePrimaryChart: piePrimaryChart,
        pieNormalChart: pieNormalChart,
        gaugePrimaryChart: gaugePrimaryChart,
        linePrimaryChart: linePrimaryChart
    }
})(window)
