function PopperInit() {
    let el = document.createElement('div')
    el.id = 'custom-popper'
    el.innerHTML = '<span class="arrow"></span><div class="content"></div>'
    document.body.appendChild(el)
    let popover = null
    $('body').on('mouseover', '.release-container .recruit-information-container .place', function () {
        let referenceEl = $(this)
        let isOverflow = $(this)[0].clientWidth < $(this)[0].scrollWidth
        if (!isOverflow) return
        let popperEl = $('#custom-popper')
        $('#custom-popper .content').text(referenceEl.text())
        popover = Popper.createPopper(referenceEl[0], popperEl[0], {
            placement: 'left',
            modifiers: [
                {
                    name: 'offset',
                    options: {
                        offset: [0, 10]
                    }
                }
            ]
        })
    })
    $('body').on('mouseout', '.release-container .recruit-information-container .place', function () {
        if (popover) popover.destroy()
    })
}
PopperInit()
