// 栏目页js文件

$(function () {
    var defaultDelay = 5000
    var swiperArray = [
        {
            // banner区域
            swiper: '.slide-picture-display',
            pagination: '.slide-picture-pagination'
        },
        {
            // 栏目页模板B热点资讯轮播图
            swiper: '.columnB-slider',
            pagination: '.columnB-pagination'
        },
        {
            // d1区域推荐单位
            swiper: '.cardlist',
            pagination: '.cardlist-pagination'
        },
        {
            // 栏目页博士后 热门单位页面轮播
            swiper: '.sort-switch',
            pagination: '.sort-pagination'
        }
    ]

    swiperArray.forEach(function (item) {
        var delay = item.delay ? item.delay : defaultDelay
        new Swiper(item.swiper, {
            pagination: {
                el: item.pagination,
                clickable: true
            },
            autoplay: {
                pauseOnMouseEnter: true,
                disableOnInteraction: false,
                delay: delay
            },
            loop: true,
            simulateTouch: false
        })
    })

    new Swiper('.newest-swiper', {
        direction: 'vertical',
        autoplay: {
            delay: defaultDelay
        },
        loop: true
    })

    // 页面滑动后固定导航栏
    !(function () {
        isNav($(window).scrollTop(0))
        $(window).on('scroll', function () {
            var boxTop = $('.main').offset().top
            isNav(boxTop)
        })
    })()

    function isNav(boxTop) {
        if ($(window).scrollTop() > boxTop) {
            $('.nav').addClass('fixed')
            $('body').css('padding-top', '44px')
        } else {
            $('.nav').removeClass('fixed')
            $('body').css('padding-top', '0')
        }
    }

    // d1区域 列表切换
    $('.navbox li').on('click', function () {
        $(this).addClass('list-change').siblings().removeClass('list-change')
        var index = $(this).index()
        $('.cardcon .t-card').eq(index).show().siblings().hide()
    })

    // 精选职位区域列表切换
    $('.switch li').on('mouseenter', function () {
        $(this).addClass('change').siblings().removeClass('change')
        var index = $(this).index()
        $('.tab-con .con').eq(index).show().siblings().hide()
    })

    // 精选职位区域(推荐职位,最新职位,热门职位)内部列表切换
    $('.option li').on('mouseenter', function () {
        $(this).addClass('active').siblings().removeClass('active')
        var index = $(this).index()
        $(this).parents('.con').find('.job-board .list').eq(index).show().siblings().hide()
    })

    // 最新公告&简章区域列表切换
    $('.notice-list li').on('mouseenter', function () {
        $(this).addClass('item').siblings().removeClass('item')
        var index = $(this).index()
        $('.recruitment-information .istop').eq(index).show().siblings().hide()
    })

    // 政府与事业单位 最新公告&简章列表切换
    $('.list-option li').on('mouseenter', function () {
        $(this).addClass('tab').siblings().removeClass('tab')
        var index = $(this).index()
        $('.data .amount').eq(index).show().siblings().hide()
        $('.announcement-content .recruitment-announcement').eq(index).show().siblings().hide()
    })

    // 圈子列表切换
    $('.inner li').on('click', function () {
        $(this).addClass('is-active').siblings().removeClass('is-active')
        var index = $(this).index()
        $('.dialogue .widget-content').eq(index).show().siblings().hide()
    })

    // 侧边导航栏
    !(function () {
        var nav = $('.nav').height()
        var flag = true
        $(window).on(
            'scroll',
            $.throttle(300, function () {
                if (flag) {
                    $('.main .w').each(function (i, ele) {
                        var offsetTop = parseInt($(ele).offset().top)
                        if ($(document).scrollTop() >= offsetTop - nav) {
                            $('.fixed-tool li').eq(i).addClass('current').siblings().removeClass()
                        }
                    })
                }
            })
        )

        $('.fixed-tool li').on('click', function () {
            flag = false
            var index = $(this).index()
            var current = $('.main .w').eq(index).offset().top
            $('body, html')
                .stop()
                .animate(
                    {
                        scrollTop: current - nav
                    },
                    function () {
                        flag = true
                    }
                )
            $(this).addClass('current').siblings().removeClass('current')
        })
    })()

    // 栏目页博士后 热门单位列表切换
    $('.fenlei-tab li').on('mouseenter', function () {
        $(this).addClass('tab-mouseover').siblings().removeClass('tab-mouseover')
        var index = $(this).index()
        $('.tab-card-content .pic').eq(index).css('display', 'flex').siblings().hide()
    })

    // 页面滚到main区块侧边导航栏往上
    !(function () {
        var boxTop = $('.main').offset().top
        $(window).on('scroll', function () {
            if ($(document).scrollTop() >= boxTop) {
                $('.fixed-tool').css('top', '270px')
            } else {
                $('.fixed-tool').css('top', '510px')
            }
        })
    })()

    // 侧边栏
    var headerHeight = $('.page-header-container').height() + $('.banner-container').height()
    !(function () {
        isFlag($(document).scrollTop(), headerHeight)
        $(window).on('scroll', function () {
            var scrollTop = $(document).scrollTop()
            isFlag(scrollTop, headerHeight)
        })
    })()

    function isFlag(scrollTop, headerHeight) {
        var navHeight = $('.nav').height()
        var sidebarHeight = $('.sidebar-container').css('height', '100%')

        if (scrollTop >= headerHeight) {
            if (/calc/g.test($('.sidebar-container').attr('style'))) {
                return
            }
            $('.sidebar-container').css({
                top: navHeight,
                height: sidebarHeight - navHeight
            })
        } else {
            var top = headerHeight - scrollTop
            var height = $(window).height()
            $('.sidebar-container').css({
                top: top,
                height: height - top
            })
        }

        // 判断是否有banner的页面
        var hasBanner = $('.banner-container').length
        if (!hasBanner) {
            var pageHeader = $('.page-header-container').height()
            $('.sidebar-container').css({
                top: scrollTop >= pageHeader ? navHeight : pageHeader,
                height: sidebarHeight - navHeight
            })
        }
    }
})
