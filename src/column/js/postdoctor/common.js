$(function () {
    // 左侧楼层锚点
    !(function () {
        var module = $('.module')
        var { length } = module
        if (!length) {
            $('.fixed-tool').hide()
            return
        }

        var fixedToolHtml = '<ul>'
        module.each((i, e) => {
            const name = $(e).attr('module-name')
            name ? (fixedToolHtml += `<li>${name}</li>`) : ''
        })
        fixedToolHtml += '</ul>'

        $('.fixed-tool').html(fixedToolHtml)
        $('.fixed-tool').find('li').eq(0).addClass('active')

        var nav = $('.postdoctor-header-container').height()
        var flag = true
        $(window).on(
            'scroll',
            $.throttle(300, false, function () {
                if (flag) {
                    $('.postdoctor-container .module').each(function (i, ele) {
                        var offsetTop = parseInt($(ele).offset().top)
                        var scrollTop = $(document).scrollTop()
                        if (scrollTop >= offsetTop - nav) {
                            $('.fixed-tool li').eq(i).addClass('active').siblings().removeClass('active')
                        }
                        if (scrollTop === 0) {
                            $('.fixed-tool li').eq(0).addClass('active').siblings().removeClass('active')
                        }
                    })
                }
            })
        )

        $('.fixed-tool li').on('click', function () {
            flag = false
            var index = $(this).index()
            var current = $('.postdoctor-container .module').eq(index).offset().top
            $('body, html')
                .stop()
                .animate(
                    {
                        scrollTop: current - nav
                    },
                    function () {
                        flag = true
                    }
                )
            $(this).addClass('active').siblings().removeClass('active')
        })
    })()

    // 登录弹框
    $('html, body').on('click', '.open-login-dialog', function (e) {
        window.globalComponents.loginDialogComponent.showLoginDialog()
        e.stopPropagation()
    })
})

/**
 *
 * @param {number} top 滚动高度
 */
function smoothScrollTo(top = 0) {
    window.scrollTo({ top, behavior: 'smooth' })
}

/**
 *
 * @param {string} target ID
 */
function scrollToTarget(target) {
    const headerDom = document.querySelector('.postdoctor-header-container')
    const targetDom = document.querySelector(`#${target}`)

    if (headerDom && targetDom) {
        const { offsetHeight } = headerDom
        const { offsetTop } = targetDom

        smoothScrollTo(offsetTop - offsetHeight)
    }
}

/**
 *
 * @param {string} key url
 */
function getHrefQuery(key) {
    const { search } = window.location

    const queryString = search.substring(1)
    const params = queryString.split('&')

    const paramMap = {}
    for (const param of params) {
        const [key, value] = param.split('=')
        if (key) {
            paramMap[key] = value
        }
    }
    return paramMap[key]
}
