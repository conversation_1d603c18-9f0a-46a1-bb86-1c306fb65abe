// 导航栏展示方向
function NavDirection() {
    var el = '.page-header-container .nav nav ul li'
    var length = $(el).length
    var middleIndex = length % 2 === 0 ? length / 2 - 1 : (length - 1) / 2
    $(el + ':gt(' + middleIndex + ')')
        .find('.nav-text')
        .addClass('right')
}
NavDirection()

//-----------发起埋点日志新增开始-----------------------
function buriedPoint(data) {
    const { actionType, actionModule, params } = data
    const token = getCookie('gaoxiaojobPositionToken')
    const cookies = getCookie('gaoxiaojob')
    const img = new Image()
    let baseUrl = `/2.gif?actionType=${actionType}&actionModule=${actionModule}&token=${token}&cookies=${cookies}`
    if (params) {
        baseUrl = baseUrl + `?params=${params}`
    }
    img.src = baseUrl
}
//-----------发起埋点日志新增结束-----------------------

function setToken(token, expires) {
    var expiresValue = typeof expires === 'number' ? new Date(expires) : expires
    document.cookie = `Authorization=${token};path=/;expires=${expiresValue}`
}

function removeToken() {
    document.cookie = `Authorization=;path=/;expires=${new Date(0)}`
}

// 公告详情隐藏邮箱地址 点击弹出登录弹窗
$(function () {
    $('body').on('click', '.detail-hidden-email-address', function () {
        window.globalComponents.loginDialogComponent.showLoginDialog()
    })
})
