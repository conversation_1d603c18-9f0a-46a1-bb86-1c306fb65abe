<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>合作申请</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/application.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <section class="container">
            <div class="header">
                <a href="/"><img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" /></a>
                <span>客户服务热线：400-166-188</span>
            </div>

            <div class="main">
                <div class="tips">
                    <div class="tips-application">
                        <div class="tips-title"><span>单位合作申请</span></div>
                        <div class="tips-news">
                            <p>感谢您对高校人才网的关注与信任!</p>
                            <p>每天，高校人才网更新着数以千计的招聘岗位，吸引着数以万计求职者的关注。</p>
                            <p>这里是中文互联网信息、访问量排名前列的高层次人才需求信息平台，选择在这里发布信息，将获得前所未有的关注和事半功倍的效果。</p>
                            <p>根据您的需求，高校人才网可为您提供信息发布和广告宣传等不同类型、不同价格的招聘发布服务。</p>
                            <p>您可以通过在右侧填写提交你的招聘需求和联系信息，我们在收到需求后将及时与您联系。</p>
                        </div>
                    </div>
                    <div class="tips-contact">
                        <div class="tips-more">若需要了解更多服务，也可通过以下方式直接与我们联系：</div>
                        <p><img src="assets/application/phone.png" />020-85611139</p>
                        <p><img src="assets/application/mobile.png" />15920573323（微信同号）</p>
                        <p><img src="assets/application/qq.png" />2881224205</p>
                        <p><img src="assets/application/email.png" /><EMAIL></p>
                    </div>
                </div>

                <div id="component">
                    <div class="form-container" style="display: none" v-show="runing">
                        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="88px">
                            <el-form-item label="申请类型" prop="type">
                                <el-select v-model="formData.type">
                                    <el-option label="发布招聘" value="1"></el-option>
                                    <el-option label="推广宣传" value="2"></el-option>
                                    <el-option label="招聘活动" value="3"></el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="单位名称" prop="company">
                                <el-input v-model="formData.company" placeholder="请填写单位全称; 具体至二级院校/课题组"></el-input>
                            </el-form-item>

                            <el-form-item label="联系人&nbsp;&nbsp;&nbsp;" prop="fullname">
                                <el-input v-model="formData.fullname" placeholder="请填写联系人姓名"></el-input>
                            </el-form-item>

                            <el-form-item label="所在部门" prop="department">
                                <el-input v-model="formData.department" placeholder="请填写联系人所在部门"></el-input>
                            </el-form-item>

                            <el-form-item label="联系电话" prop="phone">
                                <el-input v-model="formData.phone" placeholder="请填写联系人固定电话或手机号码"></el-input>
                            </el-form-item>

                            <el-form-item label="招聘岗位" prop="position">
                                <el-input v-model="formData.position" placeholder="请填写所要招聘的岗位名称"></el-input>
                            </el-form-item>

                            <el-form-item label="学历要求" prop="education">
                                <el-select v-model="formData.education">
                                    <el-option label="大专" value="1"></el-option>
                                    <el-option label="本科" value="2"></el-option>
                                    <el-option label="硕士研究生" value="3"></el-option>
                                    <el-option label="博士研究生" value="4"></el-option>
                                    <el-option label="其他" value="5"></el-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="招聘时长" prop="recruit">
                                <el-input v-model="formData.recruit" placeholder="请填写招聘时长"></el-input>
                            </el-form-item>

                            <el-form-item label="QQ/微信" prop="wechat">
                                <el-input v-model="formData.wechat" placeholder="请填写联系QQ或微信"></el-input>
                            </el-form-item>

                            <el-form-item label="联系邮箱" prop="email">
                                <el-input v-model="formData.email" placeholder="请填写联系邮箱"></el-input>
                            </el-form-item>

                            <el-form-item label="其他说明" prop="explain">
                                <el-input type="textarea" v-model="formData.explain" placeholder="已在网络发布的信息，可在此处填写内容链接"> </el-input>
                            </el-form-item>

                            <el-form-item style="text-align: right">
                                <el-button type="primary" :loading="confirmLoading" @click="onSubmit">立即提交</el-button>
                                <el-button @click="resetForm">重置</el-button>
                            </el-form-item>

                            <el-form-item class="explain">
                                <a href="/">特别说明</a>
                                <a href="/">注册成为单位会员>></a>
                            </el-form-item>
                        </el-form>
                    </div>
                </div>
            </div>
        </section>
        <script>
            ;(function () {
                const component = {
                    data() {
                        return {
                            runing: false,
                            formData: {
                                type: '1',
                                company: '',
                                fullname: '',
                                department: '',
                                phone: '',
                                position: '',
                                education: '1'
                            },
                            formRules: {
                                type: [{ required: true }],
                                company: [{ required: true, message: '请填写单位全称; 具体至二级院校/课题组' }],
                                fullname: [{ required: true, message: '请填写联系人姓名' }],
                                department: [{ required: true, message: '请填写联系人所在部门' }],
                                phone: [{ required: true, message: '请填写联系人固定电话或手机号码' }],
                                position: [{ required: true, message: '请填写所要招聘的岗位名称' }],
                                education: [{ required: true }]
                            },
                            confirmLoading: false
                        }
                    },

                    mounted() {
                        this.runing = true
                    },

                    methods: {
                        onSubmit() {
                            this.$refs['formRef'].validate((valid) => {
                                if (valid) {
                                    this.confirmLoading = true
                                    setTimeout(() => {
                                        this.$message.success('提交成功')
                                        this.confirmLoading = false
                                    }, 3000)
                                } else {
                                    this.$message.error('请填写相关信息')
                                }
                            })
                        },
                        resetForm() {
                            this.$refs['formRef'].resetFields()
                        }
                    }
                }
                Vue.createApp(component).use(ElementPlus).mount('#component')
            })()
        </script>
    </body>
</html>
