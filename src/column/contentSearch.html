<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>内容搜索</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/contentSearch.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
    </head>

    <body>
        <div class="column-wrapper">
            <header class="page-header-container">
                <div class="pagetop">
                    <!--未登录 -->
                    <div class="pagetop-container">
                        <div class="left-content">
                            <ul class="job-part">
                                <li>
                                    <a href="/" target="_blank" class="logo"></a>
                                </li>
                                <li class="job-link header-notice-container">
                                    公告&简章
                                    <div class="notice-open-part">
                                        <div class="notice-content">
                                            <div class="nav-list">
                                                <div class="nav-title">
                                                    <p>栏目导航</p>
                                                </div>
                                                <ul class="is-nav-container nav-container">
                                                    <li>
                                                        <a href="/" target="_blank">人才专场</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">高校招聘</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">科研人才</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">政府与事业单位</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">中小学校</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">医学人才</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">企业单位</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">博士后</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">海归人才</a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="nav-list">
                                                <div class="nav-title">
                                                    <p>省区导航</p>
                                                </div>
                                                <ul class="nav-container">
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">全国</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">内蒙古</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">黑龙江</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">广东</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">台湾</a>
                                                    </li>
                                                    <a class="more" href="/" target="_blank">更多</a>
                                                </ul>
                                            </div>
                                            <div class="nav-list">
                                                <div class="nav-title">
                                                    <p>城市导航</p>
                                                </div>
                                                <div class="nav-container">
                                                    <ul class="nav-container">
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <li>
                                                            <a href="/" target="_blank">北京</a>
                                                        </li>
                                                        <a href="/" target="_blank" class="more">更多</a>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="nav-list">
                                                <div class="nav-title">
                                                    <p>学科导航</p>
                                                </div>
                                                <ul class="is-nav-container nav-container">
                                                    <li>
                                                        <a href="/" target="_blank">计算机科学与技术</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">生物学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">管理科学与工程</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">临床医学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">电子信息</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">基础医学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">经济学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">马克思主义理论</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">化学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">材料科学与工程</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">机械工程</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">信息与通信工程</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">公共卫生与预防医学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">中医学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">教育学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">数学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">中国语言文学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">药学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">物理学</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">外国语言文学</a>
                                                    </li>
                                                    <a href="/" target="_blank" class="more">更多</a>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="job-link">
                                    <a href="/" target="_blank">职位</a>
                                </li>
                                <li class="job-link">
                                    <a href="/" target="_blank">单位</a>
                                </li>
                            </ul>
                        </div>

                        <div class="right-content">
                            <div class="search" id="headerSearch">
                                <el-form :model="formData" class="main-part">
                                    <el-form-item>
                                        <el-select v-model="formData.searchType" class="m-2" size="small">
                                            <el-option label="职位" value="1"></el-option>
                                            <el-option label="简章" value="2"></el-option>
                                            <el-option label="单位" value="3"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-form>
                                <input type="text" placeholder="搜索" />
                                <a href="/" target="_blank"></a>
                            </div>

                            <a href="/" target="_blank" class="application-release">申请发布职位</a>
                            <a href="/" target="_blank" class="recruit">我要招聘</a>
                            <a href="/" target="_blank" class="job-seekers">求职者登录</a>
                            <span class="line">|</span>
                            <a href="/" target="_blank">注册</a>
                        </div>
                    </div>

                    <!-- 已登录 -->
                    <!-- <div class="page-login-container">
                    <div class="left-content">
                        <ul class="job-part">
                            <li>
                                <a href="/" target="_blank" class="logo"></a>
                            </li>
                            <li class="job-link header-notice-container">公告&简章
                                <div class="notice-open-part">
                                    <div class="notice-content">
                                        <div class="nav-list">
                                            <div class="nav-title">
                                                <p>栏目导航</p>
                                            </div>
                                            <ul class="is-nav-container nav-container">
                                                <li>
                                                    <a href="/" target="_blank">人才专场</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">高校招聘</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">科研人才</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">政府与事业单位</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">中小学校</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">医学人才</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">企业单位</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">博士后</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">海归人才</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="nav-list">
                                            <div class="nav-title">
                                                <p>省区导航</p>
                                            </div>
                                            <ul class="nav-container">
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">全国</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">内蒙古</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">黑龙江</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">广东</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">台湾</a>
                                                </li>
                                                <a class="more" href="/" target="_blank">更多</a>
                                            </ul>
                                        </div>
                                        <div class="nav-list">
                                            <div class="nav-title">
                                                <p>城市导航</p>
                                            </div>
                                            <div class="nav-container">
                                                <ul class="nav-container">
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <li>
                                                        <a href="/" target="_blank">北京</a>
                                                    </li>
                                                    <a href="/" target="_blank" class="more">更多</a>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="nav-list">
                                            <div class="nav-title">
                                                <p>学科导航</p>
                                            </div>
                                            <ul class="is-nav-container nav-container">
                                                <li>
                                                    <a href="/" target="_blank">计算机科学与技术</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">生物学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">管理科学与工程</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">临床医学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">电子信息</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">基础医学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">经济学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">马克思主义理论</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">化学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">材料科学与工程</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">机械工程</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">信息与通信工程</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">公共卫生与预防医学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">中医学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">教育学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">数学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">中国语言文学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">药学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">物理学</a>
                                                </li>
                                                <li>
                                                    <a href="/" target="_blank">外国语言文学</a>
                                                </li>
                                                <a href="/" target="_blank" class="more">更多</a>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="job-link">
                                <a href="/" target="_blank">职位</a>
                            </li>
                            <li class="job-link">
                                <a href="/" target="_blank">单位</a>
                            </li>
                        </ul>
                    </div>

                    <div class="right-content">
                        <div class="search" id="headerSearch">
                            <el-form :model="formData" class="main-part">
                                <el-form-item>
                                    <el-select v-model="formData.searchType" class="m-2" size="small">
                                        <el-option label="职位" value="1"></el-option>
                                        <el-option label="简章" value="2"></el-option>
                                        <el-option label="单位" value="3"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                            <input type="text" placeholder="搜索" />
                            <a href="/" target="_blank"></a>
                        </div>

                        <a href="/" target="_blank" class="application-release">申请发布职位</a>
                        <a href="/" target="_blank" class="recruit">我要招聘</a>
                        <a href="/" target="_blank" class="message"></a>
                        <span class="personal">
                            <img
                                src="https://axure-file.lanhuapp.com/8d951de4-aefb-40f7-954e-366683d68331__71ec274fc04aad78b0db296f8ecc0ddd.png" />
                        </span>
                    </div>
                </div> -->
                </div>

                <div class="nav">
                    <nav>
                        <ul>
                            <li>
                                <a href="/" target="_blank" class="active">首页</a>
                            </li>
                            <li>
                                <a href="/" target="_blank">人才专场<span></span></a>
                                <div class="nav-text">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">高校招聘<span></span></a>
                                <div class="nav-text">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">科研人才<span></span></a>
                                <div class="nav-text">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">政府与事业单位<span></span></a>
                                <div class="nav-text">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">中小学<span></span></a>
                                <div class="nav-text right">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">医学人才<span></span></a>
                                <div class="nav-text right">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">企业招聘<span></span></a>
                                <div class="nav-text right">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <a href="/" target="_blank">博士后<span></span></a>
                                <div class="nav-text right">
                                    <div class="left-title">
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">双一流院校人才招聘专区</a>
                                        <a href="/" target="_blank">高校年度人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                        <a href="/" target="_blank">高职高专院校人才招聘专区</a>
                                    </div>
                                    <div class="right-picture">
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav001.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav002.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav003.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav004.png" alt="" />
                                        </a>
                                        <a href="/" target="_blank">
                                            <img src="assets/home/<USER>/nav005.png" alt="" />
                                        </a>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </nav>
                </div>
            </header>

            <!-- <div class="recommend-company-content" id="recommendCompany">
                <div class="swiper mySwiper recommend-company" v-cloak>
                    <div class="swiper-wrapper company-pane">
                        <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[0]">
                            <el-carousel :height="paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[0]">
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                </el-carousel-item>
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                </el-carousel-item>
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[1]">
                            <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[1]">
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                </el-carousel-item>
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[2]">
                            <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[2]">
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[3]">
                            <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[3]">
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[4]">
                            <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[4]">
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                        <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[5]">
                            <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[5]">
                                <el-carousel-item>
                                    <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                    <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                    <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                </el-carousel-item>
                            </el-carousel>
                        </div>
                    </div>

                    <div class="recommend-tabs">
                        <div class="label"><span>HOT</span>推荐单位：</div>
                        <div class="swiper-pagination tabs" @click="handleRecommend"></div>
                    </div>
                </div>
            </div> -->

            <div id="searchComponent">
                <div class="search-header__container">
                    <!-- 由后端渲染 -->
                    <div class="main-header-showcase__container">
                        <!-- 7 个 showcase-browse 为一组 carousel-item 。如果只有一组：indicator-position=“none”；否则 indicator-position=“outside” -->
                        <el-carousel height="70px" arrow="never" trigger="click" indicator-position="outside" :autoplay="true" :interval="5000">
                            <el-carousel-item>
                                <a class="showcase-browse" href="" target="_blank" data-showcase-id="" data-showcase-number="">
                                    <img src="//zt.gaoxiaojob.com/lanmuT1new_10.jpg" alt="" />
                                </a>
                            </el-carousel-item>
                        </el-carousel>
                    </div>
                </div>

                <div class="main">
                    <div class="everyday-gather-container">
                        <div class="left-information-content">
                            <div class="search-tab">
                                <el-radio-group v-model="formData.searchType" class="ml-4">
                                    <el-radio label="1" size="large">公告</el-radio>
                                    <el-radio label="2" size="large">资讯</el-radio>
                                </el-radio-group>
                            </div>

                            <div class="search-content">
                                <div class="search flex">
                                    <input type="text" v-model="formData.keyword" @keyup.enter="search" class="job-input" placeholder="请输入搜索关键词" />
                                    <a target="_blank" class="search-icon" @click="search"></a>
                                </div>

                                <div class="hot-search" v-if="formData.searchType == 1">
                                    热门搜索：
                                    <a
                                        href="http://www.gaoxiaojob.com/search?keyword=%E4%B8%AD%E5%9B%BD%E7%A7%91%E5%AD%A6%E9%99%A2%E5%A3%B0%E5%AD%A6%E7%A0%94%E7%A9%B6%E6%89%80"
                                        class="hot-job showcase-browse"
                                        data-showcase-number="gonggao_remensousuo"
                                        data-showcase-id="14132"
                                        >中国科学院声学研究所</a
                                    >
                                    <a
                                        href="http://www.gaoxiaojob.com/search?keyword=%E6%B5%99%E6%B1%9F%E5%AE%87%E7%BF%94%E8%81%8C%E4%B8%9A%E6%8A%80%E6%9C%AF%E5%AD%A6%E9%99%A2"
                                        class="hot-job showcase-browse"
                                        data-showcase-number="gonggao_remensousuo"
                                        data-showcase-id="13878"
                                        >浙江宇翔职业技术学院</a
                                    >
                                    <a
                                        href="http://www.gaoxiaojob.com/search?keyword=RPO%E7%BA%BF%E4%B8%8A%E9%9D%A2%E8%AF%95%E4%BC%9A"
                                        class="hot-job showcase-browse"
                                        data-showcase-number="gonggao_remensousuo"
                                        data-showcase-id="11869"
                                        >博士线上面试会</a
                                    >
                                    <a href="/search?type=1&keyword=辅导员" class="hot-job showcase-browse" data-showcase-number="gonggao_remensousuo" data-showcase-id="11877">辅导员</a>
                                    <a href="/search?type=1&keyword=专任教师" class="hot-job showcase-browse" data-showcase-number="gonggao_remensousuo" data-showcase-id="11875">专任教师</a>
                                    <a href="/search?type=1&keyword=博士后" class="hot-job showcase-browse" data-showcase-number="gonggao_remensousuo" data-showcase-id="11873">博士后</a>
                                    <a href="/search?type=1&keyword=科研助理" class="hot-job showcase-browse" data-showcase-number="gonggao_remensousuo" data-showcase-id="11871">科研助理</a>
                                </div>

                                <div class="hot-search" v-if="formData.searchType == 2">
                                    热门搜索：
                                    <a
                                        href="http://www.gaoxiaojob.com/search?keyword=%E4%B8%AD%E5%9B%BD%E7%A7%91%E5%AD%A6%E9%99%A2%E5%A3%B0%E5%AD%A6%E7%A0%94%E7%A9%B6%E6%89%80"
                                        class="hot-job showcase-browse"
                                        data-showcase-number="zixun_remensousuo"
                                        data-showcase-id="14133"
                                        >中国科学院声学研究所</a
                                    >
                                    <a
                                        href="http://www.gaoxiaojob.com/job?keyword=%E6%B5%99%E6%B1%9F%E5%AE%87%E7%BF%94%E8%81%8C%E4%B8%9A%E6%8A%80%E6%9C%AF%E5%AD%A6%E9%99%A2"
                                        class="hot-job showcase-browse"
                                        data-showcase-number="zixun_remensousuo"
                                        data-showcase-id="14041"
                                        >浙江宇翔职业技术学院</a
                                    >
                                    <a href="/search?type=2&keyword=高才情报局" class="hot-job showcase-browse" data-showcase-number="zixun_remensousuo" data-showcase-id="11884">高才情报局</a>
                                    <a
                                        href="http://www.gaoxiaojob.com/zhaopin/zhuanti/ycb_fdyanalysis/index.html"
                                        class="hot-job showcase-browse"
                                        data-showcase-number="zixun_remensousuo"
                                        data-showcase-id="12569"
                                        >辅导员</a
                                    >
                                    <a href="/search?type=2&keyword=博士" class="hot-job showcase-browse" data-showcase-number="zixun_remensousuo" data-showcase-id="12565">博士</a>
                                    <a href="/search?type=2&keyword=教师" class="hot-job showcase-browse" data-showcase-number="zixun_remensousuo" data-showcase-id="12563">教师</a>
                                    <a href="/search?type=2&keyword=高校" class="hot-job showcase-browse" data-showcase-number="zixun_remensousuo" data-showcase-id="11883">高校</a>
                                    <a href="/search?type=2&keyword=编制" class="hot-job showcase-browse" data-showcase-number="zixun_remensousuo" data-showcase-id="11880">编制</a>
                                </div>

                                <div class="no-result">
                                    <ul>
                                        <li><span>1</span><a href="">无锡市锡山区人民法院2022年招聘5名司法警察警务辅助人员公告</a></li>
                                        <li><span>2</span><a href="">湖南省嘉禾县2022年第二批公开招聘15名高层次和急需紧缺人才公告</a></li>
                                        <li><span>3</span><a href="">湖州学院2022年诚聘高层次人才（教师）公告</a></li>
                                        <li><span>4</span><a href="">澳門城市大學2022年誠聘教研優才</a></li>
                                        <li><span>5</span><a href="">云南商务职业学院2022年中层与教学相关岗位人才招聘方案</a></li>
                                        <li><span>6</span><a href="">浙江省平阳县县属国企2022年公开招聘75名正式编制员工的公告（第一批）</a></li>
                                        <li><span>7</span><a href="">福州市闽侯县生态环境局关于2022年10月招聘2名临时工作人员的公告</a></li>
                                        <li><span>8</span><a href="">中国银行总行2022年社会招聘2名授信管理部人员公告</a></li>
                                        <li><span>9</span><a href="">中国出版集团有限公司2023年校园招聘信息</a></li>
                                        <li><span>10</span><a href="">中国银行总行2022年社会招聘2名授信管理部人员公告</a></li>
                                    </ul>
                                </div>

                                <div class="result-list">
                                    <div class="current-state">
                                        <div class="exhibition-quantity">共检索到<span class="amount">66</span>条结果</div>
                                        <div class="sort-list">
                                            <!-- ascending为升序 descending为降序 -->
                                            <span class="sort descending" @click="changeDesc()">时间</span>
                                            <span class="sort" @click="changeSort()">阅读量</span>
                                        </div>
                                    </div>

                                    <div class="recruitment-information-content">
                                        <div class="block-content offline-mark">
                                            <a href="/" target="_blank" class="title offline-gray-first">
                                                <span class="offline-tag">已下线</span>
                                                <span class="belonging-school">深圳大学</span
                                                >高等研究院朱薇院士课题组2021年10月招聘1名副研1名副研究究高等研究院朱薇院士课题组2021年10月招聘1名副研1名副研究究
                                            </a>
                                            <div class="praise offline-gray-first">提供年薪80-10万、事业编制、提供住宅、提供住宅、提供住宅、提供住宅、提供...</div>
                                            <div class="recruitment-information">
                                                <div class="top-information offline-gray-first">
                                                    <span class="establishment-tag">编</span>
                                                    <span class="recruitment-describe">36个职位</span><span class="recruitment-describe">招60人</span
                                                    ><span class="recruitment-describe">广东-广州</span>
                                                </div>
                                                <div class="bottom-information offline-gray-second">
                                                    <span class="column">栏目：高校教师</span>
                                                    <span v-if="formData.searchType == 2" class="column quantity">321</span>
                                                    <span class="column time">2021-10-16</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="block-content">
                                            <a href="/" target="_blank" class="title">
                                                <span class="offline-tag">已下线</span>
                                                <span class="belonging-school">深圳大学</span>高等研究院朱薇院士课题组2021年10月招聘1名副研1名副研究究...
                                            </a>
                                            <div class="praise offline-gray-first">提供年薪80-10万、事业编制、提供住宅、提供住宅、提供住宅、提供住宅、提供...</div>
                                            <div class="recruitment-information">
                                                <div class="top-information">
                                                    <span class="establishment-tag">编</span>
                                                    <span class="recruitment-describe">36个职位</span><span class="recruitment-describe">招60人</span
                                                    ><span class="recruitment-describe">广东-广州</span>
                                                </div>
                                                <div class="bottom-information">
                                                    <span class="column">栏目：高校教师</span>
                                                    <span class="column time">2021-10-16</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="block-content">
                                            <a href="/" target="_blank" class="title"> <span class="belonging-school">深圳大学</span>高等研究院朱薇院士课题组2021年10月招聘1名副研1名副研究究... </a>
                                            <div class="recruitment-information">
                                                <div class="top-information">
                                                    <span class="recruitment-describe">36个职位</span><span class="recruitment-describe">招60人</span
                                                    ><span class="recruitment-describe">广东-广州</span>
                                                </div>
                                                <div class="bottom-information">
                                                    <span class="column">栏目：高校教师</span>
                                                    <span class="column time">2021-10-16</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="block-content">
                                            <a href="/" target="_blank" class="title"> <span class="belonging-school">深圳大学</span>高等研究院朱薇院士课题组2021年10月招聘1名副研1名副研究究... </a>
                                            <div class="recruitment-information">
                                                <div class="top-information">
                                                    <span class="recruitment-describe">36个职位</span><span class="recruitment-describe">招60人</span
                                                    ><span class="recruitment-describe">广东-广州</span>
                                                </div>
                                                <div class="bottom-information">
                                                    <span class="column">栏目：高校教师</span>
                                                    <span class="column time">2021-10-16</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="block-content">
                                            <a href="/" target="_blank" class="title"> <span class="belonging-school">深圳大学</span>高等研究院朱薇院士课题组2021年10月招聘1名副研1名副研究究... </a>
                                            <div class="recruitment-information">
                                                <div class="top-information">
                                                    <span class="recruitment-describe">36个职位</span><span class="recruitment-describe">招60人</span
                                                    ><span class="recruitment-describe">广东-广州</span>
                                                </div>
                                                <div class="bottom-information">
                                                    <span class="column">栏目：高校教师</span>
                                                    <span class="column time">2021-10-16</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <el-pagination
                                        background
                                        :layout="'total, sizes, prev, pager, next, jumper'"
                                        :current-page="page"
                                        :page-size="pageSize"
                                        :total="count"
                                        @size-change="(val) => handleFilter(val, 'pageSize')"
                                        @current-change="(val) => handleFilter(val, 'page')"
                                    >
                                    </el-pagination>
                                </div>
                            </div>
                        </div>

                        <div class="right-details-content">
                            <div class="daily-topic">
                                <div class="common-title">
                                    <h2>最新日报</h2>
                                </div>

                                <ul class="newest-daily">
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                    <li>
                                        <a href="/" target="_blank" class="daily-link"> 高校人才网招聘信息日报（2021年10月19日） </a>
                                    </li>
                                </ul>

                                <div class="common-title">
                                    <h2>话题</h2>
                                </div>

                                <div class="Job-search-dynamics national-examination">
                                    <h5>#国考报名进入“井喷期”#</h5>
                                    <div class="civil-service-examination">
                                        <a href="/" target="_blank" class="civil-link main-link">
                                            <img src="assets/home/<USER>/dcaaf84bgy1gx4aw9lyxhj20b4078dgo.jpg" alt="" />
                                            <span>2022年国考：6天77.8万人报名 热门岗位竞争比超2000：1</span>
                                        </a>
                                    </div>
                                    <div class="hot-position">
                                        <a href="/" target="_blank">热门岗位有啥特征？</a>
                                        <a href="/" target="_blank">热门岗位有啥特征？</a>
                                        <a href="/" target="_blank">热门岗位有啥特征？</a>
                                        <a href="/" target="_blank">热门岗位有啥特征？</a>
                                        <a href="/" target="_blank">热门岗位有啥特征？</a>
                                    </div>
                                </div>
                            </div>

                            <!-- vue渲染窗口登录 -->
                            <div class="register-login"></div>

                            <div class="school-picture">
                                <a href="/" target="_blank">
                                    <img src="http://img.gaoxiaojob.com/h2_75.jpg" alt="" />
                                </a>
                            </div>

                            <div class="school-picture">
                                <a href="/" target="_blank">
                                    <img src="http://img.gaoxiaojob.com/h2_75.jpg" alt="" />
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar-container">
                <div class="guild-part">
                    <a href="/member/person/home" target="_blank" class="tools-link is-home"> <span class="icon"></span> 主页</a>

                    <a href="/member/person/resume" target="_blank" class="tools-link is-resume">简历</a>

                    <a href="/member/person/invite" target="_blank" class="tools-link is-invite">
                        <span>职位<br />邀约</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/delivery" target="_blank" class="tools-link is-delivery">
                        <span>投递</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/collection" target="_blank" class="tools-link is-collect">收藏</a>

                    <!-- <a href="/member/person/message" target="_blank" class="tools-link is-news">消息</a> -->
                </div>

                <div class="status-part">
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>

                    <a href="javascript:;" class="link-icon mobile">
                        <span>移动端</span>
                        <span class="mobile-hover">查看高校人才网移动端</span>
                    </a>

                    <a href="//weibo.com/jobgd" target="_blank" class="link-icon weibo">
                        <span>微博</span>
                        <span class="weibo-hover">关注高校人才网官方微博</span>
                    </a>
                </div>
            </div>

            <script>
                $(function () {
                    $('.sidebar-container .guild-part').on('click', 'a', function (event) {
                        var href = $(this).attr('href')

                        if (href.indexOf(';') > -1) {
                            event.preventDefault()
                            window.globalComponents.loginDialogComponent.showLoginDialog()
                        }
                    })
                })
            </script>

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">人才招聘</a>| <a href="/" target="_blank">广告服务</a>|
                    <a href="/" target="_blank">免责声明</a>| <a href="/" target="_blank">网站导航</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>
        </div>

        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
        <script src="./js/column.js"></script>
        <script src="./js/citySwitch.js"></script>
        <script>
            $(function () {
                const headerSearch = {
                    data() {
                        return {
                            formData: {
                                searchType: '1'
                            }
                        }
                    }
                }

                Vue.createApp(headerSearch).use(ElementPlus).mount('#headerSearch')

                const recommendCompany = {
                    data() {
                        return {
                            paneHeight: '70px',
                            runTime: 3000,
                            running: [true, false, false, false, false, false],
                            delayTime: []
                        }
                    },
                    mounted() {
                        var $panes = $('.recommend-company .pane')

                        // 获取每个分类的页数，赋予对应轮播时间
                        for (var i = 0; i < $panes.length; i++) {
                            let time = 0
                            let length = $panes.eq(i).find('.el-carousel__item').length

                            // 当分类的页数超过2页才显示轮播点
                            if (length > 1) {
                                $panes.eq(i).find('.el-carousel__indicators').css('visibility', 'visible')
                            }

                            time = length * this.runTime
                            this.delayTime.push(time)
                        }
                    },
                    methods: {
                        handleRecommend(e) {
                            const { nodeName } = e.target
                            const index = $(e.target).index()
                            const $recommend = $('.recommend-company .pane').eq(index)

                            if (nodeName === 'SPAN') {
                                $recommend.find('.el-carousel__indicator')[0].click()
                                this.running.fill(false)[$(e.target).index()] = true
                            }
                        }
                    }
                }

                Vue.createApp(recommendCompany).use(ElementPlus).mount('#recommendCompany')

                const component = {
                    data() {
                        return {
                            formData: {
                                searchType: '1',
                                keyword: ''
                            },
                            page: 1,
                            count: 100,
                            orderByType: 1,
                            orderBySort: 1
                        }
                    },

                    watch: {
                        'formData.searchType': {
                            handler() {
                                this.search()
                            }
                        }
                    },

                    methods: {
                        search() {
                            this.page = 1
                            this.orderByType = 1
                            this.orderBySort = 1
                            this.jump()
                        },
                        handleFilter(val, key) {
                            if (key === 'page') {
                                this.page = val
                                this.jump()
                            }
                        },
                        changeSort() {
                            this.page = 1
                            // 改变类型
                            this.orderByType = this.orderByType == '1' ? '2' : '1'
                            this.jump()
                        },
                        changeDesc() {
                            this.page = 1
                            this.orderBySort = this.orderBySort == '1' ? '2' : '1'
                            this.jump()
                        },
                        // handleSort(val) {
                        //     this.sort = val
                        //     this.handleFilter(val, 'sort')
                        // },
                        jump() {
                            let page = this.page

                            if (page > this.count) {
                                page = this.count
                            }
                            if (page < 1) {
                                page = 1
                            }

                            // if (!this.formData.keyword) {
                            //     return
                            // }

                            const json = { keyword: this.formData.keyword }
                            if (page > 1) {
                                json.page = page
                            }

                            if (this.orderByType != '1') {
                                json.orderByType = this.orderByType
                            }
                            if (this.orderBySort != '1') {
                                json.orderBySort = this.orderBySort
                            }

                            if (this.formData.searchType && this.formData.searchType != '1') {
                                json.type = this.formData.searchType
                            }
                            const params = Object.keys(json)
                                .map(function (key) {
                                    // body...
                                    return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
                                })
                                .join('&')

                            // 把plus/search.php 替换成search
                            var path = location.pathname
                            path = path.replace('plus/search.php', 'search')

                            if (params) {
                                location.href = path + '?' + params
                            } else {
                                location.href = path
                            }
                        }
                    }
                }

                Vue.createApp(component)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#searchComponent')
            })
        </script>

        <script src="./lib/swiper/swiper.min.js"></script>

        <script>
            $(function () {
                const companyArray = ['高等学院', '科研院校', '医疗卫生', '中小学校', '机关事业', '知名企业']

                new Swiper('.mySwiper', {
                    loop: true,
                    simulateTouch: false,
                    autoplay: {
                        pauseOnMouseEnter: true,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                        renderBullet: function (index, className) {
                            return `<span class="${className}">${companyArray[index]}</span>`
                        }
                    },
                    on: {
                        slideChangeTransitionStart: function () {
                            this.activeIndex = this.activeIndex > companyArray.length ? 1 : this.activeIndex
                            $('.tabs span')
                                .eq(this.activeIndex - 1)
                                .click()
                        }
                    }
                })
            })
        </script>
    </body>
</html>
