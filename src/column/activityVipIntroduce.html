<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>VIP</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/vipIntroduce.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message">
                        <!-- 有消息 -->
                        <el-badge :value="100" class="item"><i class="el-icon-bell"></i></el-badge>
                        <!-- 无消息 -->
                        <!-- <i class="el-icon-bell"></i> -->
                    </a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        {
                                            label: '职位',
                                            value: '1',
                                            path: '/job'
                                        },
                                        {
                                            label: '公告',
                                            value: '2',
                                            path: '/search'
                                        },
                                        {
                                            label: '单位',
                                            value: '3',
                                            path: '/company'
                                        },
                                        {
                                            label: '资讯',
                                            value: '4',
                                            path: '/search',
                                            query: 'type=2'
                                        }
                                    ],

                                    keyword: ''
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <main class="el-main">
                <div class="search-header">
                    <div class="recommend-container">
                        <div class="title">升级高才VIP会员</div>
                        <div class="subheading">尊享<span>8</span>大求职权益,找工作快人一步</div>
                        <div class="set-meal">
                            <div class="list" v-for="(item, index) in orderList" :key="item.equityPackageId">
                                <div class="top" :class="item.buyType == 2 ? 'red' : ''" v-if="item.buyTypeTxt">{{item.buyTypeTxt}}</div>
                                <div class="content">
                                    <div class="day">{{item.name}}</div>
                                    <div class="subheading">{{item.buyDesc}}</div>
                                    <div class="buy-price">
                                        <div class="allprice"><span>¥</span>{{item.realAmount}}</div>
                                        <div class="del">¥{{item.originalAmount}}</div>
                                    </div>
                                    <div class="buy-time">{{item.dailyAmount}}<span>元/天</span></div>
                                </div>
                                <div class="buy-btn" @click="openpayDialog(item.equityPackageCategoryId,index)">立即开通</div>
                            </div>
                        </div>
                        <div class="service-agreement">
                            * 购买即表示同意<a href="#" target="_blank"><span>《高校人才网增值服务协议》</span></a>
                        </div>
                    </div>
                </div>
                <div class="job-service">
                    <div class="job-content">
                        <div class="job-title">VIP 会员权益对比</div>
                        <div class="job-subheading">海量招聘信息，心仪岗位轻松get，从此快速便捷找工作</div>
                        <div class="job-description">
                            <div class="special">仅限PC</div>
                            <table class="el-table">
                                <tr>
                                    <th class="el-col el-col-4 radius">权益名称</th>
                                    <th class="el-col el-col-10">服务简介</th>
                                    <th class="el-col el-col-5">普通用户</th>
                                    <th class="el-col el-col-5 vip">VIP会员</th>
                                </tr>

                                <tr class="white">
                                    <td class="data-name el-col el-col-4">
                                        <p>竞争力分析</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>根据简历匹配度、职位热度和竞争者能力分布等维度输出分析报告</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold">10次/天</p>
                                    </td>
                                </tr>

                                <tr class="gray">
                                    <td class="data-name el-col el-col-4">
                                        <p>公告热度</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>查看公告的热度数据及求职者的画像分布</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold">10次/天</p>
                                    </td>
                                </tr>

                                <tr class="white">
                                    <td class="data-name el-col el-col-4">
                                        <p>编制查询</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>一键筛选编制岗位，助你“上岸”快人一步</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold"><img src="assets/vip/correct.png" alt="" /></p>
                                    </td>
                                </tr>

                                <tr class="gray">
                                    <td class="data-name el-col el-col-4">
                                        <p>简历模板</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>可使用高层次人才专属简历模板制作简历，并支持免费下载</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold">VIP专属模板</p>
                                    </td>
                                </tr>

                                <tr class="white">
                                    <td class="data-name el-col el-col-4">
                                        <p>浏览足迹</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>可快速查看近3个月公告和职位的浏览记录</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold"><img src="assets/vip/correct.png" alt="" /></p>
                                    </td>
                                </tr>

                                <tr class="gray">
                                    <td class="data-name el-col el-col-4">
                                        <p>收藏查看数量</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>职位收藏和公告收藏的可查看个数</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="">限制各50个</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold">无限制</p>
                                    </td>
                                </tr>

                                <tr class="white">
                                    <td class="data-name el-col el-col-4">
                                        <p>求职资源</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>硕博求职资源干货包，含年度招聘报告</p>
                                        <p>（需前往服务号领取）</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold"><img src="assets/vip/correct.png" alt="" /></p>
                                    </td>
                                </tr>

                                <tr class="gray">
                                    <td class="data-name el-col el-col-4">
                                        <p>专属标识</p>
                                    </td>
                                    <td class="data-name el-col el-col-10">
                                        <p>VIP专属身份标识</p>
                                        <p>（需前往服务号领取）</p>
                                    </td>
                                    <td class="data-name el-col el-col-5">
                                        <p class="powder">X</p>
                                    </td>
                                    <td class="data-name el-col el-col-5 vip-tips">
                                        <p class="gold"><img src="assets/vip/correct.png" alt="" /></p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="user">
                    <div class="userreview-content">
                        <div class="user-title">用户评价</div>
                        <div class="user-description">
                            <div class="right-content">
                                <div class="right-catalogue" v-for="(item, index) in announcementList" :key="item.Id" @click="announcementTabClick(index)">
                                    <div class="head">
                                        <div class="avatar">
                                            <img :src="item.img" alt="" />
                                            <div :class="item.sex"></div>
                                        </div>
                                        <div class="user-content">
                                            <div class="name">{{item.name}}</div>
                                            <div class="category">
                                                <div class="icon">{{item.icon1}}</div>
                                                <div class="icon">{{item.icon2}}</div>
                                                <div class="icon">{{item.icon3}}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="text">{{item.text}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="search-content">
                            <div class="search-title">服务说明</div>
                            <div class="text-content">
                                <div class="left-text">
                                    <p>1. 服务购买后立即生效；过期未使用则权益失效。</p>
                                    <p>2. 竞争力分析&公告热度 权益，在服务期内，按自然日，每项服务可使用的次数上限为10次，不累计。</p>
                                    <p>3. 本产品为线上虚拟服务，不支持退款。</p>
                                    <p>
                                        4. 购买即表示同意<a href="#" target="_blank"><span>《高校人才网增值服务协议》</span></a
                                        >。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            jobIndex: 0,
                            announcementIndex: 0,
                            orderList: [],
                            timer: null,
                            orderLoading: false,
                            jobList: [
                                {
                                    img: 'assets/compete/resume.png',
                                    imgblack: 'assets/compete/resume-black.png',
                                    id: '1',
                                    text: '简历匹配度分析',
                                    subheading: '结合求职意向，一键解析你与岗位的匹配度'
                                },
                                {
                                    img: 'assets/compete/jobicon.png',
                                    imgblack: 'assets/compete/jobicon-black.png',
                                    id: '2',
                                    text: '职位热度分析',
                                    subheading: '火爆or冷门，求职热度看得见，职位“捡漏”也能轻松上岸'
                                },
                                {
                                    img: 'assets/compete/person.png',
                                    imgblack: 'assets/compete/person-black.png',
                                    id: '3',
                                    text: ' 竞争者能力分布 ',
                                    subheading: '直观展示你的竞争优劣形势，让投递更有底气'
                                }
                            ],
                            announcementList: [
                                {
                                    img: 'assets/vip/male-avatar.png',
                                    name: '陈**',
                                    sex: 'male',
                                    id: '1',
                                    text: '职位匹配功能很实用！报告能看到自己符不符合条件，哪里不符合。今天优化了下简历，希望能接到offer！！',

                                    icon1: '硕士',
                                    icon2: '哲学',
                                    icon3: '武汉大学'
                                },
                                {
                                    img: 'assets/vip/female-avatar.png',
                                    name: '林**',
                                    sex: 'female',
                                    id: '1',
                                    text: '试了下那个竞争力分析，竟然可以跟同岗位的求职者对比，看到自己是什么水平，有点意思，就是这结果确实很卷……',
                                    icon1: '硕士',
                                    icon2: '教育学',
                                    icon3: '北京师范大学'
                                },
                                {
                                    img: 'assets/vip/male-avatar2.png',
                                    name: '王**  ',
                                    sex: 'male',
                                    id: '1',
                                    text: '终于有编制筛选了，对于想找事业编的人来说很方便，后续如果能出个跟老师线上沟通的功能就好了',
                                    icon1: '博士',
                                    icon2: '机械工程',
                                    icon3: '华南理工大学'
                                }
                            ],
                            jobImgList: [
                                {
                                    img: 'assets/compete/mate.png'
                                },
                                {
                                    img: 'assets/compete/job-heat.png'
                                },
                                {
                                    img: 'assets/compete/ability.png'
                                }
                            ],
                            announcementImgList: [
                                {
                                    img: 'assets/compete/heat.png'
                                },
                                {
                                    img: 'assets/compete/job-draw.png'
                                }
                            ]
                        }
                    },
                    methods: {
                        jobTabClick(index) {
                            this.jobIndex = index
                        },
                        announcementTabClick(index) {
                            this.announcementIndex = index
                        },
                        openpayDialog(id, index) {
                            let api = '/api/person/resume-equity-package/get-activity-popup-package-list'
                            window.globalComponents.PayDialogAlertComponent.show(api, id, index)
                        },
                        show(id) {
                            this.orderLoading = true
                            // 请求数据
                            httpGet('/api/person/resume-equity-package/get-activity-buy-package-list?equityPackageCategoryId=' + id).then((r) => {
                                this.orderList = r
                                this.orderLoading = false
                            })
                        }
                    },
                    mounted() {
                        this.show(1)
                    }
                }

                Vue.createApp(component).use(ElementPlus).mount('#component')
            })
        </script>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <!-- 底部测试滚动使用 -->
        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/index.html" target="_blank">关于我们</a>| <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/contactUs.html">联系我们</a>|
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/joinUs.html" target="_blank">人才招聘</a>| <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html">产品服务</a>|
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/disclaimers.html" target="_blank">免责声明</a>| <a href="/data/sitemap.html" target="_blank">网站导航</a>|
                <a href="//www.gaoxiaojob.com/zhaopin/zhuanti/zzzm2021/index.html" target="_blank">资质证明</a>|
                <a href="//gaocai.gaoxiaojob.com" target="_blank">高才科技官网</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2023 高校人才网 版权所有 网站备案信息：
                    <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13048400号</a>
                    粤公网安备：
                    <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010602004138" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="//zt.gaoxiaojob.com/zzdxywjyxkz.jpg" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>
    </body>
</html>
