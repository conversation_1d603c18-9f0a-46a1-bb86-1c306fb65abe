<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>公共弹窗组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/dialogAlert.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script type="text/javascript" src="https://ssl.captcha.qq.com/TCaptcha.js"></script>
    </head>

    <body>
        <div id="successDialogAlert" class="success-dialog-alert-template" v-cloak>
            <el-dialog v-model="wechatVisible" @close="handelWxClose">
                <div class="wechat-container" v-if="showWechatCode">
                    <div class="tips">
                        <strong>职位投递成功！</strong>
                    </div>
                    <div class="wechat-text">
                        <div class="qr-code" v-loading="loading">
                            <img :src="urCodeUrl" alt="" />
                            <img class="logo" src="https://img.gaoxiaojob.com/uploads/static/image/logo/logo_square.png" alt="" />
                        </div>
                        <div class="text-right">
                            <span>微信扫码关注【高校人才网服务号】</span>
                            <p>求职效率翻倍</p>
                            <div class="select">实时接收投递反馈</div>
                            <div class="select">精彩活动抢先知晓</div>
                        </div>
                    </div>

                    <!-- <div class="wechat-footer ft14 color-basic">
                        <div class="tips">
                            <span>{{ applySuccessMsg }}</span>
                        </div>
                    </div> -->
                    <!-- <img class="recommend" src="assets/icon/recommend.png" alt="" /> -->
                    <div class="recommend">为你推荐</div>
                    <div class="relogin-content">
                        <div class="analyse" @click="openAnalysis">
                            <img src="assets/icon/deliver-analyse.png" alt="" />
                            <div class="right-content">
                                <p>求职快</p>
                                <div class="text-analyse">
                                    <span>想加快求职进程？</span>
                                    <div class="see-analysis">去置顶投递</div>
                                </div>
                            </div>
                        </div>
                        <a href="vip.html" target="_blank" rel="VIP介绍">
                            <div class="gaocai-vip">
                                <img src="assets/icon/deliver-vip.png" alt="" />
                                <div class="right-content">
                                    <p>高才VIP</p>
                                    <div class="text-vip">
                                        <span>11+项求职特权</span>
                                        <span class="open">立即解锁</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="wechat-container">
                    <div class="tips">
                        <strong>职位投递成功！</strong>
                    </div>
                    <div class="relogin-content">
                        <div class="analyse" @click="openAnalysis">
                            <img src="assets/icon/deliver-analyse.png" alt="" />
                            <div class="right-content">
                                <p>求职快</p>
                                <div class="text-analyse">
                                    <span>想加快求职进程？</span>
                                    <div class="see-analysis">去置顶投递</div>
                                </div>
                            </div>
                        </div>
                        <a href="vip.html" target="_blank" rel="VIP介绍">
                            <div class="gaocai-vip">
                                <img src="assets/icon/deliver-vip.png" alt="" />
                                <div class="right-content">
                                    <p>高才VIP</p>
                                    <div class="text-vip">
                                        <span>11+项求职特权</span>
                                        <span class="open">立即解锁</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </el-dialog>
        </div>

        <script>
            $(function () {
                const successDialogAlertOptions = {
                    data() {
                        return {
                            successVisible: false,
                            wechatVisible: false,
                            showWechatCode: true,
                            applySuccessMsg: '',
                            params: {
                                apiPull: '/api/person/job/check-generate-report',
                                apiCreate: '/api/person/job/create-report',
                                param: { jobId: '' }
                            },
                            urCodeUrl: 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQE57zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyZWduUmNqZjlmR0QxZ2JTRE5BY3kAAgTfNGdkAwQsAQAA'
                        }
                    },

                    methods: {
                        showSuccessDialogAlert(successData, jobId, callback = () => {}) {
                            const { wxBindQrCodeImageUrl, applySuccessMsg } = successData
                            this.params.param.jobId = jobId
                            this.applySuccessMsg = applySuccessMsg
                            if (wxBindQrCodeImageUrl) {
                                this.urCodeUrl = wxBindQrCodeImageUrl
                                this.showWechatCode = true
                            }
                            this.wechatVisible = true
                            callback()
                        },
                        removeWechat() {
                            this.wechatVisible = false
                        },
                        handelWxClose() {
                            this.wechatVisible = false
                        },
                        openAnalysis() {
                            window.globalComponents.PromptDialogComponent.pull(this.params)
                            this.wechatVisible = false
                        }
                    },
                    mounted() {
                        this.wechatVisible = true
                    }
                }

                const SuccessDialogAlertComponent = Vue.createApp(successDialogAlertOptions).use(ElementPlus).mount('#successDialogAlert')

                window.globalComponents = { ...window.globalComponents, SuccessDialogAlertComponent }
            })
        </script>
    </body>
</html>
