<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>单位详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/unCooperationCompanyDetail.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div class="el-main">
            <div class="detail-container">
                <section id="companyTemplate" class="section">
                    <div class="main-header">
                        <div class="header-content">
                            <section>
                                <h1>湖南师范大学</h1>
                                <div class="tags" id="tagsTemplate">
                                    <span>985</span>
                                    <span>211</span>
                                    <span>一流大学</span>

                                    <el-popover placement="bottom" :width="430" trigger="hover" v-cloak>
                                        <template #reference>
                                            <span>
                                                <i class="el-icon boon-more" style="--font-size: 12px">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                        <path
                                                            fill="currentColor"
                                                            d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                        ></path>
                                                    </svg>
                                                </i>
                                            </span>
                                        </template>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">五险一金</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">绩效奖金</span>
                                    </el-popover>
                                </div>
                            </section>

                            <aside class="wait-auth">
                                <button class="el-button el-button--primary company-collect-button">
                                    <span>立即关注</span>
                                </button>
                                <!-- <button class="el-button el-button--default company-collect-button">
                                        <span>已关注</span>
                                    </button> -->
                            </aside>
                        </div>

                        <div class="header-main">
                            <div class="header-tabs" @click="handleActive">
                                <span class="tab-pane-common tab-pane intro">单位介绍</span>
                                <span class="tab-pane-common tab-pane notice active">
                                    <div>招聘公告<span> (17)</span></div>
                                </span>
                                <span class="tab-pane-common tab-pane position">
                                    <div>在招职位<span> (17)</span></div>
                                </span>
                                <span class="tab-pane-common tab-pane contact">联系方式</span>
                            </div>
                        </div>
                    </div>

                    <div class="tabs-common detail-content">
                        <div class="tab-content intro">
                            <pre>
                                    清华大学的前身清华学堂始建于1911年，1912年更名为清华学校。1928年更名为国立清华大学。1937年抗日战争全面爆发后南迁长沙，与北京大学、南开大学组建国立长沙临时大学，1938年迁至昆明改名为国立西南联合大学。1946年迁回清华园，设有文、法、理、工、农等5个学院、26个系。

                                    1952年全国高等学校院系调整后，清华大学成为一所多科性工业大学，重点为国家培养工程技术人才，被誉为“红色工程师的摇篮”。改革开放以来，清华大学逐步确立了建设世界一流大学的长远目标，进入了蓬勃发展的新时期。学校先后恢复或新建了理科、经济、管理和文科类学科，并成立了研究生院和继续教育学院。

                                    1999年，与中央工艺美术学院合并成立清华大学美术学院。2012年，原中国人民银行研究生部并入，成为清华大学五道口金融学院。在国家和社会的大力支持下，通过实施“211工程”“985工程”，开展“双一流”建设。</pre
                            >
                        </div>

                        <div class="tab-content notice active">
                            <h4 class="detail-subtitle">招聘公告</h4>

                            <div class="filter">
                                <el-select :class="{'is-select': noticeData.areaId}" v-model="noticeData.areaId" :clearable="true" placeholder="地区">
                                    <el-option v-for="item in announcementAreaOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': noticeData.jobType}" v-model="noticeData.jobType" :clearable="true" placeholder="职位类型">
                                    <el-option v-for="item in announceJobCategoryList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': noticeData.majorId}" v-model="noticeData.majorId" :clearable="true" placeholder="专业">
                                    <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': noticeData.educationId}" v-model="noticeData.educationId" :clearable="true" placeholder="学历">
                                    <el-option v-for="item in educationOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>
                            </div>

                            <ul v-if="hasNotice" class="result">
                                <li class="item offline-mark">
                                    <a href="/" target="_blank">
                                        <section class="offline-gray-first">
                                            <h5><span class="offline-tag">已下线</span>湖南师范大学2021年度招聘公告</h5>

                                            <div class="tips">
                                                <span>共26个职位</span>
                                                <span>招2人</span>
                                            </div>
                                        </section>

                                        <aside class="offline-gray-second">
                                            <span class="ft14">0201发布</span>
                                            <span>上海</span>
                                        </aside>
                                    </a>
                                </li>

                                <li class="item">
                                    <a href="/" target="_blank">
                                        <section class="offline-gray-first">
                                            <h5><span class="offline-tag">已下线</span>湖南师范大学2021年度招聘公告</h5>

                                            <div class="tips">
                                                <span>共26个职位</span>
                                                <span>招2人</span>
                                            </div>
                                        </section>

                                        <aside class="offline-gray-second">
                                            <span class="ft14">0201发布</span>
                                            <span>上海</span>
                                        </aside>
                                    </a>
                                </li>
                            </ul>

                            <el-empty v-else description="暂无数据"></el-empty>

                            <el-pagination
                                background
                                :layout="'total, sizes, prev, pager, next, jumper'"
                                v-model:current-page="noticePagination.page"
                                v-model:page-size="noticePagination.size"
                                :total="noticePagination.total"
                            >
                            </el-pagination>
                        </div>

                        <div class="tab-content position">
                            <h4 class="detail-subtitle">招聘职位</h4>

                            <div class="filter">
                                <el-select :class="{'is-select': positionData.areaId}" v-model="positionData.areaId" :clearable="true" placeholder="地区">
                                    <el-option v-for="item in jobAreaOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': positionData.jobType}" v-model="positionData.jobType" :clearable="true" placeholder="职位类型">
                                    <el-option v-for="item in jobCategoryList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': positionData.majorId}" v-model="positionData.majorId" :clearable="true" placeholder="专业">
                                    <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': positionData.educationId}" v-model="positionData.educationId" :clearable="true" placeholder="学历">
                                    <el-option v-for="item in educationOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>
                            </div>

                            <ul v-if="hasPosition" class="result">
                                <li class="item">
                                    <a href="/" target="_blank">
                                        <section class="offline-gray-first">
                                            <div class="info">
                                                <h5><span class="offline-tag">已下线</span>湖南师范大学2021年度招聘公告</h5>
                                                <span class="date">7-5发布</span>
                                            </div>

                                            <div class="tips">
                                                <span>经验不限</span>
                                                <span>博士及以上</span>
                                                <span>招2人</span>
                                            </div>
                                        </section>

                                        <aside class="offline-gray-second">
                                            <span class="ft14 color-salary">10k</span>
                                            <span>广东-广州</span>
                                        </aside>
                                    </a>

                                    <a href="#" target="_blank" class="announcement-detail offline-gray-first">中国科学院分子细胞科学卓越创新中心招聘公告中心招聘公告中心...</a>
                                </li>

                                <li class="item offline-mark">
                                    <a href="/" target="_blank">
                                        <section class="offline-gray-first">
                                            <div class="info">
                                                <h5><span class="offline-tag">已下线</span>湖南师范大学2021年度招聘公告</h5>
                                                <span class="date">7-5发布</span>
                                            </div>

                                            <div class="tips">
                                                <span>经验不限</span>
                                                <span>博士及以上</span>
                                                <span>招2人</span>
                                            </div>
                                        </section>

                                        <aside class="offline-gray-second">
                                            <span class="ft14 color-salary">10k</span>
                                            <span>广东-广州</span>
                                        </aside>
                                    </a>
                                    <a href="#" target="_blank" class="announcement-detail offline-gray-first">中国科学院分子细胞科学卓越创新中心招聘公告中心招聘公告中心...</a>
                                </li>
                            </ul>

                            <el-empty v-else description="暂无数据"></el-empty>

                            <el-pagination
                                background
                                :layout="'total, sizes, prev, pager, next, jumper'"
                                v-model:current-page="positionPagination.page"
                                v-model:page-size="positionPagination.size"
                                :total="positionPagination.total"
                            >
                            </el-pagination>
                        </div>

                        <div class="tab-content contact">
                            <h4 class="detail-subtitle">联系我们</h4>

                            <div class="contact-main">
                                <ul class="box">
                                    <li class="user">
                                        <span>联系人：李老师</span>
                                    </li>
                                    <li class="mobile">
                                        <span>联系方式：010-25665778</span>
                                        <span>传真：010-25665778</span>
                                    </li>
                                    <li class="addr">
                                        <span>单位地址：湖南省长沙市平安区大学城 南路16号湖南省长沙市平安区大学城 南路16号湖南省长沙市平安区大学城 南路16号</span>
                                    </li>
                                    <li class="site">
                                        <span>官网：<a href="http://www.gaoxiaojob.com/" target="_blank">http://www.gaoxiaojob.com/</a></span>
                                    </li>
                                </ul>
                            </div>

                            <h4 class="detail-subtitle">二级院校</h4>

                            <ul class="contact-other">
                                <li class="item">
                                    <h6>经管系</h6>
                                    <p>联系人：李老师</p>
                                    <p>
                                        <span>电话：010-2568956</span>
                                        <span>传真：010-2568956</span>
                                    </p>
                                </li>
                                <li class="item">
                                    <h6>经管系</h6>
                                    <p>联系人：李老师</p>
                                    <p>
                                        <span>电话：010-2568956</span>
                                        <span>传真：010-2568956</span>
                                    </p>
                                </li>
                                <li class="item">
                                    <h6>经管系</h6>
                                    <p>联系人：李老师</p>
                                    <p>
                                        <span>电话：010-2568956</span>
                                        <span>传真：010-2568956</span>
                                    </p>
                                </li>
                                <li class="item">
                                    <h6>经管系</h6>
                                    <p>联系人：李老师</p>
                                    <p>
                                        <span>电话：010-2568956</span>
                                        <span>传真：010-2568956</span>
                                    </p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>

                <aside class="aside">
                    <div class="detail-basic">
                        <div class="basic-data">
                            <img src="http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png" alt="" class="logo" />
                            <div class="name">
                                <h5>湖南师范大学</h5>
                                <h6>Hunan Normal University</h6>
                            </div>
                        </div>

                        <div class="basic-info">
                            <span class="category">教育/培训/院校</span>
                            <span class="nature"> <i>高等院校</i><i>公立</i> </span>
                            <span class="scale">1000人以上</span>
                            <span class="site"><a href="http://www.gaoxiaojob.com/" target="_blank">http://www.gaoxiaojob.com/</a></span>
                        </div>
                    </div>

                    <div class="same-wrapper">
                        <div class="title">同类型雇主职位推荐</div>

                        <div class="swiper same-swiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国</a>
                                            <div class="address">乌鲁</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                    <div class="item">
                                        <div class="top">
                                            <a href="" class="name" target="_blank">海外优青机械与汽车工程学院海外优青机械与汽车工程学院</a>
                                            <div class="salary">80-100万/年</div>
                                        </div>
                                        <div class="middle">3-5年 | 博士研究生 | 招若干个</div>
                                        <div class="bottom">
                                            <a href="" class="company" target="_blank">华南理工大学广州国际广州国...</a>
                                            <div class="address">乌鲁木齐乌鲁木齐...</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-pagination same-pagination"></div>
                        </div>
                    </div>

                    <div class="to-miniprogram"></div>
                </aside>
            </div>
        </div>

        <script src="./lib/jquery-throttle-debounce/jquery-throttle-debounce.min.js"></script>
        <script src="./lib/popper/popper.min.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>

        <script>
            $(function () {
                Vue.createApp({}).use(ElementPlus).mount('#tagsTemplate')

                const companyOptions = {
                    data() {
                        return {
                            companyId: '',
                            activeName: 'intro',

                            announcementAreaOptions: [
                                { label: '广州', value: '1' },
                                { label: '深圳', value: '2' }
                            ],

                            announceJobCategoryList: [{ label: '技术', value: '1' }],

                            majorOptions: [
                                { label: '毛泽东理论', value: '1' },
                                { label: '计算机应用', value: '2' }
                            ],

                            educationOptions: [
                                { label: '不限', value: '1' },
                                { label: '大专', value: '2' },
                                { label: '本科', value: '3' },
                                { label: '博士', value: '4' }
                            ],

                            jobAreaOptions: [
                                { label: '广州', value: '1' },
                                { label: '深圳', value: '2' }
                            ],

                            jobCategoryList: [{ label: '技术', value: '1' }],

                            noticeApi: '/api/person/company/get-announcement-list',
                            positionApi: '/api/person/company/get-job-list',

                            noticeData: {
                                areaId: '',
                                jobType: '',
                                majorId: '',
                                educationId: ''
                            },

                            noticePagination: {
                                page: 1,
                                size: 20,
                                total: 100
                            },

                            positionData: {
                                areaId: '',
                                jobType: '',
                                majorId: '',
                                educationId: ''
                            },

                            positionPagination: {
                                page: 1,
                                size: 20,
                                total: 100
                            }
                        }
                    },

                    computed: {
                        hasNotice() {
                            return this.noticePagination.total !== 0
                        },
                        hasPosition() {
                            return this.positionPagination.total !== 0
                        }
                    },

                    mounted() {
                        this.handleHash()
                    },

                    watch: {
                        noticeData: {
                            handler(val) {
                                this.handleFilter('notice')
                            },
                            deep: true
                        },

                        noticePagination: {
                            handler(val) {
                                this.handleFilter('notice')
                            },
                            deep: true
                        },

                        positionData: {
                            handler(val) {
                                this.handleFilter('position')
                            },
                            deep: true
                        },

                        positionPagination: {
                            handler(val) {
                                this.handleFilter('position')
                            },
                            deep: true
                        }
                    },

                    methods: {
                        handleActive(ev) {
                            const $el = $(ev.target)
                            const $target = $el.hasClass('tab-pane-common') ? $el : $el.parents('.tab-pane-common')
                            const $content = $('.tabs-common').find('.tab-content')

                            if ($target.hasClass('tab-pane-common')) {
                                $target.siblings().removeClass('active')
                                $content.removeClass('active')
                                $target.addClass('active')
                                $content.eq($target.index()).addClass('active')

                                const pattern = /tab-pane-common\s(\w+)\s?/
                                const className = $target.attr('class')

                                if (pattern.test(className)) {
                                    this.activeName = RegExp.$1
                                }
                            }
                        },

                        handleHash() {
                            var hash = location.hash
                            var key

                            if (hash) {
                                key = hash.replace('#', '')
                                var option = {
                                    job: 'position',
                                    announcement: 'notice'
                                }
                                if (!option[key]) return

                                key = option[key]

                                $('.header-tabs')
                                    .find('.' + key)
                                    .addClass('active')
                                    .siblings()
                                    .removeClass('active')

                                $('.detail-content')
                                    .find('.' + key)
                                    .addClass('active')
                                    .siblings()
                                    .removeClass('active')
                            }
                        },

                        handleFilter(target) {
                            const { jobType: jobCategoryId, ...data } = this[`${target}Data`]
                            const { page, size: pageSize } = this[`${target}Pagination`]
                            const { companyId } = this
                            const api = this[`${target}Api`]
                            const query = { ...data, jobCategoryId, page, pageSize, companyId }

                            httpGet(api, query).then((res) => {
                                const { list, total } = res

                                $(`${target}`).find('.result').html(list)
                                this[`${target}Pagination`]['total'] = total
                            })
                        }
                    }
                }

                Vue.createApp(companyOptions)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#companyTemplate')

                var id = "<?= $info['companyId']?>"
                var $collectButtons = $('.company-collect-button')

                // get miniprogram qrcode
                var miniprogramSlot = $('.to-miniprogram')

                httpGet('/company/get-detail-mini-code?id=' + id).then(function (data) {
                    miniprogramSlot.append($('<img src="' + data.url + '" />'))
                })

                var swiper = new Swiper('.same-swiper', {
                    loop: true,
                    autoplay: {
                        delay: 5000
                    },
                    pagination: {
                        clickable: true,
                        el: '.same-pagination'
                    }
                })

                $collectButtons.on('click', function () {
                    var $this = $(this)
                    var isCollected = $this.hasClass('el-button--default')

                    httpPost('/api/person/company/collect', { companyId: id }).then(function () {
                        if (isCollected) {
                            $this.removeClass('el-button--default').addClass('el-button--primary').find('span').text('立即关注')
                        } else {
                            $this.removeClass('el-button--primary').addClass('el-button--default').find('span').text('已关注')
                        }
                    })
                })

                // 侧边导航栏---->column.js
                !(function () {
                    var nav = $('.el-header').height()
                    var flag = true
                    $(window).on(
                        'scroll',
                        $.throttle(300, function () {
                            if (flag) {
                                $('.el-main .w').each(function (i, ele) {
                                    var offsetTop = parseInt($(ele).offset().top)
                                })
                            }
                        })
                    )
                })()
            })
        </script>
    </body>
</html>
