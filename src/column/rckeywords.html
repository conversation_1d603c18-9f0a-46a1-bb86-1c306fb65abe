<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>职位中心</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/rckeywords.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div class="el-main">
            <div class="header-container">
                <header>
                    <h1>天津地铁招聘</h1>
                    <span>高校人才网为您准备了高校、科研等企事业单位{当前年；需要实时获取}年招聘信息汇总，帮您了解关键词招聘岗位相关信息</span>
                </header>
            </div>

            <div class="job-container">
                <div class="job-content">
                    <!-- 判断job-list为空 -->
                    <div class="job-empty">
                        <div class="empty">
                            暂无相关职位，请修改筛选条件试试

                            <a class="view-more-job" href="/job">查看更多 <span>关键词</span> 招聘职位</a>
                        </div>

                        <div class="empty-bottom">
                            <div class="recommend-title">更多职位推荐</div>
                            <a class="more" href="/job">查看更多></a>
                        </div>
                    </div>

                    <ul class="job-list">
                        <li class="job-card">
                            <div class="job-body">
                                <div class="job-detail">
                                    <a class="job-name" href="/job.html" target="_blank">
                                        湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院---湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院
                                    </a>
                                    <div class="job-basic">
                                        <span class="job-salary">15K-25K/月</span>
                                        <span class="job-info">江苏-苏州 | 博士研究生 | 招3人</span>
                                        <div class="job-welfare">
                                            <span class="tag">带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假带薪年假</span>
                                            <span class="tag">五险一金</span>
                                            <span class="tag">节日福利</span>
                                        </div>
                                    </div>
                                    <a class="job-announcement" target="_blank" href="">
                                        中国科学院分子细胞科学卓越创新中心邹卫国研究组2022年招聘博士后/副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副</a
                                    >
                                </div>
                                <button class="job-button job-apply" data-id="12">申请职位</button>
                                <!-- <button class="job-button job-applied" disabled>已申请</button>
                            <button class="job-button job-offline" disabled>已下线</button> -->
                            </div>
                            <div class="job-footer">
                                <div class="company-basic">
                                    <a class="company-name" href="" target="_blank">中国科学院上海微系统与信息技科学院上海信息技科学院中国科学院上海微系统与信息技科学院上海信息技科学院</a>
                                    <div class="company-info">科研院所 | 公立（国有）</div>
                                </div>
                                <div class="release-date">09-07发布</div>
                            </div>
                        </li>
                        <li class="job-card">
                            <div class="job-body">
                                <div class="job-detail">
                                    <a class="job-name" href="" target="_blank">
                                        湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院---湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院
                                    </a>
                                    <div class="job-basic">
                                        <span class="job-salary">15K-25K/月</span>
                                        <span class="job-info">江苏-苏州 | 博士研究生 | 招3人</span>
                                        <div class="job-welfare">
                                            <span class="tag">带薪年假</span>
                                            <span class="tag">五险一金</span>
                                            <span class="tag">节日福利</span>
                                        </div>
                                    </div>
                                    <!-- <a class="job-announcement" target="_blank" href="">
                                    中国科学院分子细胞科学卓越创新中心邹卫国研究组2022年招聘博士后/副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副</a> -->
                                </div>
                                <button class="job-button job-apply" data-id="12">申请职位</button>
                            </div>
                            <div class="job-footer">
                                <div class="company-basic">
                                    <a class="company-name" href="" target="_blank">中国科学院上海微系统与信息技科学院上海信息技科学院中国科学院上海微系统与信息技科学院上海信息技科学院</a>
                                    <div class="company-info">科研院所 | 公立（国有）</div>
                                </div>
                                <div class="release-date">09-07发布</div>
                            </div>
                        </li>
                        <li class="job-card">
                            <div class="job-body">
                                <div class="job-detail">
                                    <a class="job-name" href="" target="_blank">
                                        湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院---湖北商贸学院招聘教师湖北商贸学院商贸学学院学院湖北商贸学院招聘教师师学院学院学院
                                    </a>
                                    <div class="job-basic">
                                        <span class="job-salary">15K-25K/月</span>
                                        <span class="job-info">江苏-苏州 | 博士研究生 | 招3人</span>
                                        <div class="job-welfare">
                                            <span class="tag">带薪年假</span>
                                            <span class="tag">五险一金</span>
                                            <span class="tag">节日福利</span>
                                        </div>
                                    </div>
                                    <a class="job-announcement" target="_blank" href="">
                                        中国科学院分子细胞科学卓越创新中心邹卫国研究组2022年招聘博士后/副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副副研究员副研究副</a
                                    >
                                </div>
                                <button class="job-button job-apply" data-id="12">申请职位</button>
                            </div>
                            <div class="job-footer">
                                <div class="company-basic">
                                    <a class="company-name" href="" target="_blank">中国科学院上海微系统与信息技科学院上海信息技科学院中国科学院上海微系统与信息技科学院上海信息技科学院</a>
                                    <div class="company-info">科研院所 | 公立（国有）</div>
                                </div>
                                <div class="release-date">09-07发布</div>
                            </div>
                        </li>
                    </ul>

                    <a class="view-more-job" href="/job">查看更多 <span>关键词</span> 招聘职位</a>
                </div>

                <aside class="aside-content">
                    <!-- 登录及登录引导模块 start -->
                    <!-- guideCard.html -->
                    <!-- loginAside.html -->
                    <!-- 登录及登录引导模块 end -->

                    <div class="advert-vip" v-if="!isLogin || !isVip" @click="openVip">
                        <img src="assets/vip/advert-vip.png" alt="" />
                    </div>

                    <div class="ad-content">
                        <a href="" target="_blank">
                            <img src="http://img.gaoxiaojob.com/xbwyC1_18.jpg" alt="" />
                        </a>
                        <a href="" target="_blank">
                            <img src="http://img.gaoxiaojob.com/xbwyC1_18.jpg" alt="" />
                        </a>
                    </div>

                    <div class="job-recommendation">
                        <div class="title">
                            <h5>相关招聘</h5>
                        </div>

                        <div class="keywords-list">
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                        </div>
                    </div>

                    <div class="job-recommendation">
                        <div class="title">
                            <h5>更多招聘</h5>
                        </div>

                        <div class="keywords-list">
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                            <a href="" class="keyword">北大</a>
                        </div>
                    </div>
                </aside>
            </div>

            <div class="footer-link-container">
                <div class="footer-link-content">
                    <div class="footer-link-for-spider">
                        <div class="tab-nav">
                            <span class="is-active">热门岗位</span>
                            <span>附近职位</span>
                            <span>相似职位</span>
                            <span>热门搜索</span>
                            <span>职位百科</span>
                        </div>

                        <div class="tab-pane">
                            <div class="pane is-active">
                                <a href="" target="_blank">热门地区</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">热门学科</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">相似职位</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">热门搜索</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>

                            <div class="pane">
                                <a href="" target="_blank">职位百科</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">广州高校人才网</a>
                                <a href="" target="_blank">深圳高校人才网</a>
                                <a href="" target="_blank">呼和浩特高校人才网</a>
                                <a href="" target="_blank">乌鲁木齐高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                                <a href="" target="_blank">上海高校人才网</a>
                            </div>
                        </div>
                    </div>

                    <script>
                        $(function () {
                            $linkForSpider = $('.footer-link-for-spider')
                            $nav = $linkForSpider.find('.tab-nav span')
                            $pane = $linkForSpider.find('.tab-pane .pane')
                            activeClassName = 'is-active'

                            $nav.on('click', function () {
                                $(this).addClass(activeClassName).siblings().removeClass(activeClassName)
                                $pane.eq($(this).index()).addClass(activeClassName).siblings().removeClass(activeClassName)
                            })
                        })
                    </script>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                var isLogin = 0
                var isVip = 0
                // 申请职位
                $('html body').on('click', '.job-apply', function () {
                    var $this = $(this)
                    var jobId = $this.attr('data-id')
                    window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                        $this.prop('disabled', true).addClass('job-applied').text('已申请')
                    })
                })

                function handleShowLogin() {
                    window.globalComponents.loginDialogComponent.showLoginDialog()
                }

                function openVip() {
                    const { isLogin, isVip } = this
                    if (!isLogin) {
                        this.handleShowLogin()
                        return false
                    } else {
                        window.open('/vip.html', '_blank')
                    }
                }
            })
        </script>
    </body>
</html>
