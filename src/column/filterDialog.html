<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>选择组件</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/filterDialog.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
    </head>

    <body>
        <div class="filter-dialog-template">
            <button class="el-button el-button--primary el-button dialog-target" type="button" data-target="#filterDialog">
                <span>确定</span>
            </button>

            <div class="el-overlay" id="filterDialog" style="z-index: 2003">
                <div class="el-overlay-dialog">
                    <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
                        <div class="el-dialog__header">
                            <div class="filter-header">
                                <div class="title">请选择职位类型</div>
                                <!-- <div class="tips">
                                (最多选择<span>2</span>项)
                            </div> -->
                                <div class="search" id="searchTemplate">
                                    <el-select
                                        v-model="filterHref"
                                        :remote-method="handleSearch"
                                        filterable
                                        remote
                                        reserve-keyword
                                        :size="size"
                                        placeholder="请输入关键词"
                                        @change="handleSearchChange"
                                    >
                                        <el-option v-for="item in filterOptions" :key="item.k" :label="item.v" :value="item.href" />
                                    </el-select>
                                </div>
                            </div>
                            <button class="el-dialog__headerbtn close-dialog" type="button">
                                <i class="el-dialog__close el-icon el-icon-close"></i>
                            </button>
                        </div>
                        <div class="el-dialog__body">
                            <div class="filter-content">
                                <div class="data-content">
                                    <div class="left-content">
                                        <span class="list">正高级职称</span>
                                        <span class="list active">副高级职称</span>
                                        <span class="list has-select">中级职称</span>
                                        <span class="list">初级职称</span>
                                    </div>
                                    <div class="right-content">
                                        <div class="second-content column-4">
                                            <div class="second-item">
                                                <a href="">教授级高级兽医师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">教授级高级畜牧师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任医师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任药师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任护师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任技师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级经济师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级会计师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级审计师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级统计师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">高级记者</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">高级编辑</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">编审</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">研究馆员</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">播音指导</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">译审</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">一级律师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">一级公证员</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">其他正高级职称</a>
                                            </div>
                                        </div>
                                        <div class="second-content column-4 show">
                                            <div class="second-item">
                                                <a href="">教授</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级讲师</a>
                                            </div>
                                            <div class="second-item">
                                                <a class="active" href="">正高级实习指导教师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级教师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">研究员</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级工程师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级实验师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">教授级高级农艺师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">教授级高级兽医师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">教授级高级畜牧师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任医师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任药师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任护师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">主任技师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级经济师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级会计师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级审计师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">正高级统计师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">高级记者</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">高级编辑</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">编审</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">研究馆员</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">播音指导</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">译审</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">一级律师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">一级公证员</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">其他正高级职称</a>
                                            </div>
                                        </div>
                                        <div class="second-content column-4">
                                            <div class="second-item">
                                                <a href="">教授级高级农艺师</a>
                                            </div>
                                        </div>
                                        <div class="second-content column-4">
                                            <div class="second-item">
                                                <a href="">正高级实验师</a>
                                            </div>
                                            <div class="second-item">
                                                <a href="">教授级高级农艺师</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--v-if-->
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                $('.dialog-target').on('click', function () {
                    $('body').addClass('el-popup-parent--hidden')
                    var target = $(this).attr('data-target')
                    $(target).fadeIn()
                })
                $('.close-dialog').on('click', function () {
                    $('body').removeClass('el-popup-parent--hidden')
                    $(this).parents('.el-overlay').fadeOut()
                })

                $('.filter-content .left-content .list').on('click', function () {
                    $(this).addClass('active').siblings().removeClass('active')

                    var index = $(this).index()
                    var $childEl = $('.filter-content .right-content .second-content')
                    $childEl.eq(index).addClass('show').siblings().removeClass('show')
                })
            })
        </script>

        <script>
            $(function () {
                const searchOptions = {
                    data() {
                        return {
                            size: 'small',

                            filterHref: '',
                            options: [
                                {
                                    k: '5',
                                    code: '5',
                                    v: '教授',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/11'
                                },
                                {
                                    k: '6',
                                    code: '6',
                                    v: '正高级讲师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/22'
                                },
                                {
                                    k: '7',
                                    code: '7',
                                    v: '正高级实习指导教师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/33'
                                },
                                {
                                    k: '8',
                                    code: '8',
                                    v: '正高级教师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/44'
                                },
                                {
                                    k: '9',
                                    code: '9',
                                    v: '研究员',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/55'
                                },
                                {
                                    k: '10',
                                    code: '10',
                                    v: '正高级工程师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/66'
                                },
                                {
                                    k: '11',
                                    code: '11',
                                    v: '正高级实验师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/77'
                                },
                                {
                                    k: '12',
                                    code: '12',
                                    v: '教授级高级农艺师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/88'
                                },
                                {
                                    k: '13',
                                    code: '13',
                                    v: '教授级高级兽医师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/99'
                                },
                                {
                                    k: '14',
                                    code: '14',
                                    v: '教授级高级畜牧师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/10'
                                },
                                {
                                    k: '15',
                                    code: '15',
                                    v: '主任医师',
                                    parentId: '268',
                                    level: '2',
                                    href: 'baidu.com/aaa/bbb/12'
                                }
                            ],

                            filterOptions: []
                        }
                    },

                    mounted() {},

                    methods: {
                        handleSearch(query) {
                            if (query) {
                                const { options } = this
                                setTimeout(() => {
                                    const objMap = new Map()
                                    this.filterOptions = options.filter((item) => {
                                        const isIncludes = item.v.toLowerCase().includes(query.toLowerCase())
                                        const { v } = item
                                        const flag = isIncludes && !objMap.has(v)
                                        if (flag) objMap.set(v, 1)
                                        return flag
                                    })
                                }, 200)
                            } else {
                                this.filterOptions = []
                            }
                        },
                        handleSearchChange(href) {
                            window.location.href = href
                        }
                    }
                }

                const searchTemplate = Vue.createApp(searchOptions).use(ElementPlus).mount('#searchTemplate')
            })
        </script>
    </body>
</html>
