<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>报名参加</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/dbMeetingApply.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/datePicker.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <div class="activity-form-main" v-loading="loading">
                <div class="banner" v-if="details.backgroundUrl" :style="`background-image:url(${details.backgroundUrl})`"></div>

                <el-form class="form-container" label-position="top" size="small" :model="formData" :rules="formRules" ref="form">
                    <div class="form-header form-wrapper">
                        <h1 class="title">{{details.name}}</h1>
                        <div class="introduction" v-html="details.introduction"></div>
                    </div>

                    <template v-if="isLogin">
                        <div class="intention-choose form-wrapper">
                            <div class="common-title">
                                <span class="required">*</span>报名意向选择
                                <span class="required-tips">（暂无可报名活动':'至少选择1项）</span>
                            </div>
                            <div class="form-content">
                                <div v-if="details.intentExplain" class="tips">{{details.intentExplain}}</div>
                                <el-checkbox-group class="checkbox-group" v-model="formData.optionIds">
                                    <div class="checkbox-list" v-for="(item, index) in details.intentionOption">
                                        <el-checkbox :label="item.id" :disabled="oldOptionIds.includes(item.id)">
                                            <div class="checkbox-label">
                                                <div class="name">{{item.title}}</div>
                                                <span v-if="oldOptionIds.includes(item.id)" class="attended">已报名</span>
                                            </div>
                                        </el-checkbox>
                                        <div class="bottom">
                                            <a v-if="item.type === 3" class="detail" :href="item.link" target="_blank">{{item.linkVersion ? item.linkVersion : '查看详情'}}</a>
                                            <div v-if="item.type === 2" class="other">{{item.version}}</div>
                                        </div>
                                    </div>
                                </el-checkbox-group>
                            </div>
                        </div>

                        <div class="user-info">
                            <div v-show="isShowUserInfo">
                                <div class="basic-wrapper form-wrapper">
                                    <div class="common-title"><span class="required">*</span>基本信息</div>
                                    <div class="form-content">
                                        <el-form-item label="姓名" prop="name">
                                            <el-input v-model="formData.name" placeholder="请输入您的姓名" />
                                        </el-form-item>
                                        <el-form-item label="性别" prop="gender">
                                            <el-radio-group v-model="formData.gender">
                                                <el-radio label="1">男</el-radio>
                                                <el-radio label="2">女</el-radio>
                                            </el-radio-group>
                                        </el-form-item>
                                        <el-form-item label="出生日期" prop="birthday">
                                            <date-picker
                                                v-model="formData.birthday"
                                                :default-value="birthdayDefault"
                                                :disabled-date="birthdayDisabledDate"
                                                placeholder="请选择您的出生日期"
                                            ></date-picker>
                                        </el-form-item>
                                        <el-form-item label="户籍/国籍" prop="householdRegisterId">
                                            <select-dialog :list="allCityAreaList" v-model="formData.householdRegisterId" name="allNative" title="请选择您的户籍/国籍"></select-dialog>
                                        </el-form-item>
                                        <el-form-item label="手机" prop="mobile">
                                            <el-input v-model="formData.mobile" :readonly="!hasOnlineResume" placeholder="请输入您的手机号" />
                                        </el-form-item>
                                        <el-form-item label="邮箱" prop="email">
                                            <el-input v-model="formData.email" placeholder="用于接收信息、安全认证" />
                                        </el-form-item>
                                        <el-form-item label="政治面貌" prop="politicalStatusId">
                                            <el-select v-model="formData.politicalStatusId" placeholder="请选择您的政治面貌" clearable>
                                                <el-option v-for="(item, index) in politicalList" :label="item.v" :value="item.k" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="职称" prop="titleId">
                                            <select-dialog :list="titleList" v-model="formData.titleId" multiple :multiple-limit="3" isJoin name="title" title="请选择您的职称"></select-dialog>
                                        </el-form-item>
                                        <el-form-item label="现居住地" prop="residence">
                                            <select-dialog
                                                :list="allCityAreaList"
                                                v-model="formData.residence"
                                                name="allNative"
                                                placeholder="请选择您的现居住地"
                                                title="请选择现居住地"
                                            ></select-dialog>
                                        </el-form-item>
                                    </div>
                                </div>

                                <div class="education-wrapper form-wrapper">
                                    <div class="common-title"><span class="required">*</span>最高教育经历</div>
                                    <div class="form-content">
                                        <el-form-item label="学校名称" prop="school">
                                            <el-input v-model="formData.school" placeholder="请填写学校名称" />
                                        </el-form-item>
                                        <el-form-item label="二级院系（机构）名称" prop="college">
                                            <el-input v-model="formData.college" placeholder="请填写二级院系（机构）名称" />
                                        </el-form-item>

                                        <el-form-item class="form-item-checkbox">
                                            <el-checkbox class="checkbox-content" v-model="formData.isRecruitment" label="item.k" true-label="1" false-label="2">统招</el-checkbox>
                                            <el-checkbox class="checkbox-content" v-model="formData.isProjectSchool" label="item.k" true-label="1" false-label="2"> 985/211 </el-checkbox>
                                            <el-checkbox class="checkbox-content" v-model="formData.isAbroad" label="item.k" true-label="1" false-label="2"> 留学 </el-checkbox>
                                        </el-form-item>
                                        <el-form-item></el-form-item>

                                        <el-form-item label="学历水平" prop="educationId">
                                            <el-select v-model="formData.educationId" placeholder="请选择您的学历水平" clearable>
                                                <el-option v-for="(item, index) in educationList" :label="item.v" :value="item.k" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="就读时间" required>
                                            <div class="study-time">
                                                <el-form-item prop="beginDate">
                                                    <date-picker v-model="formData.beginDate" :disabled-date="studyBeginDisabledDate" placeholder="开始"></date-picker>
                                                </el-form-item>
                                                <span class="line"></span>
                                                <el-form-item prop="endDate">
                                                    <date-picker v-model="formData.endDate" :disabled-date="studyEndDisabledDate" :disabled="!formData['beginDate']" placeholder="结束"></date-picker>
                                                </el-form-item>
                                            </div>
                                        </el-form-item>
                                        <el-form-item v-if="majorType === 1 && showCustomMajorEducationIds.includes(formData.educationId)" label="所学专业" prop="majorCustom">
                                            <el-input v-model="formData.majorCustom" placeholder="请输入您的专业" />
                                        </el-form-item>
                                        <el-form-item v-else label="所学专业" prop="majorId">
                                            <select-dialog :list="majorList" v-model="formData.majorId" title="请选择您的专业" name="major"></select-dialog>
                                        </el-form-item>
                                        <el-form-item></el-form-item>
                                        <el-form-item v-show="showCustomMajorEducationIds.includes(formData.educationId)" class="form-item-checkbox">
                                            <el-checkbox class="checkbox-content" v-model="majorType" :true-label="1" :false-label="2">自定义录入</el-checkbox>
                                        </el-form-item>
                                    </div>
                                </div>

                                <div class="form-wrapper">
                                    <div class="common-title"><span class="required">*</span>求职意向</div>
                                    <div class="form-content">
                                        <el-form-item label="求职状态" prop="workStatus">
                                            <el-select v-model="formData.workStatus" placeholder="请选择求职状态" clearable>
                                                <el-option v-for="(item, index) in jobStatusList" :label="item.v" :value="item.k" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="到岗时间" prop="arriveDateType">
                                            <el-select v-model="formData.arriveDateType" placeholder="请选择到岗时间" clearable>
                                                <el-option v-for="(item, index) in arriveDateList" :label="item.v" :value="item.k" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="意向职位" prop="jobCategoryId">
                                            <select-dialog :list="categoryJobList" v-model="formData.jobCategoryId" title="请选择意向职位" name="categoryJob"></select-dialog>
                                        </el-form-item>
                                        <el-form-item label="工作性质" prop="natureType">
                                            <el-select v-model="formData.natureType" placeholder="请选择工作性质" clearable>
                                                <el-option v-for="(item, index) in natureList" :label="item.v" :value="item.k" />
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="意向城市" prop="areaId">
                                            <select-dialog :list="areaList" v-model="formData.areaId" title="请选择意向地区" multiple :multiple-limit="5" name="area" isJoin></select-dialog>
                                        </el-form-item>
                                        <el-form-item label="期望月薪" prop="wageType">
                                            <el-select v-model="formData.wageType" placeholder="请选择期望月薪" clearable>
                                                <el-option v-for="(item, index) in wageList" :label="item.v" :value="item.k" />
                                            </el-select>
                                        </el-form-item>
                                    </div>
                                </div>
                            </div>

                            <div class="switch-user-info">
                                <button :class="{reverse:isShowUserInfo}" type="button" @click="isShowUserInfo = !isShowUserInfo">{{isShowUserInfo ? '收起':'展开'}}个人信息模块</button>
                            </div>
                        </div>

                        <template v-for="(item, index) in showMessageList">
                            <div v-if="item.id === 1" class="intention-type form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    您的意向就业单位类型
                                    <span class="required-tips">（至少选择1项）</span>
                                </div>
                                <div class="form-content">
                                    <el-checkbox-group class="checkbox-group" v-model="formData.unitType">
                                        <div class="checkbox-list" v-for="(item, index) in unitTypeList">
                                            <el-checkbox :label="item.k">
                                                <div class="checkbox-label">{{item.v}}</div>
                                            </el-checkbox>
                                        </div>
                                    </el-checkbox-group>
                                </div>
                            </div>

                            <div v-if="item.id === 2" class="channel form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    您是通过什么渠道了解到这场活动的？
                                    <span class="required-tips">（至少选择1项）</span>
                                </div>
                                <div class="form-content">
                                    <el-checkbox-group class="checkbox-group" v-model="formData.channels">
                                        <div class="checkbox-list" v-for="(item, index) in channelList">
                                            <el-checkbox :label="item.k">
                                                <div class="checkbox-label">{{item.v}}</div>
                                            </el-checkbox>
                                        </div>
                                    </el-checkbox-group>
                                </div>
                            </div>

                            <div v-if="item.id === 3" class="accept-recommend form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    您是否愿意让高校人才网的招聘顾问根据您的专业/意向就业单位类型/意向就业城市等将您的简历推荐给其他的相关用人单位，让您的求职快人一步抢占先机？
                                </div>
                                <div class="form-content">
                                    <el-radio-group v-model="formData.isShare">
                                        <el-radio :label="1">是</el-radio>
                                        <el-radio :label="2">否</el-radio>
                                    </el-radio-group>
                                </div>
                            </div>

                            <div v-if="item.id === 4" class="upload-resume form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    上传附件简历
                                    <span class="required-tips">（大小15M以内，最多5份）</span>
                                </div>
                                <div class="form-content">
                                    <div v-if="details.uploadInstructions" class="tips">{{details.uploadInstructions}}</div>
                                    <div class="file-content">
                                        <div class="file-select">
                                            <!-- /upload/activity -->
                                            <!-- http://gray.gcjob.jugaocai.com -->
                                            <el-upload
                                                class="upload-file"
                                                :show-file-list="false"
                                                action="/api/upload/activity"
                                                :on-success="handleUploadFile"
                                                :disabled="uploadResumeFileDisabled"
                                                :before-upload="handleBeforeUpload"
                                            >
                                                <el-button :disabled="uploadResumeFileDisabled" class="upload"><i class="upload-icon"></i>上传文件</el-button>
                                            </el-upload>

                                            <el-popover :visible="selectFileVisible" placement="bottom" :width="385" trigger="click" :append-to-body="false" v-if="resumeFileList.length>0">
                                                <template #reference>
                                                    <div class="select" @click="selectFileVisible = true">
                                                        选择已有附件
                                                        <i class="select-icon"></i>
                                                    </div>
                                                </template>

                                                <div class="exist-file">
                                                    <el-checkbox-group v-model="selectResumeFile" :max="residueNumber" :disabled="residueNumber===0">
                                                        <el-checkbox :disabled="handleResumeFileDisabled(item.token)" :label="`${item.token},${item.fileName}`" v-for="(item, index) in resumeFileList">
                                                            {{item.fileName}}
                                                        </el-checkbox>
                                                    </el-checkbox-group>
                                                </div>
                                                <div class="exist-bottom">
                                                    <el-button @click="handleSelectFileCancel">取消</el-button>
                                                    <el-button type="primary" @click="handleSelectFile">确定</el-button>
                                                </div>
                                            </el-popover>
                                        </div>

                                        <div class="file-list">
                                            <div class="list" v-for="(item, index) in selectResumeFileList">
                                                <!-- <i class="file-type" :class="`${item.type}`"></i> -->
                                                <span class="name">{{item.name}}</span>
                                                <i class="remove" @click="handleRemoveFile(index)"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="item.id === 5" class="wechat-wrapper form-wrapper">
                                <div class="common-title"><span v-if="item.isRequired === 1" class="required">*</span>您的微信号</div>
                                <div class="form-content">
                                    <div v-if="details.wechatInstructions" class="tips">{{details.wechatInstructions}}</div>
                                    <el-form-item class="wechat" lable-width="0px" prop="wechatNumber">
                                        <el-input v-model="formData.wechatNumber" placeholder="请填写您的微信号，方便我们联系您" :maxlength="50" />
                                    </el-form-item>
                                </div>
                            </div>

                            <div v-if="item.id === 6" class="reference-wrapper form-wrapper">
                                <div class="common-title"><span v-if="item.isRequired === 1" class="required">*</span>您的推荐人真实姓名及相应场次</div>
                                <div class="form-content">
                                    <div v-if="details.referenceInstructions" class="tips">{{details.referenceInstructions}}</div>
                                    <el-form-item class="reference" lable-width="0px" prop="reference">
                                        <el-input v-model="formData.reference" placeholder="格式：推荐人真实姓名+场次，如张三+成都站" :maxlength="200" />
                                    </el-form-item>
                                </div>
                            </div>

                            <div v-if="item.id === 7" class="employment-wrapper form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    您目前的学业/就业状态是
                                    <span class="required-tips">（单选题）</span>
                                </div>
                                <div class="form-content">
                                    <el-form-item class="form-item" lable-width="0px" prop="employmentStatus">
                                        <el-radio-group v-model="formData.employmentStatus">
                                            <el-radio v-for="(item, index) in employmentStatusList" :key="index" :label="item.k">{{item.v}}</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </div>
                            </div>

                            <div v-if="item.id === 8" class="postdoctor-wrapper form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    您的博士后培养机构是
                                </div>
                                <div class="form-content">
                                    <div v-if="details.postdoctorInstitutionInstructions" class="tips">{{details.postdoctorInstitutionInstructions}}</div>
                                    <el-form-item class="form-item" lable-width="0px" prop="postdoctorInstitution">
                                        <el-input v-model="formData.postdoctorInstitution" placeholder="请填写" :maxlength="50" />
                                    </el-form-item>
                                </div>
                            </div>

                            <div v-if="item.id === 9" class="working-hours-wrapper form-wrapper">
                                <div class="common-title">
                                    <span v-if="item.isRequired === 1" class="required">*</span>
                                    您博士毕业后的海外连续工作时长（含海外博士后）是
                                    <span class="required-tips">（单选题）</span>
                                </div>
                                <div class="form-content">
                                    <div v-if="details.postdoctorOverseasDurationInstructions" class="tips">{{details.postdoctorOverseasDurationInstructions}}</div>
                                    <el-form-item class="form-item" lable-width="0px" prop="postdoctorOverseasDuration">
                                        <el-radio-group v-model="formData.postdoctorOverseasDuration">
                                            <el-radio v-for="(item, index) in overseasWorkingTimeList" :label="item.k">{{item.v}}</el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </div>
                            </div>
                        </template>

                        <div class="end-wrapper" v-if="details.conclusion" v-html="details.conclusion"></div>

                        <div class="submit-wrapper">
                            <el-button type="primary" class="submit" :loading="submitLoading" @click="handleSubmit">立即提交</el-button>
                            <div class="submit-tips">提示：报名成功后您的信息将同步至在线简历</div>
                        </div>
                    </template>
                    <template v-else>
                        <div class="non-login">
                            <el-button size="large" type="primary" @click="openLoginDialog">开始报名</el-button>
                        </div>
                    </template>
                </el-form>
            </div>
        </div>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <script>
            function handleRedirect() {
                var redirect = ''
                var search = window.location.search
                var pathname = window.location.pathname
                var href = window.location.href
                if (/isNeedStep=/.test(search)) {
                    return
                }
                if (search) {
                    window.location.replace(`${href}&isNeedStep=2`)
                } else {
                    window.location.replace(`${href}?isNeedStep=2`)
                }
            }
            handleRedirect()
        </script>

        <script>
            $(function () {
                var _this = null
                const doubleMeetingOptions = {
                    computed: {
                        uploadResumeFileDisabled() {
                            const {
                                selectResumeFileList: { length }
                            } = this
                            return 5 - length === 0
                        },
                        // 附件简历剩余可选个数--最多可选5个文件
                        residueNumber() {
                            const {
                                selectResumeFileList: { length },
                                realSelectResumeFile: { length: idsLength }
                            } = this
                            return 5 - length + idsLength
                        },
                        token() {
                            const { pathname } = window.location
                            const arr = pathname.split('/')
                            const { length } = arr
                            return arr[length - 1]
                        },
                        birthdayDefault() {
                            const current = new Date()
                            const year = current.getFullYear() - 18
                            const month = current.getMonth() + 1
                            return new Date(`${year}/${month}/01`)
                        },
                        selectResumeFileIds() {
                            const { selectResumeFileList } = this
                            return selectResumeFileList.map((item) => item.value)
                        },
                        fromType() {
                            // 1 通过活动报名链接访问
                            var href = window.location.href
                            if (/fromType=\d+/.test(href)) {
                                var search = window.location.search
                                var URLParams = new URLSearchParams(search)
                                return URLParams.get('fromType')
                            } else {
                                return ''
                            }
                        }
                    },
                    data() {
                        function validMobile(mobile) {
                            const reg = /^1[3-9]\d{9}$/
                            return reg.test(mobile)
                        }

                        const validateMobile = (rule, value, callback) => {
                            if (validMobile(value)) {
                                callback()
                            } else if (!value) {
                                callback('请输入您的手机号')
                            } else {
                                callback('请输入正确的手机号码')
                            }
                        }

                        function validEmail(email) {
                            const reg = /^[\w\-\\.]+@[\w\-\\.]+(\.\w+)+$/
                            return reg.test(email)
                        }

                        const validateEmail = (rule, value, callback) => {
                            if (validEmail(value)) {
                                callback()
                            } else if (!value) {
                                callback('请输入您的邮箱')
                            } else {
                                callback('请输入正确的邮箱')
                            }
                        }

                        return {
                            isLogin: false,
                            loading: false,
                            isShowUserInfo: false,
                            hasOnlineResume: false,

                            selectFileVisible: false,

                            showCustomMajorEducationIds: ['1', '2', '5'],

                            allCityAreaList: [],
                            areaList: [],
                            categoryJobList: [],
                            channelList: [],
                            educationList: [],
                            jobStatusList: [],
                            arriveDateList: [],
                            majorList: [],
                            natureList: [],
                            politicalList: [],
                            resumeFileList: [],
                            titleList: [],
                            unitTypeList: [],
                            wageList: [],
                            employmentStatusList: [],
                            overseasWorkingTimeList: [],

                            // 简历步骤
                            resumeStep: '',

                            details: {
                                intentionOption: []
                            },

                            oldOptionIds: [],

                            showMessageToast: [
                                {
                                    id: 1,
                                    formKey: 'unitType'
                                },
                                {
                                    id: 2,
                                    formKey: 'channels'
                                },
                                {
                                    id: 3,
                                    formKey: 'isShare'
                                },
                                {
                                    id: 4,
                                    formKey: 'fileToken'
                                },
                                {
                                    id: 5,
                                    formKey: 'wechatNumber'
                                },
                                {
                                    id: 6,
                                    formKey: 'reference'
                                },
                                {
                                    id: 7,
                                    formKey: 'employmentStatus'
                                },
                                {
                                    id: 8,
                                    formKey: 'postdoctorInstitution'
                                },
                                {
                                    id: 9,
                                    formKey: 'postdoctorOverseasDuration'
                                }
                            ],
                            showMessageList: [],

                            realSelectResumeFile: [],
                            // 勾选的附件简历
                            selectResumeFile: [],
                            /*
                        勾选的附件简历列表和上传的文件
                        selectResumeFileList Array<object>
                        {
                            value 文件id或者token
                            name  文件名称
                            type  文件类型，如pdf、doc等
                            uploadType: 1文件id，2token类型
                        }
                    */
                            selectResumeFileList: [],

                            // 1是 2否
                            majorType: 2,
                            formData: {
                                activityFormId: '',
                                optionIds: [],

                                name: '',
                                gender: '1',
                                birthday: '',
                                householdRegisterId: '',
                                mobile: '',
                                email: '',
                                politicalStatusId: '',
                                titleId: '',
                                residence: '',

                                school: '',
                                isRecruitment: '2',
                                isProjectSchool: '2',
                                isAbroad: '2',
                                college: '',
                                educationId: '',
                                beginDate: '',
                                endDate: '',
                                majorId: '',
                                majorCustom: '',

                                workStatus: '',
                                arriveDateType: '',
                                jobCategoryId: '',
                                natureType: '',
                                areaId: '',
                                wageType: '',

                                unitType: [],
                                isShare: '',
                                fileToken: '',
                                wechatNumber: '',
                                reference: '',
                                channels: [],

                                employmentStatus: '',
                                postdoctorInstitution: '',
                                postdoctorOverseasDuration: '',
                                // 本次允许提交的最多场次
                                maxCount: 0
                            },

                            formRules: {
                                name: [{ required: true, message: '请输入您的姓名', trigger: 'blur' }],
                                gender: [{ required: true, message: '请选择您的性别', trigger: 'change' }],
                                birthday: [{ required: true, message: '请选择您的出生日期', trigger: 'blur' }],
                                householdRegisterId: [{ required: true, message: '请选择您的户籍/国籍', trigger: 'blur' }],
                                mobile: [{ required: true, validator: validateMobile, trigger: 'blur' }],
                                email: [{ required: true, validator: validateEmail, trigger: 'blur' }],
                                politicalStatusId: [{ required: true, message: '请选择您的政治面貌', trigger: 'change' }],

                                school: [{ required: true, message: '请填写学校名称', trigger: 'blur' }],
                                educationId: [{ required: true, message: '请选择您的学历水平', trigger: 'change' }],
                                beginDate: [{ required: true, message: '请选择入学时间', trigger: 'blur' }],
                                endDate: [{ required: true, message: '请选择毕业时间', trigger: 'blur' }],
                                majorId: [{ required: true, message: '请选择您的专业', trigger: 'blur' }],
                                majorCustom: [{ required: true, message: '请填写您的专业', trigger: 'blur' }],

                                workStatus: [{ required: true, message: '请选择求职状态', trigger: 'change' }],
                                arriveDateType: [{ required: true, message: '请选择到岗时间', trigger: 'change' }],
                                jobCategoryId: [{ required: true, message: '请选择意向职位', trigger: 'blur' }],
                                natureType: [{ required: true, message: '请选择工作性质', trigger: 'change' }],
                                areaId: [{ required: true, message: '请选择意向城市', trigger: 'blur' }],
                                wageType: [{ required: true, message: '请选择期望月薪', trigger: 'change' }]
                            },

                            submitLoading: false,
                            myMaxCount: 0,
                            optionTips: '',
                            allIntentionOptionDisabled: false
                        }
                    },

                    methods: {
                        getDetail() {
                            this.loading = true
                            const { token } = this
                            httpGet('/activity-form/get-detail', { token })
                                .then((resp) => {
                                    const {
                                        data: { id: activityFormId, oldOptionIds, resumeStep, baseInfo, educationList, intentionList, parameter, showMessageList, isLogin, ...details }
                                    } = resp
                                    const { resumeId, ...otherBaseInfo } = baseInfo
                                    const { educationText, majorText, ...otherEducationList } = educationList

                                    const resumeStepForm = {
                                        activityFormId,
                                        ...otherBaseInfo,
                                        ...otherEducationList,
                                        ...intentionList
                                    }

                                    // 简历步骤表单数据
                                    Object.keys(resumeStepForm).forEach(function (key) {
                                        _this.formData[key] = resumeStepForm[key]
                                    })

                                    // 选项数据
                                    Object.keys(parameter).forEach(function (key) {
                                        _this[key] = parameter[key]
                                    })

                                    this.details = details
                                    this.oldOptionIds = oldOptionIds || []
                                    this.showMessageList = showMessageList
                                    this.isShowUserInfo = resumeStep < 4
                                    this.hasOnlineResume = resumeStep > 3
                                    this.resumeStep = resumeStep
                                    this.majorType = resumeStepForm.majorCustom !== '' ? 1 : 2
                                    this.loading = false

                                    this.isLogin = isLogin != '2'

                                    // 禅道需求：活动报名页，如果活动报名人数已满，所有意向选项都不可选
                                    this.myMaxCount = details.myMaxCount
                                    this.optionTips = details.optionTips
                                    this.allIntentionOptionDisabled = details.allIntentionOptionDisabled
                                })
                                .catch(() => {
                                    this.isLogin = false
                                    const { fromType } = this
                                    if (fromType == 1) {
                                        ElementPlus.ElMessage({
                                            message: '请登录后再进行活动报名!',
                                            type: 'error',
                                            duration: 5000
                                        })
                                    }
                                })
                        },

                        getCurrentDate() {
                            const current = new Date()
                            const year = current.getFullYear()
                            const month = current.getMonth() + 1
                            const date = current.getDate()
                            const toStringFormat = (n) => n.toString().padStart(2, '0')
                            return `${year}/${toStringFormat(month)}/${toStringFormat(date)}`
                        },

                        birthdayDisabledDate(date) {
                            const startDate = new Date()
                            const year = startDate.getFullYear()
                            startDate.setFullYear(year - 127)

                            const startDateTime = startDate.getTime()
                            const dateTime = new Date(date).getTime()
                            return startDateTime > dateTime || dateTime > new Date().getTime()
                        },

                        studyBeginDisabledDate(date) {
                            const current = this.getCurrentDate()
                            return new Date(date).getTime() > new Date(current).getTime()
                        },
                        studyEndDisabledDate(date) {
                            const { beginDate } = this.formData
                            const begin = `${beginDate}`.replace(/-/g, '/')
                            return new Date(date) <= new Date(begin)
                        },

                        handleBeforeUpload(rawFile) {
                            const { size } = rawFile
                            const maxSize = 15 * 1024 * 1024
                            if (size > maxSize) {
                                ElementPlus.ElMessage.warning('文件大小不能超过15M')
                                return false
                            }
                            return true
                        },

                        handleUploadFile(response) {
                            const { data, result, msg } = response
                            if (result !== 1) {
                                ElementPlus.ElMessage.error(msg)
                                retrun
                            }
                            let { name, id } = data
                            const splitName = name.split('.')
                            const { length } = splitName
                            const type = splitName[length - 1]
                            this.selectResumeFileList.push({ value: id, name, type, uploadType: 1 })
                        },

                        handleRemoveFile(index) {
                            const { selectResumeFileList } = this
                            const [item] = selectResumeFileList.splice(index, 1)
                            const { value, name } = item
                            this.selectResumeFileList = selectResumeFileList
                            this.selectResumeFile = this.selectResumeFile.filter((item) => item !== `${value},${name}`)
                            this.realSelectResumeFile = [...this.selectResumeFile]
                        },

                        handleSelectFile() {
                            const { selectResumeFileIds } = this
                            this.selectResumeFile.forEach((item) => {
                                const [token, ...other] = item.split(',')
                                const isIncludes = selectResumeFileIds.includes(token)
                                if (!isIncludes) {
                                    const name = other.join()
                                    const nameArr = name.split('.')
                                    const { length } = nameArr
                                    const type = nameArr[length - 1]
                                    this.selectResumeFileList.push({ value: token, name, type, uploadType: 2 })
                                }
                                this.realSelectResumeFile = [...this.selectResumeFile]
                                this.selectFileVisible = false
                            })
                        },

                        handleResumeFileDisabled(token) {
                            const { selectResumeFileIds } = this
                            const isIncludes = selectResumeFileIds.some((item) => item === token)
                            return isIncludes
                        },

                        handleSelectFileCancel() {
                            this.selectFileVisible = false
                            this.selectResumeFile = [...this.realSelectResumeFile]
                        },

                        handleRequired(postData) {
                            const {
                                showMessageToast,
                                showMessageList,
                                showMessageList: { length }
                            } = this
                            let requiredValid = true
                            for (let i = 0; i < length; i++) {
                                let wrapperValid = true
                                const { id, isRequired } = showMessageList[i]
                                if (isRequired === 1) {
                                    showMessageToast.forEach((item) => {
                                        const value = postData[item.formKey]
                                        const isEmpty = Array.isArray(value) ? value.length === 0 : !value
                                        if (id === item.id && isEmpty) {
                                            requiredValid = false
                                            wrapperValid = false
                                            // V1.8.3统一提示文案
                                            ElementPlus.ElMessage.warning('请填写必填项(*)后再提交！')
                                        }
                                    })
                                }
                                if (!wrapperValid) {
                                    return
                                }
                            }
                            return requiredValid
                        },

                        validateResumeSteps() {
                            var isValid = false
                            _this.$refs.form.validate(function (valid, invalidFields) {
                                isValid = valid
                                var invalidArray = []
                                Object.keys(invalidFields).forEach(function (key) {
                                    var value = invalidFields[key]
                                    if (Array.isArray(value)) {
                                        value.forEach(function (item) {
                                            if (item.message) {
                                                invalidArray.push(item)
                                            }
                                        })
                                    }
                                })
                                if (!valid) {
                                    ElementPlus.ElMessage.warning(invalidArray[0].message)
                                }
                            })
                            return isValid
                        },

                        handleSubmit() {
                            const { formData, selectResumeFileList, majorType } = this
                            let postData = {}
                            Object.keys(formData).forEach((key) => {
                                const value = formData[key]
                                postData[key] = Array.isArray(value) ? value.join() : value
                            })

                            postData['fileToken'] = selectResumeFileList.map(function (item) {
                                var { value, uploadType } = item
                                return {
                                    value,
                                    uploadType
                                }
                            })
                            const { optionIds } = postData
                            if (!optionIds) {
                                ElementPlus.ElMessage.warning('请填写必填项(*)后再提交！')
                                return
                            }

                            if (!this.validateResumeSteps()) {
                                return
                            }

                            const requiredWrapper = this.handleRequired(postData)
                            if (!requiredWrapper) {
                                return
                            }
                            postData[majorType === 1 ? 'majorId' : 'majorCustom'] = ''

                            this.submitLoading = true
                            httpPost('/activity-form-registration-form/registration-activity-form', postData)
                                .then(() => {
                                    this.submitLoading = false

                                    const {
                                        resumeStep,
                                        formData: { activityFormId },
                                        fromType
                                    } = this
                                    window.location.href = `/activity-form-registration-form/index?activityFormId=${activityFormId}&resumeStep=${resumeStep}`
                                })
                                .catch(() => {
                                    this.submitLoading = false
                                })
                        },

                        changeIntentionOption(value, options) {
                            // 取消选中
                            if (this.details.maxCount > 0 && value) {
                                const thisOptionId = options.id
                                // 看一下当前有多少是选中的
                                if (this.formData.optionIds.length > this.myMaxCount) {
                                    ElementPlus.ElMessageBox.alert(this.details.optionTipsErrorMessage, '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定'
                                    })
                                    // 去掉当前选中的
                                    this.formData.optionIds = this.formData.optionIds.filter((item) => item !== thisOptionId)
                                    return
                                }
                            }
                        },

                        openLoginDialog() {
                            ElementPlus.ElMessage({
                                message: '请登录后再进行活动报名!',
                                type: 'warning',
                                zIndex: 3000,
                                duration: 5000
                            })

                            window.globalComponents.loginDialogComponent.showLoginDialog()
                        }
                    },

                    mounted() {
                        _this = this
                        setTimeout(() => {
                            this.getDetail()
                        }, 200)
                    }
                }
                const doubleMeetingApp = Vue.createApp(doubleMeetingOptions)
                doubleMeetingApp.component('select-dialog', selectDialogComponent).component('date-picker', datePickerComponent)

                doubleMeetingApp.use(ElementPlus).mount('#component')
            })
        </script>

        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/date/datePicker.js"></script>
    </body>
</html>
