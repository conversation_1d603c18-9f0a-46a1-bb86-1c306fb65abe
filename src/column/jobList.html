<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>职位中心</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/jobList.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./js/login-guide-popup.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <!-- <a href="/vip.html" class="nav-link">
                        VIP<span class="gaocai-vip">升级</span>
                    </a> -->
                    <div class="vip">
                        <a href="/vip.html" target="_blank" class="nav-link"
                            >求职VIP<span class="arrow"></span>
                            <span class="artifact">神器</span>
                        </a>
                        <div class="more">
                            <a href="/" target="_blank">简历曝光</a>
                            <a href="/" target="_blank">上岸直通车</a>
                            <a href="/" target="_blank">竞争力分析</a>
                            <a href="/" target="_blank">精选课程</a>
                            <a href="/" target="_blank">高校求职顾问</a>
                        </div>
                    </div>
                    <a class="nav-link join"
                        >小程序
                        <div class="join-code">
                            <div class="news-part">
                                <p class="scan-join">高才优聘小程序上线拉！</p>
                                <!-- <h4 class="scan-join"></h4> -->
                                <p class="explain">随时随地看机会</p>
                                <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/26.png" />
                            </div>
                        </div>
                    </a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <!-- <el-popover popper-class="header-popover-message" trigger="hover" :width="180">
                        <template #reference>
                            <span class="message">
                                <el-badge :value="total" :hidden="total === 0">
                                    <i class="el-icon-bell"></i>
                                </el-badge>
                            </span>
                        </template>

                        <ul class="message-options">
                            <li v-for="(item, index) in messageOptions" :key="index" class="item pointer" @click="handleClick(item)">
                                <span class="label">{{ item.label }}</span>

                                <span v-if="item.value" class="value"> <span class="num"> {{ item.value }} </span>条未读 </span>
                            </li>
                        </ul>
                    </el-popover>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => (isOpenVip ? openVip('/vip.html') : handleRoute('/job?tab=service'))">
                                    <div class="dropdown-item__card">
                                        <div class="dropdown-item__card-title">
                                            <span>高才</span>
                                            <span>去开通</span>
                                        </div>

                                        <div class="dropdown-item__card-desc">高效求职 尊享11+权益</div>

                                        <div class="dropdown-item__card-tips">•投递置顶 •简历曝光 •竞争力分析</div>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown> -->
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div>
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        {
                                            label: '职位',
                                            value: '1',
                                            path: '/job'
                                        },
                                        {
                                            label: '公告',
                                            value: '2',
                                            path: '/search'
                                        },
                                        {
                                            label: '单位',
                                            value: '3',
                                            path: '/company'
                                        },
                                        {
                                            label: '资讯',
                                            value: '4',
                                            path: '/search',
                                            query: 'type=2'
                                        }
                                    ],

                                    keyword: '',
                                    isVip: true,
                                    vipInfo: {},

                                    total: 100,
                                    messageOptions: [
                                        {
                                            label: '我的直聊',
                                            value: '10',
                                            path: '/chat'
                                        },
                                        {
                                            label: '求职动态',
                                            value: '99+',
                                            path: '/message?read=2&type=0'
                                        },
                                        {
                                            label: '系统通知',
                                            value: '',
                                            path: '/message?read=2&type=3'
                                        }
                                    ]
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                handleClick(data) {
                                    this.handleRoute(data.path)
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <main class="el-main">
                <div class="search-header">
                    <div class="recommend-container" v-if="recommendList.length">
                        <el-carousel
                            ref="recommendRef"
                            trigger="click"
                            height="90px"
                            arrow="never"
                            :autoplay="autoplay"
                            :interval="interval"
                            :indicator-position="indicatorPosition"
                            :loop="loop"
                            :pause-on-hover="pauseOnHover"
                            @change="handleCarouselChange"
                            @mouseover="handleCarouseMouse(true)"
                            @mouseout="handleCarouseMouse(false)"
                        >
                            <el-carousel-item v-for="item in recommendGroupList">
                                <a v-for="{ id, number, link, src, alt } in item" class="showcase-browse" :href="link" target="_blank" :data-showcase-id="id" :data-showcase-number="number">
                                    <img :src="src" :alt="alt" />
                                </a>
                            </el-carousel-item>
                        </el-carousel>

                        <div class="recommend-tabs">
                            <span class="label">推荐单位:</span>
                            <span class="value" v-for="(item, index) in recommendTabList" :class="index === recommendIndex ? 'is-active' : ''" @click="handleRecommendTabClick(index)">{{ item }}</span>
                        </div>
                    </div>

                    <!-- 由后端渲染 -->
                    <div class="main-header-showcase__container">
                        <!-- 7 个 showcase-browse 为一组 carousel-item 。如果只有一组：indicator-position=“none”；否则 indicator-position=“outside” -->
                        <el-carousel height="70px" arrow="never" trigger="click" indicator-position="outside" :autoplay="true" :interval="5000">
                            <el-carousel-item>
                                <a class="showcase-browse" href="" target="_blank" data-showcase-id="" data-showcase-number="">
                                    <img src="//zt.gaoxiaojob.com/lanmuT1new_10.jpg" alt="" />
                                </a>
                            </el-carousel-item>
                        </el-carousel>
                    </div>

                    <div class="search-query">
                        <div class="search-input-cell">
                            <el-input
                                class="search-keyword"
                                :class="jobTypeText.length ? 'is-checked' : ''"
                                v-model="keyword"
                                clearable
                                placeholder="请输入职位/公告/单位等关键词搜索"
                                @keydown.enter="updateQueryValue('keyword', keyword)"
                            >
                                <template #prefix>
                                    <span class="job-category" @click="handleDialogActive('jobTypeList')">{{ calcJobTypeText }}</span>
                                    <i class="el-select__caret el-input__icon el-icon-circle-close" @click="handleDialogClear('jobTypeList')"></i>
                                    <i class="el-select__caret el-input__icon el-icon-arrow-down" @click="handleDialogActive('jobTypeList')"></i>
                                </template>

                                <template #suffix>
                                    <el-button class="search-button" type="primary" @click="updateQueryValue('keyword', keyword)">搜索</el-button>
                                </template>
                            </el-input>

                            <div class="miniprogram-cell">
                                <span class="title">高才优聘小程序</span>
                                <span class="tips"
                                    >好机会随时Get
                                    <i class="el-icon" data-v-ea893728="" style="font-size: 12px">
                                        <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" data-v-ea893728="">
                                            <path fill="currentColor" d="M384 192v640l384-320.064z"></path>
                                        </svg>
                                    </i>
                                </span>
                            </div>
                        </div>

                        <div class="search-params">
                            <div class="hot-search" v-if="hotSearchList.length">
                                热门搜索：
                                <a
                                    v-for="{ id, number, text } in hotSearchList"
                                    class="showcase-browse"
                                    :data-showcase-id="id"
                                    :data-showcase-number="number"
                                    @click="updateQueryValue('keyword', text)"
                                >
                                    {{ text }}
                                </a>
                            </div>

                            <div class="search-filter is-area" v-if="areaList.length">
                                <div class="cell">
                                    <span class="label">工作地点</span>

                                    <div class="value-cell">
                                        <span
                                            class="value"
                                            v-for="{ label, value, checked } in calcHotAreaList"
                                            :class="value ? '' : 'is-special'"
                                            :checked="checked"
                                            @click="handleCheck('areaId', { label, value })"
                                        >
                                            {{ label }}
                                        </span>
                                    </div>
                                </div>

                                <span class="event is-primary" v-html="areaCountHtml" @click="handleDialogActive('areaList')"></span>
                            </div>

                            <div class="search-filter" v-if="companyTypeList.length">
                                <div class="cell">
                                    <span class="label">单位类型</span>

                                    <div class="value-cell" :class="{'show-values': showAllCompanyType}">
                                        <span
                                            class="value"
                                            v-for="{ label, value, checked } in calcCompanyTypeList"
                                            :class="value ? '' : 'is-special'"
                                            :checked="checked"
                                            @click="handleCheck('companyType', { label, value })"
                                        >
                                            {{ label }}
                                        </span>
                                    </div>
                                </div>

                                <span class="event is-arrow" :class="{'is-reverse': showAllCompanyType}" @click="showAllCompanyType = !showAllCompanyType">更多</span>
                            </div>

                            <div class="search-filter filter-option">
                                <div class="filter-cell">
                                    <span class="label">更多筛选</span>

                                    <div class="el-select el-select--mini filter-area" :class="{ 'is-checked': hasSelectedArea }" @click="handleDialogActive('areaList')">{{ calcSelectArea }}</div>

                                    <div class="el-select el-select--mini filter-major" :class="majorId.length ? 'is-checked' : ''">
                                        <div class="select-trigger" aria-describedby="el-popper-9776">
                                            <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('majorList')">
                                                <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="学科分类" v-model="calcMajorText" />
                                                <span class="el-input__suffix">
                                                    <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('majorList')">
                                                        <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                    </span>
                                                    <span class="el-input__suffix-inner">
                                                        <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <el-select
                                        v-model="educationType"
                                        :class="educationType ? 'is-checked' : ''"
                                        placeholder="学历要求"
                                        size="mini"
                                        clearable
                                        @change="updateQueryValue('educationType', educationType)"
                                    >
                                        <el-option v-for="{ label, value } in educationList" :label="label" :value="value" />
                                    </el-select>

                                    <el-select
                                        v-model="companyNature"
                                        :class="companyNature ? 'is-checked' : ''"
                                        placeholder="单位性质"
                                        size="mini"
                                        clearable
                                        @change="updateQueryValue('companyNature', companyNature)"
                                    >
                                        <el-option v-for="{ label, value } in companyNatureList" :label="label" :value="value" />
                                    </el-select>

                                    <el-cascader
                                        v-model="industryId"
                                        placeholder="行业类别"
                                        size="mini"
                                        :class="industryId ? 'is-checked' : ''"
                                        :options="industryList"
                                        :clearable="true"
                                        :show-all-levels="false"
                                        :props="{ checkStrictly: false, emitPath: false, expandTrigger: 'hover' }"
                                        @change="updateQueryValue('industryId', industryId)"
                                    >
                                    </el-cascader>

                                    <el-select
                                        v-model="releaseTimeType"
                                        :class="releaseTimeType ? 'is-checked' : ''"
                                        placeholder="发布时间"
                                        size="mini"
                                        clearable
                                        @change="updateQueryValue('releaseTimeType', releaseTimeType)"
                                    >
                                        <el-option v-for="{ label, value } in releaseList" :label="label" :value="value" />
                                    </el-select>

                                    <el-select
                                        v-model="titleType"
                                        :class="titleType ? 'is-checked' : ''"
                                        placeholder="职称类型"
                                        size="mini"
                                        clearable
                                        @change="updateQueryValue('titleType', titleType)"
                                    >
                                        <el-option v-for="{ label, value } in titleList" :label="label" :value="value" />
                                    </el-select>

                                    <el-select
                                        v-model="natureType"
                                        :class="natureType ? 'is-checked' : ''"
                                        placeholder="职位性质"
                                        size="mini"
                                        clearable
                                        @change="updateQueryValue('natureType', natureType)"
                                    >
                                        <el-option v-for="{ label, value } in natureList" :label="label" :value="value" />
                                    </el-select>
                                </div>

                                <span class="trash-button" @click="handleReset" v-html="filterCountHtml"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="search-content">
                    <section class="search-section">
                        <div class="search-result">
                            <div class="result-header">
                                <div class="title">职位列表</div>

                                <div class="filter">
                                    <el-checkbox
                                        v-if="getValidValue(filterCheckbox[3])"
                                        v-model="isEstablishment"
                                        true-label="1"
                                        false-label="2"
                                        :disabled="calcIsEstablishmentDisabled"
                                        @change="updateQueryValue('isEstablishment', isEstablishment)"
                                        @click="handleEstablishmentClick"
                                    >
                                        <div class="establishment">
                                            编制<el-popover placement="bottom" :width="410" trigger="hover" content="可查询包含行政编制/事业编制/备案制等编制类型的优质职位">
                                                <template #reference>
                                                    <img src="https://img.gaoxiaojob.com/uploads/person/question.png" class="is-tips" />
                                                </template>
                                            </el-popover>
                                        </div>
                                    </el-checkbox>
                                    <el-checkbox v-if="getValidValue(filterCheckbox[0])" v-model="isFast" true-label="1" false-label="2" @change="updateQueryValue('isFast', isFast)">
                                        反馈快
                                    </el-checkbox>
                                    <el-checkbox v-if="getValidValue(filterCheckbox[1])" v-model="isFresh" true-label="1" false-label="2" @change="updateQueryValue('isFresh', isFresh)">
                                        应届生
                                    </el-checkbox>
                                    <el-checkbox
                                        v-if="getValidValue(filterCheckbox[2])"
                                        v-model="isCooperation"
                                        true-label="1"
                                        false-label="2"
                                        @change="updateQueryValue('isCooperation', isCooperation)"
                                    >
                                        认证单位
                                    </el-checkbox>
                                </div>
                            </div>

                            <div class="result-guide" v-if="!hasJobIntention && showIntentionGuide">
                                <div>
                                    <p class="question">您想找什么工作？</p>
                                    <p class="tips">系统可根据您的教育信息、求职意向等 为您精准推荐最新职位</p>
                                </div>

                                <span class="button" @click="handleGuideClick">完善意向</span>
                            </div>

                            <ul class="result-list" v-loading="loading" v-if="resultList.length">
                                <li
                                    :class="jobId ? 'result-item': 'showcase-item'"
                                    v-for="{
                                        jobId, jobName, url, src, isTop, isFast, isEstablishment, isCooperation, wage, areaName,
                                        education, amount, refreshDate, announcementName, announcementUrl, companyName,
                                        companyLogo, companyUrl, companyTypeName, companyNatureName, welfareTagArr, status,chatButtonType, chatButtonName
                                    }, index in resultList"
                                >
                                    <template v-if="jobId">
                                        <div class="item-cell item-cell-3">
                                            <div class="title" :class=" status === '0' ? 'offline' : '' ">
                                                <a :href="url" target="_blank" :title="jobName">{{ jobName }}</a>
                                                <span class="establishment" v-if="getValidValue(isEstablishment)">编制</span>
                                                <span class="help-wanted" v-if="getValidValue(isTop)">急聘</span>
                                                <span class="fast-feedback" v-if="getValidValue(isFast)">反馈快</span>
                                            </div>

                                            <div class="label">
                                                <span class="wage">{{ wage }}</span>
                                                <span class="area is-special">{{ areaName }}</span>
                                                <span class="education is-special">{{ education }}</span>
                                                <span class="amount is-special">{{ amount }}人</span>
                                                <span class="refresh">{{ refreshDate }}发布</span>
                                            </div>

                                            <a :href="announcementUrl" v-if="announcementName" class="source" target="_blank" :title="announcementName">{{ announcementName }}</a>
                                        </div>

                                        <div class="item-cell item-cell-2">
                                            <a :href="companyUrl" class="name" target="_blank" :title="companyName">{{ companyName }}</a>

                                            <div class="type">
                                                <span>{{ companyTypeName }}</span>
                                                <span>{{ companyNatureName }}</span>
                                            </div>

                                            <div class="tags" v-if="welfareTagArr.length">
                                                <span v-for="item in welfareTagArr">{{ item }}</span>
                                            </div>

                                            <div class="operate" :class="getOperateClassName(announcementName, welfareTagArr)">
                                                <el-button v-if="chatButtonType != 1" class="chat-button" size="mini" @click="handleChatClick(jobId, index)">{{ chatButtonName }}</el-button>

                                                <!-- <el-button size="mini" class="collect-button" :class="getCollectClassName(jobId)" @click="handleCollectClick(jobId)">{{ getCollectText(jobId) }}</el-button> -->
                                                <el-button type="primary" size="mini" :disabled="getApplyDisabled(jobId)" @click="handleApplyClick(jobId)">{{ getApplyText(jobId) }}</el-button>
                                            </div>
                                        </div>

                                        <div class="item-cell item-cell-1">
                                            <a :href="companyUrl" :class="getValidValue(isCooperation) ? 'is-special' : ''" target="_blank">
                                                <img :src="companyLogo" alt="" />
                                            </a>
                                        </div>
                                    </template>

                                    <template v-else>
                                        <a class="showcase-link" :href="url" target="_blank">
                                            <img :src="src" alt="" />
                                        </a>
                                    </template>
                                </li>
                            </ul>

                            <div class="empty" v-else>暂无相关职位，请修改筛选条件试试</div>
                        </div>

                        <div class="single-page" v-if="calcSinglePage">已经到底啦~</div>

                        <el-pagination
                            background
                            layout="prev, pager, next"
                            hide-on-single-page
                            v-model:current-page="currentPage"
                            :page-size="pageSize"
                            :total="total"
                            @current-change="handleCurrentChange"
                        />
                    </section>

                    <aside class="search-aside">
                        <div class="guide-card" v-if="!isLogin" @click="handleShowLogin">
                            <img :src="loginGuideImage" alt="" />
                        </div>

                        <div class="subscription" v-if="showAsideSubscription">
                            <h5 class="title">职位订阅</h5>

                            <p>已有3.8万人才进行职位订阅，定期接收定制化职位信息，求职投递快人一步</p>

                            <div class="subscribe">
                                <el-button type="primary" size="mini" @click="handleSubscribeClick">立即订阅</el-button>
                            </div>
                        </div>

                        <template v-if="showAsideQrCodeContent">
                            <div class="qr-code-content">
                                <img class="qr-code" :src="bindCodeUrl" alt="" />
                                <div class="tips">
                                    <div class="tips-title">实时接收求职反馈</div>
                                    <div class="scan">请使用微信【扫一扫】<span>关注服务号绑定</span></div>
                                </div>
                            </div>
                        </template>

                        <div class="advert-vip" v-if="!isLogin || !isVip" @click="openVip">
                            <img src="./assets/vip/advert-vip.png" alt="" />
                        </div>

                        <div class="aside-link" v-if="asideLinkList.length">
                            <a
                                v-for="{ id, number, link, src, alt } in asideLinkList"
                                class="showcase-browse"
                                :href="link ? link : 'javascript:;'"
                                :target="link ? '_blank' : ''"
                                :data-showcase-id="id"
                                :data-showcase-number="number"
                            >
                                <img :src="src" :alt="alt" />
                            </a>
                        </div>
                    </aside>
                </div>
            </main>

            <select-dialog
                ref="selectDialogRef"
                :title="calcDialogTitle"
                :search-placeholder="calcDialogPlaceholder"
                :list="calcDialogList"
                :name="calcDialogName"
                multiple
                :multiple-limit="5"
                v-model="calcDialogValue"
                @update="handleDialogChange"
            ></select-dialog>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            recommendData: [],
                            recommendIndex: 0,
                            size: 8,
                            loop: false,
                            autoplay: true,
                            interval: 3000,
                            pauseOnHover: true,
                            timer: null,

                            keyword: '',
                            jobType: [],
                            jobTypeText: [],
                            areaId: [],
                            areaText: [],
                            companyType: [],

                            majorId: [],
                            majorText: [],
                            educationType: '',
                            companyNature: '',
                            industryId: '',
                            releaseTimeType: '',
                            titleType: '',
                            natureType: '',

                            showAllCompanyType: false,

                            isEstablishment: '2',
                            isFast: '2',
                            isFresh: '2',
                            isCooperation: '2',
                            // [反馈快, 应届生, 认证单位, 编制]
                            filterCheckbox: ['2', '2', '2', '2'],
                            // 暂时不展示
                            showIntentionGuide: false,

                            jobTypeList: [],
                            hotSearchList: [],
                            hotAreaList: [],
                            areaList: [],
                            companyTypeList: [],

                            majorList: [],
                            educationList: [],
                            companyNatureList: [],
                            industryList: [],
                            releaseList: [],
                            titleList: [],
                            natureList: [],

                            isLogin: false,
                            isVip: false,
                            hasJobIntention: true,
                            hasJobSubscription: true,
                            bindCodeUrl: '',

                            loading: true,
                            resultList: [],
                            currentPage: 1,
                            pageSize: 20,
                            total: 0,

                            loginGuideImage: '//img.gaoxiaojob.com/uploads/static/image/loginGuideImage.png',
                            asideLinkList: [],

                            dialogActive: ''
                        }
                    },

                    computed: {
                        recommendList() {
                            const { recommendData, recommendIndex } = this
                            return recommendData[recommendIndex] ? recommendData[recommendIndex].value : []
                        },

                        recommendGroupSize() {
                            const { size, recommendList } = this
                            return Math.ceil(recommendList.length / size)
                        },

                        recommendGroupList() {
                            const { size, recommendList, recommendGroupSize } = this
                            const group = []

                            new Array(recommendGroupSize).fill('').forEach(function (item, index) {
                                group[index] = recommendList.slice(index * size, (index + 1) * size)
                            })

                            return group
                        },

                        indicatorPosition() {
                            const { recommendList, size } = this
                            return recommendList.length > size ? '' : 'none'
                        },

                        recommendTabList() {
                            const { recommendData } = this
                            return recommendData.reduce(function (previous, current) {
                                previous.push(current.label)
                                return previous
                            }, [])
                        },

                        recommendTabSize() {
                            return this.recommendTabList.length
                        },

                        calcJobTypeText() {
                            const { jobTypeText } = this

                            if (jobTypeText.length) {
                                return jobTypeText.join(',')
                            }

                            return '职位类型'
                        },

                        calcHotAreaList() {
                            const { areaId, hotAreaList } = this
                            return this.getCalcDataList(areaId, hotAreaList)
                        },

                        calcCompanyTypeList() {
                            const { companyType, companyTypeList } = this
                            return this.getCalcDataList(companyType, companyTypeList)
                        },

                        hasSelectedArea() {
                            return this.areaId.length > 0
                        },

                        calcSelectArea() {
                            const { areaText } = this
                            return areaText.length ? areaText.join('+') : '全国'
                        },

                        hotAreaSelectedCount() {
                            const { areaId, calcHotAreaList } = this
                            const count = calcHotAreaList.reduce(function (previous, current) {
                                const { value, checked } = current
                                if (value && checked) {
                                    return previous + 1
                                }
                                return previous
                            }, 0)

                            return areaId.length - count
                        },

                        areaCountHtml() {
                            const count = this.hotAreaSelectedCount
                            const defaultText = '更多地点'

                            if (count) {
                                return `${defaultText}<i>（${count}）</i>`
                            }
                            return defaultText
                        },

                        calcMajorText() {
                            const { majorId } = this
                            const len = majorId.length

                            if (len) {
                                return `学科分类(${len})`
                            }
                            return ''
                        },

                        calcFilterCount() {
                            const { keyword, isFast, isFresh, isCooperation, isEstablishment, ...rest } = this.getSearchData()
                            const count = Object.keys(rest).reduce(function (previous, current) {
                                const value = rest[current]
                                const isValid = value && value.length

                                if (isValid) {
                                    return previous + 1
                                }
                                return previous
                            }, 0)

                            return count
                        },

                        filterCountHtml() {
                            const defaultText = '清空筛选条件'
                            const { calcFilterCount } = this
                            const countHtml = calcFilterCount ? `<i>（${calcFilterCount}）</i>` : ''

                            return `${defaultText}${countHtml}`
                        },

                        calcSinglePage() {
                            const { pageSize, total } = this

                            return total <= pageSize && total > 0
                        },

                        calcDialogTitle() {
                            const { dialogActive } = this
                            const options = {
                                jobTypeList: '请选择职位类型',
                                areaList: '请选择地点',
                                majorList: '请选择学科'
                            }

                            return options[dialogActive] || '请选择'
                        },

                        calcDialogPlaceholder() {
                            const { dialogActive } = this
                            const options = {
                                jobTypeList: '请输入职位类型关键词',
                                areaList: '搜索城市',
                                majorList: '请输入学科关键词'
                            }

                            return options[dialogActive] || '请输入关键词'
                        },

                        calcDialogList() {
                            const { dialogActive } = this
                            return this[dialogActive] || []
                        },

                        calcDialogName() {
                            const { dialogActive } = this
                            return 'area'
                        },

                        calcDialogValueTextKey() {
                            const { dialogActive } = this
                            const options = {
                                jobTypeList: ['jobType', 'jobTypeText'],
                                areaList: ['areaId', 'areaText'],
                                majorList: ['majorId', 'majorText']
                            }
                            const value = options[dialogActive]

                            if (value) {
                                return value
                            }
                            return ['', '']
                        },

                        calcDialogValue: {
                            get() {
                                const [valKey, textKey] = this.calcDialogValueTextKey

                                if (valKey) {
                                    return this[valKey]
                                }
                                return []
                            },
                            set(val) {
                                const [valKey, textKey] = this.calcDialogValueTextKey

                                if (valKey) {
                                    this[valKey] = val
                                }
                            }
                        },

                        calcIsEstablishmentDisabled() {
                            const { isLogin, isVip } = this
                            if (isLogin && isVip) {
                                return false
                            }

                            return true
                        },

                        showAsideSubscription() {
                            const { isLogin, bindCodeUrl, hasJobSubscription } = this

                            return !isLogin || !bindCodeUrl ? !hasJobSubscription : false
                        },

                        showAsideQrCodeContent() {
                            const { isLogin, bindCodeUrl } = this

                            return isLogin && bindCodeUrl
                        }
                    },

                    watch: {
                        recommendIndex() {
                            this.handleCarouselAutoPlay()
                        }
                    },

                    methods: {
                        handleCarouselChange(index) {
                            const { recommendGroupSize } = this

                            if (recommendGroupSize - 1 === index) {
                                this.handleRecommendTabAutoPlay()
                            }
                        },

                        handleCarouseMouse(status) {
                            if (status) {
                                clearTimeout(this.timer)
                            } else {
                                this.handleCarouselAutoPlay()
                            }
                        },

                        handleCarouselAutoPlay() {
                            const { recommendGroupSize } = this
                            if (recommendGroupSize === 1) {
                                this.handleRecommendTabAutoPlay()
                            }
                        },

                        handleRecommendTabClick(index) {
                            clearTimeout(this.timer)
                            this.autoplay = false
                            this.recommendIndex = index
                            this.$refs.recommendRef.setActiveItem(0)
                            this.$nextTick(function () {
                                this.autoplay = true
                            })
                        },

                        handleRecommendTabAutoPlay() {
                            const _this = this
                            const { recommendIndex, interval, recommendTabSize } = this

                            if (this.timer) {
                                clearTimeout(this.timer)
                            }

                            this.timer = setTimeout(function () {
                                const index = recommendIndex + 1
                                _this.handleRecommendTabClick(index > recommendTabSize - 1 ? 0 : index)
                            }, interval)
                        },

                        getCalcDataList(checkedList, dataList) {
                            const defaultChecked = checkedList.length ? checkedList : ['']

                            return dataList.map(function (item) {
                                const { label, value } = item
                                const checked = defaultChecked.includes(value)
                                return { label, value, checked }
                            })
                        },

                        getQueryParams() {
                            const query = `{"${location.search.replace('?', '')}"}`
                            const params = query.replace(/&/g, '","').replace(/=/g, '": "')
                            const arrayKeys = ['jobType', 'areaId', 'companyType', 'majorId']

                            try {
                                const data = JSON.parse(params)

                                Object.keys(data).forEach(function (item) {
                                    const value = decodeURIComponent(data[item])
                                    if (item === 'currentPage') {
                                        data[item] = value * 1
                                    } else {
                                        data[item] = arrayKeys.includes(item) ? value.split('_') : value
                                    }
                                })
                                return data
                            } catch {
                                return {}
                            }
                        },

                        getSearchData() {
                            const {
                                keyword,
                                jobType,
                                areaId,
                                companyType,
                                majorId,
                                educationType,
                                companyNature,
                                industryId,
                                releaseTimeType,
                                titleType,
                                natureType,
                                isEstablishment,
                                isFast,
                                isFresh,
                                isCooperation,
                                currentPage
                            } = this

                            return {
                                keyword,
                                jobType,
                                areaId,
                                companyType,
                                majorId,
                                educationType,
                                companyNature,
                                industryId,
                                releaseTimeType,
                                titleType,
                                natureType,
                                isEstablishment,
                                isFast,
                                isFresh,
                                isCooperation,
                                currentPage
                            }
                        },

                        updateQueryValue(key, value) {
                            // 未登录弹登录框
                            if (!this.isLogin) {
                                this.handleShowLogin()
                                return false
                            }
                            const methodName = key === 'currentPage' ? 'handleSearch' : 'handleSearchAgain'
                            this[key] = value
                            this[methodName]()
                        },

                        updateQueryData(data) {
                            const {
                                keyword,
                                jobType,
                                areaId,
                                companyType,
                                majorId,
                                educationType,
                                companyNature,
                                industryId,
                                releaseTimeType,
                                titleType,
                                natureType,
                                isEstablishment,
                                isFast,
                                isFresh,
                                isCooperation,
                                currentPage
                            } = data

                            this.keyword = keyword || ''
                            this.jobType = jobType || []
                            this.areaId = areaId || []
                            this.companyType = companyType || []
                            this.majorId = majorId || []
                            this.educationType = educationType || ''
                            this.companyNature = companyNature || ''
                            this.industryId = industryId || ''
                            this.releaseTimeType = releaseTimeType || ''
                            this.titleType = titleType || ''
                            this.natureType = natureType || ''
                            this.isEstablishment = isEstablishment || '2'
                            this.isFast = isFast || '2'
                            this.isFresh = isFresh || '2'
                            this.isCooperation = isCooperation || '2'
                            this.currentPage = currentPage || 1
                        },

                        handleSearch(forward) {
                            let updateRoute = !!forward || true
                            const search = decodeURIComponent(location.search)
                            const data = this.getSearchData()
                            const params = {}
                            const checkboxKeys = ['isFast', 'isFresh', 'isCooperation', 'isEstablishment']

                            const query = Object.keys(data).reduce(function (previous, current) {
                                let value = data[current]

                                if (checkboxKeys.includes(current) && value === '2') {
                                    value = ''
                                }

                                if (current === 'currentPage' && value === 1) {
                                    value = ''
                                }

                                if (value) {
                                    const prefix = `${previous}${previous === '' ? '?' : '&'}`

                                    if (Array.isArray(value)) {
                                        if (value.length) {
                                            params[current] = value.join(',')
                                            return `${prefix}${current}=${value.join('_')}`
                                        } else {
                                            return previous
                                        }
                                    } else {
                                        params[current] = value
                                        return `${prefix}${current}=${value}`
                                    }
                                }

                                return previous
                            }, '')

                            if (typeof forward === 'boolean' && forward === false) {
                                updateRoute = false
                            }

                            if (updateRoute && search !== query) {
                                history.pushState(JSON.stringify(data), '', query || location.pathname)
                            } else {
                                this.getParamsText()
                            }

                            setTimeout(function () {
                                $('#component').find('.search-keyword .el-input__inner').trigger('focus')
                                $(window).scrollTop(0)
                            }, 0)

                            this.getSearchResult(params)
                        },

                        handleSearchAgain() {
                            this.currentPage = 1
                            this.handleSearch()
                        },

                        handleReset() {
                            const { calcFilterCount } = this

                            if (calcFilterCount) {
                                const { keyword, isFast, isFresh, isCooperation, isEstablishment } = this

                                this.jobTypeText = []
                                this.areaText = []
                                this.updateQueryData({ keyword, isFast, isFresh, isCooperation, isEstablishment })
                                this.handleSearchAgain()
                            }
                        },

                        handleCheck(key, data) {
                            // 未登录弹登录框
                            if (!this.isLogin) {
                                this.handleShowLogin()
                                return false
                            }
                            const _this = this
                            const { label, value, limit } = data
                            const keyValue = this[key]

                            const index = keyValue.findIndex(function (item) {
                                return item === value
                            })
                            const size = limit || 5

                            const isArea = key === 'areaId'

                            let needFetchData = true

                            const validHandler = function () {
                                if (index > -1) {
                                    keyValue.splice(index, 1)

                                    isArea && _this.areaText.splice(index, 1)
                                } else {
                                    if (keyValue.length < size) {
                                        keyValue.push(value)

                                        isArea && _this.areaText.push(label)
                                    } else {
                                        needFetchData = false
                                        return ElementPlus.ElMessage.warning(`您最多可选${size}项`)
                                    }
                                }
                                _this[key] = keyValue
                            }

                            const invalidHandler = function () {
                                _this[key] = []

                                if (isArea) {
                                    _this.areaText = []
                                }
                            }

                            value ? validHandler() : invalidHandler()

                            needFetchData && this.handleSearchAgain()
                        },

                        handleDialogClear(name) {
                            const _this = this
                            this.dialogActive = name

                            this.$nextTick(function () {
                                const [valKey, textKey] = _this.calcDialogValueTextKey

                                _this[valKey] = []
                                _this[textKey] = []
                                _this.handleSearchAgain()
                            })
                        },

                        handleDialogActive(name) {
                            this.dialogActive = name
                            this.$refs.selectDialogRef.handleOpen()
                        },

                        handleDialogChange(data) {
                            const [valKey, textKey] = this.calcDialogValueTextKey
                            const { label, value } = data

                            if (textKey) {
                                this[textKey] = label
                                this.handleSearchAgain()
                            }
                        },

                        handleCurrentChange(value) {
                            this.updateQueryValue('currentPage', value)
                        },

                        getOperateClassName(name, arr) {
                            if (name.length && arr.length) {
                                return ''
                            } else {
                                if (name.length || arr.length) {
                                    return ''
                                }
                                return 'is-special'
                            }
                        },

                        getConfigData() {
                            const _this = this

                            httpGet('/job/home-showcase-list')
                                .then(function (data) {
                                    const { recommend, search, aside } = data

                                    // _this.recommendData = recommend

                                    _this.hotSearchList = search
                                    _this.asideLinkList = aside

                                    _this.handleCarouselAutoPlay()
                                })
                                .catch(function () {})
                        },

                        getParamsData() {
                            const _this = this

                            httpGet('/job/home-params')
                                .then(function (data) {
                                    const {
                                        jobTypeList,
                                        hotAreaList,
                                        areaList,
                                        companyTypeList,
                                        majorList,
                                        educationList,
                                        companyNatureList,
                                        industryList,
                                        releaseList,
                                        titleList,
                                        natureList,
                                        selectButtonList
                                    } = data
                                    const areaItem = { label: '全国', value: '' }
                                    const companyTypeItem = { label: '全部', value: '' }

                                    hotAreaList.unshift(areaItem)
                                    companyTypeList.unshift(companyTypeItem)

                                    _this.jobTypeList = jobTypeList
                                    _this.hotAreaList = hotAreaList
                                    _this.areaList = areaList
                                    _this.companyTypeList = companyTypeList
                                    _this.majorList = majorList
                                    _this.educationList = educationList
                                    _this.companyNatureList = companyNatureList
                                    _this.industryList = industryList
                                    _this.releaseList = releaseList
                                    _this.titleList = titleList
                                    _this.natureList = natureList
                                    _this.filterCheckbox = selectButtonList
                                })
                                .catch(function () {})
                        },

                        getParamsText() {
                            const _this = this
                            const { jobType, areaId, majorId } = this
                            const params = { jobType: jobType.join(','), areaId: areaId.join(','), majorId: majorId.join(',') }

                            httpGet('/job/home-params-text', params)
                                .then(function (data) {
                                    const { jobTypeText, areaText, majorText } = data

                                    _this.jobTypeText = jobTypeText
                                    _this.areaText = areaText
                                    _this.majorText = majorText
                                })
                                .catch(function () {})
                        },

                        getSearchResult(params) {
                            const _this = this

                            this.loading = true
                            const { isLogin, isVip } = this
                            if ('isEstablishment' in params) {
                                if (!isLogin) {
                                    this.handleShowLogin()
                                    return false
                                } else {
                                    if (isVip !== true) {
                                        this.$confirm('开通VIP服务可一键筛选有编制的职位，捕捉每一次入编机会', '提示', {
                                            confirmButtonText: '升级VIP',
                                            cancelButtonText: '取消',
                                            buttonSize: 'large'
                                        })
                                            .then(() => {
                                                // todo 跳转至VIP介绍页
                                                window.open('/vip.html', '_blank')
                                            })
                                            .catch(() => {})
                                    }
                                }
                            }
                            httpGet('/job/home-list', params)
                                .then(function (data) {
                                    const { list, totalNum, showcaseInfo = {} } = data
                                    const { img: src, url, position: index } = showcaseInfo

                                    if (src) {
                                        list.splice(index, 0, { url, src })
                                    }

                                    _this.total = totalNum || 0
                                    _this.resultList = list || []
                                    _this.loading = false
                                })
                                .catch(function () {})
                        },

                        getUserData() {
                            const _this = this
                            return new Promise(function (resolve, reject) {
                                httpGet('/job/home-user-info')
                                    .then(function (data) {
                                        const { isLogin, isVip, hasJobIntention, hasJobSubscription, bindCodeUrl } = data

                                        _this.isLogin = isLogin
                                        _this.isVip = isVip
                                        _this.hasJobIntention = hasJobIntention
                                        _this.hasJobSubscription = hasJobSubscription
                                        _this.bindCodeUrl = bindCodeUrl

                                        if (!isLogin && decodeURIComponent(location.search).includes('isEstablishment')) {
                                            window.location.href = '/job'
                                        }

                                        resolve()
                                    })
                                    .catch(function (r) {
                                        reject(r)
                                    })
                            })
                        },

                        handleShowLogin() {
                            window.globalComponents.loginDialogComponent.showLoginDialog()
                        },

                        handleCompleteResume() {
                            window.location.href = '/member/person/'
                        },

                        handleGuideClick() {
                            const { isLogin, hasJobIntention } = this

                            if (!isLogin) {
                                this.handleShowLogin()
                                return false
                            }

                            if (!hasJobIntention) {
                                this.handleCompleteResume()
                                return false
                            }

                            return true
                        },

                        handleSubscribeClick() {
                            const { hasJobSubscription } = this
                            const validAccount = this.handleGuideClick()

                            if (validAccount && !hasJobSubscription) {
                                window.globalComponents.subscribeDialog.showSelectDialog(() => {
                                    this.hasJobSubscription = true
                                })
                            }
                        },

                        handleEstablishmentClick() {
                            const { isLogin, isVip } = this
                            if (!isLogin) {
                                this.handleShowLogin()
                                return false
                            } else {
                                if (isVip !== true) {
                                    this.showVipDialog()
                                }
                            }
                            // this.handleShowLogin()
                        },

                        showVipDialog() {
                            this.$confirm('开通VIP服务可一键筛选有编制的职位，捕捉每一次入编机会', '提示', {
                                confirmButtonText: '升级VIP',
                                cancelButtonText: '取消',
                                buttonSize: 'large'
                            })
                                .then(() => {
                                    window.open('/vip.html')
                                })
                                .catch(() => {})
                        },

                        getValidValue(val) {
                            return val === '1'
                        },

                        getIsActivity(val) {
                            return val === '3'
                        },

                        getSelectJobIndex(jobId) {
                            const { resultList } = this
                            const index = resultList.findIndex(function (item) {
                                return item.jobId === jobId
                            })

                            return index
                        },

                        getSelectJobData(jobId) {
                            const { resultList } = this
                            const index = this.getSelectJobIndex(jobId)

                            return index > -1 ? resultList[index] : false
                        },

                        updSelectJobData(jobId, key, val) {
                            const { resultList } = this
                            const item = this.getSelectJobData(jobId)
                            const index = this.getSelectJobIndex(jobId)

                            item[key] = val
                            this.resultList.splice(index, 1, item)
                        },

                        getCollectData(jobId) {
                            const data = this.getSelectJobData(jobId)

                            if (data) {
                                const { isCollect } = data
                                const valid = this.getValidValue(isCollect)

                                return [valid ? 'is-checked' : '', valid ? '已收藏' : '收藏']
                            }
                            return ['', '收藏']
                        },

                        getCollectClassName(jobId) {
                            const [className, text] = this.getCollectData(jobId)
                            return className
                        },

                        getCollectText(jobId) {
                            const [className, text] = this.getCollectData(jobId)
                            return text
                        },

                        handleCollectClick(jobId) {
                            const _this = this

                            httpPost('/api/person/job/collect', { jobId })
                                .then(function (data) {
                                    const { isCollect } = data

                                    _this.updSelectJobData(jobId, 'isCollect', isCollect)
                                })
                                .catch(function () {})
                        },

                        getApplyData(jobId) {
                            const data = this.getSelectJobData(jobId)

                            if (data) {
                                const { status, applyStatus, templateId } = data
                                const isActivity = this.getIsActivity(templateId)
                                const options = {
                                    1: {
                                        disabled: true,
                                        text: isActivity ? '已报名' : '已申请'
                                    },
                                    2: {
                                        disabled: false,
                                        text: '申请职位'
                                    }
                                }

                                if (status === '0') {
                                    return [true, '已下线']
                                }

                                const value = options[applyStatus]

                                if (value) {
                                    const { disabled, text } = value
                                    return [disabled, text]
                                }

                                return [false, '申请职位']
                            }
                            return [false, '申请职位']
                        },

                        getApplyDisabled(jobId) {
                            const [disabled, text] = this.getApplyData(jobId)
                            return disabled
                        },

                        getApplyText(jobId) {
                            const [disabled, text] = this.getApplyData(jobId)
                            return text
                        },

                        handleActivity(jobId, resumeStep) {
                            const _this = this

                            if (resumeStep > 3) {
                                httpPost('/api/person/job/apply', { jobId })
                                    .then(function (data) {
                                        window.globalComponents.successDialogApplyJob.showSuccessDialog(data, function () {
                                            const { applyStatus } = data

                                            _this.updSelectJobData(jobId, 'applyStatus', applyStatus)
                                        })
                                    })
                                    .catch(function () {})
                            } else {
                                ElementPlus.ElMessage({
                                    message: `当前简历信息不完善，请先完善简历！5S后自动帮您打开简历完善页面`,
                                    type: 'warning',
                                    duration: 4000,
                                    onClose: () => {
                                        window.open('/member/person/resume')
                                    }
                                })
                            }
                        },

                        handleApplyClick(jobId) {
                            const _this = this

                            window.globalComponents.applyDialogComponent.beforeApply(jobId, function (data) {
                                const { applyStatus } = data

                                _this.updSelectJobData(jobId, 'applyStatus', applyStatus)
                            })
                        },

                        handleChatClick(jobId, index) {
                            window.globalComponents.ChatDialogComponent.create(jobId, (resp) => {
                                const { chatButtonText = '聊一聊' } = resp
                                this.resultList[index].chatButtonName = chatButtonText
                            })
                        },

                        popStateHandler() {
                            let state = {}

                            try {
                                state = JSON.parse(history.state) || {}
                            } catch {}

                            this.updateQueryData(state)
                            this.handleSearch(false)
                        },

                        visibilityChangeHandler() {
                            if (!document.hidden) {
                                this.getUserData()
                            }
                        },

                        async handleInit() {
                            this.updateQueryData(this.getQueryParams())
                            await this.getUserData()
                            this.getConfigData()
                            this.getParamsData()
                            this.handleSearch(false)
                        },

                        bindScrollEvent() {
                            let previousScrollTop = 0
                            let defaultSearchHeight = 0
                            const _this = this
                            const $body = $('body')
                            const $header = $('.el-header')

                            const bodyPaddingTop = $body.css('padding-top')
                            const headerHeight = $header.height()

                            $(window).on('scroll', function () {
                                const scrollTop = $(window).scrollTop()

                                const $searchHeader = $('#component .search-header')
                                const $searchResult = $('#component .search-result')
                                const $searchAside = $('#component .search-aside')
                                const $searchAsideLink = $('#component .aside-link')

                                const searchHeaderHeight = $searchHeader.outerHeight()
                                const searchResultHeight = $searchResult.outerHeight()
                                const searchAsideHeight = $searchAside.outerHeight()
                                const searchAsideLinkOffset = $searchAsideLink.offset()

                                const isDown = scrollTop > previousScrollTop

                                previousScrollTop = scrollTop

                                if (searchHeaderHeight > defaultSearchHeight) {
                                    defaultSearchHeight = searchHeaderHeight
                                }

                                const paddingTop = headerHeight + defaultSearchHeight
                                const fixed = scrollTop > paddingTop

                                if (fixed) {
                                    $body.css('padding-top', paddingTop)
                                    $header.addClass('is-fixed')
                                    $searchHeader.addClass('is-fixed')

                                    if (searchAsideLinkOffset) {
                                        const fixedSearchHeaderHeight = $searchHeader.outerHeight()
                                        const searchAsideLinkOffsetTop = searchAsideLinkOffset.top
                                        const currentPaddingTop = parseInt($searchAsideLink.css('padding-top'))
                                        const nextPaddingTop = scrollTop - searchAsideLinkOffsetTop + headerHeight + fixedSearchHeaderHeight + 20
                                        const value = nextPaddingTop - currentPaddingTop

                                        if (nextPaddingTop > 0) {
                                            if (searchAsideHeight + value > searchResultHeight && isDown) return
                                            $searchAsideLink.css('padding-top', nextPaddingTop)
                                        } else {
                                            $searchAsideLink.css('padding-top', 0)
                                        }
                                    }
                                } else {
                                    $body.css('padding-top', bodyPaddingTop)
                                    $header.removeClass('is-fixed')
                                    $searchHeader.removeClass('is-fixed')
                                    $searchAsideLink.css('padding-top', 0)
                                }
                            })
                        },
                        openVip() {
                            const { isLogin, isVip } = this
                            if (!isLogin) {
                                this.handleShowLogin()
                                return false
                            } else {
                                window.open('/vip.html', '_blank')
                            }
                        }
                    },

                    mounted() {
                        this.handleInit()
                        this.bindScrollEvent()
                        window.addEventListener('popstate', this.popStateHandler)
                        document.addEventListener('visibilitychange', this.visibilityChangeHandler)
                    }
                }

                Vue.createApp(component)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                },
                                select: {
                                    loading: '\u52A0\u8F7D\u4E2D',
                                    noMatch: '\u65E0\u5339\u914D\u6570\u636E',
                                    noData: '\u65E0\u6570\u636E',
                                    placeholder: '\u8BF7\u9009\u62E9'
                                }
                            }
                        }
                    })
                    .component('select-dialog', selectDialogComponent)
                    .mount('#component')
            })
        </script>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <!-- 底部测试滚动使用 -->
        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/index.html" target="_blank">关于我们</a>| <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/contactUs.html">联系我们</a>|
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/joinUs.html" target="_blank">人才招聘</a>| <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/productService.html">产品服务</a>|
                <a href="http://www.gaoxiaojob.com/zhaopin/aboutUs/disclaimers.html" target="_blank">免责声明</a>| <a href="/data/sitemap.html" target="_blank">网站导航</a>|
                <a href="//www.gaoxiaojob.com/zhaopin/zhuanti/zzzm2021/index.html" target="_blank">资质证明</a>|
                <a href="//gaocai.gaoxiaojob.com" target="_blank">高才科技官网</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2023 高校人才网 版权所有 网站备案信息：
                    <a href="https://beian.miit.gov.cn/" target="_blank">粤ICP备13048400号</a>
                    粤公网安备：
                    <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010602004138" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="//zt.gaoxiaojob.com/zzdxywjyxkz.jpg" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：************ 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <script>
            $(function () {
                window.headerLoginGuideAutoShow()
            })
        </script>
    </body>
</html>
