@import './variables';

.success-dialog-alert-template {
    .el-dialog {
        width: 560px;
    }

    .el-dialog__header {
        padding: 0;

        .el-dialog__title {
            font-size: 20px;
            font-weight: 700;
        }
    }

    .el-dialog__body {
        padding: 0;
        .el-form-item__label {
            color: $font-color-basic;
            font-size: 14px;
        }
        .sussess-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            div {
                text-align: left;
                line-height: 2;
                color: $font-color;
            }
            img {
                width: 100px;
                height: 100px;
                margin: 20px auto;
            }
            .el-button {
                width: 200px;
            }
        }
    }
    .wechat-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
        color: $font-color;
        .checktips {
            margin-bottom: 18px;
            padding-top: 40px;
            font-size: 20px;
            text-align: center;
            .online-tips {
                text-align: center;
                width: 168px;
                line-height: 24px;
                margin-top: 15px;
                margin-bottom: -5px;
                font-size: 12px;
                color: $color-primary;
                background-color: #fff3e0;
            }
        }
        .recommend {
            font-size: 16px;
            color: $color-primary;
            width: 427px;
            text-align: center;
            font-weight: 700;
            margin-bottom: -20px;
            background: url(../assets/icon/recommend.png) no-repeat right center/450px 18px;
        }
        .relogin-content {
            display: flex;
            padding: 40px 30px 30px 30px;
            text-align: center;
            width: 100%;
            img {
                width: 60px;
                height: 60px;
            }
            .right-content {
                margin-left: 15px;
            }
            .analyse {
                width: 320px;
                display: flex;
                padding: 15px 20px;
                align-items: center;
                box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.1);
                border-radius: 10px;
                margin-right: 20px;
                .text-analyse {
                    display: flex;
                    font-size: 14px;
                    align-items: center;
                    color: #868686;
                    margin-top: 15px;
                    .see-analysis {
                        cursor: pointer;
                        padding-right: 10px;
                        font-size: 14px;
                        color: $color-primary;
                        background: url(../assets/detail/to-next.png) no-repeat right center/5px 7px;
                    }
                }
            }
            .gaocai-vip {
                width: 320px;
                display: flex;
                padding: 15px 20px;
                align-items: center;
                box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.1);
                border-radius: 10px;
                .text-vip {
                    display: flex;
                    font-size: 14px;
                    align-items: center;
                    color: #868686;
                    margin-top: 15px;
                }
            }
            p {
                // margin-bottom: 50px;
                text-align: left;
                font-size: 14px;
                color: #333333;
                font-weight: 700;
            }
            button {
                width: 100%;
            }
        }
        .tips-text {
            display: flex;
            margin: 0 0 30px 0;
            justify-content: center;
            font-size: 14px;
            .qr-code {
                position: relative;
                width: 150px;
                height: 150px;
                padding: 2px;
                background: url(../assets/icon/base-code.png) no-repeat center/150px 150px;
                margin-right: 30px;
                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    // border: 1px solid #ebebeb;
                    // border-radius: 10px;
                }
                .logo {
                    position: absolute;
                    top: 55px;
                    left: 55px;
                    width: 40px;
                    height: 40px;
                    border: none;
                    border-radius: 4px;
                    object-fit: contain;
                }
            }
            .text-right {
                font-size: 14px;
                color: rgba(51, 51, 51, 0.8);
                text-align: center;
                margin-top: 10px;
                padding: 0 80px;
                line-height: 1.5;
                .text-second {
                    font-size: 12px;
                    margin-top: 10px;
                    color: $color-primary;
                    text-align: center;
                }
                .select {
                    background: url(../assets/icon/symbol.png) no-repeat left center/15px 15px;
                    padding-left: 20px;
                    margin-bottom: 5px;
                    letter-spacing: 1px;
                    font-weight: 700;
                }
            }
        }

        .wechat-footer {
            width: 300px;
        }
        .el-message-box__btns {
            margin-bottom: 60px;
            text-align: center;
            .el-button {
                width: 160px;
                color: rgba(51, 51, 51);
            }
            .el-button--primary {
                margin-left: 40px;
                color: #fff;
            }
            .change {
                &:hover {
                    color: #ffa000;
                }
            }
        }
    }

    .el-dialog__footer {
        padding: 0;

        .el-button {
            & + .el-button {
                margin-left: 20px;
            }
        }
    }
}
