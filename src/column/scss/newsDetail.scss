@import './variables';

@import './common/currentLocation';
@import './common/commonTitle';
@import './common/sideBar.scss';
@import './common/component/detailShare';

$border-color-contact: #efefef;

.column-wrapper {
    .main {
        &::after {
            display: block;
            content: '';
            clear: both;
        }

        .left {
            width: 845px;
            float: left;
            margin-right: 10px;
            padding: 0 30px;
            background-color: #fff;

            .article-top {
                text-align: center;

                .title {
                    font-size: 18px;
                    font-weight: bold;
                    margin: 30px 0 12px;
                    padding: 0 15px;
                }

                .info {
                    color: rgba(51, 51, 51, 0.6);
                    text-align: center;
                    padding-bottom: 20px;
                    border-bottom: 1px solid #ededed;

                    small {
                        font-size: 13px;
                    }

                    .origin {
                        margin: 0 20px;
                        display: inline-block;
                    }

                    a {
                        color: $color-primary;
                        display: inline-block;
                        font-size: 13px;
                        margin-left: 20px;
                        border-bottom: 1px solid $color-primary;
                    }
                }
            }

            .content {
                .abstract {
                    font-weight: normal;
                    padding: 7px 14px 7px;
                    border-radius: 4px;
                    font-size: 14px;
                    background-color: #ededed;
                    margin: 20px 0;
                    line-height: 2;
                }

                .article {
                    @import './common/wangeditor';

                    a {
                        display: revert;
                        color: revert;

                        &:hover {
                            color: revert;
                            text-decoration: revert;
                        }
                    }
                }

                .origin {
                    padding: 30px 0;
                    border-bottom: 1px solid #ededed;
                }

                .qr {
                    padding-top: 25px;
                    text-align: center;
                    color: $color-primary;
                    font-weight: bold;

                    img {
                        width: 150px;
                        height: 150px;
                        margin: 20px auto;
                    }
                }
            }
        }

        .right {
            float: right;
            width: 345px;

            .recommend {
                background-color: $color-white;
                padding: 20px;
                width: 100%;
                overflow: hidden;

                .common-title {
                    padding-bottom: 10px;
                }

                ul {
                    a {
                        position: relative;
                        padding: 4px 0 4px 9px;
                        display: flex;
                        align-items: center;

                        &::before {
                            position: absolute;
                            content: '';
                            left: 0;
                            top: 12px;
                            display: block;
                            width: 4px;
                            height: 4px;
                            border-radius: 50%;
                            background-color: $color-primary;
                            margin-right: 5px;
                            flex-shrink: 0;
                        }

                        .title {
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            padding-right: 9px;
                        }
                    }
                }
            }
        }
    }
}
