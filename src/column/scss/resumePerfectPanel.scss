@use './variables' as *;

$height-primary: 102px;
$close-primary: 24px;

.resume-perfect-panel-template {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 41%, rgba(255, 154, 65, 0.92) 41%);
    z-index: 99;
    min-width: 1200px;

    .resume-perfect-panel-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 320px;
        padding-top: 42px;
        width: 1200px;
        margin: 0 auto;
        height: $height-primary;
        color: $color-white;
        background: url('../assets/footer/monkey.png') no-repeat left bottom/128px, url('../assets/footer/tips.png') no-repeat 180px 60px/120px;
    }

    .resume-perfect-tips {
        flex-grow: 1;
        font-size: 17px;
        padding-top: 8px;

        .percentage {
            font-weight: bold;
            color: #fff646;
            font-size: 18px;
        }
    }

    @mixin elHover {
        &:hover {
            background-color: transparent;
            color: $color-white;
            border-color: $color-white;
        }
    }

    .resume-perfect-btn {
        padding: 0;
        width: 120px;
        height: 32px;
        min-height: 32px;
        line-height: 30px;
        border: 1px solid $color-white;
        color: $color-white;
        background-color: transparent;
        margin: 0 10px 0 15px;
        border-radius: 16px;

        @include elHover;
    }

    .close-button {
        margin-left: 10px;
        padding: 0;
        width: $close-primary;
        height: $close-primary;
        min-height: $close-primary;
        background: transparent;
        border: 1px solid $color-white;
        border-radius: 50%;

        .el-icon {
            color: $color-white;
        }

        @include elHover;
    }
}
