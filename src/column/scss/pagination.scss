@use './variables' as *;

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;

    @mixin page-btn {
        display: inline-block;
        margin: 0 5px;
        background-color: #fff;
        color: $font-color;
        border-radius: 2px;
        min-width: 32px;
        padding: 0 4px;
        height: 30px;
        line-height: 30px;
        text-align: center;

        &:hover {
            color: $color-primary;
        }
    }

    .more {
        @include page-btn;
    }

    .btn-quickprev {
        &:hover {
            &::before {
                content: '';
            }
        }
    }

    .btn-quicknext {
        &:hover {
            &::before {
                content: '';
            }
        }
    }

    .number {
        @include page-btn;
        &.current {
            color: #fff;
            background-color: $color-primary;
        }
    }

    .btn-prev,
    .btn-next {
        @include page-btn;
        position: relative;

        &::after {
            content: '';
            width: 10px;
            height: 10px;
            left: 50%;
            top: 50%;
            position: absolute;
            border-left: 2px solid $font-color-basic;
            border-bottom: 2px solid $font-color-basic;
            transform-origin: center;
            transform: scale(0.8) translate(-50%, -50%) rotate(45deg);
        }

        &.disabled {
            cursor: not-allowed;

            &::after {
                opacity: 0.6;
            }
        }
    }

    .btn-next {
        &::after {
            transform: scale(0.8) translate(-50%, -50%) rotate(-135deg);
        }
    }
}
