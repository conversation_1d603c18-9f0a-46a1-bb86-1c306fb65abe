@import './variables';

.success-dialog-alert-template {
    .pay-dialog {
        width: 820px !important;
        overflow: hidden;

        .el-dialog__headerbtn {
            width: 0;
            height: 0;
            top: 25px;
            right: 40px;
            i.el-dialog__close {
                color: #333;
                font-size: 16px;
                font-weight: bold;
            }
        }

        .el-dialog__header {
            padding: 0;

            .el-dialog__title {
                font-size: 20px;
                font-weight: 700;
            }
        }

        .el-dialog__body {
            padding: 0;
            .el-form-item__label {
                color: $font-color-basic;
                font-size: 14px;
            }
            .sussess-box {
                display: flex;
                flex-direction: column;
                align-items: center;
                div {
                    text-align: left;
                    line-height: 2;
                    color: $font-color;
                }
                img {
                    width: 100px;
                    height: 100px;
                    margin: 20px auto;
                }
                .el-button {
                    width: 200px;
                }
            }
        }
        .pay-container {
            display: flex;
            flex-direction: column;
            // align-items: center;
            font-size: 14px;
            .tips {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20px;
                color: #333;
                background: #fdf5e8;
                width: 100%;
                border-radius: 10px 10px 0 0;
                font-size: 22px;
                .online-tips {
                    text-align: center;
                    width: 168px;
                    line-height: 24px;
                    margin-top: 15px;
                    margin-bottom: -5px;
                    font-size: 12px;
                    color: $color-primary;
                    background-color: #fff3e0;
                }
            }
            .recommend {
                font-size: 16px;
                color: $color-primary;
                width: 427px;
                text-align: center;
                font-weight: 700;
                margin-bottom: -20px;
                background: url(../assets/icon/recommend.png) no-repeat right center/450px 18px;
            }
            .relogin-content {
                position: relative;
                display: flex;
                padding: 25px 30px;
                text-align: center;
                width: 100%;
                background: url(../assets/icon/choose-price.png) no-repeat center top/120px 16px, url(../assets/icon/price-background.png) no-repeat left top/cover;
                .qr-code {
                    position: relative;
                    width: 150px;
                    height: 150px;
                    padding: 2px;
                    background: url(../assets/icon/base-code.png) no-repeat center/150px 150px;
                    margin-right: 30px;
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        // border: 1px solid #ebebeb;
                        // border-radius: 10px;
                    }
                    .logo {
                        position: absolute;
                        top: 55px;
                        left: 55px;
                        width: 40px;
                        height: 40px;
                        border: none;
                        border-radius: 4px;
                        object-fit: contain;
                    }
                    .coderefresh {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        .mask-content {
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            position: absolute;
                            left: 50%;
                            top: 48%;
                            font-size: 14px;
                            transform: translate(-50%, -50%);
                            z-index: 999;
                            color: #fff;
                            cursor: pointer;
                            .img-refresh {
                                width: 58px;
                                height: 58px;
                            }
                        }
                        .mask {
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            left: 0;
                            top: 0;
                            z-index: 2;
                            // background-color: rgba($color: #fff, $alpha: 0.9);
                            background: #9a9a9a;
                            opacity: 0.9;
                            color: #fff;
                        }
                    }
                }
                .text-right {
                    font-size: 14px;
                    color: #333;
                    text-align: left;
                    margin-top: 15px;
                    .product-name {
                        font-size: 16px;
                        font-weight: 700;
                        display: flex;
                        align-items: flex-start;
                        line-height: 1.5;
                        span {
                            font-weight: 700;
                            max-width: 480px;
                        }
                    }
                    .product-price {
                        font-weight: 700;
                        font-size: 16px;
                        margin: 13px 0 25px 0;
                        display: flex;
                        align-items: baseline;
                        span {
                            color: #fa665f;
                            font-weight: 700;
                            font-size: 24px;
                        }

                        .convert {
                            font-size: 12px;
                            margin-left: 7px;
                        }
                        .what {
                            cursor: pointer;
                            width: 10px;
                            height: 10px;
                            margin-left: 5px;
                            background: url('../assets/vip/what.png') no-repeat center/contain;
                        }
                    }
                    .suppert {
                        display: flex;
                        align-items: center;
                        color: #333;
                        margin-bottom: 12px;
                        .select {
                            display: flex;
                            align-items: center;
                        }
                        .select::after {
                            content: '';
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                            background: url(../assets/icon/weixin.png) no-repeat center/contain;
                            letter-spacing: 1px;
                            margin: 0 4px;
                        }
                    }
                    .finish {
                        color: #969696;
                        display: flex;
                        span {
                            color: $color-primary;
                            cursor: pointer;
                        }
                    }
                }
            }
            .pay-text {
                display: flex;
                flex-direction: column;
                justify-content: center;
                font-size: 12px;
                padding: 20px 30px;
                width: 100%;

                .title-vip {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-bottom: 20px;
                    color: $font-color;

                    .reminder::before {
                        content: '*';
                        color: #e54d42;
                        font-size: 100%;
                        margin-right: 3px;
                    }
                    p {
                        display: flex;
                        align-items: center;
                        font-size: 16px;
                        font-weight: bold;

                        .upgrades-tips {
                            font-size: 12px;
                            color: #683e2e;
                            margin-left: 9px;
                        }
                    }
                    span {
                        color: $color-primary;
                        cursor: pointer;
                    }
                }

                .upgrades-content {
                    line-height: 28px;
                    background: url(../assets/vip/vip-icon.png) no-repeat 13px center/40px 15px, linear-gradient(90deg, #fff3ea, #f9e7d3);
                    border-radius: 4px;
                    padding: 0 10px 0 58px;
                    margin-bottom: 20px;
                    color: #683e2e;
                    font-size: 14px;

                    .color-primary {
                        font-weight: bold;
                    }
                }

                .price-content {
                    display: flex;
                    justify-content: space-between;
                    margin: 0px -30px 0;

                    .swiper {
                        width: 100%;

                        .swiper-wrapper {
                            padding-top: 10px;
                            height: auto;
                        }

                        .swiper-slide {
                            width: 240px;
                            box-sizing: border-box;
                            margin-right: 20px;

                            &:first-child {
                                margin-left: 30px;
                            }

                            &:last-child {
                                margin-right: 30px;
                            }

                            &.col-4 {
                                width: 205px;
                            }
                        }

                        .swiper-button-next,
                        .swiper-button-prev {
                            width: 32px;
                            height: 32px;
                            box-shadow: 0 16px 48px 8px rgba(28, 31, 35, 0.08);
                            border-radius: 50%;
                            margin-top: 0;
                            transform: translateY(calc(-50% + 5px));

                            &::after {
                                display: none;
                            }

                            &.swiper-button-disabled {
                                display: none;
                            }
                        }

                        .swiper-button-next {
                            right: 4px;
                            background: url('../assets/vip/swiper-next.png') no-repeat center/cover;
                        }

                        .swiper-button-prev {
                            left: 4px;
                            background: url('../assets/vip/swiper-prev.png') no-repeat center/cover;
                        }
                    }

                    .price {
                        padding: 30px 30px 0;
                        min-height: 122px;
                        color: #333333;
                        background: #f9f9f9;
                        border-radius: 10px;
                        font-size: 16px;
                        font-weight: 700;
                        cursor: pointer;
                        position: relative;
                        .price-title {
                            margin-bottom: 20px;
                            .price-title-first {
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                            }
                        }

                        .original-price {
                            font-size: 30px;
                            span {
                                font-size: 12px;
                            }
                            .del-price {
                                margin-left: 10px;
                                font-weight: 400;
                                font-size: 12px;
                                color: #969696;
                                text-decoration: line-through;
                            }
                        }
                        .prompt {
                            display: none;
                            padding: 0 5px;
                            color: #fff;
                            background: linear-gradient(90deg, #ffa000, #fa635c);
                            border-radius: 10px 0px 10px 0px;
                            position: absolute;
                            font-size: 12px;
                            line-height: 22px;
                            top: -11px;
                            left: -2px;
                        }
                    }
                    .active {
                        color: $color-primary;
                        background: #fffcf5;
                        border: 2px solid #ffa000;

                        .del-price {
                            color: $color-primary !important;
                            opacity: 0.4;
                        }
                        .prompt {
                            display: flex;
                        }
                    }

                    .append-slide {
                        padding-right: 15px;

                        .prompt {
                            display: flex;
                            color: #fcdcbe;
                            background: linear-gradient(90deg, #666666, #38383a);
                        }

                        .price-title {
                            margin-bottom: 7px;

                            .price-title-first {
                                display: flex;
                                align-items: center;

                                .icon {
                                    width: 14px;
                                    height: 14px;
                                    margin-left: 6px;
                                    background: url('../assets/vip/tips.png') no-repeat center/cover;
                                }
                            }
                        }

                        .detail {
                            display: flex;
                            align-items: center;

                            .real-price {
                                font-size: 30px;
                                flex-shrink: 0;

                                span {
                                    font-size: 12px;
                                }
                            }

                            .desc {
                                font-size: 12px;
                                padding-left: 10px;
                                font-weight: normal;
                                line-height: 18px;
                            }
                        }

                        &.active {
                            color: #b56035;
                            background: #fff5f0;
                            border-color: #e8a481;
                        }
                    }
                }
            }

            .wechat-footer {
                width: 300px;
            }
        }

        .el-dialog__footer {
            padding: 0;

            .el-button {
                & + .el-button {
                    margin-left: 20px;
                }
            }
        }
    }

    .detainment-dialog {
        width: 568px !important;
        --el-text-color-regular: $font-color;

        .el-dialog__header {
            padding: 0;
        }

        .detainment-header {
            min-height: 109px;
            background: url('../assets/vip/detainment-top-bg.png') no-repeat left top/100% auto;
            text-align: center;
            padding-top: 27px;

            .title {
                font-size: 22px;
                font-weight: bold;
                color: #553b30;
                margin-bottom: 16px;

                .remind {
                    color: #fa635c;
                }
            }

            .sub-title {
                font-size: 14px;
                color: #553b30;

                .remind {
                    color: #fa635c;
                }
            }
        }

        .el-dialog__body {
            padding: 20px 0 40px;
        }

        .detainment-content {
            .chart-img {
                width: 100%;

                img {
                    display: block;
                    width: 100%;
                }
            }

            .title {
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                color: $color-primary;
                font-weight: bold;
                padding-top: 10px;
                margin-bottom: 27px;

                &::before {
                    content: '';
                    width: 115px;
                    height: 18px;
                    margin-right: 7px;
                    background: url(../assets/vip/title-adorn-left.png) no-repeat center/100%;
                }

                &::after {
                    content: '';
                    width: 88px;
                    height: 18px;
                    margin-left: 7px;
                    background: url(../assets/vip/title-adorn-right.png) no-repeat center/100%;
                }
            }

            .equity-content {
                padding-left: 136px;

                .list {
                    display: flex;
                    align-items: center;
                    margin-bottom: 22px;

                    img {
                        width: 42px;
                        height: 42px;
                        object-fit: cover;
                        margin-right: 13px;
                    }

                    .name {
                        font-size: 16px;
                        font-weight: bold;
                        margin-bottom: 7px;
                    }

                    .desc {
                        color: $font-color-basic;
                    }
                }
            }
        }

        .detainment-footer {
            display: flex;
            justify-content: center;
            padding-top: 20px;

            button {
                width: 144px;
                height: 40px;
                border-radius: 4px;
                border: none;
                font-size: 16px;
                cursor: pointer;
            }

            .cancel {
                color: #a18a81;
                background: #fff9e9;
            }

            .confirm {
                color: #553b30;
                background: linear-gradient(90deg, #fdefcb, #f9d990);
                margin-left: 20px;
            }
        }
    }

    .pay-success-dialog {
        width: 568px !important;
        --el-text-color-regular: $font-color;
        border-radius: 10rpx;
        overflow: hidden;

        .el-dialog__header {
            padding: 0;

            .el-dialog__headerbtn {
                top: 20px;
                right: 20px;
                background: transparent;
                width: auto;
                height: auto;

                .el-dialog__close {
                    font-weight: bold;
                    color: $font-color;
                }
            }
        }

        .el-dialog__body {
            padding: 36px 0 40px;
            background: url(../assets/vip/pay-success-bg.png) no-repeat left top/100% auto, linear-gradient(180deg, #fff0d7, #ffffff);

            .pay-success-title {
                font-size: 22px;
                font-weight: bold;
                margin-bottom: 12px;
                text-align: center;
            }

            .pay-success-sub-title {
                font-size: 16px;
                text-align: center;
                margin-bottom: 28px;
            }

            .pay-success-wrapper-title {
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 16px;
                color: $color-primary;
                font-weight: bold;
                margin-bottom: 27px;

                &::before {
                    content: '';
                    width: 115px;
                    height: 18px;
                    margin-right: 7px;
                    background: url(../assets/vip/title-adorn-left.png) no-repeat center/100%;
                }

                &::after {
                    content: '';
                    width: 88px;
                    height: 18px;
                    margin-left: 7px;
                    background: url(../assets/vip/title-adorn-right.png) no-repeat center/100%;
                }
            }

            .list-content {
                .package-list {
                    display: flex;
                    align-items: center;
                    width: 340px;
                    height: 80px;
                    box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.06);
                    border-radius: 10px;
                    margin: 0 auto 20px;
                    background-color: #fff;
                    padding: 16px 10px 16px 16px;

                    .package-name {
                        font-size: 16px;
                        font-weight: bold;
                        margin-bottom: 8px;
                    }

                    .package-desc {
                        span {
                            color: $color-primary;
                        }
                    }

                    .icon {
                        width: 48px;
                        height: 48px;
                        margin-right: 10px;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }

            .footer-btn {
                font-size: 16px;
                font-weight: bold;
                text-align: center;
                width: 320px;
                line-height: 40px;
                background: linear-gradient(90deg, #fdefcb, #f9d990);
                border-radius: 4px;
                margin: 30px auto 0;
                cursor: pointer;
                color: #553b30;
            }
        }
    }
}

.append-popper {
    padding: 20px 0 0;
    width: 350px;
    box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.1);
    border-radius: 8px;

    .append-popper-content {
        display: flex;
        flex-wrap: wrap;

        .append-popper-list {
            width: 50%;
            padding-left: 40px;
            margin-bottom: 20px;
            font-size: 14px;
            background: url('../assets/vip/contain.png') no-repeat 20px/14px 14px;
        }
    }
}
