@import './variables';

@import './common/fixedTool';
@import './common/popper';

$section-width: 858px;
$aside-width: 320px;
$tabs-margin-bottom: 40px;
$auth-tabs-padding: 40px 130px 0;
$auth-tabs-height: 140px;
$auth-tab-pane-size: 60px;
$border: 2px solid $border-color;
$filter-item-width: 160px;
$filter-input-height: 28px;
$filter-input-icon-top: -5px;
$result-item-width: 75%;

body {
    min-height: 100%;
    height: auto;
    .el-header {
        @include utils-fixed;
    }
    .major-popper {
        .arrow {
            bottom: -5px;
            left: 50%;
            top: auto !important;
            transform: translateX(-50%);
        }
    }
}

.fixed-tool {
    display: none;
}

@mixin tags {
    .tags {
        font-size: 16px;

        span {
            display: inline-block;
            margin-right: 10px;
            padding: 5px 12px;
            background-color: rgba($color: $color-white, $alpha: 0.4);
            border-radius: 4px;

            &:last-of-type {
                margin-right: 0;
            }
        }
    }
}

@mixin company-basic {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px 0 60px;

    section {
        width: 919px;

        h1 {
            margin-bottom: 20px;
            font-size: 34px;
            font-weight: bold;
            @include utils-ellipsis;
        }
        @include tags;
    }

    .wait-auth {
        display: flex;
        flex-direction: column;
        align-items: center;

        .el-button {
            width: 108px;
        }
    }
}

@mixin contact {
    .box {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        padding-left: 256px;
        padding-right: 20px;
        height: 285px;
        color: $font-color;
        font-size: 14px;
        background: url(../assets/company/contact-bg.png) no-repeat center / cover;

        $box-items: user mobile addr site;
        $box-item-size: 46px;

        @each $item in $box-items {
            .#{$item} {
                display: flex;
                align-items: center;
                margin-bottom: 18px;

                &::before {
                    content: '';
                    margin-right: 8px;
                    width: $box-item-size;
                    height: $box-item-size;
                    background: url(../assets/company/#{$item}.png) no-repeat center / contain;
                }

                span {
                    @if $item != mobile {
                        flex: 1;
                        word-break: break-word;
                    } @else {
                        & + span {
                            margin-left: 15px;
                        }
                    }
                }
            }

            & > li {
                &:last-of-type {
                    margin-bottom: 0;
                }
            }
        }

        .site {
            a:hover {
                color: $color-primary;
            }
        }
    }
}

@mixin filter {
    .filter {
        margin-bottom: 20px;
        padding: 0 30px;

        .el-select,
        .el-cascader {
            margin-right: 20px;
            width: $filter-item-width;

            &.is-select {
                .el-input__inner {
                    color: $color-primary;
                    border-color: $color-primary;
                }

                .el-input {
                    .el-select__caret {
                        color: $color-primary;
                    }
                }
            }
        }

        .el-input__inner {
            height: $filter-input-height;
            line-height: $filter-input-height;

            &::placeholder {
                color: $font-color-basic;
            }
        }

        .el-input__suffix {
            top: $filter-input-icon-top;
        }

        .el-input__icon {
            line-height: $filter-input-height;
        }
    }
}

@mixin notice-list {
    .notice-top {
        display: flex;
        font-size: 14px;
        justify-content: space-between;
        h5 {
            font-size: 14px;
            width: 667px;
            @include utils-ellipsis;
        }
        .date {
            flex-shrink: 0;
            padding-left: 5px;
            text-align: right;
            color: $color-primary;
        }
    }
    .notice-info {
        display: flex;
        margin-top: 11px;
        justify-content: space-between;
        .info {
            span {
                padding-right: 10px;
                & + span {
                    padding-left: 10px;
                    border-left: 1px solid rgba(51, 51, 51, 0.4);
                }
            }
        }
        .address {
            position: relative;
            .text {
                color: rgba($color: #333, $alpha: 0.6);
            }
            #custom-popper {
                display: none;
                position: absolute;
                bottom: calc(100% + 10px);
                left: 50%;
                transform: translateX(-50%);
                min-width: 100%;
                max-width: 450px;
                width: auto;
                .arrow {
                    top: 100%;
                    left: 50%;
                    transform: translateY(-50%);
                }
            }
        }
    }

    &.offline-mark {
        .notice-top {
            .date {
                color: rgba($color: #333, $alpha: 0.4);
            }
        }
        .notice-info {
            .address {
                .text {
                    color: rgba($color: #333, $alpha: 0.4);
                }
            }
        }
    }
}

@mixin tag {
    .tag {
        flex-shrink: 0;
        height: 20px;
        padding: 0 6px;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        border-radius: 8px 0px 8px 0px;

        $effect: (1, #5386ff, #e5edff), (2, #ffa000, #fff3e0), (3, #51bd69, #f0ffdc), (4, #51bd69, #f0ffdc), (5, #fa857f, #fff4f3), (6, #786afb, #f4f3fc);

        @each $index, $color, $bg in $effect {
            &.tag#{$index} {
                color: $color;
                background: $bg;
            }
        }
    }
}
@mixin step {
    .step {
        position: relative;
        padding-left: 10px;
        font-weight: 400;
        font-size: 14px;

        &::before {
            position: absolute;
            left: 0;
            top: 50%;
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        $font-colors: (-1: #ffa000, 1: #5386ff, 2: #fa635c, 3: #ffa000, 4: #333333);

        @each $label, $value in $font-colors {
            &.step#{$label} {
                color: $value;
                &::before {
                    position: absolute;
                    left: 0;
                    top: 50%;
                    content: '';
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    transform: translateY(-50%);
                    background-color: $value;
                }
            }
        }
    }
}

@mixin activity-list {
    .activity-top {
        display: flex;
        font-size: 14px;
        align-items: center;
        .title {
            font-weight: bold;
            font-size: 16px;
            @include utils-ellipsis;
        }
        @include tag;
        .tag {
            margin-left: 4px;
        }
    }
    .activity-info {
        display: flex;
        // flex-wrap: wrap;
        margin-top: 11px;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        @include utils-ellipsis;
        @include step;
        .address {
            @include utils-ellipsis;
        }

        $items: address date session;

        @each $var in $items {
            .#{$var} {
                margin-left: 15px;
                padding-left: 20px;
                .text {
                    color: rgba($color: #333, $alpha: 0.8);
                }
                background: url(../assets/company/#{$var}.png) no-repeat left / 15px;
            }
        }
    }

    .activity-desc {
        margin-top: 12px;
        padding-left: 18px;
        font-size: 12px;
        color: rgba($color: #333, $alpha: 0.8);
        background: url(../assets/company/attachment.png) no-repeat left / 13px;
        @include utils-ellipsis;
    }

    &.offline-mark {
        opacity: 0.4;
    }
}

.el-main {
    padding: 88px 0 20px;

    &.auth {
        padding-top: $header-height;

        .detail-container {
            .detail-header {
                display: block;
            }

            .detail-main {
                .section {
                    border: none;
                    background-color: transparent;

                    .detail-cotent-auth {
                        display: block;
                    }
                }

                .aside {
                    .company-style,
                    .company-activity,
                    .company-address {
                        display: block;
                    }
                }
            }
        }

        .login-aside-template {
            border: $border;
        }
    }
}

.detail-subtitle {
    position: relative;
    margin-bottom: 30px;

    span {
        position: absolute;
        right: 0;
        padding-left: 12px;
        color: $font-color-label;
        font-size: 12px;
        background-color: $color-white;
    }
}

.boon {
    display: inline-block;
    margin: 0 10px;
    padding: 5px 12px;
    background-color: rgba($color: $color-white, $alpha: 0.4);
    border-radius: 4px;
}

.detail-container {
    .detail-header {
        display: none;
        position: relative;
        margin-bottom: 20px;
        height: 370px;
        color: $color-white;
        background: url(../assets/detail/auth-bg.jpg) no-repeat center / cover;

        .modal {
            position: absolute;
            padding-top: 78px;
            width: 100%;
            top: 0;
            left: 0;
            height: 100%;
            background-color: rgba($color: #2d4356, $alpha: 0.6);
        }

        .main {
            @include tags;
            position: relative;
            .info {
                width: $view-width;
                display: flex;
                margin: 0 auto;
                align-items: center;
            }
            .left {
                width: 120px;
                border: 1px solid rgba($color: #fff, $alpha: 0.6);
                border-radius: 10px;
                margin-right: 26px;
                padding: 16px 0;
                display: flex;
                flex-direction: column;
                align-items: center;
                overflow: hidden;
                flex-shrink: 0;
                .logo {
                    width: 87px;
                    height: 87px;
                    margin-bottom: 13px;
                    overflow: hidden;
                    border-radius: 50%;
                    border: 5px solid #fff;
                    background-color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }
                }
                .company-collect-button {
                    box-sizing: border-box;
                    height: 28px;
                    min-height: 28px;
                    width: 100%;
                    padding: 0;
                    border-radius: 28px;
                    width: 90px;
                }
            }
            .right {
                h1 {
                    font-size: 32px;
                    font-weight: bold;
                    line-height: 48px;
                    word-break: break-all;
                }
                h2 {
                    font-size: 16px;
                    font-weight: bold;
                    line-height: 1;
                    margin-bottom: 20px;
                }
                .tags {
                    margin-top: 6px;
                }
            }
        }
        .bottom {
            background-color: rgba($color: #fff, $alpha: 0.86);
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            .tabs {
                width: $view-width;
                margin: 0 auto;
                display: flex;
                a {
                    line-height: 50px;
                    padding: 0 38px;
                    color: #333;
                    font-size: 16px;
                    margin-right: 10px;
                    &.active {
                        background-color: #fff;
                        color: $color-primary;
                        border-radius: 10px 10px 0px 0px;
                        position: relative;
                        font-weight: bold;
                        &::after {
                            content: '';
                            display: block;
                            width: 34px;
                            height: 2px;
                            background: #ffa000;
                            position: absolute;
                            bottom: 0px;
                            left: 50%;
                            transform: translateX(-50%);
                        }
                    }
                }
            }
        }
    }

    .detail-main {
        display: flex;
        justify-content: space-between;
        margin: 0 auto 120px;
        width: $view-width;

        .section {
            width: $section-width;
            background-color: $color-white;
            border: $border;
            border-radius: 10px;

            .detail-cotent-auth {
                display: none;

                .tab-content {
                    display: none;
                }

                .tab-content {
                    &.active {
                        display: block;
                    }
                }

                .top {
                    display: flex;
                    justify-content: center;
                    padding: 30px 0 39px;
                    .title {
                        font-size: 20px;
                        line-height: 1;
                        font-weight: bold;
                        padding: 0 40px;
                        background: url('../assets/icon/title-ornament-left.png') no-repeat left/27px, url('../assets/icon/title-ornament-right.png') no-repeat right/27px;
                        position: relative;
                        &::after {
                            content: '';
                            position: absolute;
                            display: block;
                            bottom: -11px;
                            height: 4px;
                            width: 30px;
                            left: 50%;
                            transform: translateX(-50%);
                            background-color: $color-primary;
                            border-radius: 2px;
                        }
                    }
                }

                .wrapper {
                    font-size: 14px;
                    margin-bottom: 20px;
                    border-radius: 10px;
                    background-color: #fff;
                    padding: 0 40px 30px;
                }

                .company-intro {
                    .intro {
                        .content {
                            line-height: 30px;
                            font-size: 14px;

                            pre {
                                white-space: pre-wrap;
                            }

                            &.is-hidden {
                                visibility: hidden;
                            }

                            &.limit-seven {
                                display: -webkit-box;
                                -webkit-line-clamp: 7;
                                -webkit-box-orient: vertical;
                                word-break: break-word;
                                text-overflow: ellipsis;
                                overflow: hidden;
                            }

                            * {
                                font-size: inherit;
                            }
                        }
                        .bottom {
                            text-align: center;
                            margin-top: 28px;

                            &.is-hidden {
                                visibility: hidden;
                            }

                            &.is-show {
                                a {
                                    &::after {
                                        transform: rotate(-135deg);
                                        top: 6px;
                                    }
                                }
                            }

                            a {
                                color: $color-primary;
                                font-size: 14px;
                                position: relative;
                                &::after {
                                    transition: all ease 0.3s;
                                    display: block;
                                    position: absolute;
                                    content: '';
                                    border-right: 1px solid $color-primary;
                                    border-bottom: 1px solid $color-primary;
                                    width: 7px;
                                    height: 7px;
                                    right: -15px;
                                    top: 2px;
                                    transform: rotate(45deg);
                                }
                            }
                        }
                    }

                    .notice {
                        .item {
                            border-bottom: 1px solid $border-color;

                            a {
                                display: block;
                                padding: 20px 0px;
                            }

                            @include notice-list();
                        }
                        @include utils-offline-mark();
                    }

                    .notice,
                    .job {
                        .bottom {
                            margin-top: 20px;
                            text-align: center;
                            a {
                                display: inline-flex;
                                padding: 0 19px;
                                line-height: 32px;
                                background-color: #fff5e5;
                                color: $color-primary;
                                border-radius: 4px;
                                font-size: 14px;
                                align-items: center;
                                &::after {
                                    display: block;
                                    content: '';
                                    border-right: 1px solid $color-primary;
                                    border-bottom: 1px solid $color-primary;
                                    width: 6px;
                                    height: 6px;
                                    transform: rotate(-45deg);
                                    margin-left: 5px;
                                }
                            }
                        }
                    }

                    .job {
                        .content {
                            display: flex;
                            flex-wrap: wrap;
                            .list {
                                width: calc((100% - 22px) / 3);
                                border: 1px solid #f2f2f2;
                                border-radius: 4px;
                                margin-right: 10px;
                                padding: 12px 10px;
                                margin-bottom: 16px;
                                &:nth-child(3n) {
                                    margin-right: 0px;
                                }
                            }
                            .detail {
                                display: flex;
                                h4 {
                                    flex-grow: 1;
                                    font-size: 14px;
                                    @include utils-ellipsis;
                                }
                                .salary {
                                    color: #fa635c;
                                    margin-left: 5px;
                                    flex-shrink: 0;
                                }
                            }
                            .tag {
                                display: flex;
                                flex-wrap: wrap;
                                margin: 12px 0;
                                span {
                                    font-size: 12px;
                                    color: $font-color-basic;
                                    line-height: 20px;
                                    background: #f4f9ff;
                                    border-radius: 4px;
                                    padding: 0 8px;
                                    & + span {
                                        margin-left: 6px;
                                    }
                                }
                            }
                            .info {
                                display: flex;
                                justify-content: space-between;
                                color: $font-color-label;
                                font-size: 13px;
                            }
                        }
                        .bottom {
                            margin-top: 4px;
                        }
                    }

                    .contact {
                        @include contact;
                        .box {
                            padding-left: 277px;
                        }
                    }

                    .school {
                        ul {
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: space-between;
                        }
                        li {
                            width: calc((100% - 20px) / 2);
                            margin-bottom: 20px;
                            background-color: #fafafc;
                            padding: 18px 10px 21px 20px;
                            border-radius: 10px;
                            .name {
                                font-size: 16px;
                                font-weight: bold;
                                display: flex;
                                align-items: center;
                                &::before {
                                    content: '';
                                    display: inline-block;
                                    background-color: $color-primary;
                                    width: 8px;
                                    height: 8px;
                                    margin-right: 7px;
                                }
                            }
                            .other {
                                display: flex;
                                .mobile {
                                    width: 178px;
                                }
                            }
                            $contactInfo: contact, mobile, fax;
                            @each $key in $contactInfo {
                                .#{$key} {
                                    padding-left: 25px;
                                    line-height: 20px;
                                    margin-top: 14px;
                                    background: url(../assets/icon/#{$key}.png) no-repeat left top/20px;
                                    font-size: 14px;
                                    word-break: break-all;
                                    padding-right: 5px;
                                }
                            }
                        }
                    }
                }

                .company-notice {
                    @include filter;
                    .filter {
                        padding-left: 0;
                    }

                    .result {
                        margin-bottom: 30px;

                        .item {
                            border-bottom: 1px solid $border-color;
                            @include notice-list;
                        }
                        a {
                            display: block;
                            padding: 20px 0px;

                            &:hover {
                                h5 {
                                    color: $color-primary;
                                }
                            }
                        }

                        @include utils-offline-mark();
                    }
                }

                .company-job {
                    @include filter;
                    .filter {
                        padding-left: 0;
                    }
                    .result-header {
                        line-height: 40px;
                        background-color: #fafafc;
                        font-size: 16px;
                        font-weight: bold;
                    }
                    .result {
                        margin-bottom: 20px;
                    }
                    .result-header,
                    .result-body li {
                        display: flex;
                        & > div,
                        & > a > div {
                            &:first-child {
                                flex-grow: 1;
                                padding-left: 20px;
                                padding-right: 56px;
                                overflow: hidden;
                            }
                            &:nth-child(2) {
                                flex-shrink: 0;
                                width: 170px;
                            }
                            &:nth-child(3) {
                                flex-shrink: 0;
                                width: 140px;
                                padding-left: 50px;
                            }
                        }
                    }
                    .result-body {
                        font-size: 14px;

                        @include utils-offline-mark();

                        li {
                            display: block;
                            padding: 18px 0;
                            border-bottom: 1px solid $border-color;

                            a:first-child {
                                display: flex;
                                align-items: center;
                                width: 100%;
                            }
                            .job-top {
                                display: flex;

                                .name {
                                    font-size: 14px;
                                    @include utils-ellipsis;

                                    &:hover {
                                        color: $color-primary;
                                    }
                                }

                                .date {
                                    opacity: 0.6;
                                    flex-shrink: 0;
                                }
                            }

                            .job-bottom {
                                display: flex;
                                margin-top: 10px;
                                .salary {
                                    color: #fa635c;
                                    margin-right: 10px;
                                    font-weight: bold;
                                }
                                .tags {
                                    span {
                                        padding-right: 10px;
                                        font-size: 12px;
                                        & + span {
                                            padding-left: 10px;
                                            border-left: 1px solid rgba(51, 51, 51, 0.4);
                                        }
                                    }
                                }
                            }

                            .major {
                                @include utils-ellipsis;
                                color: rgba($color: #333, $alpha: 0.8);
                            }

                            .job-apply-button {
                                width: 72px;
                                min-height: 28px;
                                padding: 0;
                                background: $color-primary;
                                border-radius: 4px;
                                border: none;

                                &.has-applied {
                                    background-color: #ffd080;
                                }
                            }

                            .announcement-detail {
                                display: block;
                                max-width: 380px;
                                font-size: 12px;
                                padding-left: 40px;
                                color: $font-color-basic;
                                background: url(../assets/company/attachment.png) no-repeat 20px/13px 12px;
                                margin-top: 12px;
                                @include utils-ellipsis;

                                &:hover {
                                    color: $color-primary;
                                }
                            }

                            &.offline-mark {
                                div {
                                    &:nth-child(1),
                                    &:nth-child(2) {
                                        opacity: 0.8;
                                    }
                                }
                                .job-apply-button:disabled {
                                    background-color: #cbcbcb;
                                }
                            }
                        }
                    }
                }

                .company-activity {
                    .result {
                        margin-bottom: 30px;

                        .item {
                            padding: 16px 0px;
                            border-bottom: 1px solid $border-color;
                            @include activity-list;
                            &:hover {
                                .title {
                                    color: $color-primary;
                                }
                            }
                        }

                        // @include utils-offline-mark();
                    }
                }
            }
        }

        .aside {
            width: $aside-width;

            .detail-basic {
                margin-bottom: 20px;
                padding: 0 0 20px 20px;
                width: 320px;
                background: linear-gradient(180deg, #fff8ee, #ffffff 45%);
                border: $border;
                border-radius: 10px;

                .basic-data {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                    padding-top: 30px;
                    background: url(../assets/detail/company-card-bg.png) no-repeat right top / 231px 133px;

                    .logo {
                        margin-right: 20px;
                        width: 60px;
                        height: 60px;
                        object-fit: contain;
                        border-radius: 50%;
                        flex-shrink: 0;
                    }

                    .name {
                        flex: auto;
                        padding-right: 20px;

                        h5 {
                            @include utils-ellipsis-lines(2, 1.5, 16px);
                        }

                        h6 {
                            font-weight: inherit;
                            @include utils-ellipsis-lines(2, 1.5);
                        }
                    }
                }

                .basic-info {
                    display: flex;
                    flex-direction: column;
                    padding-right: 20px;
                    color: $font-color;
                    font-size: 14px;
                    line-height: 30px;

                    span {
                        padding-left: 30px;
                        word-break: break-word;
                        background-size: 15px;
                        background-position: left 8px;
                        background-repeat: no-repeat;
                    }

                    i {
                        font-style: normal;
                        padding: 0;
                        &::after {
                            content: ' · ';
                        }
                        &:last-child {
                            &::after {
                                content: '';
                            }
                        }
                    }

                    $infos: (category, category), (nature, type), (scale, number), (site, site);

                    @each $label, $value in $infos {
                        .#{$label} {
                            background-image: url(../assets/detail/#{$value}.png);
                        }
                    }

                    .site {
                        &:hover {
                            color: $color-primary;
                        }
                    }
                }
            }

            .company-style,
            .company-activity,
            .company-address {
                display: none;
                font-size: 14px;
                background-color: $color-white;
                border-radius: 10px;
                padding: 0 20px 20px;
                margin-bottom: 20px;
                .title {
                    font-size: 16px;
                    font-weight: bold;
                    line-height: 1;
                    padding: 20px 0 16px;
                }
            }

            .company-style {
                .company-style-swiper {
                    border-radius: 6px;
                    overflow: hidden;
                    height: 140px;
                    a {
                        display: block;
                        height: 100%;
                    }
                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        display: block;
                    }
                    .swiper-button {
                        --swiper-navigation-size: 20px;
                        width: 20px;
                        height: 20px;
                        background-color: rgba($color: #000000, $alpha: 0.35);
                        border-radius: 4px;
                        &::after {
                            font-size: 12px;
                            font-weight: bold;
                            color: #eee;
                        }
                    }
                }

                @include utils-swiper-bullet();

                .company-style-pagination {
                    bottom: 7px;
                }
            }

            .company-activity {
                padding-bottom: 0;
                .title {
                    padding: 20px 0 0 23px;
                    background: url(../assets/company/bell.png) no-repeat left bottom / 15px 17px;
                }
                @include tag;
                .activity {
                    padding: 10px 0;
                    border-bottom: 1px solid $border-color;

                    &:hover {
                        .activity-title {
                            color: $color-primary;
                        }
                    }
                }
                .activity-title {
                    font-size: 14px;
                    line-height: 21px;
                    @include text-ellipsis;
                }
                .activity-content {
                    display: flex;
                    align-items: center;
                    margin-top: 6px;
                    font-size: 12px;
                    .activity-tag {
                    }
                    .activity-con {
                        margin-left: 6px;
                        color: $font-color-basic;
                        @include utils-ellipsis;
                    }
                }
                .view-all {
                    a {
                        display: block;
                        line-height: 50px;
                        font-size: 14px;
                        text-align: center;
                    }
                }
            }

            .company-address {
                .address {
                    position: relative;
                    padding-left: 23px;
                    background: url('../assets/icon/address.png') no-repeat left 5px/18px;
                    line-height: 28px;
                    word-break: break-word;
                }
            }

            .to-miniprogram {
                position: relative;
                height: 138px;
                background: url(../assets/company/miniprogram.png) no-repeat center / cover;

                img {
                    position: absolute;
                    top: 10px;
                    left: 12px;
                    padding: 4px;
                    width: 118px;
                    height: 118px;
                    background-color: $color-white;
                    border-radius: 50%;
                    pointer-events: none;
                }
            }
        }
    }
}
