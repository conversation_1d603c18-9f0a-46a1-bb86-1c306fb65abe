@use './variables' as *;

.login-aside-template {
    margin-bottom: 20px;
    padding: 82px 32px 24px;
    background: $color-white url(../assets/common/aside-login-bg.png) no-repeat center top / contain;
    border-radius: 10px;

    &.is-plain {
        border-radius: 0;
    }

    .el-form {
        $icons: mobile msg;

        @each $var in $icons {
            .aside-login-#{$var} {
                display: block;
                width: 24px;
                height: 40px;
                background: url(../assets/common/aside-login-#{$var}.png) no-repeat center / contain;
            }
        }

        .el-input__inner {
            padding-left: 32px;
            border-color: transparent;

            &:focus {
                border-color: $color-primary;
            }
        }

        .mobile {
            .el-input__inner {
                padding-left: 106px;
            }

            .input-prepend {
                padding-left: 3px;
                display: flex;
                background-color: #fff;
                // align-items: center;

                &::after {
                    display: inline-block;
                    content: '';
                    width: 1px;
                    height: 16px;
                    background-color: #ebebeb;
                    transform: translateY(11px);
                }
            }

            .el-input__prefix {
                width: 98px;
                height: calc(100% - 2px);
                overflow: hidden;
                left: 2px;
                top: 1px;
            }

            .mobile-prefix-select {
                width: 68px;
                color: #333;
                margin: 0px;

                .el-input__inner {
                    padding: 0 13px 0 5px;
                    color: $font-color;
                    border: none;
                    height: 38px;
                }

                .el-input__suffix {
                    right: 0;
                    width: 13px;
                    display: flex;
                    align-items: center;
                    background: url('//img.gaoxiaojob.com/uploads/static/image/pc/column/assets/icon/down.png') no-repeat left/7px auto;

                    & > * {
                        display: none;
                    }
                }

                .el-input__prefix {
                    left: 0;
                    width: 30px;
                    background: url('../assets/icon/phone.png') no-repeat center/16px;
                }
            }
        }

        .aside-login-code {
            .el-input__inner {
                padding-right: 100px;
            }
        }

        .aside-login-msg {
            background-position: center 7px;
        }

        .el-form-item {
            margin-bottom: 24px;

            &.is-error {
                .el-input__inner {
                    border-color: $color-error;
                }
            }
        }

        .el-button--text {
            margin-right: 10px;
        }

        .mobile-login-confirm {
            width: 100%;
        }
    }
}

.el-popper.aside-mobile-prefix-popper {
    transform: translateX(-29px);

    .el-select-group__title {
        color: $font-color-label;
        font-size: 14px;
    }

    .el-select-dropdown__item {
        --el-text-color-regular: $font-color;
        font-size: 14px;
    }
}
