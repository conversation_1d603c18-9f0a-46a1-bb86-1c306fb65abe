@use './variables' as *;
@use './common/popper';

.job-report {
    margin: 20px auto 0;
    padding: 104px 50px 45px;
    width: $view-width;
    background: $color-white url(../assets/report/job-header.png) no-repeat top left / 100%;
    border-radius: 16px;

    .report-header {
        padding: 56px 0 60px;
        background: url(../assets/report/job-title-text.png) no-repeat top left / 459px 36px;

        .datetime {
            margin-bottom: 60px;
            color: #7a3800;
            font-size: 14px;
        }

        .intro {
            display: flex;
            justify-content: space-between;
        }

        .intro-section {
            .title {
                margin-bottom: 20px;
                font-size: 20px;
                font-weight: bold;
            }

            .chart {
                position: relative;
                display: flex;
                margin: 40px 0 30px;
                color: $font-color-label;

                .item {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    &.is-active {
                        &::after {
                            content: '';
                            position: absolute;
                            top: -30px;
                            left: 50%;
                            width: 29px;
                            height: 51px;
                            background: url(../assets/report/job-mark.png) no-repeat center / contain;
                            transform: translateX(-50%);
                        }
                    }

                    & + .item {
                        margin-left: 3px;
                    }

                    &::before {
                        content: '';
                        margin-bottom: 15px;
                        width: 150px;
                        height: 10px;
                    }

                    &:nth-of-type(1) {
                        &::before {
                            border-radius: 5px 0 0 5px;
                            background: linear-gradient(90deg, #fdde60, #fcc854);
                        }
                    }

                    &:nth-of-type(2) {
                        &::before {
                            background: linear-gradient(90deg, #fcc854, #fda92c);
                        }
                    }

                    &:nth-of-type(3) {
                        &::before {
                            background: linear-gradient(90deg, #fda92c, #ff8c00);
                        }
                    }

                    &:nth-of-type(4) {
                        &::before {
                            border-radius: 0 5px 5px 0;
                            background: linear-gradient(90deg, #ff8c00, #ff6600);
                        }
                    }
                }
            }

            .desc {
                font-size: 14px;
                font-weight: 400;

                span {
                    color: $color-primary;
                    font-size: 20px;
                    font-weight: bold;

                    .is-special {
                        font-size: 18px;
                        font-weight: normal;
                    }
                }
            }
        }

        .intro-aside {
            width: 421px;
            background: linear-gradient(180deg, #fffbf3, #ffffff);
            border: 1px solid #ffe8c3;
            border-radius: 16px;
            overflow: hidden;

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 10px;
                height: 54px;
                background: linear-gradient(180deg, #fff2db, #ffefdb);
            }

            .title {
                @include utils-ellipsis;

                display: flex;
                align-items: center;
                padding-left: 26px;
                width: 75%;
                font-size: 16px;
                font-weight: bold;
                background: url(../assets/report/job-title-icon.png) no-repeat left center / 20px;

                span {
                    @include utils-ellipsis;

                    font-size: 18px;
                    font-weight: bold;
                }

                &.is-new {
                    &::after {
                        content: '新发布';
                        flex: none;
                        margin-left: 10px;
                        width: 48px;
                        height: 16px;
                        color: $color-white;
                        font-size: 12px;
                        text-align: center;
                        line-height: 16px;
                        background-color: $font-color-salary;
                        border-radius: 8px;
                    }
                }
            }

            .link {
                padding-right: 14px;
                color: $color-primary;
                font-size: 14px;
                background: url(../assets/report/job-forward.png) no-repeat right center / 9px;
            }

            .content {
                padding: 0 20px 32px;
            }

            .cell {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 20px;
                font-size: 14px;
            }

            .label {
                @include utils-ellipsis;

                width: 75%;

                &.is-special {
                    color: $font-color-basic;
                }
            }

            .match {
                padding-left: 18px;
                color: $font-color-basic;
                background: url(../assets/report/job-invalid.png) no-repeat left center / 13px;

                &.valid {
                    color: $color-primary;
                    background-image: url(../assets/report/job-valid.png);
                }

                &.unknow {
                    background-image: url(../assets/report/job-unknow.png);
                }
            }
        }
    }

    .subtitle-cell {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-left: 16px;
        padding-right: 20px;
        height: 36px;
        background: #fff7ea;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 6px;
            background-color: $color-primary;
        }

        .title {
            padding-right: 20px;
            color: $color-primary;
            font-size: 18px;
            font-weight: bold;
            background: url(../assets/report/job-help.png) no-repeat right center / 14px;
        }

        .append {
            font-size: 14px;
        }

        a {
            padding: 4px 12px;
            color: $color-white;
            font-size: 14px;
            font-weight: bold;
            background-color: $color-primary;
            border-radius: 12px;
        }
    }

    .chart-cell {
        display: flex;
        flex-wrap: wrap;

        .chart-item {
            &:nth-of-type(2n) {
                margin-left: 30px;
            }
        }
    }

    .chart-item {
        margin-bottom: 30px;
        padding: 30px 20px;
        width: 535px;
        background: #f9fafc;
        border-radius: 16px;

        .chart-title {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;
            padding-left: 52px;
            height: 40px;
            background-repeat: no-repeat;
            background-position: left center;
            background-size: 40px;

            $list: addr education major overseas title work;

            @each $item in $list {
                &.is-#{$item} {
                    background-image: url(../assets/report/chart-title-#{$item}.png);
                }
            }

            .no-data {
                &::after {
                    content: '暂无应聘数据';
                    padding-left: 18px;
                    color: $font-color-basic;
                    font-size: 14px;
                    font-weight: normal;
                    background: url(../assets/report/title-tips.png) no-repeat left center / 13px;
                }
            }

            span {
                width: 100%;

                &:nth-of-type(1) {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    color: $font-color;
                    font-size: 16px;
                    font-weight: bold;
                }

                &:nth-of-type(2) {
                    @include utils-ellipsis;

                    color: $font-color-basic;
                    font-size: 14px;
                }
            }
        }

        .chart-slot {
            width: 500px;
            height: 250px;
        }
    }

    .chart-status {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        margin-bottom: 20px;
        height: 140px;
        color: $font-color-label;

        .item {
            display: flex;
            flex-direction: column;
            align-items: center;

            & + .item {
                margin-left: 5px;
            }

            &::before {
                content: '';
                margin-bottom: 8px;
                width: 112px;
                height: 39px;
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
            }

            &:nth-of-type(1) {
                &::before {
                    background-image: url(../assets/report/status-1.png);
                }
            }

            &:nth-of-type(2) {
                &::before {
                    background-image: url(../assets/report/status-2.png);
                }
            }

            &:nth-of-type(3) {
                &::before {
                    background-image: url(../assets/report/status-2.png);
                }
            }

            &:nth-of-type(4) {
                &::before {
                    background-image: url(../assets/report/status-4.png);
                }
            }

            &.is-active {
                &::before {
                    height: 91px;
                    background-image: url(../assets/report/status-active.png);
                }
            }
        }
    }

    .hot-content {
        margin-bottom: 10px;

        .chart-item {
            height: 470px;

            .title {
                font-size: 16px;
                font-weight: bold;

                span {
                    color: $color-primary;
                }
            }

            .subtitle {
                margin-bottom: 15px;
                font-size: 14px;
                text-align: center;

                span {
                    color: $color-primary;
                    font-size: 16px;
                    font-weight: bold;
                }
            }

            .tips {
                margin-top: 16px;
                color: $font-color-basic;
                font-size: 14px;
            }

            &__trending {
                height: auto;

                .chart-title {
                    &__trending {
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        padding-left: 0;
                        margin-bottom: 18px;
                        height: auto;

                        span {
                            width: auto;
                        }

                        .tooltip-trigger {
                            flex: none;
                            padding-right: 20px;
                            background: url(../assets/report/job-help.png) no-repeat right 1px / 14px;
                        }
                    }

                    &__tab {
                        display: flex;
                        color: $color-white;
                        font-size: 14px;
                        line-height: 24px;
                        background-color: #c5c5c5;
                        border-radius: 8px;
                        overflow: hidden;

                        &-item {
                            width: 60px;
                            text-align: center;
                            user-select: none;
                            cursor: pointer;

                            &.is-active {
                                background-color: $color-primary;
                            }
                        }
                    }
                }

                &-content {
                    padding-bottom: 20px;
                    background-color: $color-white;
                    border-radius: 16px;
                }

                &-stat {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 12px 20px 0;
                    padding: 0 20px;
                    height: 30px;
                    background: #f9fafc;
                    border-radius: 15px;
                    font-size: 16px;
                    font-weight: bold;

                    span {
                        position: relative;
                        padding-left: 11px;
                        color: $color-primary;

                        &::before {
                            content: '';
                            position: absolute;
                            top: calc(50% - (5px / 2));
                            left: 0;
                            width: 5px;
                            height: 5px;
                            background-color: $color-primary;
                            border-radius: 50%;
                        }
                    }
                }
            }
        }

        .stat-cell {
            padding: 20px;
            background-color: $color-white;
            border-radius: 16px;

            .item {
                padding-bottom: 15px;

                &:nth-of-type(2) {
                    padding-bottom: 0;
                }
            }

            .title {
                position: relative;
                margin-bottom: 15px;
                padding-left: 11px;
                color: $color-primary;
                font-size: 16px;

                &.has-help {
                    span {
                        display: inline-block;
                        padding-right: 20px;
                        background: url(../assets/report/job-help.png) no-repeat right center / 14px;
                    }
                }

                &::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 5px;
                    height: 5px;
                    background-color: $color-primary;
                    border-radius: 50%;
                    transform: translateY(-50%);
                }
            }

            .desc {
                color: $font-color-basic;
                font-size: 14px;

                .num {
                    color: $font-color;
                    font-size: 16px;
                    font-weight: bold;
                }

                strong {
                    color: $font-color;
                }
            }
        }

        .notice {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding-left: 14px;
            height: 28px;
            color: $font-color-basic;
            font-size: 12px;
            background-color: #fff3e0;
            border-radius: 14px;

            strong {
                color: $color-primary;
                font-size: 16px;
            }
        }

        .apply {
            margin-top: 20px;
            text-align: center;
        }

        .link {
            display: inline-block;
            width: 80px;
            height: 28px;
            color: $color-white;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            line-height: 28px;
            background-color: $color-primary;
            border-radius: 14px;
        }
    }

    .report-tips {
        position: relative;
        margin-top: 10px;
        font-size: 12px;
        border-bottom: 1px dashed #ebebeb;

        span {
            position: absolute;
            left: 50%;
            bottom: 0;
            padding: 0 5px;
            background-color: $color-white;
            transform: translate(-50%, 50%);
        }
    }

    .match-status-item {
        padding-top: 100px;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        background-repeat: no-repeat;
        background-position: center top;
        background-size: 76px 82px;

        $list: (match, #47d4aa, '匹配'), (mismatch, #fa635c, '暂不匹配'), (unknow, $color-primary, '匹配度未知');

        @each $key, $color, $text in $list {
            &.is-#{$key} {
                color: $color;
                background-image: url(../assets/report/#{$key}.png);

                &::after {
                    content: $text;
                }
            }
        }
    }

    .match-major-slot {
        margin-top: 70px;
        text-align: center;

        span {
            @include utils-ellipsis-lines(2, 1.5);

            margin-top: 40px;
            color: $font-color;
        }
    }

    .match-area-slot {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        padding: 40px 100px;
        background-color: $color-white;
        border-radius: 10px;
    }

    .match-area-item {
        width: 76px;
        color: $font-color;
        font-size: 14px;
        font-weight: bold;
        text-align: center;

        span {
            display: block;
            margin-bottom: 12px;
        }
    }

    .no-data-slot {
        margin-top: 30px;
        padding-top: 200px;
        color: $font-color-basic;
        font-size: 14px;
        text-align: center;
        background: url(../assets/report/no-data.png) no-repeat center top / 239px 183px;
    }
}
