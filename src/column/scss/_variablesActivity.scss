@import '../../variables';

:root {
    --el-color-primary: #296aff;
}
$color-primary: #296aff;

$background-info: #bababa;

$background-primary: #f8f8f8;

$liner-gradient-primary: linear-gradient(90deg, #296aff, #29a7ff);

$box-shadow-primary: 0 0 0 1px $color-primary;

$border-color: #f4c6c7;

$box-shadow-default: 0 3px 10px 0 rgba(51, 51, 51, 0.12);

$tag-background: #e5edff;

@mixin wrapper-title {
    position: relative;
    display: flex;
    justify-content: center;
    padding-bottom: 8px;
    padding-top: 20px;
    margin-bottom: 20px;
    z-index: 1;

    .title-fill {
        font-size: 28px;
        font-weight: bold;
        background: linear-gradient(0deg, #b61f22 0%, #e95d60 100%);
        background-clip: text;
        -webkit-text-fill-color: transparent;
        -webkit-background-clip: text;
    }

    .title-suffix {
        font-size: 28px;
        font-weight: bold;
    }

    .title-english {
        font-size: 28px;
        font-weight: bold;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        color: #f2ebe9;
        z-index: -1;
    }
}

.animation-transition {
    transition: all 0.3s ease;
}

.animation-mouseover {
    @extend .animation-transition;

    &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow-default;
    }
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
    border-radius: 4px;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
    background: linear-gradient(90deg, #296aff, #29a7ff);
    &:hover {
        color: $color-white !important;
    }
}
