@use 'sass:meta';
@import './variables';
@import './common/component/detailShare';

$color-secondary: rgba(
    $color: $color-primary,
    $alpha: 0.6
);
$color-main: rgba(
    $color: #ffa917,
    $alpha: 0.4
);
$border-color-bg: rgba(
    $color: $color-white,
    $alpha: 0.4
);

body {
    .el-main {
        padding-bottom: 0;
        background-color: $color-white;
    }
}

@mixin establishment {
    .establishment-tag {
        padding: 0 10px;
        line-height: 18px;
        border-radius: 4px;
        border: 1px solid #4fbc67;
        color: #4fbc67;
        font-size: 12px;

        &.superior {
            border-color: $color-white;
            background-color: $color-white;
        }
    }
}

.detail-container {
    @import './notice/headerTemplateOne';

    .detail-main {
        margin: 0 auto;
        width: $view-width;

        .detail-title {
            display: flex;
            margin-bottom: 25px;
            line-height: 36px;
            box-shadow: 0 -1px 0 0 $color-primary inset;

            span {
                @include utils-ellipsis;
                color: $color-white;
                font-size: 18px;
                font-weight: bold;
                line-height: 2;
                letter-spacing: 3px;
                background-color: $color-primary;
            }

            &::before {
                flex: none;
                width: 24px;
                height: 36px;
                content: '';
                background: url(../assets/notice/title-before.png) no-repeat center center / contain;
            }

            &::after {
                flex: none;
                width: 36px;
                height: 36px;
                content: '';
                background: url(../assets/notice/title-after.png) no-repeat center center / contain;
            }
        }

        @import './common/jobFileList';

        .notice-heat {
            background: #fffcea;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            cursor: pointer;
            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                .left {
                    background: url(../assets/notice/heat.png) no-repeat left center/35px;
                    line-height: 40px;
                    padding-left: 45px;
                    font-size: 16px;
                    font-weight: 700;
                }
                .right {
                    cursor: pointer;
                    padding-right: 10px;
                    font-size: 14px;
                    color: #ffa000;
                    background: url(../assets/detail/to-next.png) no-repeat right center/6px 8px;
                }
            }
            .text-heat {
                font-size: 14px;
                span {
                    font-size: 16px;
                    color: $color-primary;
                    font-weight: 700;
                }
                .active {
                    color: transparent;
                    text-shadow: #ffa000 0 0 10px;
                }
            }
        }

        .detail-emit {
            margin: 40px 0;
            text-align: center;

            .el-button {
                & + .el-button {
                    margin-left: 30px;
                }
            }
        }

        @import './notice/richReset';

        @include meta.load-css('./common/detailTable');
    }
}

.el-popper {
    .boon {
        display: inline-block;
        padding: 3px 8px;
        margin: 8px 6px;
        color: $color-primary;
        font-size: 12px;
        font-weight: 400;
        background: #feebcb;
        border-radius: 4px;
    }
}

@import './mini-code-popup';
