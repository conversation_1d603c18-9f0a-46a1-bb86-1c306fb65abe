@use './variables' as *;

.postdoctor-guide-fixed {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 58px, rgba($color: #d84043, $alpha: 0.9) 58px);
    z-index: 99;

    .postdoctor-content {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 0 auto;
        padding-top: 58px;
        width: calc($view-width + 74px);
        height: 128px;
        background: url('../assets/footer/postdoctor.png') no-repeat left bottom/188px 128px;
        padding-left: 37px;
        padding-right: 37px;
    }

    .postdoctor-link {
        display: block;
        width: 983px;
        height: 47px;
        background: url('../assets/footer/postdoctor-link.png') no-repeat left bottom/983px 47px;
    }

    .close-button {
        cursor: pointer;
        margin-left: 28px;
        width: 22px;
        height: 22px;
        padding: 0;
        background: transparent;
        border: 1px solid $color-white;
        border-radius: 50%;

        .el-icon {
            color: $color-white;
        }
    }
}
