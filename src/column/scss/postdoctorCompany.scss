@import './variablesPostdoctor';

.main-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    padding-top: 20px;
    padding-bottom: 1px;
    background: url(//img.gaoxiaojob.com/uploads/boshihou/company/bg.png) no-repeat left top/100% auto,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-left-bg.png) no-repeat left 169px/380px 264px,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-right-bg.png) no-repeat right 169px/380px 264px, linear-gradient(180deg, #ffe9ea 0, #f8f8f8 326px);

    & > div {
        width: $view-width;
    }

    .showcase-wrapper {
        width: $view-width;

        @import './postdoctor/a1-and-b1';
    }

    .filter-wrapper {
        width: $view-width;
        background: url(//img.gaoxiaojob.com/uploads/boshihou/company/filter-bg.png) no-repeat right 8px bottom 1px/757px 40px, $color-white;
        border-radius: 12px;
        padding: 20px 20px 5px;
        margin-bottom: 16px;

        .filter-row {
            display: flex;
            margin-bottom: 8px;

            button {
                border: none;
                background: transparent;
            }

            .filter-label {
                width: 90px;
                line-height: 32px;
                flex-shrink: 0;
                font-size: 16px;
                font-weight: bold;
            }

            .filter-value {
                display: flex;
                flex-wrap: wrap;
                flex-grow: 1;
                max-width: 980px;

                .filter-item {
                    margin-right: 12px;
                    padding: 7px 10px;
                    border-radius: 4px;
                    margin-bottom: 6px;
                    line-height: 1;
                    color: $font-color;

                    .filter-clear {
                        display: none;
                        width: 12px;
                        height: 100%;
                        margin-left: 3px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/clear.png) no-repeat left/12px 12px;
                    }

                    &:hover {
                        color: $color-primary;
                    }

                    &.active {
                        color: $color-primary;
                        background-color: $tag-background;
                        display: flex;

                        .filter-clear {
                            display: inline-flex;
                        }
                    }
                }
            }

            .filter-more {
                width: 90px;
                text-align: right;
                flex-shrink: 0;
                color: $color-primary;
                line-height: 28px;
                cursor: pointer;
            }

            .filter-clear-all {
                display: flex;
                line-height: 32px;
                cursor: pointer;
                color: $font-color-basic;
                padding-left: 19px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/delete.png) no-repeat left/15px 15px;

                span {
                    color: $color-primary;
                }
            }
        }

        .kw-row {
            margin-bottom: 15px;

            .filter-value {
                flex-wrap: nowrap;
                max-width: initial;
                overflow: hidden;
            }

            .filter-label {
                line-height: 32px;
            }

            .search {
                flex-shrink: 0;
                background-color: $color-primary;
                border-radius: 8px;
                padding: 1px;
                display: flex;
                margin-right: 20px;
                display: flex;

                .kw {
                    width: 220px;
                    height: 30px;
                    line-height: 30px;
                    display: flex;
                    align-items: center;

                    input {
                        border: none;
                        border-radius: 8px;
                        background-color: $color-white;
                        height: 100%;
                        padding-left: 10px;
                        line-height: 30px;
                        height: 30px;
                    }

                    .el-input__suffix {
                        display: flex;
                        align-items: center;
                    }
                }

                .search-btn {
                    color: $color-white;
                    padding: 0 16px;
                    line-height: 30px;
                }
            }

            .search-hot {
                display: flex;
                font-size: 12px;
                align-items: center;
                flex-grow: 1;
                overflow: hidden;

                &-label {
                    flex-shrink: 0;
                    padding-left: 20px;
                    margin-right: 7px;
                    background: url(//img.gaoxiaojob.com/uploads/boshihou/common/hot.png) no-repeat left/18px 18px;
                    color: $font-color-basic;
                }

                &-value {
                    display: flex;
                    flex-wrap: nowrap;
                }

                .item {
                    line-height: 24px;
                    padding: 0 8px;
                    border-radius: 4px;
                    background-color: #f3f8fd;
                    margin-right: 7px;
                    white-space: nowrap;

                    &.active {
                        color: $color-primary;
                        background-color: $tag-background;
                    }
                }
            }
        }
    }

    .company-wrapper {
        padding-top: 6px;

        .list-content {
            display: flex;
            flex-wrap: wrap;

            .list {
                width: calc((100% - 40px) / 3);
                margin-right: 20px;
                margin-bottom: 20px;
                background-color: $color-white;
                border-radius: 16px;
                border: 1px solid $color-white;
                padding: 1px;
                position: relative;

                &:hover {
                    border-color: $color-primary;

                    .bottom {
                        .address {
                            display: none;
                        }

                        .more {
                            display: block;
                        }
                    }
                }

                &:nth-child(3n) {
                    margin-right: 0;
                }

                .tag {
                    position: absolute;
                    right: -12px;
                    top: -6px;
                    padding: 0 8px;
                    border-radius: 10px 10px 10px 0px;
                    background-color: #fde3cb;
                    color: rgba($color: #6e4843, $alpha: 0.6);
                    font-size: 12px;
                    line-height: 20px;
                }

                .company-info {
                    padding: 15px 20px 15px 15px;
                    border-radius: 16px 16px 0px 0px;
                    background: linear-gradient(90deg, #fcedef, #fffaf7);
                    display: flex;

                    .logo {
                        border: 3px solid $color-white;
                        border-radius: 50%;
                        width: 50px;
                        height: 50px;
                        overflow: hidden;
                        margin-right: 12px;
                        flex-shrink: 0;

                        img {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .info {
                        flex-grow: 1;
                        padding: 2px 0 0;
                        overflow: hidden;

                        .title {
                            font-size: 16px;
                            font-weight: bold;
                            margin-bottom: 10px;
                            @include utils-ellipsis;
                        }

                        .type {
                            color: $font-color-basic;
                            @include utils-ellipsis;
                        }
                    }
                }

                .announcement {
                    padding-top: 18px;
                    padding-bottom: 2px;
                    min-height: 88px;

                    .item {
                        display: flex;
                        align-items: center;
                        padding: 0 20px;
                        padding-bottom: 18px;

                        &::before {
                            content: '';
                            width: 6px;
                            height: 6px;
                            flex-shrink: 0;
                            background-color: #d6d3d2;
                            border-radius: 50%;
                            margin-right: 8px;
                        }

                        .name {
                            flex-grow: 1;
                            @include utils-ellipsis;
                        }

                        .time {
                            margin-left: 20px;
                            color: $font-color-label;
                        }
                    }

                    .empty {
                        margin-top: -6px;
                        padding-left: 188px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/empty.png) no-repeat 90px/89px 65px;
                        height: 64px;
                        font-size: 12px;
                        color: $font-color-label;
                        display: flex;
                        align-items: center;

                        .text {
                            line-height: 18px;
                            text-align: center;
                        }
                    }
                }

                .bottom {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin: 0 20px;
                    border-top: 1px dashed #ebebeb;
                    color: $font-color;

                    .aside {
                        display: flex;
                    }

                    .total {
                        display: flex;
                        line-height: 55px;
                        white-space: nowrap;

                        & + .total {
                            margin-left: 16px;
                        }

                        span {
                            color: $color-primary;
                            font-weight: bold;
                        }
                    }

                    .address {
                        margin-left: 10px;
                        padding-left: 18px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/address.png) no-repeat left/12px 12px;
                        color: $font-color-basic;
                        max-width: 104px;
                        @include utils-ellipsis;
                    }

                    .more {
                        display: none;
                        background: $liner-gradient-primary;
                        line-height: 32px;
                        color: $color-white;
                        padding: 0 20px;
                        border-radius: 32px;
                        font-weight: bold;
                    }
                }
            }
        }

        .pagination {
            margin-bottom: 20px;
        }

        .empty-container {
            display: none;
            height: 380px;
            border-radius: 12px;
            padding-top: 296px;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/empty.png) no-repeat center 70px/266px 194px, $color-white;
            text-align: center;
            color: $font-color-basic;
            margin-bottom: 20px;

            &.show {
                display: block;
            }
        }
    }

    .select-dialog {
        & > .el-input {
            display: none;
        }
    }
}
