@use './variables' as *;

$margin-primary: 30px;
$dialog-padding: 60px;
$dialog-width: 568px !default;
$dialog-width: 702px;
$dialog-height: 445px;
$dialog-body-width: 300px;
$dialog-radius: 10px;

a.color-primary {
    color: $color-primary;
}

.login-dialog-template {
    .el-dialog {
        top: 50%;
        margin: 0 auto;
        width: $dialog-width;
        height: $dialog-height;
        background: $color-white;
        transform: translateY(-50%);
    }

    .el-dialog__header {
        padding: 0;
    }

    .el-dialog__body {
        margin: 0 auto;
        padding: 0;
        width: $dialog-width;
        text-align: center;
        display: flex;
    }

    .el-tabs__header {
        margin-bottom: $margin-primary;
    }

    .el-tabs__nav {
        float: none;
    }

    .el-tabs__item {
        font-size: 18px;

        &.is-active {
            font-weight: bold;
        }
    }

    .el-tabs__nav-wrap::after {
        height: 0;
    }

    .el-input-group__prepend,
    .el-input-group__append {
        background-color: $color-white;
    }

    .mobile-login-confirm,
    .account-login-confirm {
        margin: 10px auto;
        width: 100%;
        font-weight: bold;
    }

    .entry-tips {
        font-size: 12px;
        margin-bottom: 37px;
        line-height: 1.5;
        margin-top: 5px;
    }

    .signin-tips {
        text-align: center;
        line-height: 1.5;
        margin-top: 15px;
    }

    .forget-password {
        margin-top: -$margin-primary;
        text-align: right;
        height: $margin-primary;
        position: relative;

        a {
            position: absolute;
            right: 0;
            bottom: 4px;
        }
    }

    .el-form-item {
        margin-bottom: 22px !important;
    }

    .aside {
        width: 252px;
        flex-shrink: 0;
        padding: 100px 15px 15px 35px;
        background: url('../assets/login/dialog-aside-bg.png') no-repeat left top/cover;
        border-top-left-radius: $dialog-radius;
        border-bottom-left-radius: $dialog-radius;

        .introduce {
            padding-left: 45px;
            margin-bottom: 60px;
            text-align: left;

            &:nth-child(1) {
                background: url($logo-icon) no-repeat left 5px/30px auto;
            }

            &:nth-child(2) {
                background: url('../assets/login/dialog-unit.png') no-repeat left 5px/30px auto;
            }

            &:nth-child(3) {
                background: url('../assets/login/dialog-resume.png') no-repeat left 5px/30px auto;
            }

            .name {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 8px;
            }

            span {
                color: $color-primary;
                font-weight: bold;
            }
        }
    }

    .login-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex-grow: 1;
        position: relative;
        padding: 70px 75px 30px;
        border-top-right-radius: $dialog-radius;
        border-bottom-right-radius: $dialog-radius;

        .login-switch {
            position: absolute;
            right: 0;
            top: 0;

            .account-tips,
            .scan-tips {
                width: 68px;
                height: 68px;
                cursor: pointer;

                &:hover {
                    .login-tips {
                        display: block;
                    }
                }
            }

            .account-tips {
                background: url('../assets/login/dialog-scan.png') no-repeat right/contain;
            }

            .scan-tips {
                background: url('../assets/login/dialog-account.png') no-repeat right/contain;
            }

            .login-tips {
                display: none;
                position: absolute;
                z-index: 3;
                top: 26px;
                right: 38px;
                white-space: nowrap;
                line-height: 26px;
                font-size: 12px;
                padding: 0 19px 0 10px;
                background: url('../assets/login/bg-tips.png') no-repeat right top/100% 26px;
                color: $color-primary;
            }
        }

        .account-login {
            width: 100%;

            .el-input--prefix {
                .el-input__inner {
                    padding-left: 45px;
                }
            }

            .mobile {
                .el-input__inner {
                    padding-left: 90px;
                }

                .el-input__prefix {
                    width: 80px;
                    height: calc(100% - 4px);
                    overflow: hidden;
                    left: 2px;
                    top: 2px;
                }

                .mobile-prefix-select {
                    color: #333;
                    margin: 0px;
                    display: flex;
                    background-color: #fff;

                    &::after {
                        display: inline-block;
                        content: '';
                        width: 1px;
                        height: 16px;
                        background-color: #ebebeb;
                        transform: translateY(11px);
                    }

                    .el-input__inner {
                        padding: 0 13px 0 14px;
                        color: $font-color;
                        border: none;
                        border-radius: 0;
                        height: 38px;
                    }

                    .el-input__suffix {
                        right: 0;
                        width: 13px;
                        display: flex;
                        align-items: center;
                        background: url('//img.gaoxiaojob.com/uploads/static/image/pc/column/assets/icon/down.png') no-repeat left/7px auto;

                        & > * {
                            display: none;
                        }
                    }
                }
            }

            .sms-code {
                .el-input__inner {
                    padding-right: 100px;
                }

                .el-input__prefix {
                    left: 0;
                    width: 45px;
                    background: url('../assets/icon/sms.png') no-repeat 12px center/24px;
                }

                .el-input__suffix {
                    right: 0;
                }

                .el-button {
                    border: none;
                    padding: 0 12px;
                    background: initial;
                    position: relative;
                    color: $color-primary;
                    text-align: center;

                    span {
                        color: $font-color-label;
                    }

                    &::before {
                        content: '';
                        display: block;
                        width: 1px;
                        height: 16px;
                        background-color: #ebebeb;
                        position: absolute;
                        left: 0;
                        top: 12px;
                    }
                }

                .el-input__clear {
                    position: absolute;
                    right: calc(100% + 8px);
                }
            }
        }

        .home-scan-login {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: $font-color;

            .type-title {
                font-size: 22px;
                font-weight: bold;
                margin-bottom: 30px;
            }

            .qr-code {
                position: relative;
                width: 197px;
                height: 197px;
                overflow: hidden;
                margin-bottom: 22px;
                background-size: 100%;
                background-position: center;

                .code {
                    width: 100%;
                    height: 100%;
                    background-size: contain;
                }

                .coderefresh {
                    position: absolute;
                    border-radius: 4px;
                    cursor: pointer;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba($color: #fff, $alpha: 0.9) url(../assets/login/new-refresh.png) no-repeat center / 65px 65px;
                }

                .scan-success {
                    position: absolute;
                    cursor: pointer;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    font-size: 20px;
                    font-weight: bold;
                    color: $color-primary;
                    text-align: center;
                    padding-top: 147px;
                    background: url(../assets/login/success.png) no-repeat center 38px/ 82px 82px;
                }
            }

            .scan-tips {
                font-size: 14px;
                line-height: 26px;

                .success {
                    margin-top: -40px;
                    opacity: 0.8;
                    font-size: 16px;
                }
            }

            .instant {
                margin: 22px auto 0;
                font-size: 12px;
                width: 168px;
                line-height: 24px;
                color: $color-primary;
                background: #fff3e0;
            }
        }
    }
}

.el-popper.dialog-mobile-prefix-popper {
    transform: translateX(-3px);

    .el-select-group__title {
        color: $font-color-label;
        font-size: 14px;
    }

    .el-select-dropdown__item {
        --el-text-color-regular: $font-color;
        font-size: 14px;
    }
}
