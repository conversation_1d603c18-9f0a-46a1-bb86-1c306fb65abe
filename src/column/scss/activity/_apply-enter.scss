@import '../variablesActivity';

.fixed-apply-enter {
    position: fixed;
    left: 50%;
    top: 50%;
    z-index: 9;
    background-color: $color-white;
    box-shadow: 0px 16px 18px 2px rgba(219, 228, 243, 0.3);
    width: 60px;
    border-radius: 60px;
    display: flex;
    flex-direction: column;
    padding: 30px 5px;
    transform: translateX(620px) translateY(-50%);

    .apply-enter-item {
        font-size: 12px;
        font-weight: bold;
        padding-top: 33px;
        cursor: pointer;

        & + .apply-enter-item {
            margin-top: 25px;
        }

        $icons: person, company, info;

        @each $name in $icons {
            &.apply-enter-#{$name} {
                background: url(../assets/activity/apply-enter/#{$name}.png) no-repeat center bottom 15px/30px 30px;
            }
        }
    }

    .apply-enter-info {
        position: relative;

        &:hover {
            color: $color-primary;

            .code-popup {
                display: block;
            }
        }

        .code-popup {
            display: none;
            position: absolute;
            right: calc(100% + 20px);
            top: 50%;
            transform: translateY(-50%);
            background: $color-white;
            padding: 14px;
            border-radius: 8px;
            box-shadow: 0px 0px 10px 0px rgba(51, 51, 51, 0.2);

            &::after {
                content: '';
                position: absolute;
                top: calc(50% - 3px);
                right: -13px;
                border-width: 6px;
                border-style: solid;
                border-color: transparent transparent transparent $color-white;
                transform: scaleX(1.7);
            }

            img {
                object-fit: contain;
                min-width: 140px;
                max-width: 472px;
                max-height: 472px;
                min-height: 140px;
                border-radius: 4px;
            }
        }
    }
}
