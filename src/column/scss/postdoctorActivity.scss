@import './variablesPostdoctor';

.main-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    padding-top: 20px;
    padding-bottom: 1px;
    background: url(//img.gaoxiaojob.com/uploads/boshihou/company/bg.png) no-repeat left top/100% auto,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-left-bg.png) no-repeat left 169px/380px 264px,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-right-bg.png) no-repeat right 169px/380px 264px, linear-gradient(180deg, #ffe9ea 0, #f8f8f8 326px);

    & > div {
        width: $view-width;
    }

    .swiper-t1 {
        width: 1200px;
        height: 170px;
        background-color: $color-white;
        border-radius: 12px;

        @include utils-swiper-bullet(#d6d3d2, $color-primary, 0.8);

        .swiper-pagination {
            display: flex;
            bottom: 0px;
            justify-content: center;
        }

        .swiper-pagination-bullet {
            margin: 0 4px;
            height: 4px;
            width: 27px;
            // 避免误触，增加点击热区，再进行缩放
            padding: 12px 0;
            transform: scaleY(0.15);
            border-radius: 5px;
        }
    }

    .activity {
        .list-header {
            @include wrapper-title;
        }
        .list-content {
            display: flex;
            flex-wrap: wrap;

            .list {
                width: calc((100% - 40px) / 3);
                margin-right: 20px;
                margin-bottom: 20px;
                padding: 16px 16px 0 16px;
                background-color: $color-white;
                border-radius: 16px;
                border: 1px solid #fff;
                position: relative;
                &:hover {
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                    border-radius: 16px;
                    border-color: #b61f22;
                    .hd {
                        .tit {
                            color: $color-primary;
                        }
                    }
                }
                &:nth-child(3n) {
                    margin-right: 0;
                }

                .hd {
                    border-radius: 16px 16px 0px 0px;

                    .pic-box {
                        width: 354px;
                        height: 187px;
                        overflow: hidden;
                        border-radius: 8px;
                        position: relative;

                        img {
                            width: 100%;
                            height: 100%;
                            border-radius: 0 0 0 10px;
                        }

                        .tag {
                            position: absolute;
                            bottom: 0;
                            left: 0;
                            max-width: 123px;
                            line-height: 24px;
                            padding: 0 10px;
                            background-color: $tag-background;
                            border-radius: 0px 8px 0px 8px;
                            color: $color-primary;
                            @include utils-ellipsis;
                        }
                    }

                    .tit {
                        height: 70px;
                        padding: 10px 0;
                        font-weight: bold;
                        color: #333333;
                        line-height: 24px;
                        p {
                            font-size: 16px;
                            @include text-ellipsis;
                        }
                    }
                }
                .ft {
                    padding-bottom: 10px;
                    border-top: 1px #ebebeb solid;
                    align-items: center;
                    justify-content: space-between;
                    .aside {
                        height: 54px;
                    }
                    .total {
                        margin-top: 6px;
                        font-size: 14px;
                        font-weight: 400;
                        color: #333333;
                        line-height: 21px;
                        opacity: 0.8;
                    }
                    .btn {
                        width: 96px;
                        height: 32px;
                        border-radius: 16px;
                        background: $liner-gradient-primary;
                        line-height: 32px;
                        color: $color-white;
                        font-weight: bold;
                        text-align: center;
                        &.disabled {
                            background: $background-info !important;
                        }
                    }
                }
            }
        }
    }
    .newest-wrapper {
        .pagination {
            margin-bottom: 20px;
        }

        .empty-container {
            height: 380px;
            border-radius: 12px;
            padding-top: 296px;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/empty.png) no-repeat 467px 70px/266px 194px, $color-white;
            text-align: center;
            color: $font-color-basic;
            margin-bottom: 20px;
        }
    }
}
