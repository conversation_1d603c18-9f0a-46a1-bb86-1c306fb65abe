@import './variables';
@import './search';

.job-container {
    .job-content {
        margin: 0 auto;
        width: $view-width;
    }

    .search-container {
        .search-main {
            .search-input {
                padding-left: 135px;
                padding-right: 0px;
            }
            .search-options {
                right: auto;
                padding-right: 0;
                .el-input__inner {
                    padding-left: 15px;
                }
            }

            .search-record {
                position: absolute;
                top: 30px;
                left: 0;
                right: 93px;
                display: none;
                padding: 12px 0 10px;
                background: #ffffff;
                border: 2px solid $color-primary;
                border-top: none;
                border-radius: 0 0 10px 10px;
                z-index: 120;

                &.active {
                    display: block;
                }

                .list {
                    position: relative;
                    padding-top: 10px;
                    color: $font-color-basic;
                    font-weight: 400;
                    line-height: 2;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 12px;
                        right: 12px;
                        border-top: 1px solid rgba($color: $font-color, $alpha: 0.1);
                    }

                    .tips {
                        margin: 0 12px;
                        color: $font-color-tips;
                        font-size: 12px;
                    }

                    .item {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0 12px;
                        cursor: pointer;

                        span {
                            flex: 1;
                            @include utils-ellipsis;
                        }

                        i {
                            font-style: normal;
                        }

                        &:hover {
                            color: $color-primary;
                            background-color: $background-color;
                        }
                    }

                    .remove {
                        padding: 0 12px;
                        font-size: 12px;
                        text-align: right;
                        line-height: 28px;

                        span {
                            cursor: pointer;

                            &:hover {
                                color: $color-primary;
                            }
                        }
                    }
                }
            }
        }

        .search-hot {
            color: $font-color-tips;
            font-weight: 400;

            a {
                display: inline-block;
                height: 22px;
                line-height: 22px;
                margin-bottom: 5px;
                padding: 0 7px;
                color: $font-color-label;
                border-radius: 4px;

                &:hover {
                    background: #daebff;
                }
            }
        }
    }

    .result-list {
        margin-bottom: 30px;

        .result-item {
            display: flex;
            justify-content: space-between;
            padding: 20px 0 15px;
            border-bottom: 1px solid $border-color;

            .title {
                margin-bottom: 15px;
                align-items: center;
            }

            .basic {
                margin-bottom: 15px;
                color: $font-color-basic;
                font-size: 14px;
            }

            .tips {
                color: $font-color-label;
                font-size: 12px;
            }

            .job-data {
                flex: 0 0 43%;
                max-width: 43%;
                font-size: 16px;

                .title {
                    h3 {
                        margin-right: 5px;
                        max-width: 70%;
                        color: $font-color;
                        font-size: 16px;
                        font-weight: normal;
                        @include utils-ellipsis;

                        &:hover {
                            color: $color-primary;
                        }
                    }

                    span {
                        color: $color-primary;
                        background-color: $background-color;
                        font-size: 13px;
                        line-height: 20px;
                        padding: 0 8px;
                        margin-top: -1px;
                        @include utils-radius;
                    }
                }

                .basic {
                    span {
                        &.salary {
                            max-width: 120px;
                            color: $font-color-salary;
                            font-size: 16px;
                            font-weight: bold;
                            @include utils-ellipsis;

                            i {
                                font-size: 14px;
                                font-style: normal;
                            }
                        }

                        &.area {
                            max-width: 120px;
                            @include utils-ellipsis;
                        }

                        & + span {
                            position: relative;
                            margin-left: 10px;
                            padding-left: 10px;

                            &::before {
                                content: '';
                                position: absolute;
                                top: calc(50% - 13px / 2);
                                left: 0;
                                width: 1px;
                                height: 13px;
                                background-color: $font-color-tips;
                            }
                        }
                    }
                }

                .tips {
                    padding: 5px 0;
                    font-size: 13px;

                    a {
                        color: $font-color-basic;
                        max-width: 100%;
                        @include utils-ellipsis;

                        &:hover {
                            color: $color-primary;
                        }
                    }
                }
            }

            .unit-data {
                flex: 0 0 35%;
                max-width: 35%;

                .title {
                    h3 {
                        max-width: 100%;
                        color: $font-color-basic;
                        font-size: 15px;
                        font-weight: normal;
                        @include utils-ellipsis;

                        &:hover {
                            color: $color-primary;
                        }
                    }
                }

                .basic {
                    color: $font-color-label;
                }

                .tips {
                    span {
                        padding: 4px 8px;
                        border: 1px solid $border-color;
                        border-radius: 4px;
                        background-color: #f3f8fd;
                        color: $font-color-basic;
                        & + span {
                            margin-left: 8px;
                        }
                    }
                }
            }

            .operate {
                display: flex;
                align-items: center;

                .el-button {
                    width: 108px;
                }
            }
        }
    }
}
