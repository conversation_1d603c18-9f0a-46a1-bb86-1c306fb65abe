@use './variables' as *;

$height-primary: 30px;

.home-login-template {
    padding: 0 16px;
    position: relative;

    .login-switch {
        position: absolute;
        right: 0;
        top: 0;

        .account-tips,
        .scan-tips {
            width: 40px;
            height: 40px;
            cursor: pointer;

            &:hover {
                .login-tips {
                    display: block;
                }
            }
        }

        .account-tips {
            background: url('../assets/login/scan.png') no-repeat right/contain;

            &.show-tips {
                .login-tips {
                    display: block;
                }
            }
        }

        .scan-tips {
            background: url('../assets/login/account.png') no-repeat right/contain;
        }

        .login-tips {
            display: none;
            position: absolute;
            z-index: 3;
            top: 14px;
            right: 50%;
            white-space: nowrap;
            line-height: 26px;
            font-size: 12px;
            padding: 0 19px 0 10px;
            background: url('../assets/login/bg-tips.png') no-repeat right top/100% 26px;
            color: $color-primary;
        }
    }

    .home-account-login {
        .el-tabs__header {
            margin-bottom: 10px;
        }

        .el-tabs__nav {
            float: none;
        }

        .el-tabs__item {
            height: $height-primary - 4;
            line-height: $height-primary - 4;
            padding: 0 12px;

            &.is-active {
                font-weight: bold;
            }
        }

        .el-tabs__nav-wrap::after {
            height: 0;
        }

        .el-form-item {
            margin-bottom: 10px;
        }

        .el-input__icon {
            line-height: $height-primary;
        }

        .el-input-group__prepend,
        .el-input-group__append {
            min-height: $height-primary - 2;
            line-height: $height-primary - 2;
            background-color: $color-white;
        }

        .el-input {
            line-height: initial;
        }

        .el-input__inner {
            height: $height-primary;
            line-height: $height-primary;
            font-size: 12px;
        }

        .send-code-button {
            position: relative;
            font-size: 12px;
            padding: 0 8px;
            min-height: $height-primary;
            background: initial;
            color: $color-primary;

            &::before {
                content: '';
                position: absolute;
                top: 10px;
                left: 0;
                width: 1px;
                height: 10px;
                // background-color: var(--el-input-border, var(--el-border-base));
                background-color: rgb(220, 223, 230);
            }
        }

        .mobile-login-confirm,
        .account-login-confirm {
            margin: 1px auto;
            padding-top: 0;
            padding-bottom: 0;
            width: 100%;
            min-height: 32px;
            font-weight: bold;
        }

        .signin-tips {
            line-height: 1.5;
        }

        .account-login-tips {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .mobile {
            .el-input__inner {
                padding-left: 80px;
            }

            .input-prepend {
                padding-left: 14px;
                display: flex;
                background-color: #fff;

                &::after {
                    display: inline-block;
                    content: '';
                    width: 1px;
                    height: 10px;
                    background-color: #ebebeb;
                    transform: translateY(10px);
                }
            }

            .el-input__prefix {
                width: 72px;
                height: calc(100% - 2px);
                overflow: hidden;
                left: 2px;
                top: 1px;
            }

            .mobile-prefix-select {
                color: #333;
                margin: 0px;

                .el-input__inner {
                    padding: 0 13px 0 0;
                    color: $font-color;
                    border: none;
                    height: 28px;
                }

                .el-input__suffix {
                    right: 0;
                    width: 13px;
                    display: flex;
                    align-items: center;
                    background: url('//img.gaoxiaojob.com/uploads/static/image/pc/column/assets/icon/down.png') no-repeat left/7px auto;

                    & > * {
                        display: none;
                    }
                }

                .el-input__prefix {
                    left: 0;
                    width: 30px;
                    background: url('../assets/icon/phone.png') no-repeat center/16px;
                }
            }
        }

        .sms-code {
            .el-input__inner {
                padding-right: 77px;
            }

            .el-input__prefix {
                left: 0;
                width: 30px;
                background: url('../assets/icon/sms.png') no-repeat center/16px;
            }

            .el-input__suffix {
                right: 0;

                .el-button {
                    border: none;
                }
            }
        }
    }

    .home-scan-login {
        font-size: 12px;

        .scan-login-title {
            display: inline-block;
            padding: 6px 0 4px;
            border-bottom: 2px solid $color-primary;
            margin-bottom: 9px;
            font-size: 14px;
            font-weight: bold;
        }

        .qr-code {
            width: 110px;
            height: 110px;
            margin: 0 auto 15px;
            position: relative;

            .code {
                width: 100%;
                height: 100%;
                background-size: contain;

                .scan-animation {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    z-index: 10;
                    left: 0;
                    top: 0;
                    background: url(../assets/login/scan-animation.png) no-repeat left top/cover;
                }
            }

            .code-refresh {
                position: absolute;
                border-radius: 4px;
                cursor: pointer;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                z-index: 11;
                background: rgba($color: #9a9a9a, $alpha: 0.8) url(../assets/login/new-refresh.png) no-repeat center / 58px 58px;
            }

            .scan-success {
                position: absolute;
                cursor: pointer;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                font-size: 16px;
                font-weight: bold;
                color: $color-primary;
                text-align: center;
                padding-top: 86px;
                background: url(../assets/login/success.png) no-repeat center 23px/ 44px 44px;
            }
        }

        .scan-code-tips {
            text-align: center;
            line-height: 15px;
            font-size: 13px;

            .success {
                margin-top: -8px;
                opacity: 0.8;
            }
        }
    }
}

.el-popper.home-mobile-prefix-popper {
    transform: translateX(-16px);

    .el-select-group__title {
        color: $font-color-label;
        font-size: 14px;
    }

    .el-select-dropdown__item {
        --el-text-color-regular: $font-color;
        font-size: 14px;
    }
}
