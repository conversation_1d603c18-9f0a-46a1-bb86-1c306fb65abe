@import './variables';

$search-width: 93px;
$search-height: 46px;
$filter-height: 34px;

.el-input__inner::placeholder {
    color: $font-color-basic;
}

.el-dialog {
    .el-input__inner::placeholder {
        color: $font-color-tips;
    }
}

.search-container {
    padding: 20px 0;
    background-color: $color-white;
    margin-bottom: 20px;

    .search-content {
        margin: 0 auto;
        width: $view-width;
    }

    .search-main {
        position: relative;
        padding: 0 170px;

        .search-hot {
            margin-top: 15px;
        }

        .search-input {
            padding-right: 95px;
            height: $search-height;
            border-radius: 10px 0 0 10px;
            border-color: $color-primary;
            @include utils-ellipsis;

            &:focus,
            &.active {
                border-color: $color-primary;
                border-width: 2px;
            }

            &::placeholder {
                color: $font-color-tips;
            }
        }

        .search-options {
            right: 0;
            padding-right: $search-width;

            .el-input__suffix-inner {
                display: flex;
                align-items: center;

                .el-input__inner {
                    height: $search-height;
                    line-height: $search-height;
                    border: none;
                    background: transparent;
                }

                .el-input__suffix-inner {
                    height: inherit;
                    line-height: inherit;
                }

                .search-line {
                    width: 1px;
                    height: 13px;
                    background: rgba($color: $font-color, $alpha: 0.2);
                }

                .search-type {
                    width: 80px;
                }

                .search-job-type {
                    width: 120px;
                }

                .search-job-type-content {
                    display: flex;
                    width: 120px;
                    padding: 0 30px 0 15px;
                    cursor: pointer;
                    color: #606266;
                    position: relative;
                    z-index: 1;

                    .job-type-label {
                        color: #606266;
                        line-height: 46px;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        flex-grow: 1;
                    }
                }
            }
        }

        .el-input-group__append {
            padding: 0;
            width: $search-width;
            font-size: 0;
            border: none;

            .search-button {
                margin: 0;
                padding: 0;
                width: 100%;
                height: $search-height;
                background: $color-primary url(../assets/icon/search-white.png) no-repeat center / 20px;
                border-radius: 0 10px 10px 0;

                &:hover {
                    background-color: var(--el-button-hover-color);
                }
            }
        }
    }
    @import './common/component/adRecommendCompany';
}

.search-filter {
    margin-bottom: 20px;
    padding: 20px 30px;
    background: $color-white;
    border-radius: 10px;

    .filter-pane {
        display: flex;
        color: $font-color-basic;
        line-height: $filter-height;

        &.special {
            height: $filter-height;
            overflow: hidden;

            &.is-show {
                height: auto;
            }
        }

        .filter-label {
            margin-right: 12px;
            color: $font-color;
            font-size: 14px;
        }

        .filter-value {
            flex: 1;

            .el-tag {
                margin-right: 5px;
                height: 26px;
                color: inherit;
                font-size: inherit;
                line-height: 26px;
                border-color: $color-white;
                background-color: $color-white;

                &.is-level {
                    display: none;
                    padding-left: 20px;
                    background: url(../assets/icon/filter-level.png) no-repeat 5px 6px;

                    &.is-show {
                        display: inline-block;
                    }

                    &:hover {
                        color: inherit;
                        border-color: $color-white;
                        background-color: inherit;
                    }
                }

                &.active {
                    i {
                        display: inline-block;
                    }
                }

                &:hover,
                &.active {
                    color: $color-primary;
                    border-color: #ffeccc;
                    background-color: #fff6e6;
                }

                i {
                    display: none;
                }
            }
        }

        .filter-multiple {
            min-width: 74px;
            padding: 0 12px;
            height: $filter-height;
            cursor: pointer;
            background-color: #fafafc;
            display: flex;
            align-items: center;
            justify-content: center;
            @include utils-radius;
            &::after {
                content: '+';
                display: inline-block;
                font-weight: bolder;
                color: $color-primary;
                margin-left: 10px;
                font-size: 15px;
            }
            &.active {
                background-color: #fff3e0;
                color: $color-primary;
            }
        }

        .filter-more {
            padding: 0 4px;
            height: $filter-height;
            cursor: pointer;

            &::after {
                content: '';
                display: inline-block;
                margin-top: -4px;
                margin-left: 10px;
                width: 8px;
                height: 4px;
                vertical-align: middle;
                background: url(../assets/icon/filter-arrow.png) no-repeat right center / 8px 4px;
                transition: all 0.3s ease;
            }

            &.is-reverse::after {
                transform: rotate(-180deg);
            }
        }

        .filter-clear {
            color: $font-color-label;
            cursor: pointer;
        }
    }

    .filter-pane-more {
        margin-top: 10px;

        .filter-value {
            display: flex;

            .el-tag {
                i {
                    display: inline-block;
                    background: $color-primary;
                }
            }

            .el-input,
            .el-select,
            .el-cascader,
            .el-input__inner {
                width: 100px;
                height: $filter-height;
                color: $color-primary;
                font-size: 12px;
                line-height: $filter-height;
                border: none;
                @include utils-ellipsis;
            }

            .education {
                .el-select__tags {
                    & > span {
                        display: flex;
                    }
                }

                .el-input {
                    .el-input__inner {
                        padding-right: 10px;
                    }
                }
            }

            .el-input__inner {
                padding-left: 10px;
            }

            .el-select {
                margin-right: 10px;
            }

            .el-cascader {
                margin-right: 10px;

                .el-icon {
                    line-height: $filter-height;
                }
            }

            .el-input__icon {
                line-height: $filter-height;
            }
        }
    }
}

.search-result {
    padding: 30px;
    background-color: $color-white;
    border-radius: 10px;

    .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        .result-title {
            position: relative;
            padding-left: 20px;
            font-size: 18px;
            font-weight: bold;

            &::before {
                content: '';
                position: absolute;
                top: calc(50% - 16px / 2);
                left: 0;
                width: 4px;
                height: 16px;
                background-color: $color-primary;
                border-radius: 2px;
            }
        }

        .result-sort {
            span {
                display: inline-block;
                padding: 8px 20px 8px 8px;
                color: $font-color-basic;
                background: #fafafc url(../assets/icon/sort.png) no-repeat calc(100% - 5px) center / 8px 9.6px;
                cursor: pointer;

                &.active,
                &:hover {
                    color: $color-primary;
                    background-color: $background-color;
                    background-image: url(../assets/icon/sort-active.png);
                }
            }
        }
    }

    .result-list {
        margin-bottom: 20px;
    }
}
