@use './variables' as *;

$max-width: 420px;

.superior-dialog-template {
    .el-overlay-dialog {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .superior-dialog {
        width: $max-width;
        margin: 0;

        .el-dialog__header {
            padding: 0;

            .title {
                display: flex;
                height: 50px;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                font-weight: bold;
                border-radius: 10px 10px 0 0;
                background: url('../assets/job/superior-bg.png') no-repeat left top/cover;
            }

            .tag {
                line-height: 18px;
                padding: 0 6px;
                color: #684333;
                font-size: 12px;
                margin-left: 7px;
                background: linear-gradient(118deg, #ffe9ce 0%, #ffd7aa 100%);
                border-radius: 9px 9px 9px 0px;
            }
        }

        .el-dialog__body {
            color: $font-color;
            padding: 30px 30px 30px 28px;
        }

        .filter-type {
            padding-bottom: 15px;

            .type-title {
                display: flex;
                justify-content: space-between;
                margin-bottom: 15px;

                .label {
                    display: flex;
                    align-items: center;
                    font-size: 16px;
                    font-weight: bold;
                }

                .ask {
                    font-size: 12px;
                    color: $font-color-label;
                }

                .answer {
                    width: 16px;
                    height: 16px;
                    background: url(../assets/icon/question.png) no-repeat center/contain;
                }

                .desc {
                    color: $font-color-basic;
                }
            }
        }

        .el-checkbox {
            width: 83px;
            height: 32px;
            line-height: 32px;
            justify-content: center;
            margin-bottom: 15px;
            background-color: #f8f8f8;
            border-radius: 4px;
            color: $font-color;
            margin-right: 15px;

            &.is-checked {
                background-color: #fff3e0;
            }

            .el-checkbox__input {
                width: 0;
                height: 0;
                overflow: hidden;
            }

            .el-checkbox__label {
                padding-left: 0;
            }
        }

        .filter-bottom {
            display: flex;
            justify-content: center;

            .el-button {
                min-height: 32px;
                padding: 0;
                width: 94px;

                & + .el-button {
                    margin-left: 20px;
                }
            }
        }
    }

    .tips-dialog {
        width: $max-width;
        margin: 0;

        .el-dialog__header {
            padding: 0;

            .title {
                line-height: 56px;
                font-size: 18px;
                text-align: center;
            }
        }

        .el-dialog__body {
            padding: 0 0 20px;

            .desc {
                line-height: 28px;
                margin-bottom: 9px;
                text-align: center;
                color: $font-color;
            }

            .tips {
                font-size: 12px;
                color: $font-color-label;
                text-align: center;
                margin-bottom: 20px;
            }

            .tips-bottom {
                display: flex;
                justify-content: center;

                .el-button {
                    min-height: 32px;
                    padding: 0;
                    width: 94px;

                    & + .el-button {
                        margin-left: 20px;
                    }
                }
            }
        }
    }
}

.superior-popper-content {
    line-height: 1.5;
}
