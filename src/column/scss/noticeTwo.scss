@use 'sass:meta';
@import './variables';
@import './common/component/detailShare';
@import './mini-code-popup';

$color-main: #4957a1;
$default-radius: 8px;

body {
    .el-main {
        padding-bottom: 0;
        background-color: $color-white;
    }
}

.detail-container {
    background: url(../assets/notice/two/page-bg-left.png) no-repeat left bottom/655px 194px, url(../assets/notice/two/page-bg-right.png) no-repeat right bottom/913px 220px,
        linear-gradient(180deg, #e7eafb, #ddf0fb);

    .bold {
        font-weight: bold;
    }

    @import './notice/headerTemplateTwo';

    .detail-main {
        margin: 0 auto;
        width: $view-width;

        .info-wrapper {
            padding: 5px;
            background-color: $color-white;
            border-radius: $default-radius;
            margin-bottom: 30px;

            &-content {
                font-size: 16px;
                padding: 17px 24px 17px 24px;
                background: url(../assets/notice/two/info-bg.png) repeat-y left top / 100% auto, $color-white;
                border-radius: 12px;

                .item {
                    font-size: 16px;
                    line-height: 32px;
                    padding-left: 26px;

                    .primary {
                        color: #4159d8;
                    }

                    a:hover {
                        color: #4159d8;
                    }

                    &.major {
                        position: relative;

                        .major-content {
                            @include utils-ellipsis-lines(4, 32px, 16px);
                        }

                        .major-popup {
                            display: none;
                            width: 800px;
                            top: calc(100% + 8px);
                            position: absolute;
                            background: $color-white;
                            box-shadow: 0px 0px 10px 0px rgba(51, 51, 51, 0.2);
                            border-radius: 8px;
                            font-size: 12px;
                            padding: 17px 20px;
                            right: -24px;
                            z-index: 1 !important;
                            line-height: 2;

                            &::before {
                                content: '';
                                position: absolute;
                                top: -16px;
                                right: 35px;
                                border-width: 8px;
                                border-style: solid;
                                border-color: transparent transparent $color-white transparent;
                            }
                        }

                        &:hover {
                            .major-popup {
                                display: block;
                            }
                        }
                    }
                }

                $icons: info, major, welfare;
                @each $icon in $icons {
                    .#{$icon} {
                        background: url(../assets/notice/two/#{$icon}.png) no-repeat left 6px/20px 20px;
                    }

                    @if ($icon == 'info') {
                        .#{$icon} {
                            background: url(../assets/notice/two/#{$icon}.png) no-repeat left 8px/20px 20px;
                        }
                    }
                }
            }
        }

        .common-wrapper {
            margin-bottom: 30px;

            .common-title {
                margin-bottom: 22px;
                padding-top: 16px;
                background: url(../assets/notice/two/title-icon-left.png) no-repeat left calc(50% + 2px) / 34px 26px, url(../assets/notice/two/title-icon-right.png) no-repeat right bottom/207px 57px;
                padding-left: 45px;
                position: relative;
                padding-right: 269px;

                &::after {
                    position: absolute;
                    width: 100%;
                    bottom: 0;
                    left: 0;
                    content: '';
                    display: block;
                    height: 2px;
                    background: linear-gradient(90deg, #415ad8 0%, #e2eafb 100%);
                }

                h2 {
                    display: inline-block;
                    font-size: 28px;
                    background: linear-gradient(90deg, #4159d8 0%, #5cb5f4 100%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    font-weight: bold;
                    line-height: 42px;
                    padding-bottom: 3px;
                }
            }

            .common-content {
                background: url(../assets/notice/two/wrapper-bg.png) no-repeat right bottom/369px 166px, $color-white;
                border-radius: $default-radius;
                padding: 24px;
                position: relative;
                overflow: hidden;
                white-space: wrap;

                @import './notice/richReset';
            }
        }

        @import './common/jobFileList';
        @include meta.load-css('./common/detailTable');

        .file-wrapper {
            margin-bottom: 30px;

            .common-title {
                margin-bottom: 30px;
            }

            .file {
                width: calc((100% - 24px) / 3);

                &:nth-of-type(2n) {
                    margin-left: 0;
                }

                &:nth-of-type(3n - 1) {
                    margin-left: 12px;
                    margin-right: 12px;
                }
            }
        }

        .notice-heat {
            background-color: $color-white;
            border-radius: 10px;
            padding: 17px 40px 26px;
            margin-top: 30px;
            cursor: pointer;
            margin-bottom: 40px;

            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;

                .left {
                    background: url(../assets/notice/heat.png) no-repeat left center/38px;
                    line-height: 40px;
                    padding-left: 45px;
                    font-size: 18px;
                    font-weight: bold;
                }

                .right {
                    cursor: pointer;
                    padding-right: 10px;
                    font-size: 14px;
                    color: #4e6ef2;
                    background: url(../assets/notice//two/arrow-primary.png) no-repeat right center/6px 9px;
                }
            }

            .text-heat {
                font-size: 14px;

                span {
                    font-size: 16px;
                    color: #4e6ef2;
                    font-weight: bold;
                }

                .active {
                    color: transparent;
                    text-shadow: #4e6ef2 0 0 10px;
                }
            }
        }

        .detail-emit {
            display: flex;
            justify-content: center;
            padding-bottom: 40px;
            border-bottom: 1px solid #8fafe2;

            .el-button {
                border: none;

                &:hover {
                    border: none;
                }
            }

            .view-job {
                background-color: transparent;
                border: 1px solid #415ad8;
                color: #415ad8;
                margin-right: 30px;
                padding: 12px 20px;
                border-radius: 4px;
                font-size: 14px;
            }

            .el-button--apply {
                background: linear-gradient(90deg, #4159d8, #5cb5f4);
                color: #fff;
            }

            .offline {
                cursor: not-allowed;
                color: #fff;
                background-color: rgba(#415ad8, 0.4);
            }
        }
    }
}

.el-popper {
    .boon {
        display: inline-block;
        padding: 3px 8px;
        margin: 8px 6px;
        color: $color-primary;
        font-size: 12px;
        font-weight: 400;
        background: #feebcb;
        border-radius: 4px;
    }
}
