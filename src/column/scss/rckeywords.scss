@use 'sass:meta';
@use './footerLink';
@import './variables';

$filter-height: 30px;
$content-width: 860px;

.el-main {
    font-size: 14px;
    background-color: #f6f6f6 !important;

    .header-container {
        padding: 30px 0 14px;
        margin-bottom: 20px;

        background: $color-white url(//gaoxiaojob.com/static/assets/detail/detail-header.jpg) no-repeat center/cover;

        header {
            width: 1200px;
            margin: 0 auto;
        }

        h1 {
            font-size: 32px;
        }

        span {
            display: block;
            font-size: 18px;
            font-weight: bold;
            margin: 25px 0;
        }
    }

    .job-container {
        width: $view-width;
        margin: 0 auto;
        display: flex;

        .job-content {
            width: $content-width;
            margin-right: 20px;
            flex-shrink: 0;

            .job-list {
                font-size: inherit;
            }

            .job-card {
                margin-bottom: 12px;
                background-color: #fff;
                border-radius: 10px;
            }

            .job-body {
                padding: 20px;
                display: flex;

                .job-detail {
                    flex-grow: 1;
                    overflow: hidden;
                }

                .job-name {
                    display: block;
                    font-size: 16px;
                    font-weight: bold;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;

                    &:hover {
                        color: $color-primary;
                    }
                }

                .job-basic {
                    margin-top: 12px;
                    white-space: nowrap;
                    overflow: hidden;
                }

                .job-salary {
                    font-size: 16px;
                    font-weight: bold;
                    color: #fa635c;
                }

                .job-welfare {
                    display: inline-flex;
                    margin-left: 12px;

                    .tag {
                        line-height: 20px;
                        padding: 0 8px;
                        font-size: 12px;
                        background-color: #f3f8fd;
                        border-radius: 4px;
                        margin-right: 8px;
                        white-space: nowrap;
                        max-width: 256px;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                }

                .job-announcement {
                    display: block;
                    color: $font-color-basic;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    margin-top: 15px;

                    &:hover {
                        color: $color-primary;
                    }

                    &::before {
                        display: inline-block;
                        content: '关联公告';
                        padding: 0 6px;
                        line-height: 21px;
                        color: #6a6053;
                        background: url(../assets/rczhaopin/relevance.png) no-repeat left center/contain;
                        margin-right: 6px;
                    }
                }

                .job-button {
                    align-self: center;
                    min-width: 96px;
                    margin-left: 60px;
                    flex-shrink: 0;
                    line-height: 35px;
                    height: 35px;
                    padding: 0 20px;
                    color: #fff;
                    background-color: $color-primary;
                    border-radius: 4px;
                    cursor: pointer;
                    border: none;
                    align-items: flex-start;

                    &.job-applied {
                        background-color: #ffd080;
                        cursor: not-allowed;
                    }

                    &.job-offline {
                        color: #bcbec2;
                        background-color: #f4f4f5;
                        border: 1px solid #e9e9eb;
                        cursor: not-allowed;
                    }
                }
            }

            .job-footer {
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
                line-height: 48px;
                background: linear-gradient(90deg, #fff8ed 0%, #fffcf7 100%);
                border-radius: 0px 0px 10px 10px;

                .company-name {
                    float: left;
                    max-width: 365px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;

                    &:hover {
                        color: $color-primary;
                    }
                }

                .company-info {
                    float: left;
                    color: $font-color-basic;
                    margin-left: 15px;
                    font-size: 13px;
                }

                .release-date {
                    color: $color-primary;
                    padding-left: 21px;
                    background: url('../assets/icon/clock.png') no-repeat left center/16px;
                }
            }

            .pagination {
                margin: 20px auto 30px;
            }

            .job-empty {
                .empty {
                    background: url('../assets/rczhaopin/empty.png') no-repeat center 20px/150px auto, #fff;
                    border-radius: 10px;
                    padding-top: 130px;
                    padding-bottom: 20px;
                    text-align: center;
                    color: $font-color-basic;
                }

                .empty-bottom {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 30px 0 20px;

                    .recommend-title {
                        font-size: 18px;
                        font-weight: bold;
                        position: relative;

                        &::before {
                            content: '';
                            margin-right: 14px;
                            display: inline-block;
                            width: 4px;
                            height: 16px;
                            background: $color-primary;
                            border-radius: 2px;
                        }
                    }

                    .more {
                        color: $color-primary;
                    }
                }
            }

            .view-more-job {
                display: table;
                text-align: center;
                height: 35px;
                line-height: 35px;
                color: $color-primary;
                padding: 0 10px;
                background: #fff3e0;
                border: 1px solid $color-primary;
                border-radius: 4px;
                margin: 30px auto;

                span {
                    color: $font-color-salary;
                }
            }
        }

        .aside-content {
            flex-grow: 1;

            .advert-vip {
                margin-bottom: 20px;
                border-radius: 10px;
                overflow: hidden;
                cursor: pointer;

                & > img {
                    display: block;
                    width: 100%;
                }
            }

            .ad-content {
                a {
                    display: block;
                    height: 140px;
                    border-radius: 10px;
                    overflow: hidden;
                    margin-bottom: 20px;
                }

                img {
                    width: 100%;
                    display: block;
                    object-fit: contain;
                }
            }

            .job-recommendation {
                padding: 20px 20px 10px 20px;
                background: #fff;
                border-radius: 10px;
                box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);
                margin-bottom: 30px;

                .title {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 5px;

                    h5 {
                        position: relative;
                        padding-left: 12px;
                        font-size: 16px;
                        font-weight: bold;
                        line-height: 1;
                        &::before {
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            bottom: 0;
                            width: 4px;
                            background-color: #ffa000;
                            border-radius: 2px;
                        }
                    }
                }

                .keywords-list {
                    display: flex;
                    flex-direction: column;

                    .keyword {
                        padding: 12px 0;
                        border-bottom: 1px solid $border-color;
                    }
                }
            }
        }
    }

    .footer-link-container {
        width: $view-width;
        margin: 0 auto;

        .footer-link-content {
            width: $content-width;
        }
    }
}
