@use 'sass:meta';
@import './variables';

$border-radius: 10px;
$border-color: #ebebeb;
$rich-line-height: 1.4;
$column-width: 486px;

body {
    background-color: #efefef;
    font-size: 14px;
    color: $font-color;
}

.succeed-main {
    width: $view-width;
    margin: 20px auto 60px;
    border-radius: $border-radius;
    overflow: hidden;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    .banner {
        width: 100%;
        height: 300px;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .succeed-content {
        display: flex;
        flex-direction: column;
        padding: 42px 110px 40px;

        .title {
            display: inline-block;
            font-size: 20px;
            font-weight: bold;
            line-height: 32px;
            padding-left: 44px;
            background: url('../assets/icon/successful.png') no-repeat left/32px 32px;
            align-self: center;
            margin-bottom: 23px;
        }

        .goto {
            text-align: center;
            margin-bottom: 25px;
            line-height: 21px;

            a {
                display: inline-block;
                margin-left: 4px;
                color: $color-primary;
            }
        }

        .tips-content {
            font-size: 14px;
            line-height: 28px;
            margin-bottom: 38px;
        }

        .qr-code {
            align-self: center;
            display: flex;
            width: 480px;
            justify-content: space-between;
            flex-wrap: wrap;

            &.center {
                justify-content: center;
            }

            img {
                width: 200px;
                height: 200px;
                object-fit: contain;
                margin-bottom: 20px;

                &:nth-last-child(1),
                &:nth-last-child(2) {
                    margin-bottom: 40px;
                }
            }
        }

        .recommend {
            align-self: center;
            display: flex;
            width: 459px;
            padding-top: 38px;
            justify-content: center;
            background: url('../assets/activity/recommend-title.png') no-repeat top/459px 17px;

            @each $i, $url in (1, '../assets/activity/resume.png'), (2, '../assets/activity/job.png'), (3, '../assets/activity/company.png') {
                a:nth-child(#{$i}) {
                    text-align: center;
                    padding-top: 53px;
                    background: url(#{$url}) no-repeat top/40px;
                }
            }

            a {
                &:hover {
                    color: $color-primary;
                }

                & + a {
                    margin-left: 90px;
                }
            }
        }
    }
}
