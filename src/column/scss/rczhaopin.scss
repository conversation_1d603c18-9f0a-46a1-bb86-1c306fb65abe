@use 'sass:meta';
@use './footerLink';
@use './pagination';

@import './variables';

$filter-height: 30px;
$content-width: 860px;

.el-main {
    font-size: 14px;
    background-color: #f6f6f6 !important;

    .search-container {
        background-color: $color-white;
        padding: 30px 0 14px;
        margin-bottom: 20px;

        .kw-content {
            display: flex;
            margin: 0 auto;
            width: $view-width;
            margin-bottom: 17px;

            .search-kw {
                flex-grow: 1;
                width: 1200px;
                border: 2px solid $color-primary;
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
                border-right: none;
                outline: none;
                padding: 10px 20px;
                font-size: 14px;
            }

            .search-button {
                line-height: 50px;
                padding: 0 25px 0 59px;
                white-space: nowrap;
                font-size: 18px;
                font-weight: bold;
                color: $color-white;
                background: url(../assets/icon/search-white.png) no-repeat 25px center/20px, $color-primary;
                border: none;
                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;
                cursor: pointer;
            }
        }

        .filter-content {
            margin: 0 auto;
            width: $view-width;

            .filter-pane {
                line-height: $filter-height;
                padding: 5px 0px;
                display: flex;

                &.only-line {
                    overflow: hidden;
                }

                &.area {
                    height: 40px;
                    overflow: hidden;
                }

                &.is-show {
                    height: auto;
                }

                &.filter-selected {
                    margin-top: 8px;
                }

                .filter-label {
                    padding-right: 20px;
                    font-size: 16px;
                    font-weight: bold;
                    flex-shrink: 0;
                }

                .filter-value {
                    flex-grow: 1;
                    position: relative;

                    .filter-item {
                        display: inline-block;
                        padding: 0 11px;
                        line-height: $filter-height;
                        cursor: pointer;
                        position: relative;

                        &.active {
                            &:first-child {
                                &::after {
                                    display: none;
                                }
                            }

                            &::after {
                                content: '';
                                position: absolute;
                                bottom: -7px;
                                left: 50%;
                                transform: translateX(-50%);
                                border-style: solid;
                                border-width: 10px 12px;
                                border-color: transparent transparent #f8f8f8 transparent;
                            }
                        }

                        &.active,
                        &.is-select,
                        &.select,
                        &:hover {
                            color: $color-primary;
                        }
                    }

                    .filter-sub {
                        background-color: #f8f8f8;
                        border-radius: 4px;
                        display: none;
                        margin-bottom: 10px;
                        margin-top: 5px;
                        padding: 5px 0;

                        &.active {
                            display: block;
                        }

                        .filter-sub-item {
                            display: inline-block;
                            padding: 0 12px;

                            &:hover,
                            &.select {
                                color: $color-primary;
                            }
                        }
                    }

                    .selected {
                        padding: 0 12px;
                        line-height: 28px;
                        background: $color-primary;
                        border-radius: 4px;
                        font-size: 14px;
                        color: #ffffff;
                        margin-right: 12px;
                        display: inline-flex;
                        align-items: center;

                        .close {
                            margin-left: 7px;
                            width: 10px;
                            height: 10px;
                            background: url(../assets/icon/close-white.png) no-repeat center/contain;
                        }
                    }
                }

                .filter-select,
                .filter-cascade {
                    display: inline-block;
                    position: relative;
                    margin-right: 16px;
                    cursor: pointer;

                    .filter-select-dropdown {
                        position: absolute;
                        left: 0;
                        top: 40px;
                        background: #fff;
                        border-radius: 8px;
                        border: 1px solid #ededed;
                        z-index: 1;
                        transition: opacity 0.2s linear;
                        display: none;
                        overflow: hidden;

                        .filter-select-tree {
                            padding: 5px 0px;
                            max-height: 300px;
                            overflow: hidden;
                            min-width: 160px;
                            max-width: 230px;
                            border-right: 1px solid #ededed;

                            .has-children {
                                position: relative;

                                &.active {
                                    .filter-item {
                                        color: $color-primary;
                                    }

                                    &::after {
                                        border-color: $color-primary;
                                    }
                                }

                                &::after {
                                    content: '';
                                    position: absolute;
                                    right: 12px;
                                    top: 50%;
                                    display: inline-block;
                                    width: 8px;
                                    height: 8px;
                                    border-top: 1px solid $font-color-label;
                                    border-right: 1px solid $font-color-label;
                                    transform-origin: center;
                                    transform: rotate(45deg) translateY(-50%);
                                }

                                &:hover {
                                    &::after {
                                        border-color: $color-primary;
                                    }
                                }
                            }

                            &:last-child {
                                border-left: none;
                            }
                        }

                        ul {
                            height: 100%;
                            overflow: auto;
                            padding: 0px 8px;
                        }

                        li {
                            position: relative;
                            transition: all 0.2s linear;
                            border-radius: 4px;
                            font-size: 13px;

                            a {
                                display: block;
                                line-height: 40px;
                                padding: 0px 20px 0px 8px;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;

                                &.select {
                                    color: $color-primary;
                                    font-weight: 500;
                                }

                                & + .filter-select-tree {
                                    display: none;
                                }
                            }

                            &:hover {
                                color: $color-primary;
                                background-color: #fff3e0;
                                font-weight: 500;
                            }
                        }
                    }

                    &::after {
                        position: absolute;
                        width: 10px;
                        height: 10px;
                        z-index: 2;
                        content: ' ';
                        background: #fff;
                        box-sizing: border-box;
                        border: 1px solid #ededed;
                        border-bottom-color: transparent;
                        border-right-color: transparent;
                        left: 50%;
                        bottom: -17px;
                        transform: translateX(-50%) rotate(45deg);
                        display: none;
                    }

                    &::before {
                        position: absolute;
                        width: 230px;
                        height: 12px;
                        top: 100%;
                        z-index: 3;
                        content: ' ';
                        display: none;
                    }

                    &.is-select,
                    &.open {
                        .current-select {
                            color: $color-primary;
                            background-color: #fff3e0;

                            &::after {
                                background: url(../assets/icon/filter-arrow.png) no-repeat left/8px 4px;
                            }
                        }
                    }

                    &.open {
                        .current-select {
                            &::after {
                                transform: rotate(180deg);
                                background: url(../assets/icon/filter-arrow.png) no-repeat left/8px 4px;
                            }
                        }
                        .filter-select-dropdown {
                            display: flex;
                        }

                        &::after,
                        &::before {
                            display: block;
                        }
                    }
                }

                .current-select {
                    position: relative;
                    line-height: 28px;
                    border-radius: 4px;
                    padding: 0 28px 0 10px;
                    background-color: #f8f8f8;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &::after {
                        content: '';
                        width: 8px;
                        height: 4px;
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        transform: translateY(-50%);
                        transform-origin: center 1px;
                        transition: all 0.2s ease-in-out;
                        background: url(../assets/icon/filter-arrow-default.png) no-repeat left/8px 4px;
                    }

                    &:hover {
                        color: $color-primary;
                        background-color: #fff3e0;

                        &::after {
                            transform: rotate(180deg);
                            background: url(../assets/icon/filter-arrow.png) no-repeat left/8px 4px;
                        }
                    }
                }

                .filter-more {
                    padding: 0 4px;
                    line-height: $filter-height;
                    cursor: pointer;
                    flex-shrink: 0;

                    &::after {
                        content: '';
                        display: inline-block;
                        margin-top: -4px;
                        margin-left: 10px;
                        width: 8px;
                        height: 4px;
                        vertical-align: middle;
                        background: url(../assets/icon/filter-arrow.png) no-repeat right center/8px 4px;
                        transition: all 0.3s ease;
                    }

                    &.is-reverse {
                        &::after {
                            transform: rotate(-180deg);
                        }
                    }
                }

                .filter-empty {
                    flex-shrink: 0;
                    align-self: flex-start;
                    padding-left: 26px;
                    background: url(../assets/icon/delete.png) no-repeat 6px center/ 14px;
                    color: $font-color-basic;
                }
            }
        }
    }

    .job-container {
        width: $view-width;
        margin: 0 auto;
        display: flex;

        .job-content {
            width: $content-width;
            margin-right: 20px;
            flex-shrink: 0;

            .job-list {
                font-size: inherit;
            }

            .job-card {
                margin-bottom: 12px;
                background-color: #fff;
                border-radius: 10px;
            }

            .job-body {
                padding: 20px;
                display: flex;

                .job-detail {
                    flex-grow: 1;
                    overflow: hidden;
                }

                .job-name {
                    display: block;
                    font-size: 16px;
                    font-weight: bold;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;

                    &:hover {
                        color: $color-primary;
                    }
                }

                .job-basic {
                    margin-top: 12px;
                    white-space: nowrap;
                    overflow: hidden;
                }

                .job-salary {
                    font-size: 16px;
                    font-weight: bold;
                    color: #fa635c;
                }

                .job-welfare {
                    display: inline-flex;
                    margin-left: 12px;

                    .tag {
                        line-height: 20px;
                        padding: 0 8px;
                        font-size: 12px;
                        background-color: #f3f8fd;
                        border-radius: 4px;
                        margin-right: 8px;
                        white-space: nowrap;
                        max-width: 256px;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                }

                .job-announcement {
                    display: block;
                    color: $font-color-basic;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    margin-top: 15px;

                    &:hover {
                        color: $color-primary;
                    }

                    &::before {
                        display: inline-block;
                        content: '关联公告';
                        padding: 0 6px;
                        line-height: 21px;
                        color: #6a6053;
                        background: url(../assets/rczhaopin/relevance.png) no-repeat left center/contain;
                        margin-right: 6px;
                    }
                }

                .job-button {
                    align-self: center;
                    min-width: 96px;
                    margin-left: 60px;
                    flex-shrink: 0;
                    line-height: 35px;
                    height: 35px;
                    padding: 0 20px;
                    color: #fff;
                    background-color: $color-primary;
                    border-radius: 4px;
                    cursor: pointer;
                    border: none;
                    align-items: flex-start;

                    &.job-applied {
                        background-color: #ffd080;
                        cursor: not-allowed;
                    }

                    &.job-offline {
                        color: #bcbec2;
                        background-color: #f4f4f5;
                        border: 1px solid #e9e9eb;
                        cursor: not-allowed;
                    }
                }
            }

            .job-footer {
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
                line-height: 48px;
                background: linear-gradient(90deg, #fff8ed 0%, #fffcf7 100%);
                border-radius: 0px 0px 10px 10px;

                .company-name {
                    float: left;
                    max-width: 365px;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;

                    &:hover {
                        color: $color-primary;
                    }
                }

                .company-info {
                    float: left;
                    color: $font-color-basic;
                    margin-left: 15px;
                    font-size: 13px;
                }

                .release-date {
                    color: $color-primary;
                    padding-left: 21px;
                    background: url('../assets/icon/clock.png') no-repeat left center/16px;
                }
            }

            .pagination {
                margin: 20px auto 30px;
            }

            .job-empty {
                .empty {
                    background: url('../assets/rczhaopin/empty.png') no-repeat center 20px/150px auto, #fff;
                    border-radius: 10px;
                    padding-top: 130px;
                    padding-bottom: 20px;
                    text-align: center;
                    color: $font-color-basic;
                }

                .empty-bottom {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 30px 0 20px;

                    .recommend-title {
                        font-size: 18px;
                        font-weight: bold;
                        position: relative;

                        &::before {
                            content: '';
                            margin-right: 14px;
                            display: inline-block;
                            width: 4px;
                            height: 16px;
                            background: $color-primary;
                            border-radius: 2px;
                        }
                    }

                    .more {
                        color: $color-primary;
                    }
                }
            }

            .view-more-job {
                display: block;
                width: 96px;
                height: 35px;
                line-height: 35px;
                background: #fff3e0;
                color: $color-primary;
                border: 1px solid $color-primary;
                border-radius: 4px;
                margin: 30px auto;
                text-align: center;
            }
        }

        .aside-content {
            flex-grow: 1;

            @include meta.load-css('./job-aside');
           
            .advert-vip {
                margin-bottom: 20px;
                border-radius: 10px;
                overflow: hidden;
                cursor: pointer;

                & > img {
                    display: block;
                    width: 100%;
                }
            }

            .collaborate-content {
                height: 88px;
                background: url(../assets/rczhaopin/collaborate-apply-bg.png) no-repeat left/contain;
                display: flex;
                align-items: center;
                padding: 0 20px;
                margin-bottom: 20px;

                .apply-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #fae4c2;
                    margin-bottom: 12px;
                }

                .tips {
                    color: #e2d1af;
                }

                .apply-btn {
                    padding: 0 15px;
                    line-height: 30px;
                    background: #f3e2c0;
                    border-radius: 4px;
                    margin-left: 24px;
                }
            }

            .ad-content {
                a {
                    display: block;
                    height: 140px;
                    border-radius: 10px;
                    overflow: hidden;
                    margin-bottom: 20px;
                }

                img {
                    width: 100%;
                    display: block;
                    object-fit: contain;
                }
            }
            .job-recommendation {
                padding: 20px 20px 10px 20px;
                background: #fff;
                border-radius: 10px;
                box-shadow: 0px 4px 30px 0px rgba(102,102,102,.06);
                margin-top: 30px;
                .title {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    h5 {
                        position: relative;
                        padding-left: 12px;
                        font-size: 16px;
                        font-weight: bold;
                        line-height: 1;
                        &::before {
                            content: "";
                            position: absolute;
                            top: 0;
                            left: 0;
                            bottom: 0;
                            width: 4px;
                            background-color: #ffa000;
                            border-radius: 2px;
                        }
                    }
                }
                .element {
                    border-radius: 5px;
                    margin-bottom: 10px;
                    background-color: #fafafc;
                    position: relative;
                    padding: 12px 8px;
                    .position {
                        display: flex;
                        justify-content: space-between;
                        font-size: 16px;
                        font-weight: bold;
                    }
                    .pay {
                        flex: none;
                        max-width: 30%;
                        padding-left: 10px;
                        color: #fa635c;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                    .article {
                        font-size: 14px;
                        line-height: 1;
                        margin-top: 10px;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        max-width: 265px;
                    }
                    .required {
                        display: flex;
                        justify-content: flex-start;
                        color: #333333;
                        opacity: 0.8;
                        margin-top: 10px;
                        line-height: 26px;
                        font-size: 12px;
                        span {
                            border-radius: 2px;
                        }
                        .tag{
                            padding: 0 10px;
                        }
                        .time {
                            font-size: 12px;
                            background: url(../assets/rczhaopin/time.png) no-repeat left center/12px;
                            margin: 0;
                            padding-left: 16px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }
                    }
                    .introduce {
                        display: flex;
                        color: #333333;
                        opacity: 0.8;
                        line-height: 1;
                        margin-top: 10px;
                        font-size: 13px;
                        flex-direction: row;
                        justify-content: space-between;
                        .company {
                            width: 186px;
                            font-size: 13px;
                            font-weight: 400;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                        }
                        .area{
                            background: url(../assets/rczhaopin/area.png) no-repeat left center/12px;
                            padding-left: 16px;
                            text-align:right;
                        }
                        .type {
                            width: 45px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            margin-right: 5px;
                        }
                        .nature {
                            width: 45px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            margin-left: 5px;
                        }
                    }

                }
                .no-job {
                    text-align: center;
                    margin: 50px 0;
                    font-size: 16px;
                }
            }
            .more-job {
                text-align: center;
                margin-top: 15px;
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                line-height: 21px;
                width: 268px;
                display: flex;
                justify-content: center;
                a {
                    display: inline-block;
                    max-width: 166px;
                    color: #FFA000;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;

                }
            }
        }
    }

    .position-container {
        width: $view-width;
        margin: 0 auto 16px;

        .position-content {
            width: $content-width;
            color: $font-color-basic;
        }
    }

    .footer-link-container {
        width: $view-width;
        margin: 0 auto;

        .footer-link-content {
            width: $content-width;
        }
    }
}
