@import './variables';

.qr-code-content {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    height: 130px;
    background: url(../assets/rczhaopin/qr-code-bg.png) no-repeat center / 100% 100%;

    .qr-code {
        flex: none;
        width: 80px;
        margin-right: 15px;
        border-radius: 10px;
        overflow: hidden;
    }

    .tips-title {
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: bold;
    }

    .scan {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        &::before {
            content: '';
            width: 6px;
            height: 6px;
            background: $color-primary;
            border-radius: 50%;
            margin-right: 6px;
        }

        span {
            margin-top: 8px;
            padding-left: 12px;
            width: 100%;
            color: $font-color-basic;
            font-size: 12px;
        }
    }
}
