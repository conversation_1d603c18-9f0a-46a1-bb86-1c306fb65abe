@import './variables';
@import './search';

$distance: 20px;
$item-width: 270px;
$item-height: 169px;
$logo-size: 60px;
$title-width: 210px;
$title-height: 39px;

.search-container {
    .search-main {
        margin-bottom: 0;

        .search-input {
            padding-left: 130px;
        }

        .search-options {
            right: auto;
            padding-right: 0;
            .el-input__suffix-inner {
                .search-type {
                    width: 105px;
                    .el-input__inner {
                        padding-left: 15px;
                    }
                }
            }
        }

        .search-hot {
            color: $font-color-tips;
            font-weight: 400;

            a {
                display: inline-block;
                height: 22px;
                line-height: 22px;
                padding: 0 7px;
                color: $font-color-label;
                border-radius: 4px;

                &:hover {
                    background: #daebff;
                }
            }
        }
    }
    .recommend-company {
        padding-top: 19px;
    }
}

.company-container {
    .company-content {
        margin: 0 auto;
        width: $view-width;
    }

    .result-list {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 0;
    }

    .result-item {
        position: relative;
        margin-right: $distance;
        margin-bottom: $distance + 10px;
        width: $item-width;
        height: $item-height;
        background: $color-white;
        border: 1px solid $border-color;
        border-radius: 10px;

        &:nth-of-type(4n) {
            margin-right: 0;
        }

        a {
            display: block;
            padding: $distance;
        }

        .company-data {
            display: flex;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #eaeaea;

            .logo {
                margin-right: $distance;
                width: $logo-size;
                height: $logo-size;
                object-fit: contain;
            }

            .data {
                display: flex;
                flex-direction: column;
                width: $title-width;

                h5 {
                    display: -webkit-box;
                    margin-bottom: 12px;
                    height: $title-height;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 1.45;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }

                span {
                    padding-left: 23px;
                    font-size: 14px;
                    color: $font-color-basic;
                    background-repeat: no-repeat;
                    background-size: 18px;
                    background-position: left center;
                    @include utils-ellipsis;

                    &.nature {
                        background-image: url(../assets/detail/nature.png);
                    }
                }
            }
        }

        .company-info {
            display: flex;
            margin: 0 auto;
            padding: 9px 0;
            height: 53px;

            span {
                flex: 1 0 auto;
                color: $font-color-label;
                text-align: center;

                & + span {
                    border-left: 1px solid rgba($color: $font-color-basic, $alpha: 0.1);
                }

                strong {
                    display: block;
                    margin-bottom: 9px;
                    font-size: 14px;
                    font-weight: bold;
                    color: $color-primary;
                }
            }
        }

        .collect-button {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            display: none;
            height: 35px;
            color: $color-white;
            font-size: 14px;
            text-align: center;
            vertical-align: middle;
            line-height: 35px;
            background-color: rgba($color: $font-color, $alpha: 0.8);
            border-radius: 0px 0px 10px 10px;
            cursor: pointer;

            &::before {
                content: '';
                display: inline-block;
                margin-right: 7px;
                width: $distance;
                height: $distance;
                vertical-align: middle;
                background: url(../assets/detail/collect-white.png) no-repeat center / contain;
            }

            &.is-collected::before {
                background-image: url(../assets/detail/collected-white.png);
            }

            span {
                display: inline-block;
                line-height: $distance;
                vertical-align: middle;
            }
        }

        &:hover {
            .collect-button {
                display: block;
            }
        }
    }
}
