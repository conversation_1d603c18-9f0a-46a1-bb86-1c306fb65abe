@import './variables';

$head-height: 68px;
$footer-height: 160px;
$max-width: 1200px;

.column-wrapper {
    height: inherit;
}

.bind-mobile-container {
    height: 100%;

    .header-container {
        background-color: #5c5c5c;
        color: #fff;

        nav {
            display: flex;
            width: $max-width;
            justify-content: space-between;
            align-items: center;
            height: $head-height;
            margin: 0 auto;
        }

        .header-logo {
            width: $logo-column-width;
            height: $logo-column-height;
            background: url($logo-column) no-repeat center/100% auto;
        }
    }

    .bind-container {
        height: calc(100% - $head-height - $footer-height);
        background-color: #efefef;
        padding: 30px 0;
        display: flex;
        justify-content: center;
        align-items: center;

        .bind-main {
            padding: 40px 100px 50px;
            border-radius: 10px;
            background-color: #fff;
            position: relative;

            .title {
                font-size: 20px;
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 40px;

                &::before {
                    content: '';
                    width: 4px;
                    height: 17px;
                    background: $color-primary;
                    border-radius: 2px;
                    margin-right: 12px;
                }
            }

            .back {
                position: absolute;
                right: 41px;
                top: 40px;
                color: $font-color-label;
                font-size: 14px;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    border-left: 2px solid rgba($color: $font-color, $alpha: 0.4);
                    border-bottom: 2px solid rgba($color: $font-color, $alpha: 0.4);
                    transform: rotate(45deg) scale(0.85);
                }

                &:hover {
                    color: $color-primary;

                    &::before {
                        border-color: $color-primary;
                    }
                }
            }

            .tips {
                font-size: 12px;
                color: $font-color-basic;
                line-height: 32px;
                padding: 0 14px 0 46px;
                border-radius: 4px;
                background: url('../assets/icon/warning.png') no-repeat 20px center/16px, #fff9e2;
                margin-bottom: 30px;
            }

            .bind-form {
                width: 300px;
                margin: 0 auto;

                .el-input--prefix {
                    .el-input__inner {
                        padding-left: 45px;
                    }
                }

                .mobile {
                    .el-input__inner {
                        padding-left: 85px;
                    }

                    .el-input__prefix {
                        width: 80px;
                        height: calc(100% - 4px);
                        overflow: hidden;
                        left: 2px;
                        top: 2px;
                    }

                    .mobile-prefix-select {
                        width: 77px;
                        color: #333;
                        margin: 0px;
                        display: flex;
                        background-color: #fff;

                        &::after {
                            display: inline-block;
                            content: '';
                            width: 1px;
                            height: 16px;
                            background-color: #ebebeb;
                            transform: translateY(11px);
                        }

                        .el-input__inner {
                            padding: 0 13px 0 14px;
                            color: $font-color;
                            border: none;
                            border-radius: 0;
                            height: 38px;
                        }

                        .el-input__suffix {
                            right: 0;
                            width: 13px;
                            display: flex;
                            align-items: center;
                            background: url('//img.gaoxiaojob.com/uploads/static/image/pc/column/assets/icon/down.png') no-repeat left/7px auto;

                            & > * {
                                display: none;
                            }
                        }
                    }
                }

                .sms-code {
                    .el-input__inner {
                        padding-right: 140px;
                    }

                    .el-input__prefix {
                        left: 0;
                        width: 45px;
                        background: url('../assets/icon/sms.png') no-repeat 12px center/24px;
                    }

                    .el-input__suffix {
                        right: 0;
                    }

                    .el-button {
                        border: none;
                        padding: 0 12px;
                        background: initial;
                        position: relative;
                        color: $color-primary;
                        text-align: center;

                        span {
                            color: $font-color-label;
                        }

                        &::before {
                            content: '';
                            display: block;
                            width: 1px;
                            height: 16px;
                            background-color: #ebebeb;
                            position: absolute;
                            left: 0;
                            top: 12px;
                        }
                    }

                    .el-input__clear {
                        position: absolute;
                        right: calc(100% + 8px);
                    }
                }

                .bind-mobile-confirm {
                    min-height: 44px;
                    width: 100%;
                    font-weight: bold;
                }
            }
        }
    }

    .footer-container {
        background-color: #fff;

        .footer-main {
            width: $max-width;
            height: $footer-height;
            margin: 0 auto;
            background: url('../assets/bindMobile/footer-bg.png') no-repeat left top/cover;
        }
    }
}

.el-popper.bind-mobile-prefix-popper {
    transform: translateX(-3px);

    .el-select-group__title {
        color: $font-color-label;
        font-size: 14px;
    }

    .el-select-dropdown__item {
        --el-text-color-regular: $font-color;
        font-size: 14px;
    }
}
