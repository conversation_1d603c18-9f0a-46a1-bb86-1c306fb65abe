@import './variablesActivity';

[v-cloak] {
    display: none;
}

html {
    height: 100%;
    box-sizing: border-box;
    font-size: 14px;
}

body {
    min-width: $view-width;
    height: 100%;
    color: $font-color;
    font-size: 12px;
    font-family: 'Microsoft YaHei', 'Hiragino Sans GB', tahoma, arial;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background: #f9f9f9;
}

::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 5px;

    &:hover {
        background: #7c7c7c;
    }
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
    margin: 0;
    padding: 0;
    font-size: 12px;
}

ul {
    list-style: none;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

a:hover {
    color: $color-primary;
}

div:focus {
    outline: none;
}

label {
    font-weight: 700;
}

button {
    cursor: pointer;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

$font-sizes: 12 14 16 18 20;

@each $size in $font-sizes {
    .ft#{$size} {
        font-size: #{$size}px;
    }
}

.block {
    display: block !important;
}

.pointer {
    cursor: pointer;
}

.clearfix {
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: ' ';
        clear: both;
        height: 0;
    }
}

$font-colors: (
    default: $font-color,
    primary: $color-primary,
    basic: $font-color-basic,
    salary: $font-color-salary,
    label: $font-color-label,
    tips: $font-color-tips
);

@each $label, $value in $font-colors {
    .color-#{$label} {
        color: $value;
    }
}

.flex {
    display: flex;
}

.flex-1 {
    flex: 1;
}

.inline-block {
    display: inline-block;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.none-select {
    user-select: none;
}

.title-primary {
    padding-left: 12px;
    border-left: 4px solid $color-primary;
    border-radius: 2px;
}

.activity-container {
    --el-color-primary: #{$color-primary};

    font-size: 14px;
    background-color: $background-primary;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    ul {
        font-size: 14px;
    }

    img:not(.rich-img) {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 0;
        object-fit: cover;
    }
}

body {
    [v-cloak] {
        display: none !important;
    }

    * {
        box-sizing: border-box;
    }

    .el-form-item {
        margin-bottom: 30px;
    }

    .el-checkbox {
        color: $font-color;
    }

    .el-input-group__prepend,
    .el-input-group__append {
        background-color: inherit;
    }

    .el-form-item__label {
        color: $font-color-basic;
    }

    .el-form-item__content {
        line-height: initial;
    }

    .el-form-item__error--inline {
        margin-left: 0;
    }

    .el-select,
    .el-cascader,
    .el-range-editor {
        width: 100%;
    }

    .el-cascader-panel {
        .el-cascader-menu {
            .el-cascader-node {
                position: relative;

                .el-radio {
                    position: absolute;
                    left: 20px;
                    right: 30px;
                    opacity: 0;
                }
            }

            &:last-child {
                .el-cascader-node {
                    .el-radio {
                        right: 20px;
                    }
                }
            }
        }
    }

    .el-overlay-dialog {
        display: flex;

        .dialog-center {
            align-self: center !important;
            margin-top: 0;
        }
    }

    .el-dialog {
        border-radius: 10px;

        .el-dialog__headerbtn {
            line-height: 30px;

            &:focus,
            &:hover {
                .el-dialog__close {
                    color: $color-white;
                }
            }
        }

        .el-dialog__footer {
            .el-button--default {
                color: $font-color-tips;

                &:hover {
                    color: $color-primary;
                }
            }
        }
    }

    .el-dialog__headerbtn,
    .el-message-box__headerbtn {
        top: -18px;
        right: -42px;
        width: 30px;
        height: 30px;
        background: $font-color;
        border-radius: 50%;
        z-index: inherit;

        i.el-dialog__close,
        i.el-message-box__close {
            color: $color-white;
            font-size: 16px;
            font-weight: bold;
        }
    }

    .el-pagination {
        padding: 0;
        text-align: center;

        .el-input__icon {
            height: initial;
        }

        &.is-background {
            .btn-prev,
            .btn-next,
            .el-pager li {
                background-color: $color-white;
            }

            .el-pager {
                li:not(.disabled).active {
                    background-color: $color-primary;
                }
            }
        }
    }

    .el-message {
        min-width: auto;
    }

    .el-message-box {
        padding: 0;
        width: 560px;
        border-radius: 10px;

        .el-message-box__header {
            margin-bottom: 18px;
            padding-top: 40px;
            font-size: 20px;
            text-align: center;
        }

        .el-message-box__content {
            margin-bottom: 30px;
            padding: 0 120px;
            color: $font-color-basic;
            text-align: center;
        }

        .el-message-box__message {
            p {
                font-size: inherit;
            }
        }

        .el-message-box__btns {
            margin-bottom: 60px;
            text-align: center;

            .el-button {
                width: 160px;

                &.el-button--default {
                    color: $font-color-tips;

                    &:hover {
                        color: $color-primary;
                    }
                }

                &.el-button--primary {
                    color: $color-white;

                    &:hover {
                        color: $color-white;
                    }
                }

                & + .el-button {
                    margin-left: 40px;
                }
            }
        }
    }

    .el-message__icon {
        font-size: 16px;
    }
}

// 头部样式
.activity-header-container {
    display: flex;
    align-items: center;
    height: 68px;
    background-color: $color-primary;
    color: $color-white;
    position: sticky;
    z-index: $basic-index;
    top: 0px;
    font-size: 16px;
    box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.1);

    .activity-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: $view-width;
        margin: 0 auto;

        .activity-menu-content {
            display: flex;
            align-items: center;

            .logo {
                width: 120px;
                height: 30px;
                background: url(//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png) no-repeat left/100% auto;
                margin-right: 30px;
            }

            .activity-menu-item {
                position: relative;
                font-weight: bold;
                margin-right: 40px;
                padding: 6px 0;

                &::after {
                    content: '';
                    width: 0;
                    height: 2px;
                    background-color: $color-white;
                    border-radius: 2px;
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    bottom: -4px;
                    transition: all ease 0.1s;
                }

                &:hover {
                    color: $color-white;

                    &::after {
                        width: 50px;
                    }
                }

                &.active {
                    &::after {
                        width: 50px;
                    }
                }
            }
        }

        .activity-nav-content {
            display: flex;
            align-items: center;

            .activity-nav-item {
                padding: 10px 13px;
                display: flex;
                align-items: center;
                position: relative;
                font-weight: bold;

                &:hover {
                    color: $color-white;
                }
            }

            .activity-login-btn {
                padding: 0 20px;
                margin-left: 30px;
                border: 0;
                border-radius: 32px;
                line-height: 32px;
                background-color: $color-white;
                color: $color-primary;
                font-weight: bold;
            }

            .person-avatar {
                width: 36px;
                height: 36px;
                margin-left: 30px;
                border-radius: 18px;
                border: 1px solid #f3aaab;
                overflow: hidden;

                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }
    }
}

// 左侧楼层锚点
.fixed-tool {
    position: fixed;
    top: 50%;
    left: calc(50% - 743px);
    width: auto;
    transform: translateY(-50%);
    z-index: 99;

    &::after {
        content: '';
        position: absolute;
        display: block;
        width: 55px;
        height: 50px;
        top: -50px;
        left: 9px;
        background: url(../assets/activity/common/nav-icon.png) no-repeat center top/55px 50px;
    }

    ul {
        border-radius: 8px;
        box-shadow: $box-shadow-default;
        background-color: $color-white;

        li {
            padding: 9px 10px;
            text-align: center;
            font-size: 12px;
            color: $font-color;
            border-bottom: 1px solid #ebebeb;
            cursor: pointer;

            &:last-child {
                border-bottom: none;
            }
        }
    }

    .active {
        color: $color-primary;
        font-weight: bold;
        position: relative;

        &::before {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            top: 8px;
            width: 4px;
            height: 16px;
            background: $color-primary;
            border-radius: 2px;
        }
    }

    // 省区，二级栏目侧边导航栏
    .addition {
        li {
            padding: 9px 23px;
        }
    }
}

// el 分页样式
.el-pagination {
    &.is-background {
        .el-pager {
            li {
                &:not(.disabled):hover {
                    color: $color-primary;
                }
            }
        }
    }
}

// 侧边报名入口
@import './activity/apply-enter';

// 侧边栏
@import './feedback';

// 底部
@import './common/footer';
