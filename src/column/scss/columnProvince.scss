// 栏目页省区
@import './variables';

@import './common/headlines-adv';
@import './common/commonTitle';
@import './common/fixedTool';
@import './common/sideBar';

@import './common/currentLocation';
@import './common/component/propaganda';
@import './common/component/rangeD';
@import './common/component/rangeL';
@import './common/component/rangeH';
@import './common/component/link';
@import './common/component/job';
@import './common/component/banner';

$border-color: #efefef;
$color-bg: #f4f9ff;

.column-container {
    .pagetop-left {
        display: flex;

        .choice-content {
            display: flex;
            align-items: center;
        }

        .logo {
            display: inline-block;
            margin-right: 6px;
            width: 108px;
            height: 30px;
            background: url($logo-home) no-repeat center center / contain;
        }

        .switch {
            padding-right: 29px;
            font-size: 12px;
            color: $font-color-basic;
        }
    }
}

.hot-region {
    ul {
        flex-wrap: wrap;
        padding: 20px 20px 0 20px;
        border: 1px solid $border-color;

        li {
            margin: 0 9px 20px 0;
            width: 58px;
            height: 27px;
            background-color: $color-bg;
            border-radius: 4px;
            text-align: center;
            line-height: 27px;

            &:nth-child(3n) {
                margin-right: 0;
            }
        }
    }
}
