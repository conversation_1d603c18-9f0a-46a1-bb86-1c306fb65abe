@use 'sass:meta';
@import './variables';
@import './common/component/detailShare';
@import './mini-code-popup';

$color-main: #2a6bff;
$default-radius: 8px;

body {
    .el-main {
        padding-bottom: 0;
        background-color: $color-white;
    }
}

.detail-container {
    background-color: #e3f0fb;

    .bold {
        font-weight: bold;
    }

    @import './notice/headerTemplateThree';

    .detail-main {
        margin: 0 auto;
        width: $view-width;

        .info-wrapper {
            font-size: 16px;

            .item {
                line-height: 32px;
                padding-left: 26px;

                .primary {
                    color: $color-main;
                }
            }

            $icons: time, info, welfare;
            @each $icon in $icons {
                .#{$icon} {
                    background: url(../assets/notice/three/#{$icon}.png) no-repeat left 6px/20px 20px;
                }

                @if ($icon == 'info') {
                    .#{$icon} {
                        background: url(../assets/notice/two/#{$icon}.png) no-repeat left 8px/20px 20px;
                    }
                }
            }

            .info {
                .primary {
                    font-size: 20px;
                    font-weight: bold;
                }
            }
        }

        .common-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 30px;

            .common-title {
                max-width: 884px;
                padding-bottom: 12px;
                margin-bottom: 14px;
                background: url(../assets/notice/three/wrapper-title-bg.png) no-repeat right bottom/118px 15px;
                padding-right: 20px;

                &-content {
                    $skewX: 30deg;

                    position: relative;
                    padding: 5px 22px;
                    background: linear-gradient(90deg, #2a6bff, #25c9e6);
                    transform: skewX(-15deg);
                    transform-origin: bottom;

                    &::before {
                        display: block;
                        position: absolute;
                        content: '';
                        left: -12px;
                        bottom: 0;
                        width: 5px;
                        background-color: #25c9e6;
                        height: 34px;
                    }
                }

                h2 {
                    transform: skewX(15deg);
                    transform-origin: center;
                    font-size: 20px;
                    font-weight: bold;
                    color: $color-white;
                    line-height: 30px;
                }
            }

            .common-content {
                padding: 24px;
                position: relative;
                overflow: hidden;
                white-space: wrap;
                font-size: 15px;
                width: 100%;
                border-radius: 12px;
                background: url(../assets/notice/three/wrapper-bg.png) no-repeat right bottom/362px 89px, $color-white;

                @import './notice/richReset';
            }
        }

        @import './common/jobFileList';
        @include meta.load-css('./common/detailTable');

        .file-wrapper {
            margin-bottom: 30px;

            .common-content {
                padding: 30px 20px 14px;
                margin-top: 0;

                .file {
                    width: calc((100% - 40px) / 3);

                    &:nth-of-type(2n) {
                        margin-left: 0;
                    }

                    &:nth-of-type(3n - 1) {
                        margin-left: 12px;
                        margin-right: 12px;
                    }
                }
            }
        }

        .notice-heat {
            background-color: $color-white;
            border-radius: 10px;
            padding: 17px 40px 26px;
            margin-top: 30px;
            cursor: pointer;
            margin-bottom: 40px;

            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;

                .left {
                    background: url(../assets/notice/heat.png) no-repeat left center/38px;
                    line-height: 40px;
                    padding-left: 45px;
                    font-size: 18px;
                    font-weight: bold;
                }

                .right {
                    cursor: pointer;
                    padding-right: 10px;
                    font-size: 14px;
                    color: $color-main;
                    background: url(../assets/notice//three/arrow-primary.png) no-repeat right center/6px 9px;
                }
            }

            .text-heat {
                font-size: 14px;

                span {
                    font-size: 16px;
                    color: $color-main;
                    font-weight: bold;
                }

                .active {
                    color: transparent;
                    text-shadow: #4e6ef2 0 0 10px;
                }
            }
        }

        .detail-emit {
            display: flex;
            justify-content: center;
            padding-bottom: 40px;
            border-bottom: 1px solid #bdccf6;

            .el-button {
                border: none;

                &:hover {
                    border: none;
                }
            }

            .view-job {
                background-color: transparent;
                border: 1px solid $color-main;
                color: $color-main;
                margin-right: 30px;
                padding: 12px 20px;
                border-radius: 4px;
                font-size: 14px;
            }

            .el-button--apply {
                background: linear-gradient(90deg, #2a6bff, #25c9e6);
                color: #fff;
            }

            .offline {
                cursor: not-allowed;
                color: #fff;
                background-color: rgba($color-main, 0.4);
            }
        }
    }
}

.el-popper {
    display: flex;
    padding: 6px 8px !important;
    flex-wrap: wrap;

    .boon {
        display: inline-block;
        padding: 3px 8px;
        margin: 6px 4px;
        font-size: 12px;
        font-weight: 400;
        border-radius: 4px;
        color: #6893f1;
        background-color: #ecf5ff;
    }
}
