@use './variables' as *;

$margin-primary: 15px;
$padding-primary: 140px;
$height-primary: 102px;
$close-primary: 24px;

.login-panel-template {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 41%, rgba(255, 154, 65, 0.92) 41%);
    z-index: 99;

    .login-panel-container {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        margin: 0 auto;
        padding-top: 40px;
        width: $view-width;
        height: $height-primary;
        background: url('../assets/footer/monkey.png') no-repeat left bottom/128px, url('../assets/footer/plus.png') no-repeat 160px 60px/430px;
    }

    .el-form {
        display: flex;
        align-items: center;

        .el-form-item {
            margin-bottom: 0;
            width: 190px;
            height: 32px;

            & + .el-form-item {
                margin-left: $margin-primary;
            }
        }

        .el-input {
            background-color: $color-white;
            border-radius: 32px;
            line-height: 0;
            overflow: hidden;

            input {
                line-height: 30px;
                height: 32px;
                border: none;
            }
        }

        .el-input-group__append {
            border: none;
        }

        .mobile {
            .el-input__inner {
                padding: 0 5px 0 10px;
            }
            .el-input-group__prepend {
                padding: 0;
                border: none;
                position: relative;

                &::after {
                    display: inline-block;
                    content: '';
                    width: 1px;
                    height: 20px;
                    position: absolute;
                    top: 6px;
                    background-color: #ebebeb;
                }
            }

            .mobile-prefix-select {
                width: 68px;
                color: #333;
                margin: 0px;

                .el-input__inner {
                    padding: 0 13px 0 5px;
                    color: $font-color;
                }

                .el-input__suffix {
                    right: 0;
                    width: 13px;
                    display: flex;
                    align-items: center;
                    background: url('//img.gaoxiaojob.com/uploads/static/image/pc/column/assets/icon/down.png') no-repeat 2px center/7px auto;

                    & > * {
                        display: none;
                    }
                }
            }

            .el-input__prefix {
                left: 0;
                width: 30px;
                background: url('../assets/icon/phone.png') no-repeat center/16px;
            }
        }

        .send-code {
            height: 32px;
            min-height: 32px;
            border: none;
            color: $color-primary;
            position: relative;
            padding: 0 10px;

            &::before {
                position: absolute;
                content: '';
                display: inline-block;
                background-color: #d8d8d8;
                width: 1px;
                height: 20px;
                top: 6px;
                left: 0;
            }
        }

        .mobile-login-confirm {
            margin-left: $margin-primary;
            min-height: 30px;
            height: 30px;
            border-radius: 30px;
            border: 1px solid $color-white;
            background-color: transparent;
            color: $color-white;
            padding: 0;
            width: 100px;
        }
    }

    .close-button {
        margin-left: 15px;
        padding: 0;
        width: $close-primary;
        height: $close-primary;
        min-height: $close-primary;
        background: transparent;
        border: 1px solid $color-white;
        border-radius: 50%;

        .el-icon {
            color: $color-white;
        }
    }
}

.el-popper.panel-mobile-prefix-popper {
    .el-select-group__title {
        color: $font-color-label;
        font-size: 14px;
    }

    .el-select-dropdown__item {
        --el-text-color-regular: $font-color;
        font-size: 14px;
    }
}
