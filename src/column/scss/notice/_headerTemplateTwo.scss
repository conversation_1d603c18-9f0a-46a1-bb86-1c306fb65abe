// 公告高级模板二头部
@import '../variables';

$color-main: #4957a1;

.detail-header {
    position: relative;
    margin-bottom: 39px;
    color: $color-white;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    background-image: url(https://img.gaoxiaojob.com/uploads/announcement/company-bg.png);

    &::before {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(#181e3f, 0.3);
        z-index: 1;
    }

    .wave {
        position: relative;
        height: 79px;

        &::after {
            display: block;
            position: absolute;
            width: 100%;
            height: 100%;
            content: '';
            z-index: 2;
            bottom: -15px;
            background: url(../assets/notice/two/wave-bg.png) no-repeat center/auto 100%;
        }
    }

    &.is-fixed {
        &::before {
            background-color: rgba(#181e3f, 0.3);
        }
    }

    .share-mini-code-container {
        margin-right: 30px;
    }

    .el-button--collect {
        cursor: pointer;
        font-size: 14px;
        background-color: transparent;
        border-color: $color-primary;
        display: inline-block;
        background: url(../assets/notice/collect.png) no-repeat left center / 20px;
        padding: 10px 0 10px 25px;
        margin-right: 30px;

        &.collected {
            background-image: url(../assets/notice/collected.png);
        }
    }
    .el-button--analyse {
        cursor: pointer;
        font-size: 14px;
        display: inline-block;
        background: url(../assets/notice/white-analyse.png) no-repeat left center / 20px;
        padding: 10px 0 10px 25px;
    }

    .company-home {
        padding: 0 39px;
        line-height: 40px;
        border: none;
        color: $color-white;
        background-color: rgba(#f2f2f2, 0.2);
        border: 1px solid $color-white;
        font-size: 16px;
        border-radius: 8px;
        font-weight: bold;
    }

    .view-button {
        padding: 0 19px;
        margin-right: 30px;
        height: 40px;
        line-height: 40px;
        border-radius: 20px;
        color: $color-white;
        font-size: 16px;
        padding-right: 45px;
        border: 1px solid $color-white;
        background: url(../assets/detail/view-button-arrow.png) no-repeat right 20px center / 20px 11px, linear-gradient(90deg, #4159d8, #5cb5f4);
    }

    .detail-header-container {
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        width: $view-width;
        padding-top: 9px;

        .breadcrumb {
            color: rgba($color: $color-white, $alpha: 0.6);
            box-shadow: 0 1px 0 0 rgba($color-white, 0.4);
        }

        .main {
            position: relative;
            width: $view-width;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 68px auto 64px;
            font-size: 14px;

            .detail {
                .title {
                    max-width: 928px;
                    font-weight: bold;
                    color: #f8f9ff;
                    margin-bottom: 19px;
                    text-shadow: 0px 7px 4px rgba(67, 77, 127, 1);
                    @include utils-ellipsis-lines(2, 48px, 34px);
                }
            }
        }

        &--fixed {
            width: $view-width;
            margin: 0 auto;
            padding: 35px 0 23px;
            display: flex;
            justify-content: space-between;

            .detail {
                width: 845px;
            }

            .title {
                width: 100%;
                font-weight: bold;
                max-width: initial;
                margin-bottom: 16px;
                @include utils-ellipsis-lines(1, 1, 30px);
            }

            .detail {
                .bottom {
                    display: flex;
                    font-size: 18px;
                    align-items: center;

                    .tag {
                        font-size: 12px;
                        color: #415ad8;
                        padding: 0 8px;
                        line-height: 20px;
                        border-radius: 4px;
                        margin-right: 8px;
                        background-color: $color-white;
                    }

                    .view-button {
                        position: static;
                        font-weight: bold;
                        margin-left: 15px;
                        line-height: 32px;
                        height: 32px;
                        padding: 0 27px 0 12px;
                        min-height: 32px;
                        border: 1px solid $color-white;
                        background: url(../assets/notice/two/arrow.png) no-repeat right 12px center / 8px 8px, linear-gradient(90deg, #4159d8, #5cb5f4);
                    }
                }
            }

            .aside {
                display: flex;
                flex-direction: column;
                align-items: flex-end;

                .detail-button {
                    padding: 0 7px;
                    display: flex;

                    & > div {
                        align-self: center;

                        & + div {
                            margin-left: 30px;
                        }
                    }

                    .el-button--collect {
                        cursor: pointer;
                        margin-right: 0px;
                        margin-left: 0;
                    }
                }

                .bottom {
                    margin-top: 2px;
                    display: flex;

                    .offline {
                        padding: 0 16px;
                        height: 40px;
                        line-height: 40px;
                        background: rgba($color-white, 0.2);
                        border-radius: 4px;
                        color: $color-white;
                        font-size: 16px;
                        border: none;
                        font-weight: bold;
                    }

                    .company-home {
                        color: #415ad8;
                        background-color: $color-white;
                        padding: 0 55px;
                        border-radius: 4px;
                        margin-left: 16px;
                    }
                }
            }
        }
    }

    .detail-header-container,
    .detail-header-container--fixed {
        position: relative;
        z-index: 99;
    }

    .detail-header-container--fixed {
        display: none;
    }

    &.is-fixed {
        @include utils-fixed;
        @include utils-detail-header-fixed;
        top: $header-height;
        box-shadow: 0 1px 10px 4px rgba(51, 51, 51, 0.2);

        .detail-header-container {
            display: none;
        }

        .wave {
            display: none;
        }

        .detail-header-container--fixed {
            display: flex;
        }
    }
}
