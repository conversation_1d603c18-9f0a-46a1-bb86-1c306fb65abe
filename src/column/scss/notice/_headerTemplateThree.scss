// 公告高级模板三头部
@import '../variables';

$color-main: #2a6bff;

.detail-header {
    position: relative;
    margin-bottom: 57px;
    background: url(../assets/notice/three/default-bg.png) no-repeat bottom center / cover;

    .color-main {
        color: $color-main;
    }

    .title {
        max-width: 684px;
        margin-bottom: 13px;
        @include utils-ellipsis-lines(2, 54px, 36px);
    }

    .share-mini-code-container {
        margin-right: 20px;
    }

    .el-button--collect {
        cursor: pointer;
        font-size: 14px;
        background-color: transparent;
        border-color: $color-primary;
        display: inline-block;
        background: url(../assets/notice/three/collect.png) no-repeat left center / 20px;
        padding: 10px 0 10px 25px;
        margin-right: 20px;

        &.collected {
            background-image: url(../assets/notice/three/collected.png);
        }
    }

    .el-button--analyse {
        cursor: pointer;
        font-size: 14px;
        display: inline-block;
        margin-right: 20px;
        background: url(../assets/notice/three/analyse.png) no-repeat left center / 20px;
        padding: 10px 0 10px 22px;
    }

    .detail-header-container {
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        width: $view-width;
        padding-top: 9px;

        .breadcrumb {
            color: rgba($color: $font-color, $alpha: 0.4);
            box-shadow: 0 1px 0 0 rgba($font-color, 0.2);
        }

        .main {
            display: flex;
            flex-grow: 1;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            padding-top: 25px;
            padding-bottom: 30px;
            position: relative;

            .detail {
                .job-info {
                    display: flex;
                    align-items: center;
                    line-height: 1.01;
                    margin-bottom: 19px;

                    .color-main {
                        font-weight: bold;
                    }

                    .amount {
                        flex-shrink: 0;
                    }

                    .other {
                        display: flex;
                        margin-left: 20px;
                        color: $font-color-basic;

                        .time {
                            flex-shrink: 0;
                        }

                        .address {
                            flex-grow: 1;
                            padding-left: 18px;
                            margin-left: 5px;
                            background: url(../assets/icon/address.png) no-repeat left/12px 12px;
                            @include utils-ellipsis;
                        }
                    }
                }

                .welfare-tag {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;

                    span {
                        margin: 0;

                        & + span {
                            margin-left: 10px;
                        }
                    }

                    .boon {
                        color: #6893f1;
                        padding: 4px 7px;
                        font-size: 12px;
                        line-height: 1;
                        background-color: rgba($color-white, 0.4);
                        @include utils-radius;

                        &.establishment-tag {
                            background-color: $color-white;
                            color: $color-main;
                        }
                    }

                    .boon-more {
                        margin-left: 10px;
                        padding: 1px 7px;
                        width: auto;
                        font-size: 18px;
                        color: #6893f1;
                        background-color: rgba($color-white, 0.2);
                        cursor: pointer;
                        @include utils-radius;
                    }
                }

                .detail-bottom {
                    display: flex;
                    align-items: center;
                    color: $color-main;

                    .company-home {
                        cursor: pointer;
                        font-size: 14px;
                        background-color: transparent;
                        border-color: #ffa000;
                        display: inline-block;
                        background: url(../assets/notice/three/home.png) no-repeat left center / 20px;
                        padding: 10px 0 10px 22px;
                        margin-right: 20px;
                    }
                }
            }

            .cover {
                width: 459px;
                height: 298px;
                background-color: #5c85f8;
                border-radius: 12px;
                margin-left: 55px;

                img {
                    width: 460px;
                    height: 276px;
                    border-radius: 12px 12px 0 12px;
                    object-fit: cover;
                    transform: translate(29px, 22px);
                }
            }

            .view-job {
                position: absolute;
                left: 0;
                bottom: -20px;
                font-size: 16px;
                font-weight: bold;
                padding: 0px 20px;
                line-height: 40px;
                color: $color-white;
                border-radius: 40px;
                background: linear-gradient(90deg, #2a6bff, #25c9e6);
            }
        }

        &--fixed {
            width: $view-width;
            margin: 0 auto;
            padding: 34px 0 23px;
            display: flex;
            justify-content: space-between;

            .title {
                width: 100%;
                font-weight: bold;
                max-width: initial;
                margin-bottom: 16px;
                @include utils-ellipsis-lines(1, 1, 30px);
            }

            .detail {
                width: 845px;

                .bottom {
                    display: flex;
                    font-size: 18px;
                    align-items: center;

                    .tag {
                        font-size: 12px;
                        color: $color-main;
                        padding: 0 8px;
                        line-height: 20px;
                        border-radius: 4px;
                        margin-right: 8px;
                        background-color: $color-white;
                    }

                    span {
                        color: $color-main;
                    }

                    .view-button {
                        font-size: 18px;
                        font-weight: bold;
                        color: $color-main;
                        padding-right: 17px;
                        margin-left: 16px;
                        background: url(../assets/notice/three/arrow.png) no-repeat right/8px 8px;
                    }
                }
            }

            .aside {
                display: flex;
                flex-direction: column;
                align-items: flex-end;

                .detail-button {
                    display: flex;
                    padding: 0 7px;

                    & > div {
                        align-self: center;

                        & + div {
                            margin-left: 30px;
                        }
                    }

                    .el-button--collect {
                        cursor: pointer;
                        margin-right: 0px;
                        margin-left: 0;
                    }

                    .el-button--analyse {
                        margin-right: 0;
                    }
                }

                .bottom {
                    margin-top: 2px;
                    display: flex;

                    .offline {
                        padding: 0 16px;
                        height: 40px;
                        line-height: 40px;
                        background-color: #f4f4f5;
                        border-radius: 4px;
                        color: #c0c4cc;
                        font-size: 16px;
                        border: none;
                    }

                    .company-home {
                        padding: 0 55px;
                        border-radius: 4px;
                        margin-left: 16px;
                        background: linear-gradient(90deg, #2a6bff, #25c9e6);
                        line-height: 40px;
                        border-radius: 4px;
                        font-size: 16px;
                        color: $color-white;
                    }
                }
            }
        }
    }

    .detail-header-container,
    .detail-header-container--fixed {
        position: relative;
        z-index: 99;
    }

    .detail-header-container--fixed {
        display: none;
    }

    &.is-fixed {
        @include utils-fixed;
        @include utils-detail-header-fixed;
        top: $header-height;
        box-shadow: 0 1px 10px 4px rgba(51, 51, 51, 0.2);

        .detail-header-container {
            display: none;
        }

        .detail-header-container--fixed {
            display: flex;
        }
    }
}
