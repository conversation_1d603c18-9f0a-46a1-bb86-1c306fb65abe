// 公告高级模板一头部
@import '../variables';

.detail-header {
    margin-bottom: 36px;
    background: url(../assets/detail/detail-header.jpg) no-repeat center / cover;

    .detail-header-container {
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        width: $view-width;
        height: 100%;

        .breadcrumb {
            color: rgba($color: $font-color, $alpha: 0.4);
            box-shadow: 0 1px 0 0 rgba($color: $font-color, $alpha: 0.2);
        }

        .main {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: auto;
            padding: 24px 0 40px;

            section {
                padding: 0;
                width: 845px;
                color: $font-color;
                font-size: 14px;

                .title {
                    margin-bottom: 20px;

                    h1 {
                        margin: 0 20px 0 0;
                        font-weight: bold;
                        @include utils-ellipsis-lines(2, 30px, 22px);
                    }
                }

                .info {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                    font-weight: 400;
                    line-height: 1;
                    font-size: 14px;

                    ul {
                        margin-left: 20px;
                        display: flex;
                        font-size: inherit;

                        li {
                            & + li {
                                margin-left: 10px;
                                padding-left: 10px;
                                border-left: 1px solid $color-white;
                            }

                            &:last-of-type {
                                max-width: 400px;

                                @include utils-ellipsis;
                            }
                        }
                    }
                }

                .boons {
                    display: flex;
                    align-items: center;
                    font-size: 12px;

                    span {
                        & + span {
                            margin-left: 10px;
                            padding-left: 10px;
                            border-left: 1px solid $font-color-tips;
                        }

                        &.boon {
                            @include span-boon;
                        }
                    }

                    .boon-more {
                        margin-left: 10px;
                        color: #bcbcbc;
                        font-size: 18px;
                        cursor: pointer;
                    }
                }
            }

            aside {
                display: flex;
                flex-direction: column;
                align-items: flex-end;

                .detail-button {
                    display: flex;
                    justify-content: flex-end;
                    font-size: 14px;
                    cursor: pointer;

                    .share-mini-code-container {
                        align-self: center;
                        margin-right: 30px;
                    }

                    .el-button--collect {
                        background-color: transparent;
                        border-color: $color-primary;
                        display: inline-block;
                        padding-left: 25px;
                        background: url(../assets/detail/collect-white.png) no-repeat left center / 20px;
                        line-height: 38px;
                        margin-right: 30px;

                        &.collected {
                            background-image: url(../assets/detail/collected.png);
                        }
                    }
                    .el-button--analyse {
                        background-color: transparent;
                        border-color: $color-primary;
                        display: inline-block;
                        padding-left: 25px;
                        background: url(../assets/detail/analyse.png) no-repeat left center / 20px;
                        line-height: 38px;
                    }
                }

                .aside-bottom {
                    display: flex;

                    .company-home {
                        width: 175px;
                        border: none;
                    }

                    .offline {
                        padding: 0 16px;
                        height: 40px;
                        line-height: 40px;
                        background: rgba($color-white, 0.2);
                        border-radius: 4px;
                        color: $color-white;
                        font-size: 16px;
                        border: none;
                        font-weight: bold;
                        margin-right: 16px;
                    }
                }
            }

            .view-button {
                position: absolute;
                left: 0;
                padding: 0 20px;
                min-height: 35px;
                line-height: 35px;
                bottom: calc(-35px / 2);
                border-radius: calc(35px / 2);
                border: none;

                span {
                    padding-right: 25px;
                    background: url(../assets/detail/view-button-arrow.png) no-repeat right center / 20px 11px;
                }
            }
        }
    }

    .view-relation {
        display: none;
        margin-left: 20px;
        padding-right: 15px;
        color: $color-primary;
        font-weight: bold;
        background: url(../assets/detail/view-relation.png) no-repeat right center / 8px;
    }

    &.is-fixed {
        @include utils-fixed;
        @include utils-detail-header-plain-fixed;
        top: $header-height;

        .detail-header-container {
            .breadcrumb {
                display: none;
            }

            .main {
                margin-bottom: 0;
                padding-bottom: 24px;

                section {
                    .title {
                        h1 {
                            display: block;
                            @include utils-ellipsis();
                        }
                    }

                    .boons {
                        display: none;
                    }

                    .info {
                        margin-bottom: 0;
                    }
                }

                .view-button {
                    display: none;
                }
            }
        }

        .view-relation {
            display: inline-block;
        }
    }
}
