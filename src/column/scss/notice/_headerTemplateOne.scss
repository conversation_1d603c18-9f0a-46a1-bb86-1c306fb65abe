// 公告高级模板一头部
@import '../variables';

@mixin establishment {
    .establishment-tag {
        padding: 0 10px;
        line-height: 18px;
        border-radius: 4px;
        border: 1px solid #4fbc67;
        color: #4fbc67;
        font-size: 12px;

        &.superior {
            border-color: $color-white;
            background-color: $color-white;
        }
    }
}

.detail-header {
    background: url(https://img.gaoxiaojob.com/uploads/announcement/detail-header-1.png) no-repeat center / cover;
    margin-bottom: 66px;

    &.is-custom-background {
        position: relative;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: rgba(45, 67, 86, 0.5);
            z-index: 1;
        }

        .detail-header-container {
            position: relative;
            z-index: 99;
        }
    }

    &.is-fixed {
        @include utils-fixed;
        @include utils-detail-header-fixed;
        top: $header-height;

        background-image: url(../assets/notice/detail-header-fixed.png);

        .detail-header-container {
            .breadcrumb {
                display: none;
            }

            .main {
                section {
                    padding: 35px 0 30px;

                    .title {
                        @include utils-ellipsis-lines(1, 1, 30px);
                    }

                    .info {
                        margin-bottom: 0;
                        font-size: 18px;

                        ul {
                            display: none;
                        }

                        @include establishment;
                        .establishment-tag {
                            margin-right: 7px;
                            display: inline-block;
                        }
                    }

                    .tips {
                        display: none;
                    }
                }

                aside {
                    .aside-bottom {
                        .offline {
                            display: block;
                        }
                    }
                }

                .view-button {
                    display: none;
                }
            }
        }

        .view-relation {
            display: block;
        }
    }

    .detail-header-container {
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        width: $view-width;
        height: 100%;

        .breadcrumb {
            color: $border-color-bg;
            box-shadow: 0 1px 0 0 rgba(242, 242, 242, 0.4);
        }

        .main {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: auto;

            section {
                padding: 30px 0 60px;
                width: 845px;
                color: $color-white;
                font-size: 14px;

                .title {
                    margin-bottom: 20px;
                    max-width: 945px;
                    @include utils-ellipsis-lines(2, 1.5, 32px);
                }

                .info {
                    display: flex;
                    align-items: center;
                    margin-bottom: 20px;
                    font-weight: 400;
                    line-height: 1;
                    white-space: nowrap;

                    ul {
                        margin-left: 20px;
                        display: flex;
                        font-size: inherit;
                        flex-grow: 1;
                        overflow: hidden;

                        li {
                            & + li {
                                margin-left: 10px;
                                padding-left: 10px;
                                border-left: 1px solid $color-white;
                            }

                            &:last-of-type {
                                flex-grow: 1;
                                @include utils-ellipsis;
                            }
                        }
                    }

                    .establishment-tag {
                        display: none;
                    }
                }

                .tips {
                    display: flex;
                    align-items: center;

                    span {
                        margin: 0;

                        & + span {
                            margin-left: 10px;
                        }

                        &.boon {
                            padding: 4px 7px;
                            font-size: 12px;
                            line-height: 1;
                            background-color: $border-color-bg;
                            @include utils-radius;
                        }
                    }

                    .boon-more {
                        margin-left: 10px;
                        padding: 1px 7px;
                        width: auto;
                        font-size: 18px;
                        background-color: $border-color-bg;
                        cursor: pointer;
                        @include utils-radius;
                    }

                    @include establishment;
                }
            }

            aside {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                margin-bottom: 20px;

                .detail-button {
                    padding: 0 7px;
                    display: flex;
                    font-size: 14px;
                    color: #fffcea;

                    & > div {
                        align-self: center;

                        & + div {
                            margin-left: 30px;
                        }
                    }

                    .el-button--collect {
                        background-color: transparent;
                        border-color: $color-primary;
                        display: inline-block;
                        padding-left: 25px;
                        background: url(../assets/detail/collect-white.png) no-repeat left center / 20px;
                        line-height: 38px;
                        cursor: pointer;

                        &.collected {
                            background-image: url(../assets/detail/collected.png);
                        }
                    }
                    .el-button--analyse {
                        background-color: transparent;
                        border-color: $color-primary;
                        display: inline-block;
                        padding-left: 25px;
                        background: url(../assets/notice/white-analyse.png) no-repeat left center / 20px;
                        line-height: 38px;
                        cursor: pointer;
                    }
                }

                .aside-bottom {
                    display: flex;

                    .company-home {
                        width: 175px;
                        font-size: 16px;
                        border: none;
                    }

                    .offline {
                        display: none;
                        padding: 0 16px;
                        height: 40px;
                        line-height: 40px;
                        background: rgba($color-white, 0.2);
                        border-radius: 4px;
                        color: $color-white;
                        font-size: 16px;
                        border: none;
                        margin-right: 16px;
                    }
                }
            }

            .view-button {
                position: absolute;
                left: 0;
                padding: 0 20px;
                min-height: 35px;
                line-height: 35px;
                bottom: calc(-35px / 2);
                border-radius: calc(35px / 2);
                border: none;

                span {
                    padding-right: 25px;
                    background: url(../assets/detail/view-button-arrow.png) no-repeat right center / 20px 11px;
                }
            }
        }
    }

    .view-relation {
        display: none;
        margin-left: 20px;
        padding-right: 15px;
        color: $color-primary;
        font-weight: bold;
        background: url(../assets/detail/view-relation.png) no-repeat right center / 8px;
    }
}
