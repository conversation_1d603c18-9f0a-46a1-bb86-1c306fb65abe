@import './variablesActivity';

@media screen and (max-width: 1500px) {
    .fixed-tool {
        left: 10px;
    }
}

.el-popover {
    background: red;
    border-radius: 8px;

    &.el-popper {
        border-radius: 8px;
    }

    .scroll-box {
        padding-right: 6px;
        max-height: 305px;
        overflow-y: auto;

        &::-webkit-scrollbar {
            background-color: transparent;
        }
    }

    .pop-item {
        margin-top: 12px;

        &:first-child {
            margin-top: 6px;
        }

        .pop-tit {
            font-weight: bold;
            font-size: 14px;
            color: #333333;
            line-height: 21px;
        }

        .pop-time,
        .pop-address {
            display: flex;
            margin-top: 4px;
            .value {
                flex: 1;
            }
        }
    }
}

.swiper-button-next {
    width: 28px;
    height: 28px;
    background: url(../assets/activity/activityGather/next-swiper.png) no-repeat center/28px 28px;
    &::after {
        display: none;
    }
}

.swiper-button-prev {
    width: 28px;
    height: 28px;
    background: url(../assets/activity/activityGather/pre-swiper.png) no-repeat center/28px 28px;
    &::after {
        display: none;
    }
}

.swiper-pagination {
    .swiper-pagination-bullet {
        width: 27px;
        height: 4px;
        border-radius: 2px;
        background: $color-primary;

        &.swiper-pagination-bullet-active {
            background: $color-primary;
        }
    }
}

.banner-wrapper {
    min-height: 70px;
    position: relative;
    background-color: rgba($color: $color-white, $alpha: 1);

    // &::after {
    //     content: '';
    //     position: absolute;
    //     bottom: 0;
    //     left: 0;
    //     right: 0;
    //     height: 40px;
    //     background-color: rgba($color: #e0e8fd, $alpha: 0.7);
    // }

    img {
        // width: auto !important;
        // height: auto !important;
        object-fit: cover; /* 图片覆盖整个容器，可能会被裁剪 */
        object-position: center; /* 图片居中 */
    }

    .banner-swiper {
        position: relative;

        .swiper-pagination {
            bottom: 52px;
        }
    }
}

.main-container {
    background-color: #fff;

    .view-content {
        width: $view-width;
        margin: 0 auto;
    }

    .data-wrapper {
        width: 1112px;
        height: 93px;
        position: relative;
        top: -46px;
        z-index: 1;
        box-shadow: 0px 9px 15px 3px rgba(84, 136, 223, 0.1);
        border-radius: 12px;
        padding-left: 152px;
        margin: 0 auto;
        background: url(../assets/activity/activityGather/data-icon.png) no-repeat left center/204px 93px, #f7faff;
        ul {
            height: 100%;
            display: flex;
            align-items: center;
            li {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                .left {
                    width: 72px;
                    height: 72px;
                    img {
                    }
                }
                .right {
                    display: inline-block;
                    text-align: center;
                    .count {
                        font-weight: bold;
                        font-size: 28px;
                        color: #333333;
                        span {
                            font-size: 20px;
                            color: #333333;
                        }
                    }
                    p {
                        font-size: 16px;
                        color: rgba($color: #333, $alpha: 0.8);
                    }
                }
            }
        }
    }

    .hot-session-wrapper {
        padding: 0 0 40px;

        .hd {
            text-align: center;
            .title {
                img {
                    display: inline-block;
                    width: 218px;
                    height: 44px;
                    transform: scale(0.9);
                }
            }
            .text {
                margin-top: 6px;
                font-size: 16px;
                color: #333333;
                opacity: 0.8;
            }
        }
        .bd {
            margin-top: 24px;
            position: relative;

            &::after {
                position: absolute;
                right: 0;
                top: 0;
                z-index: 2;
                content: '';
                width: 20px;
                height: 100%;
                background: linear-gradient(90deg, transparent, #fff);
            }

            .swiper-button-prev {
                left: -38px;
                width: 28px;
                height: 28px;
                background: url(../assets/activity/activityGather/pre-swiper.png) no-repeat center/28px 28px;
                z-index: 1;

                &::after {
                    display: none;
                }
            }

            .swiper-button-next {
                right: -38px;
                width: 28px;
                height: 28px;
                background: url(../assets/activity/activityGather/next-swiper.png) no-repeat center/28px 28px;
                z-index: 1;

                &::after {
                    display: none;
                }
            }

            .hot-session-swiper {
                padding: 6px 10px 10px;
            }

            .swiper-v {
                position: absolute;
                left: 0;
                bottom: 0;
                height: 18px;
            }

            .session-list {
                width: 342px !important;
                height: 220px;
                background: #ffffff;
                box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                border-radius: 12px;
                border: 1px solid transparent;
                overflow: hidden;

                &:hover {
                    border: 1px solid #296aff;
                    transform: translateY(-5px);
                    transition: all 0.3s ease;
                    .button {
                        display: block;
                    }
                    .content {
                        .tit {
                            color: $color-primary;
                        }
                    }
                }

                .img-box {
                    position: relative;
                    width: 342px;
                    height: 140px;
                    .tag {
                        position: absolute;
                        top: 0;
                        left: 0;
                        height: 20px;
                        padding: 0 6px;
                        line-height: 20px;
                        background: #333333;
                        border-radius: 9px 0px 9px 0px;
                        opacity: 0.8;
                        font-size: 12px;
                        color: #ffffff;
                    }
                    .hot-tag {
                        display: inline-block;
                        max-width: 209px;
                        padding: 0 8px 0 20px;
                        line-height: 18px;
                        background: url(../assets/activity/common/hot.png) no-repeat 8px center/8px 9px, rgba($color: #333, $alpha: 0.6);
                        border-radius: 7px 9px 9px 0px;
                        font-size: 12px;
                        color: #ffffff;
                        @include utils-ellipsis();
                    }
                }

                .content {
                    padding: 0 18px;
                    .tit {
                        margin-top: 15px;
                        font-weight: bold;
                        font-size: 16px;
                        color: #333333;
                        @include utils-ellipsis();
                    }
                    .ft {
                        margin-top: 15px;
                        display: flex;
                        font-size: 14px;
                        color: rgba($color: #333, $alpha: 0.6);
                        .time {
                        }
                        .address {
                            flex: 1;
                            margin-left: 22px;
                            text-align: right;
                            @include utils-ellipsis();
                        }
                    }
                }

                .button {
                    display: none;
                    position: absolute;
                    right: 18px;
                    bottom: 10px;
                    width: 72px;
                    height: 24px;
                    text-align: center;
                    line-height: 24px;
                    font-size: 14px;
                    color: #ffffff;
                    background: linear-gradient(90deg, #296aff, #29a7ff);
                    border-radius: 12px;

                    &.disabled {
                        background: #d7d7d7;
                    }
                }
            }
        }
    }

    .activity-total-wrapper {
        padding: 40px 0;
        background: linear-gradient(90deg, #e9f3ff, #f3f8ff, #e3edff);
        .hd {
            text-align: center;
            .title {
                img {
                    display: inline-block;
                    width: 218px;
                    height: 44px;
                    transform: scale(0.9);
                }
            }
            .text {
                margin-top: 6px;
                font-size: 16px;
                color: #333333;
                opacity: 0.8;
            }
        }

        .session-swiper {
            width: 1056px;
            .session-list {
                width: 220px !important;
                background: #ffffff;
                overflow: hidden;

                &:hover {
                    .tit {
                        color: #296aff;
                    }
                }

                .img-box {
                    position: relative;
                    height: 90px;
                    border-radius: 10px;
                    overflow: hidden;
                }

                .tit {
                    padding: 0 6px;
                    text-align: center;
                    line-height: 38px;
                    font-size: 14px;
                    color: #333333;
                    @include utils-ellipsis();
                }
            }

            .swiper-button-prev {
                width: 40px;
                height: 90px;
                left: 0;
                top: 22px;
                pointer-events: auto;
                background: url(../assets/activity/activityGather/pre-swiper.png) no-repeat center/28px 28px, linear-gradient(to right, rgb(255, 255, 255, 1), rgb(255, 255, 255, 0));
                // margin-top: 0;
                z-index: 10;

                &::after {
                    display: none;
                }
            }

            .swiper-button-next {
                width: 40px;
                height: 90px;
                right: 0;
                top: 22px;
                pointer-events: auto;
                background: url(../assets/activity/activityGather/next-swiper.png) no-repeat center / 28px 28px, linear-gradient(to right, rgb(255, 255, 255, 0), rgb(255, 255, 255, 1));
                /* margin-top: 0; */
                z-index: 10;

                &::after {
                    display: none;
                }
            }
        }

        .filter-wrapper {
            margin-top: 39px;
            border-radius: 12px;
            background-color: rgba($color: $color-white, $alpha: 1);
            padding: 20px 20px 20px;

            #activityTime {
                .el-date-editor {
                    width: 224px;
                    height: 28px;
                    line-height: 28px;
                    margin-left: 10px;
                    border-radius: 4px;

                    &.is-selected {
                        color: $color-primary;
                        background: #e5edff;
                        .el-range-input {
                            background: #e5edff;
                        }
                        .el-range-input,
                        .el-range-separator,
                        .el-input__icon {
                            color: $color-primary;
                        }
                    }

                    .el-range-input {
                        padding-top: 3px;
                        font-size: 14px;
                        color: rgba($color: #333, $alpha: 0.6);
                        background-color: #f8f8f8;
                    }
                    .el-range-separator {
                        width: auto;
                        padding: 0 5px;
                    }
                    .el-input__icon {
                        width: 14px;
                    }
                }
            }

            .filter-list {
                display: flex;
                align-items: baseline;

                &.ts {
                    .label {
                        transform: translateY(-61px);
                    }
                }

                & + .filter-list {
                    margin-top: 5px;
                }

                .label {
                    flex-shrink: 0;
                    font-size: 16px;
                    font-weight: bold;
                    margin-right: 21px;
                    position: relative;
                    .pop {
                        position: absolute;
                        left: 0;
                        top: -34px;
                        width: 241px;
                        height: 23px;
                        line-height: 23px;
                        background: #e5edff;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 400;
                        color: #5c5c5c;
                        text-align: center;
                        &::before {
                            position: absolute;
                            bottom: -6px;
                            left: 26px;
                            // transform: translateX(-50%);
                            width: 0;
                            height: 0;
                            content: '';
                            border-left: 6px solid transparent;
                            border-right: 6px solid transparent;
                            border-top: 6px solid #e5edff; /* 设置三角形的颜色 */
                        }
                    }
                }

                .value {
                    flex-grow: 1;
                    display: flex;
                    flex-wrap: wrap;

                    .item {
                        margin-right: 6px;
                        margin-bottom: 8px;
                        padding: 0px 9px;
                        line-height: 26px;
                        font-size: 14px;
                        cursor: pointer;

                        &[checked='true'] {
                            border-radius: 4px;
                            color: $color-primary;
                            background: #e5edff;

                            &::after {
                                content: '';
                                display: inline-block;
                                margin-left: 3px;
                                width: 10px;
                                height: 10px;
                                background: url(../assets/activity/common/close-primary.png) no-repeat center / contain;
                            }
                        }

                        &.is-special {
                            &::after {
                                display: none;
                            }
                        }

                        &:hover {
                            color: $color-primary;
                        }
                    }

                    .el-select {
                        margin-right: 16px;
                        width: 110px;
                        overflow: hidden;

                        &:last-of-type {
                            margin-right: 0;
                        }

                        .el-select__tags {
                            display: none;
                        }
                    }

                    .filter-item {
                        &.is-checked {
                            width: 124px;

                            &:hover {
                                .el-input__suffix-inner {
                                    display: none;
                                }

                                .filter-major-clear {
                                    display: block;
                                }
                            }
                        }

                        .filter-major-clear {
                            display: none;
                        }
                    }

                    .company-type-item {
                        position: relative;
                        display: flex;
                        align-items: center;
                        background-color: #f8f8f8;

                        .el-select,
                        .el-input {
                            height: 100%;
                        }

                        .select-trigger {
                            height: 100%;
                            width: calc(100%);
                        }

                        &.is-select {
                            background-color: #e5edff;

                            .label {
                                color: $color-primary;
                            }

                            .el-select {
                                width: 124px;
                            }
                        }

                        input {
                            position: absolute;
                            left: 0;
                            right: 0;
                            height: 100%;
                            z-index: 1;
                            opacity: 0;
                        }

                        .el-input__suffix {
                            z-index: 2;
                        }

                        .label {
                            color: $font-color;
                            white-space: nowrap;
                            font-size: 14px;
                            font-weight: normal;
                            position: absolute;
                            left: 0;
                            right: 35px;
                            padding: 0 0 0 10px;
                            z-index: 0;
                        }
                    }

                    .el-input__inner {
                        @include utils-ellipsis;
                        padding-left: 10px;
                        color: $color-primary;
                        font-size: 14px;
                        background-color: #f8f8f8;
                        border: none;
                        box-sizing: border-box;

                        &::placeholder {
                            color: $font-color;
                        }
                    }

                    .is-checked {
                        .el-input__inner {
                            background-color: #e5edff;
                        }

                        .el-input__icon {
                            color: $color-primary;
                        }
                    }
                }

                .filter-aside {
                    flex-shrink: 0;

                    .clear-all {
                        padding-left: 20px;
                        background: url(../assets/activity/common/delete.png) no-repeat left/14px 14px;

                        &:hover {
                            color: #333;
                        }
                    }
                }
            }

            .tips {
                display: flex;
                align-items: center;
                justify-content: center;
                color: $color-primary;
                margin-top: 18px;

                &::before,
                &::after {
                    content: '';
                    width: 60px;
                    height: 1px;
                    background: #333333;
                    opacity: 0.5;
                    margin: 0 15px;
                }
            }
        }

        .activity-wrapper {
            padding-top: 20px;
            .company-content {
                display: grid;
                grid-template-columns: repeat(3, 386px);
                gap: 20px;
                .item {
                    padding: 16px 16px 0;
                    background: #ffffff;
                    border-radius: 12px;
                    border: 1px solid #f8faff;

                    &:hover {
                        border: 1px solid $color-primary;
                        box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                        transform: translateY(-5px);
                        transition: all 0.3s ease;

                        .hd {
                            .title {
                                color: $color-primary;
                            }
                        }

                        .bd {
                            .session {
                                .popover {
                                    background-color: #fff;
                                }
                            }
                        }
                    }

                    .hd {
                        display: block;
                        position: relative;
                        .tag {
                            position: absolute;
                            z-index: 1;
                            left: -1px;
                            top: -1px;
                            height: 20px;
                            line-height: 20px;
                            padding: 0 7px;
                            font-size: 12px;
                            border-radius: 8px 0px 8px 0px;

                            $font-colors: (1, #5386ff, #e5edff), (2, #ffa000, #fff3e0), (3, #51bd69, #f0ffdc), (4, #51bd69, #f0ffdc), (5, #fa857f, #fff4f3), (6, #786afb, #f4f3fc);

                            @each $index, $color, $bg in $font-colors {
                                &.tag#{$index} {
                                    color: $color;
                                    background: $bg;
                                }
                            }
                        }

                        .img-box {
                            position: relative;
                            height: 187px;
                            border-radius: 8px;
                            overflow: hidden;
                            .tags {
                                position: absolute;
                                padding-left: 14px;
                                bottom: 8px;
                                width: 100%;
                                height: 22px;
                                overflow: hidden;
                                text-align: left;
                                span {
                                    display: inline-block;
                                    margin-right: 6px;
                                    padding: 4px 6px;
                                    font-size: 12px;
                                    color: #ffffff;
                                    background: rgba($color: #333, $alpha: 0.8);
                                    border-radius: 4px;
                                }
                            }
                        }

                        .title {
                            // display: inline;
                            font-weight: bold;
                            font-size: 16px;
                            color: #333333;
                            line-height: 24px;
                            height: 48px;
                            margin: 10px 0 5px;
                            text-align: left;
                            @include text-ellipsis;

                            .inline-tag {
                                position: relative;
                                top: -2px;
                                display: inline;
                                transform: translateY(-4px);
                                padding: 4px 6px;
                                line-height: 20px;
                                font-size: 12px;
                                color: #ffffff;
                                border-radius: 4px;

                                &.up {
                                    background: #51bd69;
                                }

                                &.down {
                                    background: #ffa000;
                                }
                            }
                        }
                    }

                    .bd {
                        font-size: 14px;
                        color: rgba($color: #333, $alpha: 0.8);
                        line-height: 21px;
                        margin-bottom: 10px;

                        .time {
                            @include utils-ellipsis;
                        }

                        .session {
                            height: 21px;
                            position: relative;

                            p {
                                max-width: 100%;
                                display: inline-block;
                                @include utils-ellipsis;
                            }
                        }
                    }

                    .ft {
                        display: flex;
                        align-items: center;
                        height: 64px;
                        font-size: 14px;
                        border-top: 1px solid #ebebeb;

                        .left {
                            display: flex;
                            flex: 1;

                            .label {
                                width: 70px;
                                color: rgba($color: #333, $alpha: 0.8);
                            }

                            .value {
                                width: 174px;
                                @include utils-ellipsis;
                                span {
                                    color: #ffa000;
                                }
                            }
                        }

                        .right {
                            margin-left: 20px;
                            .button {
                                width: 96px;
                                height: 32px;
                                line-height: 32px;
                                text-align: center;
                                background: $liner-gradient-primary;
                                border-radius: 16px;
                                font-weight: bold;
                                color: #ffffff;
                                cursor: pointer;
                                &.disabled {
                                    background: #d7d7d7;
                                }
                            }
                        }
                    }
                }
            }

            .pagination-cotnent {
                margin-top: 28px;
            }

            .empty {
                border-radius: 12px;
                padding: 285px 15px 60px;
                text-align: center;
                border: 2px solid rgba(255, 255, 255, 0.24);
                background: url(../assets/activity/common/empty.png) no-repeat center 61px/266px 194px, rgba(255, 255, 255, 1);
            }
        }
    }

    .information-wrapper {
        padding: 40px 0;
        background-color: #fff;
        .hd {
            text-align: center;
            .title {
                img {
                    display: inline-block;
                    width: 218px;
                    height: 44px;
                    transform: scale(0.9);
                }
            }
            .text {
                margin-top: 6px;
                font-size: 16px;
                color: #333333;
                opacity: 0.8;
            }
        }

        .bd {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            .time {
                padding-left: 16px;
                background: url(../assets/activity/activityGather/time.png) no-repeat left center / 12px 13px;
            }
            .content-l {
                flex-shrink: 0;
                width: 450px;
                height: 398px;
                margin-right: 20px;
                background: $color-white;
                box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                border-radius: 12px;
                border: 1px solid $color-primary;
                padding: 30px 20px 20px;
                border: 1px solid transparent;

                &:hover {
                    border: 1px solid $color-primary;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                    transform: translateY(-5px);
                    transition: all 0.3s ease;
                    .content {
                        .title {
                            color: $color-primary;
                        }
                    }
                }

                .img {
                    height: 223px;
                    background: #e3e3e3;
                    border-radius: 12px 12px 0px 0px;
                    overflow: hidden;
                }

                .content {
                    .time {
                        margin-top: 19px;
                        font-size: 14px;
                        color: rgba($color: #333, $alpha: 0.8);
                    }
                    .title {
                        height: 48px;
                        margin-top: 14px;
                        color: #333333;
                        font-weight: bold;
                        font-size: 16px;
                        line-height: 24px;
                        @include text-ellipsis;
                    }
                    .desc {
                        margin-top: 12px;
                        font-size: 14px;
                        color: rgba($color: #333, $alpha: 0.8);
                        @include utils-ellipsis;
                    }
                }
            }

            .content-r {
                flex: 1;
                width: 730px;
                height: 398px;
                background: $color-white;
                box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                border-radius: 12px;

                .information-swiper {
                    .swiper-pagination {
                        bottom: 15px;
                    }

                    .swiper-wrapper {
                        .swiper-slide {
                            padding-bottom: 23px;
                            .information-list {
                                display: flex;
                                padding: 16px 20px;

                                &:hover {
                                    .title {
                                        color: $color-primary;
                                    }
                                }
                            }
                            .list-l {
                                width: 170px;
                                height: 93px;
                                margin-right: 20px;
                                flex-shrink: 0;
                                background: #e3e3e3;
                                border-radius: 12px;
                                overflow: hidden;
                            }
                            .list-r {
                                width: 100%;
                                overflow: hidden;
                                // flex: 1;
                                .title {
                                    font-weight: bold;
                                    font-size: 16px;
                                    color: #333333;
                                    @include utils-ellipsis;
                                }
                                .desc {
                                    height: 42px;
                                    margin-top: 10px;
                                    line-height: 21px;
                                    font-size: 14px;
                                    color: rgba($color: #333, $alpha: 0.8);
                                    @include text-ellipsis;
                                }
                                .time {
                                    margin-top: 7px;
                                    font-size: 14px;
                                    color: rgba($color: #333, $alpha: 0.8);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .server-wrapper {
        padding: 40px 0;
        background-image: url(../assets/activity/activityGather/server-bg2.png), url(../assets/activity/activityGather/server-bg1.png);
        background-position: calc(50% + 239px) 216px, left top;
        background-repeat: no-repeat, no-repeat;
        background-size: 1078px 572px, cover;
        .hd {
            text-align: center;
            .title {
                img {
                    display: inline-block;
                    width: 218px;
                    height: 44px;
                    transform: scale(0.9);
                }
            }
            .text {
                margin-top: 6px;
                font-size: 16px;
                color: #333333;
                opacity: 0.8;
            }
        }
        .bd {
            .server-top {
                margin-top: 33px;
                .server-top-tit {
                    position: relative;
                    bottom: -7px;
                    display: inline-block;
                    padding: 0 28px;
                    font-weight: bold;
                    font-size: 24px;
                    color: #ffffff;
                    line-height: 36px;
                    background: $liner-gradient-primary;
                    border-radius: 23px 23px 23px 0px;
                }
                .server-top-desc {
                    width: 790px;
                    margin: 0 0 0 -78px;
                    padding: 30px 60px 30px 78px;
                    box-shadow: 0px 11px 22px 2px rgba(138, 163, 245, 0.24);
                    border-radius: 80px 20px 80px 20px;
                    font-size: 16px;
                    color: #333333;
                    line-height: 32px;
                    background: url(../assets/activity/activityGather/server-logo.png) no-repeat 639px bottom / 140px 106px, #ffffff;
                    span {
                        font-weight: bold;
                    }
                }
            }

            .server-bottom {
                .text {
                    margin: 121px 0 40px;
                    display: flex;
                    justify-content: center;
                    p {
                        position: relative;
                        font-weight: bold;
                        font-size: 32px;
                        color: #333333;
                        z-index: 1;
                        &::before {
                            position: absolute;
                            left: -20px;
                            bottom: 6px;
                            z-index: -1;
                            content: '';
                            width: 45px;
                            height: 44px;
                            background: url(../assets/activity/activityGather/server-crice.png) no-repeat center / cover;
                        }
                    }
                }
                .tabs {
                    border-radius: 12px;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                    background-color: #fff;
                    position: relative;
                    .tab-hd {
                        display: flex;
                        border-radius: 12px 12px 0 0;
                        background-color: #f6f6f6;
                        overflow: hidden;
                        li {
                            flex: 1;
                            height: 109px;

                            background: #f6f6f6;
                            position: relative;
                            z-index: 1;
                            cursor: pointer;
                            &:first-of-type:before {
                                display: none;
                            }
                            &::before {
                                background-color: #caced3cc;
                                content: '';
                                height: 40px;
                                left: 0;
                                position: absolute;
                                top: 50%;
                                transform: translateY(-50%);
                                width: 1px;
                            }
                            &::after {
                                content: attr(data-id);
                                font-weight: bold;
                                font-size: 93px;
                                color: rgba($color: $color-primary, $alpha: 0.1);
                                position: absolute;
                                bottom: -34px;
                                right: 22px;
                                z-index: -1;
                            }
                            &.active {
                                font-size: 28px;
                                background: #ffffff;
                                border-radius: 12px 12px 0px 0px;

                                p {
                                    color: $color-primary;
                                }

                                &::after {
                                    color: rgba($color: $color-primary, $alpha: 0.1);
                                }
                                &::before {
                                    display: none;
                                }
                                & + li {
                                    &::before {
                                        display: none;
                                    }
                                }
                            }

                            p {
                                margin-top: 19px;
                                text-align: center;
                                font-weight: bold;
                                font-size: 28px;
                                color: #333333;
                            }

                            span {
                                display: block;
                                padding: 11px 36px 0;
                                font-size: 14px;
                                color: rgba($color: #333, $alpha: 0.8);
                                text-align: center;
                            }
                        }
                    }
                    .tab-bd {
                        position: relative;
                        z-index: 1;
                        width: 100%;
                        height: 504px;
                        padding: 30px 30px 10px;
                        border-radius: 0 0 12px 12px;
                        .tab-con-wrap {
                            display: none;
                            &:first-child {
                                display: block;
                            }
                        }

                        .tab-con {
                            display: flex;
                            justify-content: space-between;
                        }

                        .page-box {
                            position: absolute;
                            right: 0;
                            top: 30px;
                            width: 140px;
                            .swiper-pagination {
                                bottom: -5px;
                                font-weight: bold;
                                font-size: 22px;
                                color: #296aff;
                                .swiper-pagination-current {
                                    position: relative;
                                    left: 6px;
                                }
                                .swiper-pagination-total {
                                    position: relative;
                                    right: 6px;
                                }
                            }
                        }

                        .tab-con-wrap-n1 {
                            .swiper-slide-n1 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg1.png) no-repeat left center / 41px 40px;
                                    }
                                }
                            }

                            .swiper-slide-n2 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg2.png) no-repeat left center / 41px 40px;
                                    }
                                }
                            }
                        }

                        .tab-con-wrap-n2 {
                            .swiper-slide-n1 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg3.png) no-repeat left center / 41px 40px;
                                    }
                                }

                                .slider-bd {
                                    padding-right: 0;
                                    .odd {
                                        width: 53%;
                                    }
                                    .even {
                                        width: 47%;
                                    }
                                }
                            }

                            .swiper-slide-n2 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg4.png) no-repeat left center / 41px 40px;
                                    }
                                }
                            }
                        }

                        .tab-con-wrap-n3 {
                            .swiper-slide-n1 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg5.png) no-repeat left center / 41px 40px;
                                    }
                                }

                                .slider-bd {
                                    .odd {
                                        width: 100%;
                                    }
                                }
                            }

                            .swiper-slide-n2 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg6.png) no-repeat left center / 41px 40px;
                                    }
                                }
                            }

                            .swiper-slide-n3 {
                                .slider-hd {
                                    .p1 {
                                        background: url(../assets/activity/activityGather/server-swiper-hd-bg7.png) no-repeat left center / 41px 40px;
                                    }
                                }
                            }
                        }

                        .slider-hd {
                            .p1 {
                                height: 40px;
                                line-height: 40px;
                                padding-left: 52px;
                                font-weight: bold;
                                font-size: 24px;
                                color: #333333;
                            }
                            .p2 {
                                margin-top: 9px;
                                font-size: 18px;
                                color: #333333;
                                line-height: 36px;
                            }
                        }

                        .slider-bd {
                            display: flex;
                            margin-top: 30px;
                            padding: 30px;
                            background: #f8faff;
                            border-radius: 12px;
                            .bd-l {
                                position: relative;
                                img {
                                    display: inline-block;
                                    width: 330px;
                                    height: 220px;
                                    margin-right: 20px;
                                    border-radius: 10px;
                                }

                                p {
                                    position: absolute;
                                    font-size: 12px;
                                    color: #333333;
                                    opacity: 0.6;
                                }
                            }
                            .bd-r {
                                flex: 1;
                                display: flex;
                                align-items: center;
                                margin-left: 20px;
                                ul {
                                    width: 100%;
                                    display: flex;
                                    flex-wrap: wrap;
                                    li {
                                        width: 50%;
                                        padding: 10px 0;
                                        font-size: 16px;

                                        &.even {
                                            &::before {
                                                background: #ffa000;
                                                border: 3px solid #ffeccc;
                                            }
                                        }

                                        &.odd {
                                            &::before {
                                                background: $color-primary;
                                                border: 3px solid #bcd0ff;
                                            }
                                        }

                                        &::before {
                                            display: inline-block;
                                            content: '';
                                            width: 7px;
                                            height: 7px;
                                            margin-right: 9px;
                                            padding: 3px;
                                            border-radius: 50%;
                                        }

                                        span {
                                            font-weight: bold;
                                            color: $color-primary;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .cooperation-case-wrapper {
        padding: 40px 0 10px;
        width: 100%;
        background-color: $color-white;
        .hd {
            text-align: center;
            .title {
                img {
                    display: inline-block;
                    width: 218px;
                    height: 44px;
                    transform: scale(0.9);
                }
            }
            .text {
                margin-top: 6px;
                font-size: 16px;
                color: #333333;
                opacity: 0.8;
            }
        }
        .swiper {
            overflow: visible;
        }
        .swiper-box {
            padding: 30px 0;
            overflow: hidden;
        }
        .swiper-button-next,
        .swiper-button-prev {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 28px;
            height: 28px;
            background-repeat: no-repeat;
            background-size: contain;
            background-position: center;
        }

        .swiper-button-next {
            right: -40px;
            background-image: url(../assets/activity/activityGather/next-swiper.png);
        }

        .swiper-button-prev {
            background-image: url(../assets/activity/activityGather/pre-swiper.png);
            left: -40px;
        }

        .swiper-button-prev:after,
        .swiper-rtl .swiper-button-next:after,
        .swiper-button-next:after,
        .swiper-rtl .swiper-button-prev:after {
            display: none;
        }

        .swiper-slide {
            &:hover {
                .default {
                    opacity: 0;
                    transform: scale(0.8);
                }
                .mask {
                    opacity: 1;
                }
            }
        }
        .list-content {
            position: relative;
            padding: 20px 0 0;
        }

        .default {
            border-radius: 12px;
            box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
            background-color: #fff;
            transition: all 0.3s ease;
            transform: scale(1);
            opacity: 1;
            overflow: hidden;
            .header {
                position: relative;
                .tag {
                    position: absolute;
                    top: 0;
                    left: 0;
                    height: 24px;
                    padding: 0 8px;
                    line-height: 24px;
                    background: #333333;
                    border-radius: 9px 0px 9px 0px;
                    opacity: 0.8;
                    font-size: 14px;
                    color: #ffffff;
                }

                img {
                    height: 180px;
                }
            }
            .content-box {
                transform: translateY(-20px);
                position: relative;
                z-index: 9;
                &::before {
                    display: block;
                    content: '';
                    width: 100%;
                    height: 20px;
                    border-radius: 12px 12px 0 0;
                    background-color: #fff;
                }
                .content {
                    padding: 0 30px;
                }
                .hd {
                    font-weight: bold;
                    color: #333333;
                    line-height: 27px;
                    height: 54px;
                    p {
                        font-size: 16px;
                        @include text-ellipsis;
                    }
                }
                .bd {
                    margin-top: 20px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 21px;
                    opacity: 0.8;
                    height: 63px;
                    p {
                        @include text-ellipsis(3);
                    }
                }
                .ft {
                    margin-top: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    img {
                        width: 136px;
                        height: 50px;
                        background: #fff;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    p {
                        margin-left: 17px;
                        flex: 1;
                        font-weight: 400;
                        font-size: 14px;
                        color: #333333;
                        line-height: 21px;
                        opacity: 0.8;
                        text-align: right;
                        @include text-ellipsis(1);
                    }
                }
            }
        }
        .mask {
            height: 450px;
            background: url(../assets/activity/activityGather/cooperation-case-bg.png) no-repeat left bottom/100% 153px, $color-primary;
            border-radius: 12px;
            padding: 30px 8px 0 30px;
            box-sizing: border-box;
            position: absolute;
            left: 0;
            right: 0;
            top: -25px;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(1);
            z-index: 19;
            .hd {
                font-weight: bold;
                font-size: 18px;
                color: #ffffff;
                line-height: 28px;
                text-align: left;
            }
            .bd {
                height: 300px;
                margin-top: 10px;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
                line-height: 28px;
                overflow-y: scroll;
                padding-right: 12px;
                /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
                &::-webkit-scrollbar {
                    position: relative;
                    right: -10px;
                    width: 4px;
                    // height: 7px;
                    background-color: rgba(255, 255, 255, 0);
                }

                /*定义滚动条轨道 内阴影+圆角*/
                &::-webkit-scrollbar-track {
                    border-radius: 2px;
                }

                /*定义滑块 内阴影+圆角*/
                &::-webkit-scrollbar-thumb {
                    background: #aac0f6;
                }
            }
            .ft {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px 0;
                .button {
                    width: 121px;
                    height: 32px;
                    line-height: 32px;
                    background: #ffffff;
                    border-radius: 16px;
                    text-align: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: $color-primary;
                }
            }
        }
    }

    .department-wrap {
        padding: 40px 0;
        width: 100%;
        background-color: $color-white;
        .hd {
            text-align: center;
            .title {
                img {
                    display: inline-block;
                    width: 218px;
                    height: 44px;
                    transform: scale(0.9);
                }
            }
            .text {
                margin-top: 6px;
                font-size: 16px;
                color: #333333;
                opacity: 0.8;
            }
        }
        ul {
            padding-top: 40px;
            li {
                &:nth-child(odd) {
                    background: #eef6fd;
                }
                &:nth-child(even) {
                    background: #fff;
                }
                .list-wrap {
                    width: 1200px;
                    margin: 0 auto;
                    .list {
                        display: flex;
                        justify-content: space-between;
                    }
                }
                .list-l {
                    display: flex;
                    align-items: center;
                    p {
                        font-weight: bold;
                        font-size: 20px;
                        color: #333333;
                        position: relative;
                        &::after {
                            position: absolute;
                            left: 0;
                            bottom: -4px;
                            content: '';
                            width: 38px;
                            height: 4px;
                            width: 38px;
                            background: linear-gradient(90deg, #296aff, #29a7ff);
                            border-radius: 2px;
                        }
                    }
                }
            }
        }
        .list-n1,
        .list-n2 {
            img {
                width: 1087px;
                height: 202px;
            }
        }

        .list-n3,
        .list-n4 {
            img {
                width: 1087px;
                height: 142px;
            }
        }
        .list-n2 img {
            object-fit: cover !important;
        }
    }

    .cooperation-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: 215px;
        background: url(../assets/activity/activityGather/cooperation-bg.png) no-repeat 50% / cover, $background-primary;
        padding-top: 30px;

        .top {
            width: 104px;
            height: 30px;
            background: url(../assets/activity/activityGather/logo.png) no-repeat left/104px 30px;
            margin-bottom: 18px;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .bottom {
            display: flex;

            .cooperation {
                background: $liner-gradient-primary;
                color: $color-white;
                padding: 0 42px;
                font-weight: bold;
                line-height: 40px;
                border-radius: 40px;
                margin-right: 20px;
            }

            .tel {
                border-radius: 20px;
                border: 1px solid $color-primary;
                padding: 0 30px 0 63px;
                line-height: 38px;
                background: url(../assets/activity/activityGather/phone.png) no-repeat 30px center/25px 25px;
                color: $color-primary;
                font-weight: bold;
            }
        }
    }

    .detail-wrapper {
        padding: 28px 46px 14px 30px;
        background-color: $color-white;
        border-radius: 12px;
        position: relative;
        z-index: 1;
        display: flex;
        .activity-connect {
            position: absolute;
            left: 30px;
            top: -14px;
            // display: flex;
            // align-items: center;
            max-width: 605px;
            height: 28px;
            padding: 0 12px;
            line-height: 28px;
            color: #c18c33;
            font-weight: bold;
            background: #fff8f1;
            border-radius: 14px;
            .connect {
                // display: flex;
                // flex-wrap: nowrap;
                @include utils-ellipsis;
                p {
                    display: inline;
                    font-size: 16px;
                }

                a {
                    &:hover {
                        color: inherit;
                    }
                }
            }
        }
        .status {
            position: absolute;
            top: -15px;
            right: 0;
            padding: 5px 0px 0;
            height: 40px;
            font-size: 18px;
            font-weight: bold;
            color: $color-white;
            width: 96px;
            text-align: center;

            $status: await, await-start, start, ended;

            @each $name in $status {
                @if $name == start {
                    &.#{$name} {
                        text-align: left;
                        padding-left: 30px;
                        background: url(../assets/activity/singleDetail/start-icon.png) no-repeat 10px 8px/13px 14px, url(../assets/activity/singleDetail/#{$name}-bg.png) no-repeat left top/auto 39px;
                    }
                } @else {
                    &.#{$name} {
                        background: url(../assets/activity/singleDetail/#{$name}-bg.png) no-repeat left top/auto 39px;
                    }
                }
            }
        }

        .detail {
            flex-grow: 1;
            margin-right: 65px;
            overflow: hidden;

            .tag {
                display: flex;
                margin-bottom: 14px;

                .item {
                    padding: 0 6px;
                    font-size: 12px;
                    line-height: 20px;
                    color: $color-primary;
                    background-color: #e5edff;
                    border-radius: 4px;

                    & + .item {
                        margin-left: 6px;
                    }

                    &.yellow {
                        border-radius: 4px;
                        border: 1px solid #ffa000;
                        color: #ffa000;
                        background-color: #fff;
                    }
                }
            }

            .title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 10px;
                line-height: 1.5;
            }

            .detail-item {
                display: flex;
                font-size: 16px;
                line-height: 28px;
                margin-bottom: 2px;

                .label {
                    flex-shrink: 0;
                    padding-left: 26px;
                }

                .value {
                    flex-grow: 1;
                }

                &.organization,
                &.type {
                    .value {
                        @include utils-ellipsis;
                    }
                }
            }

            $icons: organization, time, address, type;

            @each $name in $icons {
                .#{$name} {
                    .label {
                        background: url(../assets/activity/singleDetail/#{$name}.png) no-repeat left 6px/16px 16px;
                    }
                }
            }

            .welfare {
                margin-top: 14px;
                margin-bottom: 16px;

                display: flex;
                line-height: 22px;
                padding-right: 10px;
                color: #714d10;

                .value {
                    background: url(../assets/activity/singleDetail/praise.png) no-repeat left/26px 22px, #fff8f1;
                    padding-left: 32px;
                    @include utils-ellipsis;
                    border-radius: 4px 0 0 4px;
                }

                &.has-detail {
                    .arrow {
                        cursor: pointer;
                        flex-shrink: 0;
                        font-weight: bold;
                        color: #714d10;
                        flex-shrink: 0;
                        padding-left: 6px;
                        padding-right: 28px;
                        background: url(../assets/activity/singleDetail/arrow.png) no-repeat right 10px center/13px 13px, #fff8f1;
                        border-radius: 0 4px 4px 0;
                        position: relative;

                        &::after {
                            content: '';
                            display: block;
                            position: absolute;
                            left: 6px;
                            right: 10px;
                            height: 1px;
                            background-color: #714d10;
                            bottom: 0px;
                        }
                    }
                }
            }
        }

        .aside {
            position: relative;
            flex-shrink: 0;
            position: relative;
            background: #f7f9fe;
            border-radius: 10px;
            padding: 30px 40px;
            display: flex;
            align-self: center;

            .update-status {
                position: absolute;
                right: 0;
                top: -10px;
                transform: translateX(50%);
                padding: 0 9px;
                line-height: 20px;
                color: #ffa000;
                font-size: 12px;
                background: #fff2db;
                border-radius: 12px 0px 10px 0px;
            }

            .item {
                padding: 0 20px 0 0;
                text-align: center;

                .amount {
                    font-size: 44px;
                    font-weight: bold;
                    color: #fa635c;
                }

                span {
                    margin-top: 11px;
                    color: $font-color-label;
                    font-size: 16px;
                }

                &:last-child {
                    padding: 0 0 0 20px;
                }

                & + .item {
                    position: relative;

                    &::before {
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 6px;
                        content: '';
                        width: 1px;
                        height: 50px;
                        background: #ebebeb;
                    }
                }
            }
        }
    }
}
