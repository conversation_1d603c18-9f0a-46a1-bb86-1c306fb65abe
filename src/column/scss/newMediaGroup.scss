@import './variables';

/* 交流群样式 */
.group {
    margin-top: 20px;
    padding: 15px 20px;
    background-color: $color-white;
    border-radius: 10px;
    box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);

    .group-title {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-size: 12px;
        color: #171819;
        border-bottom: 1px solid rgba(23, 24, 25, 0.29);

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            h5 {
                position: relative;
                padding-left: 12px;
                font-size: 16px;
                font-weight: bold;
                line-height: 1;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    width: 4px;
                    background-color: $color-primary;
                    border-radius: 2px;
                }
            }
        }

        h4 {
            display: flex;
            justify-content: space-between;
            align-items: center;

            strong {
                padding-bottom: 12px;
                color: $font-color;
                font-size: 12px;
                font-weight: 400;
                border-bottom: 1px solid #171819;
            }

            span {
                margin-bottom: 12px;
                cursor: pointer;
            }
        }
    }

    .group-list {
        .group-item {
            position: relative;
            padding: 16px 0 8px;
            font-size: 15px;
            line-height: 22px;
            cursor: pointer;

            &::before {
                content: '';
                display: inline-block;
                width: 22px;
                height: 22px;
                margin-right: 10px;
                vertical-align: bottom;
                background-size: 22px;
                background-position: center;
                background-repeat: no-repeat;
            }

            &::after {
                content: '';
                position: absolute;
                right: 0;
                top: 50%;
                width: 12px;
                height: 7px;
                background: url(../assets/media/arrow-bottom.png) no-repeat;
                background-size: 12px 7px;
                background-position: center;
            }
        }

        $list: ligong renwen jingguan beishangguangshen youzhidanwei shuoborencai;

        @each $var in $list {
            .#{$var} .group-item::before {
                background-image: url(../assets/media/#{$var}.png);
            }
        }

        .active {
            .group-item::after {
                transform: rotateX(180deg);
            }

            .group-codeimg {
                display: block;
            }
        }

        .group-codeimg {
            display: none;
            background: #f6f7f8;
            text-align: center;
            padding: 22px 0 18px;

            img {
                width: 119px;
                height: 119px;
                border-radius: 2px;
                margin-bottom: 14px;
            }

            p {
                color: #393b3d;

                &:last-child {
                    font-size: 12px;
                    color: #a3a3a3;
                    margin-top: 4px;
                }
            }
        }
    }
}

.group-dialog {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    margin: 0;
    z-index: $header-index;
    background: rgba(0, 0, 0, 0.45);

    .dialog {
        width: 640px;
        margin: 15vh auto 0;
        border-radius: 4px;
        background-color: $color-white;

        .dialog-top {
            display: flex;
            justify-content: space-between;
            padding: 18px 20px 18px 42px;
            background-color: #f6f7f8;
            font-size: 16px;
            font-weight: 700;
            border-radius: 4px 4px 0 0;
            color: #0e1526;

            i {
                width: 24px;
                height: 24px;
                background: url(../assets/media/close-dialog.png) no-repeat;
                background-size: contain;
                background-position: center;
                cursor: pointer;
            }
        }

        .dialog-body {
            .dialog-nav {
                display: flex;
                justify-content: center;
                margin: 30px 40px 0;
                border-bottom: 1px solid rgba(23, 24, 25, 0.1);

                span {
                    padding-bottom: 13px;
                    font-size: 17px;
                    line-height: 18px;
                    color: #393b3d;
                    cursor: pointer;
                    box-sizing: border-box;

                    &:first-child {
                        margin-right: 40px;
                    }
                }

                .active {
                    font-size: 18px;
                    font-weight: 700;
                    color: #141617;
                    border-bottom: 3px solid $color-primary;
                }
            }

            .group-box {
                display: none;
                padding: 23px 40px 28px;

                &.active {
                    display: block;
                }
            }

            .group-content {
                display: flex;
                flex-wrap: wrap;

                div {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 31.7%;
                    height: 42px;
                    line-height: 42px;
                    position: relative;
                    margin-right: 13px;
                    margin-bottom: 12px;
                    text-align: center;
                    background-color: #f6f8f8;
                    border-radius: 3px;
                    color: #4f565e;
                    cursor: pointer;

                    &:nth-child(3n) {
                        margin-right: 0;
                    }

                    &:hover {
                        color: $color-white;
                        background: $color-primary;

                        &::after {
                            content: '';
                            display: inline-block;
                            width: 14px;
                            height: 12px;
                            line-height: 14px;
                            margin-left: 3px;
                            background: url(../assets/media/arrow-double.png) no-repeat;
                            background-size: contain;
                        }

                        p {
                            display: block;
                        }
                    }

                    p {
                        display: none;
                        position: absolute;
                        left: calc(100% + 10px);
                        top: 0;
                        z-index: $header-index;
                        width: 212px;
                        padding: 20px;
                        background-color: $color-white;
                        box-shadow: 0 0 20px 0 rgb(97 109 142 / 22%);
                        font-size: 14px;
                        color: #393b3d;
                        line-height: 18px;
                        margin-bottom: 4px;

                        img {
                            width: 130px;
                            height: auto;
                        }

                        strong {
                            font-weight: 700;
                        }
                    }
                }

                &.border-top {
                    border-top: 1px solid rgba(23, 24, 25, 0.1);
                    padding-top: 12px;
                }
            }
        }
    }
}
