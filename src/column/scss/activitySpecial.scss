@import './variablesActivity';

.main-container {
    &.is-default {
        .tabs-wrapper {
            .detail-pane {
                margin-top: -44px;
                border-radius: 12px;
                background-color: $color-white;
                padding: 30px;
            }

            .apply-way-pane,
            .review-pane {
                border-radius: 12px;
                background-color: $color-white;
                padding: 30px;
            }
        }
    }

    .view-content {
        width: $view-width;
        margin: 0 auto;
    }

    .banner-wrapper {
        display: flex;
        justify-content: center;
        position: relative;

        .banner-box {
            display: flex;
            justify-content: center;
            overflow: hidden;
            img {
                display: block;
                width: auto !important;
                height: auto !important;
                object-fit: cover;
            }
        }

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background-color: rgba($color: #e0e8fd, $alpha: 0.7);
        }

        .detail-wrapper {
            position: absolute;
            left: 50%;
            bottom: 0;
            padding: 20px 46px 14px 30px;
            background-color: $color-white;
            border-radius: 12px;
            transform: translateY(83px) translateX(-50%);
            z-index: 1;
            display: flex;

            .status {
                position: absolute;
                top: -15px;
                right: 0;
                padding: 5px 0px 0;
                height: 40px;
                font-size: 18px;
                font-weight: bold;
                color: $color-white;
                width: 96px;
                text-align: center;

                $status: await, await-start, start, ended;

                @each $name in $status {
                    @if $name == start {
                        &.#{$name} {
                            text-align: left;
                            padding-left: 30px;
                            background: url(../assets/activity/special/start-icon.gif) no-repeat 10px 8px/13px 14px, url(../assets/activity/special/#{$name}-bg.png) no-repeat left top/auto 39px;
                        }
                    } @else {
                        &.#{$name} {
                            background: url(../assets/activity/special/#{$name}-bg.png) no-repeat left top/auto 39px;
                        }
                    }
                }
            }

            .detail {
                flex-grow: 1;
                margin-right: 65px;
                overflow: hidden;

                .tag {
                    display: flex;
                    margin-bottom: 14px;

                    .item {
                        padding: 0 6px;
                        font-size: 12px;
                        line-height: 20px;
                        color: $color-primary;
                        background-color: #e5edff;
                        border-radius: 4px;

                        &.offline {
                            line-height: 18px;
                            padding: 0 4px;
                            background-color: transparent;
                            color: #ffa000;
                            border: 1px solid #ffa000;
                        }

                        & + .item {
                            margin-left: 6px;
                        }
                    }
                }

                .title {
                    font-size: 20px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    line-height: 1.5;
                }

                .detail-item {
                    display: flex;
                    font-size: 16px;
                    line-height: 28px;
                    margin-bottom: 2px;

                    .label {
                        flex-shrink: 0;
                        padding-left: 26px;
                    }

                    .value {
                        flex-grow: 1;
                    }

                    &.organization,
                    &.type {
                        .value {
                            @include utils-ellipsis;
                        }
                    }
                }

                $icons: organization, time, address, type;

                @each $name in $icons {
                    .#{$name} {
                        .label {
                            background: url(../assets/activity/special/#{$name}.png) no-repeat left 6px/16px 16px;
                        }
                    }
                }

                .welfare {
                    margin-top: 14px;
                    margin-bottom: 16px;

                    display: flex;
                    line-height: 22px;
                    padding-right: 10px;
                    color: #714d10;

                    .value {
                        background: url(../assets/activity/special/praise.png) no-repeat left/26px 22px, #fff8f1;
                        padding-left: 32px;
                        @include utils-ellipsis;
                        border-radius: 4px 0 0 4px;
                    }

                    &.has-detail {
                        .arrow {
                            cursor: pointer;
                            flex-shrink: 0;
                            font-weight: bold;
                            color: #714d10;
                            flex-shrink: 0;
                            padding-left: 6px;
                            padding-right: 28px;
                            background: url(../assets/activity/special/arrow.png) no-repeat right 10px center/13px 13px, #fff8f1;
                            border-radius: 0 4px 4px 0;
                            position: relative;

                            &::after {
                                content: '';
                                display: block;
                                position: absolute;
                                left: 6px;
                                right: 10px;
                                height: 1px;
                                background-color: #714d10;
                                bottom: 0px;
                            }
                        }
                    }
                }
            }

            .aside {
                position: relative;
                flex-shrink: 0;
                position: relative;
                background: #f7f9fe;
                border-radius: 10px;
                padding: 24px 40px;
                display: flex;
                align-self: center;
                margin-top: -6px;

                .update-status {
                    position: absolute;
                    right: 0;
                    top: -10px;
                    transform: translateX(50%);
                    padding: 0 9px;
                    line-height: 20px;
                    color: #ffa000;
                    font-size: 12px;
                    background: #fff2db;
                    border-radius: 12px 0px 10px 0px;
                }

                .item {
                    padding: 0 20px 0 0;
                    text-align: center;

                    .amount {
                        font-size: 28px;
                        margin-bottom: 6px;
                        font-weight: bold;
                        color: $color-primary;
                    }

                    span {
                        margin-top: 11px;
                        color: $font-color-label;
                        font-size: 16px;
                    }

                    &:last-child {
                        padding: 0 0 0 20px;
                    }

                    & + .item {
                        position: relative;

                        &::before {
                            display: block;
                            position: absolute;
                            left: 0;
                            top: 6px;
                            content: '';
                            width: 1px;
                            height: 50px;
                            background: #ebebeb;
                        }
                    }
                }
            }
        }
    }

    .tabs-wrapper {
        background: url(../assets/activity/special/bg.png) no-repeat center 0px / cover, url(../assets/activity/special/bg-design.png) repeat-y center 100px /1780px auto,
            linear-gradient(180deg, #e3e7ff, #cfeeff);

        padding-top: 120px;
        padding-bottom: 40px;

        .tabs-header-wrapper {
            padding: 2px;
            background: linear-gradient(to top, #296aff, #bacfff);
            border-radius: 12px;
            margin-bottom: 74px;
            height: 74px;

            &.is-fixed {
                background: transparent;

                .tabs-header-content {
                    position: fixed;
                    z-index: 9;
                    left: 0;
                    right: 0;
                    top: 68px;
                    display: flex;
                    justify-content: center;
                    background-color: $color-white;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);

                    .tabs-header {
                        width: $view-width;
                        padding: 0px;

                        .tabs-nav {
                            color: $font-color;
                            font-weight: normal;
                            padding: 0;

                            & + .tabs-nav {
                                margin-left: 80px;
                            }

                            &.active {
                                color: $color-primary;
                                background: transparent;
                                font-weight: bold;
                            }
                        }
                    }
                }
            }
        }

        .tabs-header {
            display: flex;
            padding: 10px 30px;
            border-radius: 12px 12px 10px 10px;
            background: #ffffff;

            .tabs-nav {
                font-size: 20px;
                padding: 0 40px;
                color: $color-primary;
                line-height: 50px;
                border-radius: 6px;
                cursor: pointer;

                &.active {
                    font-weight: bold;
                    color: $color-white;
                    background: $liner-gradient-primary;
                }
            }

            @import './mini-code-popup';

            .share-mini-code-container {
                margin-left: auto;
                align-self: center;

                .share-mini-code-trigger {
                    font-size: 18px;
                    color: $font-color;
                    background: url(../assets/common/wechat-gray.png) no-repeat left/20px 20px;
                    &:hover {
                        color: #296aff;
                        background: url(../assets/common/wechat-info.png) no-repeat left/20px 20px;
                    }
                }
            }
        }

        .tabs-pane {
            display: none;

            &.show {
                display: block;
            }
        }

        @mixin common-wrapper {
            .common-wrapper {
                display: flex;
                flex-direction: column;
                position: relative;
                padding: 0 20px 60px;
                margin-bottom: 75px;
                background: url(../assets/activity/special/detail-wrapper-bg.png) no-repeat left top/815px 398px, #d0dfff;
                border-radius: 12px;
                border: 2px solid rgba(255, 255, 255, 0.66);

                &:last-child {
                    margin-bottom: 0;
                }

                &::after {
                    width: 354px;
                    height: 93px;
                    content: '';
                    display: block;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    background: url(../assets/activity/special/detail-wrapper-build-bg.png) no-repeat right bottom/354px 93px;
                }

                .common-title-content {
                    align-self: center;
                    margin: 0 auto 5px;
                    position: relative;
                    transform: translateY(-50%) skewX(-20deg);

                    .common-title {
                        position: relative;
                        z-index: 1;
                        line-height: 49px;
                        border-radius: 8px 0px 8px 8px;
                        color: $color-white;
                        background: linear-gradient(90deg, #2a6bff, #25c9e6);
                        padding: 0 30px;

                        span {
                            display: block;
                            font-weight: bold;
                            font-size: 28px;
                        }
                    }

                    &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        top: 0;
                        right: -17px;
                        width: 36px;
                        height: 25px;
                        background: url(../assets/activity/special/triangle.png) no-repeat center / cover;
                    }
                }

                .common-content--border {
                    background: linear-gradient(to bottom, #296bff, #e3ecff);
                    padding: 2px;
                    border-radius: 12px;

                    & + .common-content--border {
                        margin-top: 30px;
                    }
                }

                .common-content {
                    background: linear-gradient(to bottom, rgba(208, 223, 255, 0.6), rgba(240, 248, 255, 0.6)), $color-white;
                    border-radius: 10px 10px 12px 12px;
                    padding: 20px 30px;
                    font-size: 18px;
                    line-height: 36px;
                    white-space: wrap;

                    p {
                        font-size: inherit;
                        line-height: inherit;

                        & + .common-content-tittle {
                            margin-top: 30px;
                        }
                    }

                    div,
                    span,
                    h2,
                    h3,
                    h4,
                    h5,
                    h6 {
                        & + .common-content-tittle {
                            margin-top: 30px;
                        }
                    }

                    .common-content-tittle {
                        color: $color-white;
                        position: relative;
                        display: inline-flex;
                        padding-left: 10px;
                        font-size: 20px;
                        margin-top: 10px;
                        margin-bottom: 20px;
                        font-weight: bold;

                        .common-content-sort {
                            position: absolute;
                            left: 0;
                            bottom: 0;
                            width: 58px;
                            line-height: 44px;
                            background: url(../assets/activity/special/sort-bg.png) no-repeat left bottom/58px 44px;
                            text-align: center;
                        }

                        .common-content-name {
                            line-height: 35px;
                            padding-left: 50px;
                            padding-right: 12px;
                            background: $liner-gradient-primary;
                        }
                    }
                }
            }
        }

        .detail-pane {
            @include common-wrapper;
        }

        .session-pane {
            .common-wrapper {
                display: flex;
                flex-direction: column;
                position: relative;
                padding: 0 20px 60px;
                margin-bottom: 65px;
                background: rgba($color: #fff, $alpha: 0.4);
                border-radius: 12px;
                border: 2px solid rgba(255, 255, 255, 0.66);

                &:last-child {
                    margin-bottom: 0;
                }

                .common-title-content {
                    align-self: center;
                    margin: 0 auto 15px;
                    position: relative;
                    transform: translateY(-50%) skewX(-20deg);

                    .common-title {
                        position: relative;
                        z-index: 1;
                        line-height: 49px;
                        border-radius: 8px 0px 8px 8px;
                        color: $color-white;
                        background: linear-gradient(90deg, #2a6bff, #25c9e6);
                        padding: 0 30px;

                        span {
                            display: block;
                            font-weight: bold;
                            font-size: 28px;
                        }
                    }

                    &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        top: 0;
                        right: -17px;
                        width: 36px;
                        height: 25px;
                        background: url(../assets/activity/special/triangle.png) no-repeat center / cover;
                    }
                }
            }

            .hot-session-wrapper {
                padding-left: 37px;
                padding-right: 37px;
                padding-bottom: 25px;

                .common-title-content {
                    margin-bottom: 10px;
                }

                .hot-session-swiper {
                    overflow: hidden;
                    width: 100%;
                    padding: 6px 5px 15px;

                    .hot-list {
                        width: 362px;
                        border-radius: 12px;
                        display: block;
                        background-color: $color-white;
                        overflow: hidden;
                        color: $font-color;
                        box-shadow: 0px 3px 5px 0px rgba(51, 51, 51, 0.12);
                        transition: all 0.3s ease;

                        &:hover {
                            transform: translateY(-5px);
                            box-shadow: 0 0 0 1px $color-primary, 0px 3px 5px 0px rgba(51, 51, 51, 0.12);
                        }

                        .cover-content {
                            position: relative;
                            height: 140px;
                            color: $color-white;

                            .name {
                                display: inline-block;
                                position: absolute;
                                left: 0px;
                                top: 0px;
                                padding: 0 8px;
                                line-height: 24px;
                                background: rgba($color: $font-color, $alpha: 0.6);
                                border-radius: 9px 0px 9px 0px;
                                max-width: 241px;
                                @include utils-ellipsis;
                            }

                            .cover {
                                display: block;
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }

                            .view {
                                position: absolute;
                                left: 1px;
                                bottom: 0;
                                padding: 0 8px 0 21px;
                                font-size: 12px;
                                line-height: 18px;
                                background: url(../assets/activity/common/hot.png) no-repeat 8px center/8px 9px, rgba($color: $font-color, $alpha: 0.6);
                                border-radius: 7px 18px 18px 0px;
                            }
                        }

                        .detail {
                            position: relative;
                            padding: 15px 12px 19px;

                            .date {
                                background: url(../assets/activity/common/time.png) no-repeat left/16px 16px;
                                padding-left: 23px;
                            }

                            .address {
                                background: url(../assets/activity/common/address.png) no-repeat left/16px 16px;
                                padding-left: 23px;
                                margin-top: 15px;
                                @include utils-ellipsis;
                                width: 250px;
                            }

                            .apply {
                                position: absolute;
                                right: 13px;
                                bottom: 16px;
                                width: 72px;
                                border: none;
                                border-radius: 24px;
                                line-height: 24px;
                                color: $color-white;
                                background: $liner-gradient-primary;
                                border-radius: 12px;

                                &.gray-status {
                                    background: #d7d7d7;
                                }

                                &:disabled {
                                    background: #d7d7d7;
                                }
                            }
                        }
                    }

                    .swiper-button-prev {
                        width: 28px;
                        height: 28px;
                        background: url(../assets/activity/special/pre-swiper.png) no-repeat center/28px 28px;
                        margin-top: 0;
                        z-index: 1;

                        &::after {
                            display: none;
                        }
                    }

                    .swiper-button-next {
                        width: 28px;
                        height: 28px;
                        background: url(../assets/activity/special/next-swiper.png) no-repeat center/28px 28px;
                        margin-top: 0;
                        z-index: 1;

                        &::after {
                            display: none;
                        }
                    }
                }
            }

            .session-wrapper {
                padding: 0 14px 30px 0px;
            }

            .session-order-wrapper {
                $aside-width: 82px;

                // 已结束
                &.review-session-wrapper {
                    .month-title-content {
                        .aside-circle {
                            &::before,
                            &::after {
                                border-left-color: #d7d7d7;
                                border-right-color: #d7d7d7;
                            }

                            .circle {
                                border-color: #d7d7d7;
                            }
                        }

                        .month-title {
                            .month {
                                color: $font-color-label;
                            }

                            .year {
                                color: $font-color-basic;
                            }
                        }
                    }

                    .session-item {
                        .aside-date {
                            &::before,
                            &::after {
                                border-left-color: #d7d7d7;
                                border-right-color: #d7d7d7;
                            }

                            .date {
                                color: $font-color-label;
                            }
                        }
                    }
                }

                .tips {
                    padding: 30px 30px 20px 44px;
                    font-size: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $font-color-label;

                    &::after,
                    &::before {
                        content: '';
                        width: 60px;
                        height: 1px;
                        background: #333333;
                        opacity: 0.5;
                        margin: 0 7px;
                    }
                }

                & > .tips {
                    & + .month-list {
                        .month-title-content {
                            .aside-circle {
                                &::before {
                                    opacity: 0;
                                }
                            }
                        }
                    }
                }

                .month-list {
                    &:first-of-type {
                        .month-title-content {
                            .aside-circle {
                                &::before {
                                    opacity: 0;
                                }
                            }
                        }
                    }

                    &:last-child {
                        .day-session:last-child {
                            .session-item:last-child {
                                .aside-date {
                                    &::after {
                                        opacity: 0;
                                    }
                                }
                            }
                        }
                    }
                }

                .month-title-content {
                    display: flex;
                    padding-top: 1px;

                    .aside-circle {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: $aside-width;
                        position: relative;

                        &::before {
                            content: '';
                            flex-grow: 1;
                            border-left: 1px dashed #a7c1fc;
                            border-right: 1px dashed #a7c1fc;
                            transform: scaleX(0.5);
                        }

                        .circle {
                            width: 16px;
                            height: 16px;
                            background: transparent;
                            border-radius: 50%;
                            border: 3px solid #296aff;
                        }

                        &::after {
                            content: '';
                            flex-grow: 1;
                            border-left: 1px dashed #a7c1fc;
                            border-right: 1px dashed #a7c1fc;
                            transform: scaleX(0.5);
                        }
                    }

                    .month-title {
                        display: flex;
                        align-items: baseline;
                        padding-top: 10px;
                        padding-bottom: 10px;

                        .month {
                            font-size: 22px;
                            font-weight: bold;
                        }

                        .year {
                            font-size: 16px;
                            color: $font-color-basic;
                        }

                        .amount {
                            margin-left: 19px;

                            .number {
                                font-weight: bold;
                                color: $color-primary;
                            }
                        }
                    }
                }

                .session-item {
                    display: flex;

                    .aside-date {
                        width: $aside-width;
                        display: flex;
                        flex-direction: column;
                        align-items: center;

                        &::before,
                        &::after {
                            content: '';
                            flex-grow: 1;
                            border-left: 1px dashed #a7c1fc;
                            border-right: 1px dashed #a7c1fc;
                            transform: scaleX(0.5);
                        }

                        .date {
                            display: flex;
                            flex-direction: column;
                            padding: 6px 5px;
                            text-align: center;
                            font-size: 16px;
                            font-weight: bold;

                            .number {
                                margin-top: 4px;
                                font-size: 12px;
                                color: $font-color-label;
                            }
                        }
                    }

                    .session-content {
                        padding: 10px 0;
                        width: 1100px;
                    }

                    .session-detail {
                        position: relative;
                        background-color: $color-white;
                        border-radius: 8px;
                        padding: 15px 20px 19px 10px;
                        display: flex;
                        position: relative;
                        display: flex;
                        align-items: center;

                        .special-tips {
                            height: 24px;
                            position: absolute;
                            right: 0;
                            top: 0;
                            color: $color-white;
                            padding: 2px 8px 0 10px;
                            font-size: 12px;
                            background: url(../assets/activity/special/apply-bg.png) no-repeat right top/77px 24px;
                        }

                        &:hover {
                            box-shadow: 0 0 0 1px $color-primary;
                        }

                        .status {
                            align-self: center;
                            width: 24px;
                            padding: 10px 6px;
                            border-radius: 4px;
                            line-height: 16px;
                            font-size: 12px;
                            margin-right: 12px;

                            $status: (
                                'await-status': (
                                    'font-color': #5386ff,
                                    'bg-color': #e5edff
                                ),
                                'await-start-status': (
                                    'font-color': #fa635c,
                                    'bg-color': #fde9e8
                                ),
                                'start-status': (
                                    'font-color': #ffa000,
                                    'bg-color': #fdf1dc
                                ),
                                'end-status': (
                                    'font-color': #fff,
                                    'bg-color': #d7d7d7
                                )
                            );

                            @each $name, $value in $status {
                                &.#{$name} {
                                    color: map-get($map: $value, $key: 'font-color');
                                    background-color: map-get($map: $value, $key: 'bg-color');
                                }
                            }
                        }

                        .info {
                            flex-grow: 1;
                            $width: 682px;

                            .detail-link {
                                display: block;
                            }

                            .name {
                                line-height: 1.5;
                                font-size: 16px;
                                font-weight: bold;
                                margin-bottom: 12px;
                                max-width: $width;
                            }

                            .center {
                                display: flex;
                                max-width: $width;
                                color: $font-color;
                            }

                            .time {
                                flex-shrink: 0;
                                padding-left: 20px;
                                margin-right: 32px;
                                background: url(../assets/activity/common/time.png) no-repeat left/14px 14px;
                            }

                            .address {
                                @include utils-ellipsis;
                                flex-shrink: 0;
                                padding-left: 20px;
                                max-width: 381px;
                                background: url(../assets/activity/common/address.png) no-repeat left/14px 14px;
                            }

                            .company-content {
                                display: flex;
                                margin-top: 14px;
                                padding-left: 20px;
                                background: url(../assets/activity/common/company.png) no-repeat left/14px 14px;

                                .amount {
                                    margin-right: 11px;

                                    span {
                                        color: $color-primary;
                                        font-weight: bold;
                                    }
                                }

                                .company {
                                    @include utils-ellipsis;
                                    max-width: 516px;
                                    flex-grow: 1;
                                    font-size: 12px;
                                    line-height: 16px;
                                    a {
                                        color: $font-color-basic;

                                        &:hover {
                                            color: $color-primary;
                                        }
                                    }
                                }
                            }
                        }

                        .apply {
                            position: absolute;
                            right: 12px;
                            width: 96px;
                            line-height: 32px;
                            padding: 0;
                            font-weight: bold;
                            color: $color-white;
                            border-radius: 4px;
                            border: none;
                            background: $liner-gradient-primary;

                            &.gray-status {
                                background: #d7d7d7;
                            }
                        }
                    }
                }
            }
        }

        .company-pane {
            .site-wrapper {
                margin-top: -44px;
                padding: 30px 20px;
                display: grid;
                grid-template-columns: repeat(6, 182px);
                gap: 12px;
                background: rgba($color: $color-white, $alpha: 0.4);
                border-radius: 12px;
                border: 2px solid rgba($color: $color-white, $alpha: 0.66);
                margin-bottom: 30px;

                .site-item {
                    padding: 10px 14px;
                    border-radius: 8px;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;
                    background-color: $color-white;
                    cursor: pointer;
                    position: relative;

                    &.is-end {
                        .name,
                        .date {
                            color: $font-color-label;
                        }
                    }

                    &:hover,
                    &.active {
                        box-shadow: 0 0 0 1px $color-primary;
                        background-color: #eaf0fc;

                        .name,
                        .date {
                            color: $color-primary;
                            background-color: rgba($color: $color-white, $alpha: 0);
                        }
                    }

                    .status {
                        position: absolute;
                        right: -7px;
                        top: -5px;
                        font-size: 12px;
                        padding: 0 6px;
                        line-height: 14px;
                        border-radius: 7px 7px 7px 0px;

                        $status: (
                            'await-start': (
                                'color': $color-white,
                                'bg': linear-gradient(to right, #f94a42, #ff7937)
                            ),
                            'start': (
                                'color': $color-white,
                                'bg': linear-gradient(to right, #ffa000, #ff7937)
                            ),
                            'end': (
                                'color': $font-color-tips,
                                'bg': #eaeaea
                            )
                        );

                        @each $key, $value in $status {
                            &.#{$key}-status {
                                color: map-get($map: $value, $key: 'color');
                                background: map-get($map: $value, $key: 'bg');
                            }
                        }
                    }

                    .name {
                        min-width: 0;
                        max-width: 100%;
                        font-weight: bold;
                        @include utils-ellipsis;
                    }

                    .date {
                        font-size: 12px;
                        margin-top: 5px;
                    }
                }
            }

            .detail-wrapper {
                padding: 2px;
                margin-bottom: 30px;
                background: linear-gradient(to bottom, #296bff 0%, #e3ecff 50%);
                border-radius: 12px;

                .detail-content {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    border-radius: 10px 10px 12px 12px;
                    padding: 22px 156px 30px;
                    background: linear-gradient(to bottom, rgba(208, 223, 255, 0.6), rgba(240, 248, 255, 0.6)), #fff;

                    &.is-apply {
                        background: linear-gradient(to bottom, rgba(208, 223, 255, 0.6), rgba(240, 248, 255, 0.6)), url(../assets/activity/special/applied.png) no-repeat right 50px center/77px 69px,
                            #fff;
                    }

                    .title {
                        line-height: 36px;
                        font-size: 24px;
                        color: $color-primary;
                        font-weight: bold;
                        margin-bottom: 7px;
                        text-align: center;
                    }

                    .countdown-content {
                        line-height: 43px;
                        text-align: center;
                        margin-bottom: 20px;
                        font-size: 20px;
                        font-weight: bold;
                        color: $color-white;
                        min-width: 465px;
                        background: url(../assets/activity/special/countdown-bg.png) no-repeat center/contain;

                        .countdown {
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            .box {
                                line-height: 25px;
                                color: $color-primary;
                                font-size: 18px;
                                padding: 0 5px;
                                min-width: 28px;
                                height: 25px;
                                text-align: center;
                                background: $color-white;
                                border-radius: 4px;
                            }
                        }
                    }

                    .time {
                        font-size: 16px;
                        padding-left: 22px;
                        margin-bottom: 20px;
                        background: url(../assets/activity/common/time-primary.png) no-repeat left/16px 16px;
                    }

                    .address {
                        font-size: 16px;
                        @include utils-ellipsis;
                        padding-left: 22px;
                        max-width: 884px;
                        background: url(../assets/activity/common/address-primary.png) no-repeat left/16px 16px;
                    }
                }
            }

            .filter-wrapper {
                margin-bottom: 27px;
                border-radius: 12px;
                background-color: rgba($color: $color-white, $alpha: 0.7);
                padding: 40px 20px 20px;

                .filter-list {
                    display: flex;
                    align-items: baseline;

                    & + .filter-list {
                        margin-top: 5px;
                    }

                    .label {
                        position: relative;
                        flex-shrink: 0;
                        font-size: 16px;
                        font-weight: bold;
                        margin-right: 21px;

                        .area-tips {
                            position: absolute;
                            bottom: calc(100% + 10px);
                            background-color: #e5edff;
                            padding: 0 11px;
                            border-radius: 4px;
                            color: #5c5c5c;
                            font-size: 12px;
                            font-weight: 400;
                            white-space: nowrap;
                            line-height: 22px;

                            &::after {
                                content: '';
                                position: absolute;
                                bottom: -8px;
                                left: 31px;
                                border-width: 4px;
                                border-style: solid;
                                border-color: #e5edff transparent transparent transparent;
                                transform: scaleY(1.5);
                            }
                        }
                    }

                    .value {
                        flex-grow: 1;
                        display: flex;
                        flex-wrap: wrap;

                        .item {
                            margin-right: 6px;
                            margin-bottom: 8px;
                            padding: 0px 9px;
                            line-height: 26px;
                            font-size: 14px;
                            cursor: pointer;

                            &[checked='true'] {
                                border-radius: 4px;
                                color: $color-primary;
                                background: #e5edff;

                                &::after {
                                    content: '';
                                    display: inline-block;
                                    margin-left: 3px;
                                    width: 10px;
                                    height: 10px;
                                    background: url(../assets/activity/common/close-primary.png) no-repeat center / contain;
                                }
                            }

                            &.is-special {
                                &::after {
                                    display: none;
                                }
                            }

                            &:hover {
                                color: $color-primary;
                            }
                        }

                        .el-select {
                            margin-right: 16px;
                            width: 110px;
                            overflow: hidden;

                            &:last-of-type {
                                margin-right: 0;
                            }

                            .el-select__tags {
                                display: none;
                            }
                        }

                        .filter-item {
                            &.is-checked {
                                width: 124px;

                                &:hover {
                                    .el-input__suffix-inner {
                                        display: none;
                                    }

                                    .filter-major-clear {
                                        display: block;
                                    }
                                }
                            }

                            .filter-major-clear {
                                display: none;
                            }
                        }

                        .company-type-item {
                            position: relative;
                            display: flex;
                            align-items: center;
                            background-color: #f8f8f8;

                            .el-select,
                            .el-input {
                                height: 100%;
                            }

                            .select-trigger {
                                height: 100%;
                                width: calc(100%);
                            }

                            &.is-select {
                                background-color: #e5edff;

                                .label {
                                    color: $color-primary;
                                }

                                .el-select {
                                    width: 124px;
                                }
                            }

                            input {
                                position: absolute;
                                left: 0;
                                right: 0;
                                height: 100%;
                                z-index: 1;
                                opacity: 0;
                            }

                            .el-input__suffix {
                                z-index: 2;
                            }

                            .label {
                                color: $font-color;
                                white-space: nowrap;
                                font-size: 14px;
                                font-weight: normal;
                                position: absolute;
                                left: 0;
                                right: 35px;
                                padding: 0 0 0 10px;
                                z-index: 0;
                            }
                        }

                        .el-input__inner {
                            @include utils-ellipsis;
                            padding-left: 10px;
                            color: $color-primary;
                            font-size: 14px;
                            background-color: #f8f8f8;
                            border: none;
                            box-sizing: border-box;

                            &::placeholder {
                                color: $font-color;
                            }
                        }

                        .is-checked {
                            .el-input__inner {
                                background-color: #e5edff;
                            }

                            .el-input__icon {
                                color: $color-primary;
                            }
                        }
                    }

                    .filter-aside {
                        flex-shrink: 0;

                        .clear-all {
                            cursor: pointer;
                            padding-left: 20px;
                            background: url(../assets/activity/common/delete.png) no-repeat left/14px 14px;
                        }
                    }
                }

                .tips {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $color-primary;
                    margin-top: 18px;

                    &::before,
                    &::after {
                        content: '';
                        width: 60px;
                        height: 1px;
                        background: #333333;
                        opacity: 0.5;
                        margin: 0 15px;
                    }
                }
            }

            .select-dialog > .el-input {
                display: none;
            }

            .company-wrapper {
                .company-data {
                    padding: 30px 20px;
                    background: rgba(255, 255, 255, 0.4);
                    border: 2px solid rgba(255, 255, 255, 0.24);
                    margin-bottom: 30px;
                    display: grid;
                    grid-template-columns: repeat(3, 373px);
                    gap: 20px;
                    border-radius: 12px;

                    .item {
                        height: 156px;
                        box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                        background: #ffffff;
                        position: relative;
                        border-radius: 12px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        .top {
                            border-radius: 12px 12px 0 0;
                            display: flex;
                            padding: 17px 20px 11px;
                        }

                        .logo {
                            flex-shrink: 0;
                            width: 56px;
                            height: 56px;
                            border-radius: 50%;
                            overflow: hidden;
                            margin-right: 10px;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: contain;
                                display: block;
                            }
                        }

                        .grow {
                            flex-grow: 1;
                            overflow: hidden;
                        }

                        .name {
                            @include utils-ellipsis;
                            font-weight: bold;
                            font-size: 16px;
                            line-height: 28px;
                        }

                        .type {
                            @include utils-ellipsis;
                            margin-top: 7px;
                            color: $font-color-basic;
                        }

                        .middle {
                            @include utils-ellipsis;
                            font-size: 12px;
                            padding: 0px 17px 17px;
                            color: $font-color;

                            span {
                                color: $font-color-label;
                            }
                        }

                        .bottom {
                            @include utils-ellipsis;
                            padding-right: 20px;
                            color: $font-color-basic;
                            border-radius: 0 0 12px 12px;
                            line-height: 42px;
                            background: linear-gradient(90deg, #e6eeff, #f5fdff);
                            padding-left: 41px;

                            $icons: praise, announcement, site;

                            @each $name in $icons {
                                &.#{$name} {
                                    background: url(../assets/activity/common/#{$name}.png) no-repeat 20px/13px 13px, linear-gradient(90deg, #e6eeff, #f5fdff);
                                }
                            }

                            &.major {
                                padding-left: 32px;
                                position: relative;

                                &::before {
                                    content: '';
                                    display: inline-block;
                                    position: absolute;
                                    top: 19px;
                                    left: 20px;
                                    width: 6px;
                                    height: 6px;
                                    background: $color-primary;
                                    border-radius: 50%;
                                }
                            }
                        }

                        .aside {
                            position: absolute;
                            padding-left: 40px;
                            right: 20px;
                            bottom: 9px;
                            background: linear-gradient(to right, rgba(247, 248, 249, 0) 0%, rgba(247, 248, 249, 1) 41px, #f7f8f9 100%);

                            .btn {
                                border-radius: 12px;
                                width: 72px;
                                line-height: 24px;
                                padding: 0;
                                border: none;

                                &.apply {
                                    color: $color-white;
                                    background: $liner-gradient-primary;
                                }

                                &.applied {
                                    display: none;
                                    color: $color-white;
                                    background: #d7d7d7;
                                }
                            }
                        }

                        &:hover {
                            transform: translateY(-5px);
                            box-shadow: 0 0 0 1px $color-primary, $box-shadow-default;
                        }

                        &.is-apply {
                            &:hover {
                                .btn {
                                    &.applied {
                                        display: block;
                                    }
                                }
                            }
                        }

                        &.is-top {
                            background: url(../assets/activity/special/diamond-bg.png) no-repeat right 10px top 47px/107px 82px, $color-white;

                            .diamond {
                                display: block;
                                position: absolute;
                                width: 22px;
                                height: 17px;
                                top: -6px;
                                right: -9px;
                                background: url(../assets/activity/special/diamond.png) no-repeat center/ 22px 17px;
                            }
                        }

                        .diamond {
                            display: none;
                        }
                    }
                }

                .tips {
                    margin-bottom: 20px;
                    text-align: center;
                    color: $font-color-label;
                }

                .empty {
                    display: none;
                    border-radius: 12px;
                    padding: 285px 15px 60px;
                    text-align: center;
                    border: 2px solid rgba(255, 255, 255, 0.24);
                    background: url(../assets/activity/common/empty.png) no-repeat center 61px/266px 194px, rgba(255, 255, 255, 0.4);

                    &.show {
                        display: block;
                    }
                }
            }
        }

        .apply-way-pane,
        .review-pane {
            @include common-wrapper;
        }
    }
}

#welfareDialog {
    display: none;

    .el-dialog {
        width: 568px;
        background: linear-gradient(180deg, #dfe9fe, #ffffff, #ffffff);
        border-radius: 10px;
        padding: 0 8px;

        &__header {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 30px 30px 20px;
        }

        &__body {
            color: $font-color;
            line-height: 1.5;
            padding: 0 23px 0 30px;
            height: 280px;
            max-height: 280px;
            overflow-y: auto;

            &::-webkit-scrollbar-thumb {
                background: #5f8ff9;
            }

            &::-webkit-scrollbar {
                width: 6px;
            }

            .welfare-wrapper {
                & + .welfare-wrapper {
                    margin-top: 15px;
                }

                .welfare-title {
                    position: relative;
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 8px;
                    line-height: 1.5;
                    padding-left: 13px;

                    &::before {
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 9px;
                        width: 6px;
                        height: 6px;
                        background: $color-primary;
                        border-radius: 50%;
                    }
                }

                .welfare-content {
                    line-height: 21px;
                }
            }
        }

        &__footer {
            padding: 15px 30px 30px;
            display: flex;
            justify-content: center;

            .close-dialog {
                font-size: 16px;
                font-weight: bold;
                width: 320px;
                height: 40px;
                background: $liner-gradient-primary;
                border-radius: 4px;
                text-align: center;
                border: none;
                color: $color-white;
            }
        }
    }
}

.company-select {
    --el-color-primary: #{$color-primary};
}
