// 内容搜索

@import './variables';
@import './common/sideBar.scss';
@import './gather.scss';

@import './common/component/adRecommendCompany';

$color-text: #fa635c;
$color-bg: #f4f9ff;
$color-bg-current: #fff3e0;
$border-color-bottom: #eaeaea;

@mixin establishment {
    .establishment-tag {
        display: inline-block;
        padding: 0 10px;
        height: 20px;
        line-height: 20px;
        border-radius: 4px;
        background-color: #f0ffdc;
        color: #4fbc67;
        font-size: 12px;
        margin-right: 6px;
    }
}

.recommend-company-content {
    background-color: #fff;
    padding: 20px 0;

    .recommend-company {
        max-width: 1200px;
        margin: 0 auto;
    }
}

.search-header__container {
    padding-top: 20px;
    background-color: #fff;
}

.everyday-gather-container {
    margin: 20px auto 10px;
    width: $view-width;

    $search-tab-height: 43px;

    .search-tab {
        height: $search-tab-height;
        background: linear-gradient(179deg, rgba(253, 240, 218, 0.8), rgba(255, 255, 255, 0.8));

        .el-radio-group {
            display: flex;
        }

        .el-radio {
            margin: 0;
            padding: 0 32px 0 16px;
            height: $search-tab-height;
            color: $font-color-basic;

            .el-radio__label {
                font-size: 18px;
            }

            .el-radio__inner {
                display: none;
            }

            &.is-checked {
                font-weight: bold;
                background: url(../assets/search/search-tab-1.png) no-repeat top left / contain;
            }

            &:last-of-type {
                flex: 1;

                &.is-checked {
                    background-image: url(../assets/search/search-tab-2.png);
                }
            }
        }
    }

    .search-content {
        padding: 0 20px;
    }

    .search {
        align-items: center;
        margin: 20px 0 15px;
        width: 100%;
        height: 46px;
        background-color: $color-white;
        border-radius: 23px;
        border: 2px solid $color-primary;
        @include utils-ellipsis;
    }

    .job-input {
        flex: 1;
        padding: 0 20px;
        border: none;
        outline: none;
        border-left: 1px solid rgba($font-color, 0.2);
    }

    .search-icon {
        width: 93px;
        height: 100%;
        background: $color-primary url('../assets/icon/search-white.png') no-repeat center center / 20px;
    }

    .hot-search {
        margin-bottom: 23px;
        font-size: 12px;
        color: $font-color-tips;

        .hot-job {
            display: inline-block;
            height: 22px;
            line-height: 22px;
            margin-bottom: 5px;
            padding: 0 7px;
            color: $font-color-label;
            border-radius: 4px;

            &:hover {
                color: $font-color-label;
                background: #daebff;
            }
        }
    }

    .current-state {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 3px;
    }

    .exhibition-quantity {
        color: $font-color-basic;
    }

    .amount {
        color: $color-text;
    }

    .sort {
        display: inline-block;
        position: relative;
        padding: 0 23px 0 12px;
        height: 30px;
        line-height: 30px;
        background-color: $color-bg;
        border-radius: 4px;
        cursor: pointer;

        &::after {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            top: 6px;
            right: 9px;
            border-width: 4px;
            border-style: solid;
            border-color: transparent transparent rgba($font-color-basic, 0.4) transparent;
        }

        &::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            bottom: 6px;
            right: 9px;
            border-width: 4px;
            border-style: solid;
            border-color: rgba($font-color-basic, 0.4) transparent transparent transparent;
        }

        &:first-child {
            border-radius: 4px 0px 0px 4px;
        }

        &:last-child {
            border-radius: 0px 4px 4px 0px;
        }
    }

    .ascending {
        color: $color-primary;
        background-color: $color-bg-current;

        &::after {
            border-color: transparent transparent $color-primary transparent;
        }

        &::before {
            border-color: rgba($color-primary, 0.4) transparent transparent transparent;
        }
    }

    .descending {
        color: $color-primary;
        background-color: $color-bg-current;

        &::after {
            border-color: transparent transparent rgba($color-primary, 0.4) transparent;
        }

        &::before {
            border-color: $color-primary transparent transparent transparent;
        }
    }

    .recruitment-information-content {
        margin-bottom: 30px;
    }

    .block-content {
        padding: 20px 0 13px;
        border-bottom: 1px solid $border-color-bottom;

        .praise {
            line-height: 22px;
            padding-left: 29px;
            background: url(../assets/common/praise.png) no-repeat left/26px 22px;
            color: #9f6d1a;
            max-width: 549px;
            @include utils-ellipsis;
        }
    }

    @include utils-offline-mark();

    .title {
        margin-bottom: 10px;
        font-size: 16px;

        .belonging-school {
            color: $color-text;
        }
    }

    .recruitment-information {
        display: flex;
        justify-content: space-between;
        padding-top: 10px;

        .top-information {
            display: flex;

            @include establishment;
        }
    }

    .recruitment-describe {
        margin-right: 6px;
        padding: 4px 8px;
        font-size: 12px;
        color: $font-color-basic;
        background-color: $color-bg;
        border-radius: 4px;
    }

    .column {
        color: $font-color-label;
        padding-right: 20px;

        &:last-child {
            padding-right: 0;
        }
    }

    .quantity {
        padding-left: 18px;
        background: url('../assets/home/<USER>') no-repeat left center / 13px;
    }

    .time {
        padding-left: 18px;
        background: url('../assets/home/<USER>') no-repeat left center / 13px;
    }

    .daily-topic {
        margin-bottom: 10px;
        padding: 20px;
        background-color: $color-white;
    }

    .newest-daily {
        padding-bottom: 30px;

        li {
            padding-bottom: 13px;

            &:last-child {
                padding-bottom: 0;
            }
        }

        .daily-link {
            position: relative;
            padding-left: 13px;

            &::after {
                content: '';
                position: absolute;
                width: 4px;
                height: 4px;
                top: 50%;
                transform: translateY(-50%);
                left: 0;
                border-radius: 50%;
                background-color: $color-primary;
            }
        }
    }

    .national-examination {
        padding: 13px 10px 20px 10px;

        .main-link {
            width: 285px;
            height: 167px;
        }
    }

    .school-picture {
        margin-bottom: 10px;

        a {
            width: 345px;
            height: 110px;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.el-input--small .el-input__inner {
    padding-left: 0;
    border: none;
}

.el-form-item {
    margin: 0 !important;
}

.el-input--small {
    padding-left: 21px;
}

.el-input__suffix {
    right: 5px;
}

.el-pagination {
    text-align: center;
}

.no-result {
    padding-top: 50px;
    color: $font-color;
    font-size: 16px;
    background: url(../assets/search/no-result-header.png) no-repeat top center / contain;

    ul {
        padding: 0 20px;
    }

    li {
        display: flex;
        align-items: center;
        height: 45px;

        span {
            margin-right: 10px;
            width: 46px;
            height: 23px;
            color: $color-white;
            font-weight: bold;
            font-style: italic;
            text-align: center;
            line-height: 24px;
            background: url(../assets/search/hot-recommend-4.png) no-repeat center / contain;
        }

        $values: 1 2 3;

        @each $val in $values {
            &:nth-of-type(#{$val}) {
                span {
                    padding-left: 24px;
                    padding-right: 12px;
                    line-height: 28px;
                    background-image: url(../assets/search/hot-recommend-#{$val}.png);
                }

                a {
                    padding-top: 4px;
                }
            }
        }

        a {
            flex: 1;
            font-size: 16px;
            line-height: 1;
            @include utils-ellipsis;
        }
    }
}
