.nav-free-vip-enter {
    background-color: $color-white;
    cursor: pointer;

    .nav-free-vip-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            font-size: 13px;
            padding-right: 30px;
            color: $font-color;
            background: url(../assets/home/<USER>/28px auto;
        }
        .regulate {
            font-size: 12px;
            color: $font-color-label;
            padding-right: 15px;
            background: url(../assets/icon/question.png) no-repeat right center/10px 10px;
        }
    }

    .nav-free-vip-footer {
        color: $font-color-label;
        font-size: 12px;

        .invite {
            color: $color-primary;
        }
    }
}

.free-vip-share-dialog,
.free-vip-regulate-dialog {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    margin: 0;
}

.free-vip-share-dialog {
    width: 360px;
    padding: 28px 20px 30px;

    .el-dialog__header,
    .el-dialog__body {
        color: $font-color;
        padding: 0;
    }

    .main-container {
        display: flex;
        flex-direction: column;
        align-items: center;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .qr-code {
            width: 180px;
            height: 180px;
            object-fit: contain;
            margin-bottom: 36px;
        }

        .step {
            display: flex;
            width: 100%;
            padding: 45px 40px 0;
            background: url(../assets/share/title.png) no-repeat center top/319px 17px;
            justify-content: space-between;

            $icon: scan, save, send;

            .item {
                width: 60px;
                text-align: center;
                padding-top: 38px;
                font-size: 12px;

                @each $i in $icon {
                    &.#{$i} {
                        background: url(../assets/share/#{$i}.png) no-repeat center top/30px 30px;
                    }
                }
            }
        }
    }
}

.free-vip-regulate-dialog {
    width: 568px;

    .el-dialog__header,
    .el-dialog__body {
        color: $font-color;
        padding: 0;
    }

    .main-container {
        padding: 30px 30px 36px;

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }

        .wrapper {
            & + .wrapper {
                margin-top: 26px;
            }

            .name {
                font-size: 16px;
                font-weight: bold;
                display: flex;
                align-items: center;
                margin-bottom: 8px;

                &:before {
                    content: '';
                    width: 4px;
                    height: 14px;
                    background: $color-primary;
                    border-radius: 2px;
                    margin-right: 6px;
                }
            }

            .content {
                font-size: 14px;
                line-height: 24px;

                .special {
                    color: #fa635c;
                }

                .bold {
                    font-weight: bold;
                }
            }
        }
    }
}
