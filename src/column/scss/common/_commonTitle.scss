// 公用标题

@import '../variables';

$border-color-title: #efefef;

.common-title {
    position: relative;
    padding: 10px 0 18px 0;
    border-top: 1px solid $border-color-title;

    &::after {
        content: '';
        position: absolute;
        top: -1px;
        left: 0;
        width: 72px;
        height: 3px;
        background-color: $color-primary;
    }

    h2 {
        color: $color-primary;
        font-size: 18px;
    }

    .subtitle {
        padding-left: 28px;
        background: url('../assets/home/<USER>') no-repeat center left / 24px;
    }
}
