// 侧边栏

@import '../variables';

$tools-link-list: home, resume, invite, delivery, chat, collect, news;

.sidebar-container {
    position: fixed;
    top: 50%;
    right: 10px;
    width: 43px;
    transform: translateY(-40%);
    text-align: center;

    z-index: 999;

    .guild-part {
        margin-bottom: 20px;
        background-color: $color-white;
        box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.2);
        border-radius: 24px;
        padding: 20px 0;
    }

    .status-part {
        background-color: $color-white;
        box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.2);
        border-radius: 24px;
        padding: 20px 0;
        font-size: 12px;
    }

    .tools-link {
        display: block;
        position: relative;
        margin-bottom: 20px;
        padding-top: 30px;
        font-size: 12px;
        background-size: 22px;
        background-repeat: no-repeat;
        background-position: top;

        &:last-child {
            margin-bottom: 0;
        }

        &:hover {
            color: $color-primary;
        }

        @each $item in $tools-link-list {
            &.is-#{$item} {
                background-image: url(../assets/home/<USER>

                &:hover {
                    background-image: url(../assets/home/<USER>
                }
            }
        }
    }

    .badge {
        position: absolute;
        top: -10px;
        right: 0;
        padding: 0 3px;
        height: 14px;
        color: $color-white;
        font-size: 11px;
        font-weight: normal;
        line-height: 14px;
        white-space: nowrap;
        background-color: $font-color-salary;
        border-radius: 10px;
    }

    .link-icon {
        display: block;
        font-size: 12px;
        padding-top: 27px;
        margin-bottom: 20px;
        background-position: top;
        background-size: 20px;
        background-repeat: no-repeat;

        &:last-child {
            margin-bottom: 0;
        }

        &:hover {
            color: $color-primary;
        }
    }
    .is-home {
        position: relative;

        .icon {
            position: absolute;
            right: 0;
            bottom: 100%;
            width: 27px;
            height: 15px;
            background: url(../assets/home/<USER>/contain;
            animation: anmit-rotate 0.5s 2 1s;
            -webkit-animation: anmit-rotate 0.5s 2 1s;
        }

        @keyframes anmit-rotate {
            0% {
                transform: rotate(0deg);
            }

            25% {
                transform: rotate(15deg);
            }

            50% {
                transform: rotate(0deg);
            }

            75% {
                transform: rotate(-15deg);
            }

            100% {
                transform: rotate(0deg);
            }
        }

        @-webkit-keyframes anmit-rotate {
            0% {
                transform: rotate(0deg);
            }

            25% {
                transform: rotate(15deg);
            }

            50% {
                transform: rotate(0deg);
            }

            75% {
                transform: rotate(-15deg);
            }

            100% {
                transform: rotate(0deg);
            }
        }
    }

    // .phone {
    //     position: relative;
    //     background-image: url("../assets/home/<USER>");

    //     &:hover {
    //         background-image: url("../assets/home/<USER>");

    //         .phone-hover {
    //             display: flex;
    //         }
    //     }
    // }

    // .phone-hover {
    //     display: none;
    //     position: absolute;
    //     align-items: center;
    //     padding: 0 28px;
    //     width: 220px;
    //     height: 80px;
    //     text-align: left;
    //     top: 50%;
    //     transform: translateY(-50%);
    //     right: 50px;
    //     background-color: $color-white;
    //     box-shadow: 0px 4px 10px 0px rgba(102, 102, 102, 0.06);

    //     &::after {
    //         position: absolute;
    //         content: "";
    //         width: 0;
    //         height: 0;
    //         border-width: 8px;
    //         border-style: solid;
    //         border-color: transparent transparent transparent $color-white;
    //         right: -16px;
    //     }
    // }

    // .phone-icon {
    //     margin-right: 15px;
    //     width: 46px;
    //     height: 46px;
    //     background: url("../assets/home/<USER>") no-repeat center /
    //         cover;
    // }

    // .qq {
    //     background-image: url("../assets/home/<USER>");

    //     &:hover {
    //         background-image: url("../assets/home/<USER>");
    //     }
    // }

    .landline {
        padding-bottom: 8px;
    }

    .mobile {
        position: relative;
        background-image: url('../assets/home/<USER>');

        &:hover {
            background-image: url('../assets/home/<USER>');

            .mobile-hover {
                display: block;
            }
        }
    }

    .mobile-hover {
        display: none;
        position: absolute;
        width: 154px;
        height: 166px;
        top: 50%;
        transform: translateY(-50%);
        right: 60px;
        padding-top: 139px;
        font-size: 13px;
        color: $font-color;
        border-radius: 10px;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 120px;
        background-image: url($qrcode-mobile);
        box-sizing: border-box;
        box-shadow: var(--el-box-shadow-light);

        &::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-white;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .feedback-link {
        display: block;
        padding-top: 30px;

        font-size: 12px;

        background: url(../assets/common/feedback.png) no-repeat center top / 22px;

        &:hover {
            color: #ffa000;
            background-image: url(../assets/common/feedback-hover.png);
        }
    }

    .weixin {
        position: relative;
        background-size: 22px;
        padding-top: 30px;
        // background-image: url($qrcode-mpweixin);
        background-image: url('../assets/home/<USER>');
        background-position: top;

        &:hover {
            // background-image: url($qrcode-mpweixin);
            background-image: url('../assets/home/<USER>');
            background-position: top;

            .weixin-hover {
                display: block;
            }
        }
    }

    .weixin-hover {
        display: none;
        position: absolute;
        width: 154px;
        height: 166px;
        top: 50%;
        transform: translateY(-50%);
        right: 60px;
        padding-top: 139px;
        font-size: 13px;
        color: $font-color;
        border-radius: 10px;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 120px;
        background-image: url($qrcode-mpweixin);
        box-sizing: border-box;
        box-shadow: var(--el-box-shadow-light);

        &::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-white;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .miniapp {
        position: relative;
        background-size: 22px;
        padding-top: 30px;
        // background-image: url($qrcode-miniapp-sidebar);
        background-image: url('../assets/home/<USER>');

        &:hover {
            // background-image: url($qrcode-miniapp-sidebar);
            background-image: url('../assets/home/<USER>');

            .miniapp-hover {
                display: block;
            }
        }
    }

    .miniapp-hover {
        display: none;
        position: absolute;
        width: 154px;
        height: 166px;
        top: 50%;
        transform: translateY(-50%);
        right: 60px;
        padding-top: 139px;
        font-size: 13px;
        color: $font-color;
        border-radius: 10px;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 120px;
        background-image: url($qrcode-miniapp-sidebar);
        box-sizing: border-box;
        box-shadow: var(--el-box-shadow-light);

        &::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-white;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }

    .weibo {
        position: relative;
        background-image: url('../assets/home/<USER>');

        &:hover {
            background-image: url('../assets/home/<USER>');

            .weibo-hover {
                display: block;
            }
        }
    }

    .weibo-hover {
        display: none;
        position: absolute;
        width: 154px;
        height: 166px;
        top: 50%;
        transform: translateY(-50%);
        right: 60px;
        padding-top: 139px;
        font-size: 13px;
        color: $font-color;
        border-radius: 10px;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 120px;
        background-image: url($qrcode-weibo);
        box-sizing: border-box;

        &::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-white;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.feedback {
    .link-icon {
        display: block;
        font-size: 12px;
        padding-top: 40px;
        background-position: top;
        background-size: 35px;
        background-repeat: no-repeat;
    }
    .weixin {
        font-size: 12px;
        position: relative;
        background-image: url($qrcode-mpweixin);
        // background-image: url('../assets/home/<USER>');

        &:hover {
            color: #ffa000;
            background-image: url($qrcode-mpweixin);
            // background-image: url('../assets/home/<USER>');

            .weixin-hover {
                display: block;
            }
        }
    }
    .weixin-hover {
        display: none;
        position: absolute;
        width: 154px;
        height: 166px;
        top: 50%;
        transform: translateY(-50%);
        right: 60px;
        padding-top: 139px;
        font-size: 13px;
        color: $font-color;
        border-radius: 10px;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 120px;
        background-image: url($qrcode-mpweixin);
        box-sizing: border-box;

        &::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-white;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

// 博士后栏目样式
.postdoctor-container {
    .sidebar-container {
        @import '../variablesPostdoctor';

        .tools-link {
            &:hover {
                color: $color-primary;
            }

            @each $item in $tools-link-list {
                &.is-#{$item} {
                    background-image: url(//img.gaoxiaojob.com/uploads/boshihou/sidebar/#{$item}.png);

                    &:hover {
                        background-image: url(//img.gaoxiaojob.com/uploads/boshihou/sidebar/#{$item}-hover.png);
                    }
                }
            }
        }

        .link-icon {
            &:hover {
                color: $color-primary;
            }
        }

        .weixin-hover {
            background-image: url(//img.gaoxiaojob.com/uploads/boshihou/sidebar/postdoctor-qrcode.jpg);
        }

        $link-icon: miniapp, weixin, mobile, feedback-link;

        @each $item in $link-icon {
            .#{$item} {
                &:hover {
                    background-image: url(//img.gaoxiaojob.com/uploads/boshihou/sidebar/#{$item}-hover.png);
                }
            }
        }
    }
}
