// 职位附件列表

@import '../variables';

$file-type: (
    word: #eff2ff,
    excel: #e6fef2,
    pdf: #ffeceb,
    zip: #e6fef2,
    ppt: #ffeceb,
    jpg: #eff2ff,
    txt: #fff1d9,
    common: #fff1d9
);

.file-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;

    .file {
        display: flex;
        align-items: center;
        width: calc((100% - 15px) / 2);
        height: 40px;
        margin: 10px 0;
        border-radius: 4px;
        text-decoration: none;
        background: #fafafc;

        &:nth-of-type(2n) {
            margin-left: 15px;
        }

        // 遍历是为key,value in Array
        @each $name, $color in $file-type {
            .#{$name} {
                width: 40px;
                height: 40px;
                border-radius: 4px 0px 0px 4px;
                opacity: 0.6;
                background: $color url(../assets/icon/#{$name}.png) no-repeat center / 30px;
            }
        }

        .file-name {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 85%;
            margin-left: 8px;
            font-size: 14px;
        }
    }
}
