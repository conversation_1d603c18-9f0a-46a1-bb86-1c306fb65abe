// 栏目页公用导航栏

@import '../variables';

$border-color-bottom: #d4d4d4;

.fixed-tool {
    position: fixed;
    top: 510px;
    left: 50%;
    width: $view-width;
    transform: translateX(-50%);

    ul {
        position: absolute;
        left: -80px;
        background-color: $color-white;

        li {
            padding: 9px 10px;
            text-align: center;
            font-size: 12px;
            color: $font-color-basic;
            border-bottom: 1px dashed $border-color-bottom;
            cursor: pointer;
        }
    }

    .current {
        color: $color-white;
        background-color: $color-primary;
    }

    // 省区，二级栏目侧边导航栏
    .addition {
        li {
            padding: 9px 23px;
        }
    }
}
