@import '../variables';

#custom-popper {
    left: calc(0px - 100%);
    position: fixed;
    background: var(--el-color-white);
    border: 1px solid var(--el-border-color-light);
    max-width: 250px;
    border-radius: 4px;
    color: $font-color;
    padding: 5px 11px;
    z-index: 1800;
    font-size: 12px;
    line-height: 20px;
    min-width: 10px;
    word-wrap: break-word;
    visibility: visible;
    box-shadow: var(--el-box-shadow-light);

    .arrow {
        position: absolute;
        top: calc(50% - 5px);
        right: -5px;
        width: 10px;
        height: 10px;
        z-index: -1;
        &:before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            border: 1px solid $color-white;
            border-left-color: transparent;
            border-bottom-color: transparent;
            transform: rotate(45deg);
            background: $color-white;
            box-sizing: border-box;
        }
    }
}
