// 头部
@import '../variables';

$color-bg-hover: #fff6e6;
$border-color-hover: #ffe3b3;
$color-bg-search: #5c5c5c;
$border-color-solid: #d8d8d8;

.page-header-container {
    a {
        &:hover {
            color: $color-primary;
        }
    }

    .pagetop {
        position: relative;
        width: 100%;
        height: $page-header-height;
        line-height: $page-header-height;
        background-color: $color-white;
        z-index: $header-index;

        &.is-login {
            .pagetop-container {
                display: none;
            }

            .page-login-container {
                display: flex;
            }
        }
    }

    // 未登录
    .pagetop-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 auto;
        width: $view-width;
        height: 100%;
    }

    // 已登录
    .page-login-container {
        display: none;
        justify-content: space-between;
        margin: 0 auto;
        width: $view-width;
        height: 100%;
    }

    // 头部左边部分
    .left-content {
        display: flex;
        align-items: center;

        // logo
        .logo {
            margin-right: 20px;
            width: $logo-width;
            height: $logo-height;
            background: url($logo-home) no-repeat center / contain;
        }

        // 信息平台
        .platform-news {
            margin-left: 20px;
            margin-right: 7px;
            font-size: 12px;
            color: $font-color-basic;
        }

        .switch-container {
            display: flex;
            cursor: pointer;
        }

        // 全国
        .countries {
            margin-right: 7px;
            padding-left: 13px;
            color: $color-primary;
            background: url('../assets/home/<USER>') no-repeat left center;
            background-size: 12px 16px;
        }

        // 切换
        .switch {
            position: relative;
            padding: 0 !important;
            font-size: 12px;
            font-weight: 500 !important;
            color: $font-color-basic;
        }

        // 鼠标点击顶部(切换)展开地区
        .countries-city {
            position: absolute;
            display: flex;
            flex-wrap: wrap;
            padding: 4px 20px 20px;
            width: 480px;
            top: $page-header-height;
            left: -62px;
            background-color: $color-white;
            box-shadow: 0px 4px 10px 0px rgba(102, 102, 102, 0.1);
            border-radius: 4px;
            z-index: $header-index;

            a {
                margin: 16px 16px 0 0;
                width: 60px;
                height: 32px;
                text-align: center;
                line-height: 32px;
                color: $font-color;
                border: 1px solid $border-color;
                border-radius: 4px;
                @include utils-ellipsis;
            }

            & > a:nth-child(6n) {
                margin-right: 0;
            }

            & > a:hover {
                color: $color-primary;
                background-color: $color-bg-hover;
                border: $border-color-hover;
            }

            .current-city {
                color: $color-primary;
                background-color: $color-bg-hover;
                border: $border-color-hover;
            }
        }

        .job-part {
            display: flex;
            align-items: center;
        }

        // 栏目页左边公告&简章,职位,单位
        .job-link {
            padding: 0 10px;
            font-weight: bold;
        }
        .vip {
            position: relative;
            a {
                .arrow {
                    display: inline-block;
                    margin-left: 10px;
                    width: 11px;
                    height: 6px;
                    vertical-align: middle;
                    background: url('../assets/home/<USER>') no-repeat center / cover;
                }
                // .artifact {
                //     position: absolute;
                //     width: 32px;
                //     height: 14px;
                //     background: url(../assets/home/<USER>/contain;
                //     top: 2px;
                //     left: 68px;
                // }
            }
            .artifact {
                width: 32px;
                height: 14px;
                background: #fa635c;
                border-radius: 7px 0px 7px 0px;
                font-size: 12px;
                line-height: 14px;
                text-align: center;
                display: block;
                position: absolute;
                font-family: Source Han Sans CN;
                // font-weight: 400;
                color: #ffffff;
                top: 4px;
                left: 68px;
                font-weight: normal;
            }
            &:hover {
                .arrow {
                    background: url('../assets/home/<USER>') no-repeat center / cover;
                }
                .more {
                    display: block;
                }
            }
            .more {
                display: none;
                position: absolute;
                padding: 16px 20px;
                left: 3px;
                top: 45px;
                z-index: 999;
                background: #ffffff;
                box-shadow: 0px 4px 10px 3px rgba(51, 51, 51, 0.09);
                border-radius: 8px;
                &::after {
                    content: '';
                    position: absolute;
                    top: -20px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 10px;
                    height: 10px;
                    border-width: 10px;
                    border-style: solid;
                    border-color: transparent transparent $color-white transparent;
                }
                a {
                    margin-bottom: 26px;
                    padding: 0;
                    font-size: 14px;
                    line-height: 14px;
                    font-weight: 400;
                    color: $color-black;
                    background-color: inherit;
                    white-space: nowrap;

                    &:last-child {
                        margin-bottom: 4px;
                    }

                    &:hover {
                        color: $color-primary;
                    }
                }
            }
        }
        // vip
        .gaocai-vip {
            border-radius: 8px 8px 8px 0px;
            background-color: #fa635c;
            color: #fff !important;
            padding: 0px 6px;
            font-size: 12px;
            margin-left: 5px;
        }

        .job-link__tool {
            position: relative;

            &:hover {
                .job-link__tool-list {
                    display: flex;
                }
            }

            &-list {
                position: absolute;
                left: 50%;

                display: none;
                padding: 20px 0;
                line-height: 1;
                background: $color-white;
                box-shadow: 0px 4px 10px 3px rgba(51, 51, 51, 0.09);
                border-radius: 8px;
                z-index: $header-index;
                transform: translateX(-50%);

                &::before {
                    content: '';
                    position: absolute;
                    top: -20px;
                    left: 50%;
                    transform: translateX(-50%);
                    border-width: 10px;
                    border-style: solid;
                    border-color: transparent transparent $color-white transparent;
                }
            }

            &-item {
                display: flex;
                flex-direction: column;
                padding: 0 20px;
                min-width: 150px;
                white-space: nowrap;
                border-left: 1px dashed #ebebeb;

                &:first-of-type {
                    border: none;
                }

                .tool-item__title {
                    margin-bottom: 15px;
                    padding-left: 20px;
                    font-size: 16px;
                    background: url(../assets/icon/title.png) no-repeat left center / 14px 12px;
                }

                a {
                    margin-left: 20px;
                    color: $color-black;
                    font-weight: normal;

                    &:hover {
                        color: $color-primary;
                    }

                    & + a {
                        margin-top: 15px;
                    }
                }
            }
        }
    }

    // 头部右边部分
    .right-content {
        display: flex;
        align-items: center;

        // 登录后信息提示
        .message {
            margin-right: 30px;
            width: 20px;
            height: 20px;
            user-select: none;
            // background-repeat: no-repeat;
            // background-size: contain;
            // background-position: center;

            .el-badge {
                display: block;
            }

            .el-icon-bell {
                display: block;
                background-image: url('../assets/home/<USER>');
            }
        }

        // 登录后个人头像
        .personal {
            width: 28px;
            height: 28px;
            border-radius: 50%;
        }

        // 登录后个人头像
        .personal img {
            border-radius: 50%;
        }

        // 页面顶部职位下拉
        .search {
            display: flex;
            align-items: center;
            margin-right: 20px;
            padding-right: 11px;
            width: 202px;
            max-height: 26px;
            border: 1px solid $border-color-solid;
            border-radius: 16px;
            @include utils-ellipsis;

            .main-part {
                width: 60px;
                background-color: $color-bg-search;
            }

            .el-select .el-input__inner {
                padding-right: 0;
            }

            .el-input--small .el-input__inner {
                padding-left: 0;
                height: 26px;
                border: none;
                color: $color-white;
                background-color: $color-bg-search;
            }

            .el-form-item {
                margin: 0;
            }

            .el-input--small {
                padding-left: 12px;
                line-height: 26px;
                font-size: 12px;
            }

            .el-input__suffix {
                right: 0;
            }

            .el-input__icon {
                line-height: 26px;
            }

            input {
                width: 42px;
                flex: 1;
                padding-left: 12px;
                outline: none;
                border: none;

                &::placeholder {
                    color: $font-color-tips;
                }
            }

            a {
                margin: 7px 0;
                width: 16px;
                height: 16px;
                background: url('../assets/home/<USER>') no-repeat center / cover;
            }
        }

        // 申请发布职位
        .application-release {
            margin-right: 20px;
            padding-left: 23px;
            background: url('../assets/home/<USER>') no-repeat left center / 20px;
        }

        // 我要招聘
        .recruit {
            margin-right: 20px;
            padding-left: 23px;
            background: url('../assets/home/<USER>') no-repeat left center / 20px;
        }

        // 求职者登录
        .job-seekers {
            padding-left: 23px;
            background: url('../assets/home/<USER>') no-repeat left center / 20px;
        }

        // 分隔线
        .line {
            margin: 0 5px;
        }
    }

    .header-account-template {
        display: flex;
        align-items: center;
    }

    .header-account-dropdown {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: default;
        position: relative;

        .el-avatar {
            margin-right: 7px;
        }
        .vip-logo {
            position: absolute;
            bottom: 0px;
            right: 8px;
            width: 17px;
            height: 13px;
            background: url(https://img.gaoxiaojob.com/uploads/person/vip-logo.png) no-repeat center/contain;
        }
    }
}

.header-popover-message {
    .message-options {
        margin-bottom: 0;

        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-right: 12px;
            height: 30px;
            color: $font-color;
            font-size: 14px;
            line-height: 1;
            background: url(../assets/icon/gray-arrow.png) no-repeat right center / 7px 12px;
            user-select: none;
        }

        .value {
            color: $font-color-basic;
        }

        .num {
            margin-right: 3px;
            color: $color-primary;
        }
    }
}

.header-dropdown-popper {
    .dropdown-item__card {
        padding: 10px 10px 8px;
        width: 210px;
        height: 88px;
        line-height: 1;
        background: url(../assets/common/header-dropdown.png) no-repeat center / contain;
        box-sizing: border-box;

        &-title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            span {
                flex: none;

                &:first-of-type {
                    padding-right: 41px;
                    color: #74511f;
                    font-size: 18px;
                    font-weight: bold;
                    background: url(../assets/vip/vip-icon.png) no-repeat right center / 39px 15px;
                }

                &:last-of-type {
                    padding: 3px 8px;
                    color: $color-white;
                    font-size: 14px;
                    background: #74511f;
                    border-radius: 10px;
                }
            }
        }

        @mixin util-dropdown-card__item {
            @include utils-ellipsis;

            span {
                color: $font-color-salary;
            }
        }

        &-desc {
            margin-top: 10px;
            color: #74511f;
            font-size: 13px;
            line-height: 16px;

            @include util-dropdown-card__item;
        }

        &-tips {
            margin-top: 15px;
            color: #bb9678;
            font-size: 11px;

            @include util-dropdown-card__item;
        }
    }

    .dropdown-item-cell {
        display: flex;
        flex-direction: column;
        width: 100%;

        &.is-logout {
            padding-top: 10px;
            border-top: 1px solid #ebebeb;
        }

        .name {
            color: $font-color;
            font-size: 14px;
        }

        .person {
            display: flex;
            align-items: center;

            .icon {
                width: 28px;
                height: 15px;
                margin-left: 5px;
                background: url(../assets/home/<USER>/contain;
            }
        }

        .complete {
            margin-left: 3px;
            padding: 0 4px;
            color: $color-white;
            font-size: 12px;
            line-height: 1;
            vertical-align: 1px;
            background-color: $font-color-salary;
            border-radius: 10px;

            &.is-special {
                color: $color-primary;
                background-color: #fff3e0;
            }
        }

        .tips {
            color: $font-color-label;
            font-size: 12px;
        }
    }

    .el-dropdown-menu {
        padding: 7px 0;
    }

    .el-dropdown-menu__item {
        padding: 7px 20px;
        line-height: 22px;

        &:not(.is-disabled):focus,
        &:not(.is-disabled):hover {
            background-color: transparent;

            .name {
                color: $color-primary;
            }
        }
    }
}

.header-login-guide-popup {
    display: none;
    position: absolute;
    padding: 20px 15px 15px;
    width: 375px;
    background: $color-white;
    box-shadow: 0px 4px 10px 3px rgba(51, 51, 51, 0.09);
    border-radius: 8px;
    z-index: $header-index;

    &.animation-guide-jump {
        animation: animation-guide-jump 300ms;
    }

    &::before {
        content: '';
        position: absolute;
        top: -20px;
        right: 28px;
        border-width: 10px;
        border-style: solid;
        border-color: transparent transparent $color-white transparent;
    }

    .title {
        font-size: 16px;
        font-weight: bold;
    }

    .list {
        display: flex;
        flex-wrap: wrap;
    }

    .item {
        margin-top: 18px;
        padding-left: 36px;
        width: 50%;
        font-size: 14px;
        line-height: 30px;
        background-repeat: no-repeat;
        background-size: 30px;
        background-position: left center;

        @for $i from 1 to 5 {
            &:nth-of-type(#{$i}) {
                background-image: url(../assets/common/header/icon-#{$i}.png);
            }
        }
    }

    .el-button {
        display: block;
        margin: 12px auto 0;
        padding: 7px 15px;
        width: 290px;
        font-size: 14px;
        font-weight: bold;
    }
}
