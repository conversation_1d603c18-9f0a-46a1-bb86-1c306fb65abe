// 公告_职位列表 侧边栏
@import '../variables';

aside {
    width: 320px;

    .unit {
        margin-bottom: 20px;
        padding: 0 0 20px 20px;
        background: linear-gradient(180deg, #fff8ee, #ffffff 45%);
        border-radius: 10px;
        // box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);

        .unit-name {
            display: flex;
            align-items: center;
            padding-bottom: 20px;
            background: url(../assets/detail/company-card-bg.png) no-repeat right top / 231px 133px;

            .logo {
                margin-top: 20px;
                margin-right: 20px;
                width: 60px;
                height: 60px;
                object-fit: contain;
                border-radius: 50%;
            }

            .name {
                flex: 1;
                padding-top: 20px;
                padding-right: 20px;
                overflow: hidden;

                h3 {
                    margin-bottom: 10px;
                    font-weight: bold;
                    @include utils-ellipsis-lines(2, 1.5, 16px);
                }

                h5 {
                    font-size: 14px;
                    font-weight: normal;
                    @include utils-ellipsis-lines(2, 1.5);
                }
            }
        }

        .unit-tips {
            margin-bottom: 20px;
            margin-right: 20px;
            padding-left: 30px;
            color: $font-color;
            font-size: 14px;
            line-height: 18px;

            $unit-tips: category number type;

            @each $tip in $unit-tips {
                &.#{$tip} {
                    background: url(../assets/detail/#{$tip}.png) no-repeat left center / 18px;
                }
            }
        }

        .unit-data {
            margin-right: 20px;
            padding: 16px 0;
            line-height: 1;
            background: #fafafc;
            border-radius: 10px;

            .el-col {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border-radius: 4px;

                &:nth-of-type(2) {
                    border-left: 1px solid rgba($color: $font-color, $alpha: 0.1);
                }

                strong {
                    margin-bottom: 10px;
                    font-size: 16px;
                    font-weight: bold;
                }

                span {
                    color: $font-color;
                }

                &.to-home {
                    align-items: flex-end;

                    a {
                        padding: 5px 8px;
                        font-size: 14px;
                        background: linear-gradient(90deg, #eedfd7 0%, #dcc9bb 100%);
                        border-radius: 12px 0px 0px 12px;

                        span {
                            padding-right: 10px;
                            color: #6c4133;
                            background: url(../assets/detail/to-home.png) no-repeat right center / 6px 9px;
                        }
                    }
                }
            }
        }
    }

    .contact {
        margin-bottom: 20px;
        padding: 20px;
        background-color: $color-white;
        box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);
        border-radius: 10px;
        color: $font-color;
        font-size: 16px;
        line-height: 1;
        word-break: break-word;

        .title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            font-weight: bold;

            &::before {
                content: '';
                margin-right: 6px;
                width: 4px;
                height: 16px;
                background-color: $color-primary;
                border-radius: 2px;
            }
        }

        .intro {
            display: flex;
            align-items: center;
        }

        .avatar-content {
            position: relative;

            .online-status {
                position: absolute;
                width: 12px;
                height: 12px;
                background: #4fbc67;
                border-radius: 50%;
                border: 2px solid #ffffff;
                left: 37px;
                top: 37px;
            }

            .avatar {
                margin-right: 12px;
                width: 50px;
                height: 50px;
                border-radius: 50%;
            }
        }

        .box {
            width: 100%;
        }

        .top {
            display: flex;
            align-items: center;
            margin-bottom: 6px;

            .name {
                line-height: 1.5;
                max-width: 140px;
                @include utils-ellipsis;
            }

            .active-time {
                font-size: 12px;
                margin-left: 22px;
                color: $font-color-label;
            }
        }

        .desc {
            color: $font-color-basic;
            font-size: 14px;
            line-height: 21px;
        }

        .chat {
            margin-top: 20px;
            height: 40px;
            color: $color-primary;
            font-weight: bold;
            text-align: center;
            line-height: 40px;
            background: #fff1da;
            border-radius: 4px;
            cursor: pointer;

            span {
                display: inline-block;
                padding-left: 25px;
                line-height: 20px;
                background: url(../assets/common/chat-primary.png) no-repeat left center / 20px;
            }
        }
    }

    .recommend {
        padding: 20px;
        background: $color-white;
        border-radius: 10px;
        box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

            h5 {
                position: relative;
                padding-left: 12px;
                font-size: 16px;
                font-weight: bold;
                line-height: 1;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    width: 4px;
                    background-color: $color-primary;
                    border-radius: 2px;
                }
            }
        }

        .swiper {
            padding-bottom: 20px;

            .swiper-pagination {
                bottom: 0;
            }
        }

        @include utils-swiper-bullet($inactive-color: #e5e5e5, $inactive-opacity: 1);

        .recommend-list {
            display: flex;
            flex-direction: column;
            font-size: 14px;

            .recommend-item {
                padding: 15px 10px;
                line-height: 1;
                background-color: #fafafc;
                border-radius: 4px;

                & + .recommend-item {
                    margin-top: 10px;
                }

                .recommend-item-title {
                    display: flex;
                    justify-content: space-between;

                    .name {
                        @include utils-ellipsis-lines(2, 1.5);
                        margin-bottom: 10px;
                        max-width: 65%;
                        color: $font-color;
                    }

                    span {
                        color: $font-color-salary;
                        line-height: 1.5;
                    }
                }

                .recommend-item-tips {
                    margin-bottom: 15px;
                    color: $font-color-basic;
                    font-size: 12px;

                    span {
                        & + span {
                            margin-left: 10px;
                            padding-left: 10px;
                            border-left: 1px solid $font-color-tips;
                        }
                    }
                }

                .recommend-item-data {
                    display: flex;
                    justify-content: space-between;

                    h6 {
                        margin-right: 10px;
                        max-width: 60%;
                        color: $font-color-basic;
                        font-size: 14px;
                        font-weight: normal;
                        @include utils-ellipsis;
                    }

                    span {
                        padding-left: 16px;
                        color: $font-color-label;
                        font-size: 13px;
                        background: url(../assets/icon/address.png) no-repeat left center / 12px;
                        @include utils-ellipsis;
                    }
                }
            }
        }

        &.is-notice {
            .recommend-list {
                .recommend-item {
                    .recommend-item-title {
                        .name {
                            max-width: 100%;
                        }
                    }

                    .recommend-item-data {
                        display: flex;
                        justify-content: space-between;

                        .tips {
                            color: $font-color-basic;
                            font-size: 12px;

                            i {
                                font-style: normal;

                                & + i {
                                    margin-left: 10px;
                                    padding-left: 10px;
                                    border-left: 1px solid $font-color-tips;
                                }
                            }
                        }

                        span {
                            padding-left: 16px;
                            max-width: 30%;
                            color: $font-color-label;
                            font-size: 13px;
                            background: url(../assets/icon/address.png) no-repeat left center / 12px;
                            @include utils-ellipsis;
                        }
                    }
                }
            }
        }
    }
}
