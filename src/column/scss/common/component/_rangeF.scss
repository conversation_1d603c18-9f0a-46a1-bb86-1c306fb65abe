// f区样式

@import '../../variables';

$border-color: #e5e5e5;
$border-bottom: #d2d2d2;
$bg-color: #f7f7f7;

.f {
    padding: 0 20px;
    background-color: $color-white;

    ul {
        flex-wrap: wrap;
        padding: 20px 0 16px 0;
        border-bottom: 1px solid $border-bottom;

        li {
            margin: 10px 10px 0 0;
            width: 224px;
            height: 82px;
            border: 1px solid $border-color;
            box-sizing: inherit;

            &:nth-child(5n) {
                margin-right: 0;
            }

            p {
                padding: 5px;
                text-align: center;
                width: 100%;
                line-height: 1;
                background-color: $bg-color;
                @include utils-ellipsis;
            }

            .picture {
                margin-top: 7px;
                padding: 0 5px 0;
                height: 49px;
            }
        }
    }
}

@for $i from 1 through 5 {
    .f ul li:nth-of-type(#{$i}) {
        margin-top: 0;
    }
}

.f1 {
    padding: 0 20px;
    background-color: $color-white;

    ul {
        flex-wrap: wrap;
        padding: 16px 0 22px;

        li {
            margin: 10px 10px 0 0;
            width: 185px;
            height: 75px;
            border: 1px solid $border-color;

            &:nth-child(6n) {
                margin-right: 0;
            }

            p {
                padding: 5px;
                width: 100%;
                line-height: 1;
                text-align: center;
                font-size: 12px;
                background-color: $bg-color;
                @include utils-ellipsis;
            }

            .picture {
                margin: 7px 0 4px;
                padding: 0 5px;
                height: 40px;
            }
        }
    }
}

@for $i from 1 through 6 {
    .f1 ul li:nth-of-type(#{$i}) {
        margin-top: 0;
    }
}
