@import '../../variables';

$border-color-title: #efefef;
$border-color-bottom: #d2d2d2;
$border-color-before: #bbb;
$color-bg-arrow: #f4f4f4;
$color-actionTime: #7aa5b9;
$border-color-img: #dcdcdc;
$color-bg-follow: #fd4f5a;
$color-bg-end: #f0f0f0;
$color-bg-hot: #f33;

.hot-topics {
    .job-information {
        padding: 18px 15px 15px 13px;
        border: 1px solid $border-color-title;

        .job-announcement {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            img {
                width: 80px;
                height: 60px;
                margin-right: 8px;
                border-radius: 4px;
            }

            .exhibition {
                margin-right: 8px;
                width: 80px;
                height: 60px;
                border-radius: 4px;
            }

            .meeting-title {
                width: 100%;

                span {
                    line-height: 1.5;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
            }
        }

        .hot-content {
            a {
                position: relative;
                display: flex;
                align-items: center;
                margin-bottom: 14px;
                padding-left: 11px;

                &:last-child {
                    margin-bottom: 0;
                }

                &::after {
                    content: '';
                    position: absolute;
                    top: 5px;
                    left: 0;
                    width: 4px;
                    height: 4px;
                    background-color: $color-primary;
                    border-radius: 50%;
                }

                .hot-text {
                    @include utils-ellipsis;
                }

                .current-hot {
                    display: block;
                    margin-left: 7px;
                    width: 18px;
                    line-height: 18px;
                    text-align: center;
                    color: rgba($color-white, 0.8);
                    background-color: $color-bg-hot;
                    border-radius: 4px;
                    flex-shrink: 0;
                }
            }
        }
    }
}
