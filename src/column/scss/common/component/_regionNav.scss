// 地区导航

@import '../../variables';

.recruitment-city {
    .location {
        display: flex;
        flex-wrap: wrap;
        padding: 20px 20px 6px;
        border: 1px solid $border-color-title;
    }

    .city-link {
        position: relative;
        padding: 0 0 14px 8px;
        width: calc((100% - 38px) / 2);
    }

    .city-link:nth-child(3n) {
        width: 38px;
    }

    .city-link::after {
        position: absolute;
        content: '';
        width: 4px;
        height: 4px;
        background-color: $color-primary;
        border-radius: 50%;
        top: 7px;
        left: 0;
    }
}
