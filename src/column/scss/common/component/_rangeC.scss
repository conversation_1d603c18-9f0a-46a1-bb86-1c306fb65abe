// c区样式

@import '../../variables';

$border-color: #ededed;
$color-icons: #f5f7fa;
$color-circular: #dbdbdb;
$border-color-input: #d4d4d4;

// 共同c区hover样式
.share {
    ul {
        li {
            position: relative;
            border-bottom: 1px solid $border-color;

            &:hover {
                p {
                    display: flex;
                }
            }

            a {
                width: 329px;
                height: 55px;
            }

            p {
                display: none;
                justify-content: space-between;
                align-items: center;
                position: absolute;
                padding: 0 10px;
                width: 100%;
                height: 100%;
                color: $color-primary;
                font-weight: bold;
                background-color: $background-color;
            }

            .text {
                display: block;
                width: 240px;
                @include utils-ellipsis;
            }

            .details {
                position: relative;
                display: block;
                text-align: center;
                width: 65px;
                height: 20px;
                line-height: 20px;
                color: $color-white;
                border-radius: 10px;
                background-color: $color-primary;

                &::after {
                    content: '';
                    position: absolute;
                    margin-left: 2px;
                    width: 0;
                    height: 0;
                    border-bottom: 5px solid transparent;
                    border-top: 5px solid transparent;
                    border-left: 5px solid $color-white;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }

            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.headlines {
    margin: 10px 0;
    background-color: $color-white;

    .headlines-sidebar {
        width: 330px;

        .c1 {
            .c1-banner {
                .c1-pagination {
                    bottom: 5px;
                }

                .swiper-pagination-bullet {
                    width: 8px;
                    height: 8px;
                    background-color: #e5e5e5;
                    opacity: 1;
                }

                .swiper-pagination-bullet-active {
                    background-color: $color-primary;
                }
            }

            a {
                height: 265px;
            }
        }

        .c3 {
            margin-top: 14px;
            border-right: 1px solid $border-color;
        }

        .first {
            border-top: 1px solid #ededed;
        }
    }

    .headlines-middle {
        flex-direction: column;
        padding: 10px 20px 0px;
        width: 540px;

        .headline-content {
            width: 500px;

            .headline-news {
                padding-bottom: 9px;
                border-bottom: 1px solid $border-color;
            }

            h4 {
                font-size: 16px;

                a {
                    margin: 0 auto;
                    @include utils-ellipsis;
                    padding: 8px 0;
                }

                i {
                    display: inline-block;
                    width: 56px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    color: $color-white;
                    background: url('../assets/home/<USER>') no-repeat center / cover;
                }

                .recommend {
                    background: url('../assets/home/<USER>') no-repeat center / cover;
                }

                .first {
                    padding-top: 0;
                }
            }

            p {
                color: $font-color-basic;
                line-height: 1.8;
                display: -webkit-box;
                text-overflow: ellipsis;
                overflow: hidden;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
            }

            ul {
                padding-bottom: 20px;

                li {
                    position: relative;
                    padding-left: 15px;
                    width: 50%;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 0;
                        width: 4px;
                        height: 4px;
                        background-color: $color-primary;
                        border-radius: 50%;
                        transform: translateY(-50%);
                    }

                    a {
                        @include utils-ellipsis;
                    }
                }
            }
        }

        .c4 {
            flex: 1;

            .main-headline {
                background-color: $color-icons;

                ul {
                    li {
                        padding: 10px 18px;
                        line-height: 1;
                        font-size: 16px;
                        cursor: pointer;

                        &:last-child {
                            border-right: none;
                        }
                    }

                    .self {
                        padding-left: 28px;
                        padding-right: 8px;
                        font-weight: bold;
                        border-top: 3px solid $color-primary;
                        color: $color-primary;
                        vertical-align: middle;
                        background: $color-white url('../assets/home/<USER>') no-repeat 7px center / 17px;
                    }
                }
            }

            .main-headline-container {
                .tab-content {
                    display: none;

                    ul {
                        li {
                            padding-top: 13px;
                            line-height: 1.1;

                            a {
                                position: relative;
                                padding-left: 11px;
                                @include utils-ellipsis;

                                &::after {
                                    content: '';
                                    position: absolute;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    left: 0;
                                    width: 4px;
                                    height: 4px;
                                    background-color: $color-primary;
                                    border-radius: 50%;
                                }

                                .main {
                                    margin-right: 8px;
                                    font-weight: bold;
                                }
                            }
                        }
                    }

                    &:first-child {
                        display: block;
                    }
                }

                .paihang {
                    ul {
                        li {
                            display: flex;
                            justify-content: space-between;
                        }
                    }

                    .secondary {
                        flex: none;
                        margin-left: 5px;
                        color: $font-color-basic;
                    }
                }
            }
        }

        .search {
            justify-content: space-between;
            align-items: center;

            .search-content {
                flex: 1;
                margin: 20px 0;
                padding-right: 19px;
                border: 2px solid $color-primary;
                border-radius: 19px;
                @include utils-ellipsis;
            }

            .showcase-link {
                flex: none;
                margin: 0 0 0 10px;
                width: 150px;
                height: 48px;
                background: url(../assets/home/<USER>/ contain;
            }

            .m-2 {
                width: 54px;
            }

            .el-form-item__content {
                line-height: 32px;
            }

            .el-select .el-input__inner {
                padding-right: 0;
            }

            .el-input--small .el-input__inner {
                padding-left: 0;
                border: none;
            }

            .el-form-item {
                margin: 0;
            }

            .el-input--small {
                padding-left: 12px;
            }

            .el-input__suffix {
                right: -6px;
            }

            .input {
                margin: 8px 0 8px 5px;
                flex: 1;
                padding-left: 8px;
                outline: none;
                border: none;
                border-left: 1px solid $border-color-input;

                &::placeholder {
                    color: $font-color-tips;
                }
            }

            a {
                margin: 6px 0;
                width: 20px;
                height: 20px;
                background: url('../assets/home/<USER>') no-repeat center / cover;
            }
        }
    }

    .headlines-ad {
        width: 330px;

        .c2 {
            border-left: 1px solid $border-color;
        }
    }
}

.info-bar {
    margin-bottom: 10px;

    .c5 {
        margin-right: 10px;
        padding: 13px 20px 20px;
        width: 859px;
        background-color: $color-white;

        h4 {
            font-size: 18px;
            color: $color-primary;
            background: url('../assets/home/<USER>') no-repeat 79px center / 46px;
        }

        .info-bar-left-con {
            ul {
                flex-wrap: wrap;
                width: 100%;

                li {
                    padding-top: 13px;
                    width: 49%;
                    line-height: 1.1;

                    a {
                        position: relative;
                        padding-left: 13px;
                        display: flex;

                        &::after {
                            content: '';
                            position: absolute;
                            top: 50%;
                            left: 0;
                            width: 4px;
                            height: 4px;
                            background-color: $color-circular;
                            border-radius: 50%;
                            transform: translateY(-50%);
                        }

                        .main {
                            margin: 0;
                            width: auto;
                            @include utils-ellipsis;
                        }

                        .hot {
                            flex: none;
                            margin-top: -8px;
                            padding-left: 5px;
                            width: 23px;
                            height: 13px;
                            background: url('../assets/home/<USER>') no-repeat center center / cover;
                        }
                    }
                }
            }
        }
    }

    .c2 {
        width: 330px;
        border-left: 1px solid rgba($color-white, 0);
    }
}
