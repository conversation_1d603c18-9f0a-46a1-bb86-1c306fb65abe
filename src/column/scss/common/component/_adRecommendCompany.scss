// 推荐单位
// 一级栏目"最新公告&简章"平铺

@import '../../variables';

$color-hot: #f23b3b;

.recommend-company {
    padding-top: 14px;
    .company-pane {
        .pane {
            // display: none;
            // &.active {
            //     display: block;
            // }
            .el-carousel__container {
                height: 100%;
            }
            .el-carousel__indicators {
                visibility: hidden;
                margin-top: 7px;
            }
            .el-carousel__indicator--horizontal {
                padding: 0;
                &.is-active {
                    .el-carousel__button {
                        width: 20px;
                        background-color: $color-primary;
                    }
                }
            }
            .el-carousel__button {
                margin: 0 4px;
                width: 6px;
                height: 6px;
                background-color: #dedede;
                border-radius: 6px;
                opacity: 1;
            }
            a {
                display: inline-block;
                // padding: 5px;
                border: 1px solid $border-color;
                border-radius: 8px;
                width: calc(((1200px - 70px) / 8));
                height: 100%;
                box-sizing: border-box;
                & + a {
                    margin-left: 10px;
                }
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }
    }
    .recommend-tabs {
        display: flex;
        justify-content: center;
        font-size: 14px;
        margin-top: 12px;
        .label {
            display: flex;
            align-items: center;
            padding-bottom: 3px;
            color: #333;
            span {
                color: $color-white;
                background-color: $color-hot;
                font-size: 12px;
                font-weight: bolder;
                position: relative;
                letter-spacing: 0px;
                padding: 2px 4px;
                border-radius: 4px;
                transform: scale(0.8);
                &::after {
                    position: absolute;
                    content: '';
                    width: 6px;
                    height: 6px;
                    right: -3px;
                    top: 50%;
                    background-color: $color-hot;
                    transform: translateY(-50%) rotate(45deg);
                }
            }
        }
        .tabs {
            position: static;
            width: auto;

            span {
                width: auto;
                height: auto;
                margin: 0 15px;
                padding-bottom: 3px;
                color: $font-color;
                border-radius: unset;
                background: none;
                &.active {
                    color: $color-primary;
                    border-bottom: 2px solid $color-primary;
                }
                &.swiper-pagination-bullet {
                    opacity: 1;
                }
                &.swiper-pagination-bullet-active {
                    color: #ffa000;
                    border-bottom: 2px solid #ffa000;
                }
            }
        }
    }
}
