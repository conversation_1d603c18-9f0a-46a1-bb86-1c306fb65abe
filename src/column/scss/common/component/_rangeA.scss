// a区样式

@import '../../variables';

$background-color-a3: #ffb12e;

#rangeA {
    display: flex;
    margin-bottom: 8px;
    height: 70px;

    .a1 {
        width: 590px;

        .my-swiper {
            .my-pagination {
                bottom: 0px;
            }

            .swiper-pagination-bullet {
                width: 6px;
                height: 6px;
            }

            .swiper-pagination-bullet-active {
                background-color: $color-primary;
            }
        }

        img {
            height: 70px;
        }
    }

    .a2 {
        a {
            width: 295px;
            margin: 0 10px;

            &:last-child {
                margin: 0;
            }
        }
    }
}

.left-loop {
    margin-bottom: 8px;
    background: $color-white url('../assets/home/<USER>') no-repeat 22px 6px / 20px;
    box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);

    .left-marquee {
        margin: 0 20px 0 58px;
        padding: 8px 0;
        width: 1140px;
        @include utils-ellipsis;

        #scrollBegin,
        #scrollEnd {
            a {
                position: relative;
                color: $font-color-basic;
                margin-left: 31px;
                padding-left: 7px;
                padding-right: 40px;

                &::before {
                    content: '';
                    position: absolute;
                    top: 50%;
                    height: 55%;
                    left: 0;
                    width: 2px;
                    background-color: #486cf5;
                    transform: translateY(-50%);
                }

                &:hover {
                    color: $color-primary;

                    &::before {
                        background-color: $color-primary;
                    }
                }
            }
        }
    }

    .a3 {
        padding-top: 6px;
        box-shadow: 0 -1px #ededed;

        ul {
            padding: 0 20px 4px 20px;
            flex-wrap: wrap;

            li {
                width: 280px;
                @include utils-ellipsis;

                a {
                    @include utils-ellipsis;
                    color: $font-color;
                    line-height: 1;

                    .hot {
                        display: inline-block;
                        margin-bottom: 3px;
                        padding: 2px 5px;
                        text-align: center;
                        color: $color-white;
                        font-size: 13px;
                        font-weight: normal;
                        background-color: $background-color-a3;
                        border-radius: 4px;
                    }
                }

                .a-text {
                    margin-right: 3px;
                }

                &:nth-child(3n) {
                    margin-right: 0;
                }
            }
        }
    }
}

.slide-swiper-container {
    position: fixed;
    top: 340px;
    left: 50%;
    width: $view-width;
    transform: translateX(-50%);
    // z-index: -1;

    .swiper {
        width: 120px;
        height: 180px;
        border-radius: 12px 12px 0px 0px;
        @include utils-ellipsis;

        img {
            min-height: 180px;
        }
    }

    .swiper-pagination {
        bottom: 4px;
    }

    .swiper-pagination-bullet {
        width: 5px;
        height: 5px;
    }

    .swiper-pagination-bullet-active {
        background-color: $color-primary;
    }

    // 左侧边栏
    .a4 {
        position: absolute;
        left: -132px;
    }

    // 右侧边栏
    .a5 {
        position: absolute;
        right: -132px;
    }
}
