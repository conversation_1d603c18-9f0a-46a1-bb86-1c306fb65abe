// 导航分类

@import '../../variables';

$border-color-title: #efefef;

.dropnav {
    li {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        border: 1px solid $border-color-title;
        cursor: pointer;

        &:last-child {
            margin-bottom: 0px;
        }

        .currency {
            display: inline-block;
            padding: 16px 0 16px 56px;
            color: $font-color;
            flex: 1;
            background-size: 20px;
            background-repeat: no-repeat;
            background-position: 18px center;
        }

        $list: information province city engineering science economics philosophy literature education medical agronomy other;

        @each $var in $list {
            .#{$var} {
                background-image: url(../assets/home/<USER>
            }
        }

        .icon-arrow {
            display: inline-block;
            margin-right: 24px;
            width: 18px;
            height: 18px;
            background: url('../assets/home/<USER>') no-repeat center / cover;
        }

        .post-nav {
            padding-left: 12px;
        }

        &:hover {
            background-color: $background-color;

            .currency {
                color: $color-primary;
            }

            .icon-arrow {
                background: url('../assets/home/<USER>') no-repeat center / cover;
            }

            .sub-cate {
                display: block;
                z-index: $basic-index;
            }
        }
    }
}

.sub-cate {
    display: none;
    position: absolute;
    padding: 30px 0 30px 30px;
    width: 550px;
    top: 0;
    left: 234px;
    background-color: $color-white;
    border: 1px solid $color-primary;
}

.classify-list {
    padding-bottom: 17px;

    &:last-child {
        padding-bottom: 0;
    }

    .more {
        padding-left: 13px;
        background: url('../assets/home/<USER>') no-repeat center left / 10px;
        color: $color-primary;
    }
}

.classify-title {
    position: relative;
    margin-bottom: 10px;
    padding-left: 9px;
    color: $color-primary;
}

.classify-title::after {
    content: '';
    position: absolute;
    width: 2px;
    height: 12px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: $color-primary;
    border-radius: 2px;
}

.classify {
    display: inline-block !important;
    line-height: 22px;
    font-size: 13px;
    width: auto;
    margin-right: 20px;
}
