// h区样式

@import '../../variables';

$border-color-title: #efefef;
$border-color-bottom: #d2d2d2;
$border-color-hot: #dcdfe6;
$color-number: #aeaeae;
$color-one: #ff3333;
$color-two: #ff9000;
$color-three: #fbb250;
$border-color-img: #e5e5e5;
$border-color-notice: #ebebeb;

.h1 {
    padding: 0 20px;
    background-color: $color-white;

    a {
        height: 60px;
    }
}

.h2 {
    padding: 20px 20px 0px;
    width: 345px;
    background-color: $color-white;

    .right-siderbar-box {
        margin-bottom: 20px;
    }

    .h2-1,
    .h2-3,
    .h2-4,
    .h2-5 {
        a {
            height: 120px;
        }
    }
}

.brand-unit {
    .h2-2 {
        .brand {
            display: flex;

            img {
                margin: 0 19px 19px 0;
                width: 116px;
                height: 90px;
                border: 1px solid $border-color-img;
            }

            .unit-information {
                h6 {
                    margin-bottom: 2px;
                    width: 171px;
                    line-height: 1.8;
                    font-size: inherit;
                    color: $color-primary;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                }

                p {
                    width: 161px;
                    line-height: 1.8;
                    font-size: 12px;
                    color: $font-color-basic;
                    text-overflow: -o-ellipsis-lastline;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    -webkit-box-orient: vertical;
                }
            }
        }
    }

    ul {
        padding-bottom: 15px;

        li {
            padding: 0 0 14px 10px;
            width: 295px;
            @include utils-ellipsis;

            a {
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: -10px;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 4px;
                    background-color: $color-primary;
                    border-radius: 50%;
                }
            }

            &:last-child {
                border-bottom: 1px solid $border-color-bottom;
            }
        }
    }
}

.focus-information {
    .icon-focus {
        position: absolute;
        height: 100%;
        padding-top: 9px;

        li {
            position: relative;
            height: 36px;

            &::after {
                position: absolute;
                content: '';
                width: 6px;
                height: 6px;
                background-color: $color-primary;
                border-radius: 50%;
                left: 0;
                top: 6px;
            }

            &::before {
                position: absolute;
                content: '';
                width: 1px;
                height: 40px;
                border-left: 1px solid $color-primary;
                left: 2.5px;
                top: 6px;
            }

            &:last-child::before {
                display: none;
            }
        }
    }

    #scrollText {
        position: relative;
        height: 893px;
        margin-bottom: 19px;
        @include utils-ellipsis;

        #scroll1,
        #scroll2 {
            ul {
                li {
                    padding-left: 16px;
                    line-height: 36px;
                    overflow: visible;

                    &:last-child {
                        padding-bottom: 0;
                    }

                    a {
                        @include utils-ellipsis;
                    }
                }
            }
        }
    }
}

.recommended-information {
    .sitebox-piclist {
        flex-wrap: wrap;
        width: 100%;
        padding-bottom: 14px;

        li {
            a {
                margin-bottom: 28px;
                width: 144px;
                height: 114px;

                img {
                    border: 1px solid $border-color-notice;
                }

                p {
                    padding-top: 3px;
                    text-align: center;
                    @include utils-ellipsis;
                }
            }
        }
    }

    .textbreak {
        padding-top: 14px;
        border-top: 1px solid $border-color-bottom;

        li {
            padding: 0 0 14px 10px;
            width: 295px;
            @include utils-ellipsis;

            a {
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: -10px;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 4px;
                    background-color: $color-primary;
                    border-radius: 50%;
                }
            }
        }

        .last {
            padding-bottom: 0;
        }
    }
}

.hot-information {
    .dwm-ranking {
        .hb {
            ul {
                padding-bottom: 21px;
                justify-content: center;

                li {
                    padding: 9px 10px;
                    border: 1px solid $border-color-hot;
                    color: $font-color-basic;
                    cursor: pointer;
                }

                .hot1 {
                    border-radius: 4px 0px 0px 4px;
                    border-right: none;
                }

                .hot2 {
                    border-right: none;
                }

                .hot3 {
                    border-radius: 0px 4px 4px 0px;
                }

                .on {
                    color: $color-white;
                    background-color: $color-primary;
                }
            }
        }
    }
}

.dwm-ranking {
    ul {
        li {
            display: flex;
            padding-bottom: 14px;

            &:last-child {
                padding-bottom: 0;
            }

            span {
                display: block;
                width: 22px;
                color: $color-number;
            }

            .one {
                font-weight: bold;
                color: $color-one;
            }

            .two {
                color: $color-two;
            }

            .three {
                color: $color-three;
            }

            a {
                padding-left: 10px;
                width: 280px;
                @include utils-ellipsis;
            }
        }
    }

    .item {
        display: none;
    }
}
