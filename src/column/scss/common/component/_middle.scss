// 置顶信息区域样式

@import '../../variables';

$border-color: #486cf5;
$border-color-title: #efefef;
$border-color-bottom: #d2d2d2;
$border-color-radius: #dbdbdb;
$bg-color-more: #edf1ff;

.middle {
    margin-right: 10px;
    padding: 20px 20px 0;
    width: 560px;
    background-color: $color-white;

    .istop-announcement {
        span {
            color: $font-color;
            font-size: 12px;
            padding-left: 9px;
        }
    }

    .newest {
        margin-top: 30px;
        padding-bottom: 0;
    }

    .istop {
        ul {
            li {
                padding: 9px 0;
                border-bottom: 1px dashed $border-color-bottom;

                &:first-child {
                    padding-top: 0;
                }

                .orange {
                    position: relative;
                    flex-shrink: 0;
                    padding-left: 9px;
                    color: $color-primary;
                }

                .orange::after {
                    position: absolute;
                    content: '';
                    width: 4px;
                    height: 4px;
                    left: 0;
                    top: 7px;
                    background-color: $border-color-radius;
                    border-radius: 50%;
                }

                .city {
                    color: $border-color;
                }

                .announcement {
                    flex-grow: 1;
                    width: 0px;
                    @include utils-ellipsis;
                }

                small {
                    padding-top: 1px;
                    padding-left: 21px;
                    color: $font-color-basic;
                }
            }
        }
    }

    .box {
        .themes {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
        }

        & + .box {
            .themes {
                padding-top: 20px;
            }
        }

        .job-recruit {
            position: relative;
            width: 465px;
            border-top: 1px solid $border-color;

            span {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                padding: 0 5px;
                background-color: $color-white;
                font-size: 16px;
                font-weight: bold;
                color: $border-color;
            }
        }

        .more {
            width: 52px;
            height: 22px;
            text-align: center;
            line-height: 20px;
            background-color: $bg-color-more;
            color: $border-color;
            border: 1px solid $border-color;
            border-radius: 4px;
        }

        .more:hover a {
            color: inherit;
        }
    }
}
