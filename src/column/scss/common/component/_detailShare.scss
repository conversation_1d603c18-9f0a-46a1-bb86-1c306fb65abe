@import '../../variables';

/* 清除默认样式影响iframe以及第三方组件样式 start */
#bsBox,
#bsWXBox,
#bsPanelHolder {
    box-sizing: content-box;
}
/* 清除默认样式影响iframe以及第三方组件样式 end */

.share-custom {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
    border-top: 1px solid #ededed;

    .bshare-custom {
        a {
            display: inline-block;
        }
    }

    .custom-more-btn {
        background: url(http://static.bshare.cn/frame/images/logos/s4/more-style-addthis.png) no-repeat;
    }
}
