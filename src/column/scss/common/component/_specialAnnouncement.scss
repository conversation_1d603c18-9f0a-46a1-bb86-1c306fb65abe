// 公用职位区域

@import '../../variables';

$border-color-title: #efefef;
$border-color-istop: #d2d2d2;
$border-color-bottom: #ebebeb;
$color-bg-after: #dbdbdb;
$color-city: #486cf5;
$color-bg-requirement: #f4f9ff;

.job-container {
    margin-bottom: 10px;

    // 职位区域左边
    .left-content {
        margin-right: 10px;
        padding: 20px;
        width: 275px;
        background-color: $color-white;

        .sidebar {
            margin-bottom: 20px;
        }

        .left-sidebar {
            margin-bottom: 0;
        }

        // 圈子
        .exchange-title {
            margin-top: 20px;
        }

        // QQ群，微信群tab切换
        .inner {
            ul {
                li {
                    cursor: pointer;
                }

                .is-active {
                    color: $color-primary;
                }
            }
        }

        @import './circle';

        // 首页广告位
        .disseminate {
            padding: 0px 0 68px;

            a {
                margin-bottom: 10px;
                width: 235px;
                height: 110px;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        // 栏目页(博士人才交流)
        .communication {
            padding: 15px 0;
            text-align: center;
            font-size: 12px;
            border: 1px solid $border-color-title;

            .communication-group {
                padding-bottom: 5px;

                span {
                    color: $color-primary;
                }
            }

            .code {
                margin: 12px auto 0;
                width: 80px;
                height: 80px;
                border: 1px solid $color-primary;
            }
        }

        @import './regionNav';

        // 栏目页广告位
        .college-type {
            a {
                height: 110px;
            }
        }

        // 栏目页登录，注册
        .registration-release {
            width: 235px;
        }

        // 栏目页登录，注册(标题)
        .recruit-title {
            padding: 12px 0;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: $color-white;
            background: url('../assets/home/<USER>') no-repeat center / cover;
        }

        .main-content {
            padding: 0 16px;
            border: 1px solid $border-color-title;
            border-top: none;
        }

        .introduce-part {
            display: flex;
            align-items: center;
            padding: 20px 0;
        }

        .registration-part {
            border-bottom: 1px solid $border-color-bottom;
        }

        .left-picture {
            width: 34px;
            height: 34px;
            background-repeat: no-repeat;
            background-size: cover;
            background-position: center;
        }

        // (注册)图片
        .register {
            background-image: url('../assets/home/<USER>');
        }

        // (发布)图片
        .release {
            background-image: url('../assets/home/<USER>');
        }

        .middle-content {
            padding: 0 30px 0 9px;
        }

        .recruit-subtitle {
            padding-top: 8px;
            font-size: 12px;
            color: $font-color-basic;
        }

        // (去发布)跳转链接
        .right-link {
            width: 56px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background-color: $color-primary;
            font-size: 12px;
            color: $color-white;
            border-radius: 12px;
        }

        .link:hover {
            color: $color-white;
        }
    }

    // 职位区域右边
    .right-content {
        width: 915px;

        // 栏目页(精选职位，最新公告&简章，推荐单位)容器
        .selected-job,
        .release-container,
        .recommend-job {
            margin-bottom: 10px;
            padding: 20px;
            background-color: $color-white;
        }

        // 栏目页(精选职位)容器高度
        .selected-job {
            min-height: 620px;
        }

        // 栏目页(推荐单位)容器
        .recommend-job {
            margin-bottom: 0;
        }

        .job-nav {
            margin-bottom: 20px;
            height: 37px;
            line-height: 35px;
            border-top: 1px solid $border-color-title;
            border-bottom: 1px solid $border-color-title;

            // (精选职位)下tab切换，(热门单位)下tab切换
            .switch,
            .list {
                ul {
                    li {
                        margin-right: 93px;
                        width: 57px;
                        color: $font-color-basic;
                        cursor: pointer;

                        &:last-child {
                            margin-right: 0;
                        }
                    }

                    // (精选职位)下tab切换当前激活类名，(热门单位)下tab切换当前激活类名
                    .change,
                    .current {
                        color: $color-primary;
                        border-bottom: 2px solid $color-primary;
                    }

                    .region-type {
                        margin-right: 81px;
                        width: 70px;
                    }

                    .colleges-type {
                        width: 98px;
                    }
                }
            }
        }

        .more {
            color: $color-primary;
        }

        // (精选职位)下tab切换
        .tab-con {
            .con {
                & + .con {
                    display: none;
                }

                .list + .list {
                    display: none;
                }

                .job-board {
                    ul {
                        display: flex;
                        flex-wrap: wrap;
                        width: 100%;
                        padding-top: 20px;

                        li {
                            margin: 10px 10px 0 0;
                            padding: 16px 10px;
                            width: 285px;
                            border: 1px solid $border-color;
                            border-radius: 4px;

                            &:nth-child(3n) {
                                margin-right: 0;
                            }

                            a {
                                .position {
                                    display: flex;
                                    justify-content: space-between;

                                    h6 {
                                        width: 170px;
                                        font-size: inherit;
                                        @include utils-ellipsis;
                                    }

                                    .money {
                                        display: inline-block;
                                        width: 85px;
                                        text-align: right;
                                        color: $font-color-salary;
                                        @include utils-ellipsis;
                                    }
                                }

                                .requirement {
                                    span {
                                        display: inline-block;
                                        margin: 12px 6px 12px 0;
                                        padding: 4px 7px;
                                        color: $font-color-basic;
                                        font-size: 12px;
                                        background-color: $color-bg-requirement;
                                        border-radius: 4px;
                                    }
                                }

                                .region {
                                    display: flex;
                                    justify-content: space-between;

                                    span {
                                        display: block;
                                        color: $font-color-label;
                                        font-size: 13px;
                                    }

                                    .school {
                                        width: 170px;
                                        color: $font-color-basic;
                                        @include utils-ellipsis;
                                    }

                                    .city-place {
                                        display: flex;
                                        flex-direction: row-reverse;
                                        width: 80px;
                                    }

                                    .city {
                                        padding-left: 15px;
                                        background: url('../assets/home/<USER>') no-repeat center left / 12px;
                                        @include utils-ellipsis;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        .tab {
            padding-bottom: 40px;
        }

        .option {
            display: flex;
            justify-content: space-between;
            align-items: center;

            ul {
                li {
                    margin-right: 10px;
                    padding: 6px 10px;
                    border: 1px solid $border-color;
                    border-radius: 4px;
                    color: $font-color-basic;
                    cursor: pointer;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                .active,
                .item,
                .play,
                .play-1,
                .play-2,
                .tab {
                    color: $color-white;
                    background-color: $color-primary;
                }
            }
        }

        // (最新公告&简章)容器
        .release-container {
            margin-bottom: 10px;
            // min-height: 632px;
            background-color: $color-white;

            .newest-release {
                margin-bottom: 0;
                border-bottom: none;

                .release-title {
                    padding-bottom: 20px;
                }
            }

            .recruitment-information {
                margin-bottom: 10px;
                background-color: $color-white;

                .istop {
                    display: none;
                    padding-top: 20px;

                    ul {
                        display: flex;
                        justify-content: space-between;
                        flex-wrap: wrap;
                        width: 100%;

                        li {
                            display: flex;
                            align-items: center;
                            width: 49%;
                            padding: 9px 0;
                            border-bottom: 1px dotted $border-color-istop;

                            .regional-college {
                                position: relative;
                                display: flex;
                                flex: 1;
                                padding-left: 10px;

                                &::after {
                                    content: '';
                                    position: absolute;
                                    top: 50%;
                                    transform: translateY(-50%);
                                    left: 0;
                                    width: 4px;
                                    height: 4px;
                                    background-color: $color-bg-after;
                                    border-radius: 50%;
                                }

                                .city {
                                    white-space: nowrap;
                                    color: $color-city;
                                }

                                .announcement {
                                    max-width: 300px;
                                    @include utils-ellipsis;
                                }
                            }
                        }

                        small {
                            color: $font-color-basic;
                        }
                    }

                    @for $i from 1 through 2 {
                        ul li:nth-of-type(#{$i}) {
                            padding-top: 0;
                        }
                    }
                }
            }
        }

        @import './announcementTiled';

        .college {
            &:not(&:nth-child(1)) {
                display: none;
            }

            ul {
                display: flex;
                flex-wrap: wrap;

                li {
                    margin: 10px 10px 0 0;
                    width: 211px;
                    border: 1px solid $border-color;
                    border-radius: 4px;

                    &:nth-child(4n) {
                        margin-right: 0;
                    }

                    .school-name {
                        display: flex;
                        align-items: center;
                        padding: 16px 12px;

                        .logo {
                            margin-right: 10px;
                            width: 48px;
                            height: 48px;
                        }

                        .school-information {
                            flex: 1;
                            overflow: hidden;

                            p {
                                padding-bottom: 7px;
                                @include utils-ellipsis;
                            }

                            span {
                                display: block;
                                font-size: 12px;
                                color: $font-color-basic;
                                @include utils-ellipsis;
                            }
                        }
                    }
                }
            }
        }

        .product-list {
            .college-region,
            .college-job {
                padding-top: 20px;
            }

            .college-job {
                display: none;
            }
        }
    }

    .job {
        padding: 20px;
        background-color: $color-white;
    }

    .eliminate {
        border-top: none;
        padding: 0;
    }
}

// 栏目页换一批样式
.update {
    padding-left: 17px !important;
    color: $font-color-basic;
    background: url('../assets/home/<USER>') no-repeat left center / 13px;
    cursor: pointer;
}

@for $i from 1 through 4 {
    .job-container .right-content .college ul li:nth-of-type(#{$i}) {
        margin-top: 0;
    }
}

@for $i from 1 through 3 {
    .job-container .right-content .tab-con .con .job-board ul li:nth-of-type(#{$i}) {
        margin-top: 0;
    }
}

@for $i from 1 through 2 {
    .card:nth-of-type(#{$i}) {
        padding-top: 0;
    }
}
