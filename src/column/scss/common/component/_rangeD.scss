// d区样式

@import '../../variables';

$border-color: #e5e5e5;

.d1 {
    margin-bottom: 10px;

    .navbox {
        ul {
            justify-content: center;

            li {
                padding: 12px 0;
                width: 240px;
                text-align: center;
                font-size: 16px;
                background-color: $border-color;
                cursor: pointer;
            }

            .list-change {
                position: relative;
                color: $color-white;
                background-color: $color-primary;

                &::after {
                    content: '';
                    position: absolute;
                    width: 0;
                    height: 0;
                    border-left: 10px solid transparent;
                    border-right: 10px solid transparent;
                    border-bottom: 10px solid $color-white;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                }
            }
        }
    }

    .cardcon {
        background-color: $color-white;

        .t-card {
            padding: 20px 18px 0;

            &:not(:first-child) {
                display: none;
            }

            .cardlist {
                padding-bottom: 20px;

                .cardlist-pagination {
                    bottom: 7px;
                }

                .swiper-pagination-bullet {
                    width: 6px;
                    height: 6px;
                }

                .swiper-pagination-bullet-active {
                    width: 34px;
                    border-radius: 3px;
                    background-color: $color-primary;
                }
            }

            .content-tab {
                ul {
                    display: flex;
                    flex-wrap: wrap;

                    li {
                        width: calc(100% / 6);
                        height: 86px;
                        border-left: 1px solid $border-color;
                        border-top: 1px solid $border-color;
                        border-bottom: 1px solid $border-color;
                        box-sizing: border-box;
                        margin-bottom: 10px;

                        &:nth-child(6n) {
                            border-right: 1px solid $border-color;
                        }

                        &:last-child {
                            border-right: 1px solid $border-color;
                        }

                        a {
                            padding-top: 4px;
                            text-align: center;
                            font-size: 13px;
                            width: 100%;
                            height: 100%;
                        }

                        img {
                            margin: 0 auto;
                            max-width: 80%;
                            height: 40px;
                        }

                        p {
                            width: 100%;
                            @include utils-ellipsis;
                        }

                        span {
                            display: block;
                            width: 100%;
                            @include utils-ellipsis;
                            color: $font-color-basic;
                        }
                    }

                    &:last-child {
                        padding-bottom: 0;
                    }
                }
            }
        }

        .card-part {
            padding-top: 10px;
        }

        .card-position {
            margin: 0 auto 10px;
            width: 1045px;
            height: 26px;
            text-align: center;
            line-height: 26px;
            background-color: $background-color;
            color: $color-primary;
            border-radius: 40px;
        }
    }
}

// 栏目页使用

.cardtab {
    padding: 20px 20px 0;
    background-color: $color-white;

    .tabnav {
        ul {
            li {
                width: 232px;
            }
        }
    }

    .cardcon {
        .card {
            padding: 10px 0 0;
        }

        .recommend {
            padding-top: 20px;
        }
    }
}
