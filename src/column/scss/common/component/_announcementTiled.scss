// 一级栏目"最新公告&简章"平铺

@import '../../variables';

$border-color-title: #efefef;
$border-color-istop: #d2d2d2;
$border-color-bottom: #ebebeb;
$color-bg-after: #dbdbdb;
$color-city: #486cf5;
$color-bg-requirement: #f4f9ff;

$color-bg-top: #ff0101;

// (最新公告&简章)容器
.release-container {
    margin-bottom: 10px;
    background-color: $color-white;

    .release-title {
        display: flex;
        justify-content: space-between;
    }

    .newest-swiper {
        width: 374px;
        height: 24px;
        margin-right: -20px;
        background: url(../assets/icon/light-primary.png) no-repeat 11px center/19px 19px, #fff2e2;
        border-radius: 12px 0px 0px 12px;
        padding: 0 20px 0 34px;

        a {
            width: 100%;
            box-sizing: border-box;
            line-height: 24px;
            color: #714e2f;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 12px;
            background: url(../assets/icon/arrow-brown.png) no-repeat right/6px 10px;
        }
    }

    .recruitment-information {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-bottom: 10px;
        background-color: $color-white;
    }

    .type-container {
        margin-top: 26px;
        width: 48%;
        &:nth-child(1),
        &:nth-child(2) {
            margin-top: 0;
        }

        .title {
            position: relative;
            text-align: center;
            font-size: 16px;
            color: $color-primary;

            &::after {
                content: '';
                position: absolute;
                width: 100%;
                height: 1px;
                top: 50%;
                transform: translateY(-50%);
                left: 0;
                background-color: $color-primary;
            }

            span {
                position: relative;
                padding: 0 10px;
                background-color: $color-white;
                z-index: 1;
            }
        }

        .type-list {
            li {
                position: relative;
                display: flex;
                line-height: 37px;
                padding-left: 14px;
                border-bottom: 1px dotted #d2d2d2;

                &::before {
                    content: '';
                    position: absolute;
                    width: 4px;
                    height: 4px;
                    top: 50%;
                    z-index: 1;
                    transform: translateY(-50%);
                    left: 0;
                    background-color: $color-primary;
                    border-radius: 50%;
                }
                &.top-mark {
                    &::before {
                        width: 8px;
                        height: 11px;
                        transform: translateY(-50%);
                        left: -2px;
                        background: url(../assets/icon/top-column.png) no-repeat center/contain;
                    }
                }
            }

            .job-link {
                flex: 1;
                padding-right: 20px;
                @include utils-ellipsis;
            }

            .date {
                color: $font-color-basic;
                font-size: 12px;
            }
        }

        .see-more {
            margin: 15px auto 0;
            width: 78px;
            height: 22px;
            text-align: center;
            line-height: 22px;
            background-color: #fff3e0;
            color: $color-primary;
            border: 1px solid $color-primary;
            border-radius: 4px;
        }
    }
}
