// e区样式

@import '../../variables';

$border-color: #e5e5e5;
$border-color-bottom: #d2d2d2;
$color-after: #dbdbdb;
$border-color-right: #f6f6f6;
$color-bg-blue: #486cf5;
$color-bg-green: #16cb98;

.e {
    margin-bottom: 10px;
    background-color: $color-white;

    .e1 {
        .e1-currency {
            width: 400px;

            .title {
                padding: 12px 0;
                text-align: center;
                color: $color-white;
                font-size: 16px;
                background-color: $color-primary;
            }

            &:nth-of-type(2) .title {
                background-color: $color-bg-blue;
            }

            &:nth-of-type(3) .title {
                background-color: $color-bg-green;
            }

            .rectangle {
                padding-left: 30px;
                background: url('../assets/home/<USER>') no-repeat left center / 20px;
            }

            .plan {
                padding: 20px;
                border-right: 1px solid $border-color-right;

                .site-img {
                    a {
                        display: flex;
                        align-items: center;
                        padding: 0 10px;
                        height: 60px;
                        border-left: 1px solid $border-color;
                        border-right: 1px solid $border-color;
                        border-bottom: 1px solid $border-color;

                        img {
                            width: 203px;
                        }

                        h5 {
                            margin-left: 50px;
                            width: 87px;
                            line-height: 1.5;
                            font-size: inherit;
                            text-align: center;
                            display: -webkit-box;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 2;
                        }
                    }

                    .first {
                        border: 1px solid $border-color;
                    }
                }
            }
        }
    }

    .school-information-content {
        .information-title {
            width: 400px;

            ul {
                padding: 0 20px;
                border-right: 1px solid $border-color-right;

                li {
                    padding: 9px 0;
                    border-bottom: 1px solid $border-color-bottom;

                    a {
                        position: relative;
                        padding-left: 9px;

                        .main {
                            display: inline-block;
                            margin-right: 5px;
                            width: 200px;
                            @include utils-ellipsis;
                        }

                        .secondary {
                            display: inline-block;
                            width: 136px;
                            color: $font-color-basic;
                            @include utils-ellipsis;
                        }

                        &::after {
                            content: '';
                            position: absolute;
                            top: 8px;
                            left: 0;
                            width: 4px;
                            height: 4px;
                            background-color: $color-primary;
                            border-radius: 50%;
                        }

                        &:hover {
                            span {
                                color: $color-primary;
                            }
                        }
                    }

                    &:first-child {
                        padding-top: 0;
                    }
                }
            }

            .more {
                a {
                    margin: 20px auto;
                    padding: 5px 0;
                    text-align: center;
                    width: 70px;
                    border: 1px solid $color-primary;
                    border-radius: 4px;
                    color: $color-primary;
                    font-size: 12px;
                }
            }
        }
    }
}
