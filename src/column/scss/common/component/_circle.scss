@import '../../variables';

.exchange {
    .inner {
        ul {
            .is-active {
                color: $color-primary;
            }

            li {
                cursor: pointer;
            }
        }
    }

    .dialogue {
        padding-bottom: 30px;

        ul {
            li {
                padding-bottom: 15px;
                display: flex;

                a:hover {
                    color: #fff;
                }

                &:last-child {
                    padding-bottom: 0;
                }

                .dialogue-title {
                    display: inline-block;
                    padding-left: 27px;
                    flex-grow: 1;
                    line-height: 21px;
                    color: $font-color-basic;
                    @include utils-ellipsis;
                    background-image: url('../assets/home/<USER>');
                    background-position: left;
                    background-repeat: no-repeat;
                    background-size: contain;
                }

                .exchange-list2 {
                    background-image: url('../assets/home/<USER>');
                }

                .exchange-list3 {
                    background-image: url('../assets/home/<USER>');
                }

                .exchange-list4 {
                    background-image: url('../assets/home/<USER>');
                }

                .join {
                    display: inline-block;
                    position: relative;
                    flex-shrink: 0;
                    margin-left: 30px;
                    padding: 4px 6px;
                    background-color: $color-primary;
                    font-size: 12px;
                    color: $color-white;
                    border-radius: 4px;
                    cursor: pointer;
                    z-index: 10;
                }

                .join:hover {
                    .join-code {
                        display: flex;

                        &.is-join-code {
                            display: flex;
                        }
                    }
                }

                // 其他页面展示
                .join-code {
                    position: absolute;
                    display: none;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 10px;
                    width: 260px;
                    height: 120px;
                    right: -280px;
                    bottom: -48px;
                    background-color: $color-white;
                    box-shadow: 0px 4px 10px 0px rgba(102, 102, 102, 0.2);
                    border-radius: 4px;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 50px;
                        left: -20px;
                        width: 0;
                        height: 0;
                        border-width: 10px;
                        border-style: solid;
                        border-color: transparent $color-white transparent transparent;
                    }
                }

                // 资讯列表展示
                .is-join-code {
                    left: -280px;

                    &::after {
                        content: '';
                        left: 260px;
                        border-color: transparent transparent transparent $color-white;
                    }
                }

                .public-code {
                    width: 98px;
                    height: 98px;
                }

                .scan-join {
                    padding-bottom: 18px;
                    text-align: center;
                    color: $color-primary;
                    font-size: 14px;
                }

                .explain {
                    color: $font-color-basic;
                }
            }
        }

        .widget-content {
            display: none;
        }
    }
}
