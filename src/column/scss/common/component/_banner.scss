// 公用banner区域
@import '../../variables';

$color-bg-nav: rgba(92, 92, 92, 0.7);
$border-color-bottom: #ebebeb;

.banner-container {
    position: relative;
    display: flex;
    width: 100%;
    height: 190px;

    .slide {
        width: 100%;

        .slide-picture-display {
            height: 100%;
        }

        .swiper-pagination-bullet {
            width: 27px;
            height: 4px;
            border-radius: 2px;
            background-color: rgba($color-white, 0.6);
        }

        .swiper-pagination-bullet-active {
            background-color: $color-white;
        }

        @include utils-swiper-bullet();

        .slide-picture-pagination {
            width: 400px;
            left: 50%;
            transform: translateX(-50%);
        }

        img {
            margin: 0 auto;
            width: 100%;
            height: 100%;
            object-fit: none;
        }
    }

    // banner区域左侧导航
    .subnav {
        position: absolute;
        width: 160px;
        height: 100%;
        top: 0;
        left: calc((100% - 1200px) / 2);
        background-color: $color-bg-nav;
        z-index: 99;

        ul {
            height: 190px;

            li {
                position: relative;
                padding: 0 20px;
                height: 25%;
                line-height: 49.5px;
                font-size: 13px;
                color: $color-white;
                background: url(../assets/home/<USER>/ 4px;

                &:hover {
                    color: $color-primary;

                    .list-tab {
                        display: block;
                    }
                }
                @for $i from 2 through 4 {
                    &:nth-of-type(#{$i}) {
                        .list-tab {
                            top: calc(-100% * ($i - 1));
                        }
                    }
                }

                & > a,
                span {
                    font-weight: bold;
                }
            }

            a {
                display: block;
            }
        }
    }

    // banner区域左侧鼠标经过显示
    .list-tab {
        display: none;
        position: absolute;
        top: 0;
        left: 160px;
        padding: 30px 30px 0;
        width: 800px;
        line-height: 1;
        font-size: 14px;
        background-color: $color-white;
        border: 1px solid $color-primary;
        border-radius: 4px;

        .title {
            padding-left: 22px;
            padding-bottom: 12px;
            font-size: 14px;
            color: $font-color;
            border-bottom: 1px solid $border-color-bottom;
            background: url(../assets/icon/title.png) no-repeat left 2px / 14px 12px;
        }

        // 工学
        .is-subject {
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid $border-color-bottom;

            .title {
                border: none;
            }

            // 更多
            .is-more {
                padding: 0 0 0 17px;
                background: url('../assets/home/<USER>') no-repeat left top / 14px;
                color: $color-primary;
            }
        }

        .inside-nav {
            position: relative;
            margin: 2px 0 20px;
            color: $font-color-basic;
        }

        .nav-link {
            display: inline-block;
            margin-right: 20px;
            padding: 10px 0 10px;
            font-size: 14px;

            &:hover {
                color: $color-primary;
            }
        }

        .is-hot {
            padding-right: 33px;
            background: url(../assets/icon/hot.png) no-repeat right center / 30px 10px;
        }

        // #819 优化版需求点NO5：添加“学科汇总”页面
        // .more {
        //     position: absolute;
        //     right: 0;
        //     bottom: 0;
        //     margin-right: 0;
        //     padding-left: 17px;
        //     color: $color-primary;
        //     font-weight: bold;
        //     background: url('../assets/home/<USER>') no-repeat left center / 14px;
        // }
    }

    // banner区域右侧 未登录
    .banner-login {
        position: absolute;
        width: 240px;
        height: 100%;
        line-height: 1;
        top: 0;
        right: calc((100% - 1200px) / 2);
        background-color: $color-white;
        z-index: 100;
    }

    // banner区域右侧 已登录
    .banner-login-container {
        position: absolute;
        width: 240px;
        height: 100%;
        top: 0;
        right: calc((100% - 1200px) / 2);
        background-color: $color-white;
        z-index: 100;
    }
}
