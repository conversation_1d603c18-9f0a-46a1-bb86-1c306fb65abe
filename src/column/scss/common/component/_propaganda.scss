// 热门公告&简章

@import '../../variables';

$border-color-title: #efefef;
$border-color-bottom: #d2d2d2;

.propaganda-container {
    display: flex;
    margin-bottom: 10px;

    .propaganda-title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
            padding-left: 4px;
            color: $font-color-basic;
            cursor: pointer;
        }
    }

    .announcement-news {
        display: none;
    }

    .propaganda-hot {
        margin-right: 10px;
        padding: 20px;
        width: 275px;
        background-color: $color-white;

        ul {
            li {
                padding: 9px 0 9px 8px;
                border-bottom: 1px dotted $border-color-bottom;

                &:first-child {
                    padding-top: 0;
                }

                a {
                    position: relative;
                    width: 100%;
                    @include utils-ellipsis;

                    &::after {
                        content: '';
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: -8px;
                        width: 4px;
                        height: 4px;
                        background-color: $color-primary;
                        border-radius: 50%;
                    }
                }
            }
        }
    }

    .propaganda-recommend {
        padding: 20px;
        width: 915px;
        background-color: $color-white;

        .recommend-content {
            ul {
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;
                width: 100%;

                li {
                    display: flex;
                    justify-content: space-between;
                    padding: 9px 0;
                    width: 49%;
                    border-bottom: 1px dotted $border-color-bottom;

                    a {
                        flex: 1;
                        position: relative;
                        padding: 0 20px 0 8px;
                        @include utils-ellipsis;

                        &::after {
                            content: '';
                            position: absolute;
                            top: 50%;
                            transform: translateY(-50%);
                            left: 0;
                            width: 4px;
                            height: 4px;
                            background-color: $color-primary;
                            border-radius: 50%;
                        }
                    }

                    small {
                        color: $font-color-basic;
                        font-size: 12px;
                    }
                }
            }
        }

        @for $i from 1 through 2 {
            .recommend-content ul li:nth-of-type(#{$i}) {
                padding-top: 0;
            }
        }
    }
}
