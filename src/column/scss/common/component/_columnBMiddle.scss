// 栏目页模板B
$color-number: #aeaeae;
$color-one: #ff3333;
$color-two: #ff9000;
$color-three: #fbb250;
$border-color-bottom: #ededed;

.slide-part {
    display: flex;
    margin-bottom: 10px;
    height: 303px;
    line-height: 1.1;

    .left-content {
        margin-right: 10px;
        padding: 20px;
        width: 500px;
        background-color: $color-white;

        .swiper-pagination-bullet-active {
            background-color: $color-white;
        }

        @include utils-swiper-bullet();

        a {
            position: relative;
            width: 460px;
            height: 264px;
        }

        .title {
            position: absolute;
            padding: 4px 0;
            bottom: 30px;
            width: 100%;
            text-align: center;
            background-color: $font-color-label;
            color: $color-white;
            @include utils-ellipsis;
        }
    }

    .middle-content {
        margin-right: 10px;
        padding: 20px 20px 0;
        width: 388px;
        background-color: $color-white;
    }

    .information {
        ul {
            li {
                display: flex;
                padding-bottom: 15px;

                span {
                    display: block;
                    padding-right: 19px;
                    color: $color-number;
                    font-weight: bold;
                }

                .one {
                    color: $color-one;
                }

                .two {
                    color: $color-two;
                }

                .three {
                    color: $color-three;
                }

                a {
                    width: 320px;
                    @include utils-ellipsis;
                }
            }
        }
    }

    .right-content {
        width: 292px;
        background-color: $color-white;

        ul {
            li {
                border-bottom: 1px solid $border-color-bottom;

                &:last-child {
                    border-bottom: none;
                }

                a {
                    display: flex;
                }

                img {
                    margin: 5px 18px 5px 12px;
                    width: 50px;
                    height: 50px;
                }

                .con-txt {
                    padding-top: 13px;
                }

                p {
                    width: 161px;
                    @include utils-ellipsis;
                }

                .recruitment-information {
                    padding-top: 6px;
                    color: $font-color-basic;
                }
            }
        }
    }
}
