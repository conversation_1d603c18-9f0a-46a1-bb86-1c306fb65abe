@import '../../variables';

$border-color-contact: #efefef;
$color-bg: #f4f9ff;
$color-border-bottom: #f2f2f2;
$color-bg-title: #fafafc;

.release-container {
    .new-announcement {
        align-items: center;
    }

    .type-select {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
    }

    .el-form {
        display: flex;

        .m-2 {
            width: 160px;
        }
    }
}

.data {
    color: $font-color-basic;

    span {
        color: $color-primary;
    }
}

.announcement-content {
    line-height: 1.2;

    @include utils-offline-mark();

    ul {
        display: flex;
        flex-wrap: wrap;

        li {
            margin: 10px 11px 0 0;
            padding: 15px 15px 0;
            width: 432px;
            border: 1px solid $border-color;
            border-radius: 4px;
        }

        & > li:nth-child(2n) {
            margin-right: 0;
        }
    }

    .recruit-information {
        display: flex;
        padding: 20px 9px;
        border-bottom: 1px solid $color-border-bottom;
        font-size: 12px;
        align-items: center;

        span {
            display: block;
        }

        .date {
            width: 43px;
            @include utils-ellipsis;
        }

        .title {
            flex: 1;
            font-size: 14px;
            @include utils-ellipsis;
            position: relative;
        }

        .place {
            width: 48px;
            margin-right: 15px;
            @include utils-ellipsis;
        }

        .job-quantity,
        .recruit-quantity {
            margin-right: 17px;
            width: 60px;
            @include utils-ellipsis;
        }

        .closing-date {
            width: 70px;
            @include utils-ellipsis;
        }

        .text {
            color: $color-primary;
            width: 70px;
        }

        &.top-mark {
            .title {
                padding-left: 13px;
                &::before {
                    content: '';
                    width: 8px;
                    height: 11px;
                    position: absolute;
                    top: 1px;
                    left: 0;
                    background: url(../assets/icon/top-column.png) no-repeat center/contain;
                }
            }
        }
    }

    .offline-mark {
        .text {
            color: #333;
        }
    }

    .recruit-title {
        padding: 0 9px;
        line-height: 44px;
        background-color: $color-bg-title;
        font-size: 14px;
        border: none;
        font-weight: bold;
    }

    .college-job {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        h4 {
            flex: 1;
            @include utils-column-title;
        }

        .time {
            margin-left: 5px;
            color: $font-color-label;
        }
    }

    .requirement {
        display: flex;
        justify-content: space-between;
    }

    .recruitment-content {
        margin-bottom: 15px;

        span {
            display: inline-block;
            margin-right: 6px;
            padding: 4px 8px;
            font-size: 12px;
            color: $font-color-basic;
            background-color: $color-bg;
        }
    }

    .school-information {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .money {
        width: 80px;
        text-align: right;
        color: $font-color-salary;
        @include utils-ellipsis;
    }

    .offline-mark {
        .college-job {
            margin-bottom: 8px;
            h4 {
                position: relative;
                text-indent: 4.5em;
                line-height: 22px;
                max-height: 44px;
                .offline-tag {
                    position: absolute;
                    left: 0;
                    top: 0;
                    text-indent: 0em;
                }
            }
        }
        .money {
            color: #333;
        }
    }

    .school-type {
        display: inline-block;
        color: $font-color-label;
        flex-grow: 1;
        @include utils-ellipsis;
        span {
            width: 105px;
            padding-right: 10px;
            color: $font-color-basic;
            @include utils-ellipsis;
        }
    }

    .city {
        font-size: 13px;
        color: $font-color-label;
        flex-shrink: 0;
        max-width: 80px;
        @include utils-ellipsis;
        span {
            padding-left: 15px;
            vertical-align: middle;
            background: url('../assets/home/<USER>') no-repeat center left / 12px;
        }
    }
}

.latest-job {
    padding: 20px;
    background-color: $color-white;

    .type-select {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        flex-wrap: wrap;

        .type-select-tips {
            width: 100%;
            flex-shrink: 0;
        }
    }

    .el-form {
        display: flex;

        .m-2 {
            width: 160px;
        }
    }
}

.el-pagination {
    text-align: center;
    margin: 30px 0 10px;
}

@for $i from 1 through 2 {
    .announcement-content ul li:nth-of-type(#{$i}) {
        margin-top: 0;
    }
}
