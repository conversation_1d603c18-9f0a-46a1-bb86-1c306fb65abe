// l区样式

@import '../../variables';
@import './hot-topics';

$border-color-title: #efefef;
$border-color-bottom: #d2d2d2;
$border-color-before: #bbb;
$color-bg-arrow: #f4f4f4;
$color-actionTime: #7aa5b9;
$border-color-img: #dcdcdc;
$color-bg-follow: #fd4f5a;
$color-bg-end: #f0f0f0;
$color-bg-hot: #f33;

.part {
    margin-bottom: 10px;
}

.l {
    margin-right: 10px;
    padding: 20px 20px 0px;
    width: 275px;
    background-color: $color-white;

    .activity-region {
        background: url('../assets/home/<USER>') no-repeat 73px center / 42px;
    }

    .left-siderbar-box {
        margin-bottom: 20px;
    }

    .l01,
    .l02,
    .l03,
    .l04,
    .l05 {
        a {
            height: 110px;
        }
    }

    .weixin-show {
        box-sizing: border-box;
        border: 1px solid $border-color-title;

        .tips {
            h4 {
                padding: 19px 0 10px 66px;
                background: url('../assets/home/<USER>') no-repeat 40px 20px / 18px;
            }
        }

        .weixin-developer-detail {
            padding: 0 20px;

            .official-weixin {
                align-items: center;
                padding: 8px 0;
                border-top: 1px solid $border-color-bottom;

                .official-information {
                    padding: 18px 0;

                    p {
                        font-size: 12px;

                        &:first-child {
                            padding-bottom: 5px;
                        }
                    }
                }

                .code {
                    width: 70px;
                    height: 70px;
                    background: url($qrcode-mpweixin) no-repeat center / cover;

                    &:hover {
                        transform: scale(1.03);
                    }
                }
            }

            .school-picture-display {
                padding-bottom: 18px;

                a {
                    width: 195px;
                    height: 80px;

                    img {
                        border-radius: 4px;
                    }
                }
            }

            .school-information {
                padding-bottom: 26px;

                a {
                    display: flex;
                    padding-bottom: 9px;
                    border-bottom: 1px solid $border-color-bottom;

                    .information-content {
                        span {
                            margin-right: 10px;
                            width: 143px;
                            line-height: 1.5;
                            font-size: 13px;
                            text-overflow: -o-ellipsis-lastline;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                        }
                    }

                    img {
                        display: inline-block;
                        width: 40px;
                        height: 40px;
                        border-radius: 4px;
                    }

                    &:last-child {
                        padding-top: 9px;
                        padding-bottom: 0;
                        border-bottom: none;
                    }
                }
            }
        }
    }

    .development {
        align-items: center;
        padding: 10px 0;
        font-size: 13px;
        border: 1px solid $border-color-title;

        .gx-media {
            color: $font-color-basic;
            padding: 0 28px 0 48px;
            background-image: url($logo-icon);
            background-size: contain;
            background-repeat: no-repeat;
            background-position: 14px;
        }

        .see {
            color: $color-primary;
        }
    }

    .weibo-show {
        padding: 0 20px;
        border: 1px solid $border-color-title;

        .weibo-show-title-top {
            border-bottom: 1px solid $border-color-bottom;

            h4 {
                padding: 19px 0 10px 46px;
                background: url('../assets/home/<USER>') no-repeat 16px 16px / 26px;
            }
        }

        .official-weibo {
            align-items: center;
            padding: 8px 0 14px;

            .code {
                margin-right: 9px;
                width: 40px;
                height: 40px;
                background: url($logo-text-bottom) no-repeat center / cover;
            }

            .official-information {
                .weibo-title {
                    padding-bottom: 9px;
                    color: $font-color-basic;
                }

                .follow-weibo {
                    width: 63px;
                    height: 22px;
                    text-align: center;
                    line-height: 22px;
                    color: $color-white;
                    background-color: $color-bg-follow;
                    border-radius: 4px;
                }
            }
        }

        .weibo-show-main {
            min-height: 336px;
            @include utils-ellipsis;

            #part {
                .up-scroll {
                    position: relative;
                    width: 100%;
                    height: 6px;
                    background-color: $color-bg-arrow;
                    cursor: pointer;

                    &::before {
                        content: '';
                        position: absolute;
                        width: 0;
                        height: 0;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-bottom: 6px solid $border-color-before;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                }

                .weibo-show-content {
                    height: 300px;
                    overflow-y: scroll;

                    &::-webkit-scrollbar {
                        display: none;
                    }

                    ul {
                        li {
                            .weibo-main {
                                .weibo-news {
                                    white-space: pre-wrap;
                                    font-size: 12px;
                                    color: $font-color-basic;
                                    border-top: 1px solid $border-color-bottom;
                                }

                                img {
                                    padding: 5px;
                                    width: 119px;
                                    height: 78px;
                                    border: 1px solid $border-color-img;
                                    border-radius: 4px;
                                }
                            }

                            .actionTime {
                                display: inline-block;
                                padding: 8px 28px 8px 0;
                                font-size: 12px;
                                color: $color-actionTime;
                            }

                            .actionMore {
                                display: inline-block;
                                padding-bottom: 8px;
                                font-size: 12px;
                                color: $color-actionTime;
                            }
                        }
                    }
                }

                .down-scroll {
                    position: relative;
                    width: 100%;
                    height: 6px;
                    background-color: $color-bg-arrow;
                    cursor: pointer;

                    &::before {
                        content: '';
                        position: absolute;
                        width: 0;
                        height: 0;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        border-top: 6px solid $border-color-before;
                        left: 50%;
                        transform: translateX(-50%);
                    }
                }
            }
        }
    }

    .activity-area {
        .state {
            position: relative;
            padding-left: 18px;

            &::after {
                content: '';
                position: absolute;
                top: 8px;
                left: 0px;
                width: 4px;
                height: 4px;
                background-color: $color-primary;
                border-radius: 50%;
            }

            &::before {
                content: '';
                position: absolute;
                top: 8px;
                left: 1.5px;
                width: 1px;
                height: 102px;
                border-left: 1px dashed $color-primary;
            }

            &:last-child:before {
                display: none;
            }

            .activity-title {
                padding-bottom: 8px;
                width: 209px;
                @include utils-ellipsis;
            }

            .on-going,
            .end {
                display: inline-block;
                margin: 0 0 23px 10px;
                width: 50px;
                height: 20px;
                text-align: center;
                line-height: 20px;
                font-size: 12px;
                color: $color-white;
                background-color: $color-primary;
                border-radius: 4px;
            }

            .end {
                color: $font-color-tips;
                background-color: $color-bg-end;
            }

            small {
                font-size: 12px;
                color: $font-color-basic;
            }
        }
    }
}

.Job-search-dynamics {
    padding: 15px;
    border: 1px solid $border-color-title;

    h5 {
        padding-bottom: 17px;
        text-align: center;
        font-size: inherit;
    }

    .civil-service-examination {
        .civil-link {
            position: relative;
            margin: 0 auto;
            width: 205px;
            height: 120px;

            img {
                border-radius: 4px;
            }

            span {
                position: absolute;
                display: block;
                padding: 5px;
                width: 100%;
                bottom: 0;
                line-height: 1.8;
                background-color: rgba($font-color, 0.3);
                font-size: 12px;
                color: $color-white;
                border-radius: 0px 0px 4px 4px;
            }
        }
    }

    .hot-position {
        a {
            position: relative;
            margin-top: 16px;
            padding-left: 11px;
            width: 100%;
            @include utils-ellipsis;

            &::after {
                content: '';
                position: absolute;
                bottom: 6px;
                left: 0;
                width: 4px;
                height: 4px;
                background-color: $color-primary;
                border-radius: 50%;
            }
        }
    }
}

// 首页高才情报局
.hot-topics {
    .information {
        padding: 0;
        border: none;
    }
}

// 引入圈子样式
@import './circle';
