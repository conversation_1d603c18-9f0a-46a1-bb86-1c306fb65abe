// b区样式

@import '../../variables';

$border-color: #ededed;

.b1 {
    a {
        width: 595px;
        height: 72px;
        border: 1px solid $border-color;

        &:last-child {
            margin-right: 0;
        }
    }
}

.b2 {
    margin: 8px 0;

    a {
        margin-right: 8px;
        width: calc((100% - 32px) / 5);
        height: 70px;
        border: 1px solid $border-color;

        &:nth-child(5n) {
            margin-right: 0;
        }
    }
}

.b3 {
    margin-bottom: 10px;
    @include utils-ellipsis;
    border: 1px solid $border-color;
    border-bottom: none;
    border-right: none;

    ul {
        flex-wrap: wrap;
        width: 100%;

        li {
            position: relative;
            border-right: 1px solid $border-color;
            border-bottom: 1px solid $border-color;

            a {
                width: 170px;
                height: 75px;
            }

            .hover {
                position: absolute;
                justify-content: space-between;
                flex-direction: column;
                padding: 8px 5px;
                text-align: center;
                width: 100%;
                height: 100%;
                font-weight: bold;
                color: $color-primary;
                background-color: $background-color;
                display: none;

                p {
                    white-space: pre-wrap;
                    display: -webkit-box;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                }

                span {
                    position: relative;
                    display: inline-block;
                    margin: 0 auto;
                    width: 65px;
                    height: 20px;
                    line-height: 20px;
                    color: $color-white;
                    border-radius: 10px;
                    background-color: $color-primary;

                    &::after {
                        content: '';
                        position: absolute;
                        margin-left: 2px;
                        top: 6px;
                        width: 0;
                        height: 0;
                        border: 5px;
                        border-style: solid;
                        border-color: transparent transparent transparent $color-white;
                    }
                }
            }

            &:hover {
                .hover {
                    display: flex;
                }
            }
        }
    }
}

.b4 {
    @include utils-ellipsis;

    .cardlist {
        .cardlist-pagination {
            bottom: 0px;
        }

        .swiper-pagination-bullet {
            width: 6px;
            height: 6px;
        }

        .swiper-pagination-bullet-active {
            width: 34px;
            border-radius: 3px;
            background-color: $color-primary;
        }
    }

    a {
        position: relative;
        margin-right: 0;
        width: calc((100% - 0px) / 7);
        height: 70px;

        &::after {
            content: '';
            position: absolute;
            top: 20%;
            height: 60%;
            right: 0;
            width: 1px;
            background-color: $border-color;
        }

        &:last-child:after {
            display: none;
        }
    }
}
