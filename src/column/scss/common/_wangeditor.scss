p,
h1,
h2,
h3,
h4,
h5,
table,
pre {
    margin: 10px 0;
    line-height: 1.5;
}

ul,
ol {
    margin: 10px 0 10px 20px;
}

blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
}

code {
    display: inline-block;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
}

pre {
    code {
        display: block;
    }
}

table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;

    td,
    th {
        border-bottom: 1px solid #ccc;
        border-right: 1px solid #ccc;
        padding: 3px 5px;
        min-height: 30px;
        height: 30px;
    }

    th {
        border-bottom: 2px solid #ccc;
        text-align: center;
        background-color: #f1f1f1;
    }
}

.w-e-todo {
    margin: 0 0 0 20px;
    li {
        list-style: none;
        font-size: 1em;
        span:nth-child(1) {
            position: relative;
            left: -18px;
            input {
                position: absolute;
                margin-right: 3px;
            }
            // 防止其他样式通过属性选择器重置input样式
            input[type='checkbox'] {
                top: 50%;
                margin-top: -6px;
            }
        }
    }
}
