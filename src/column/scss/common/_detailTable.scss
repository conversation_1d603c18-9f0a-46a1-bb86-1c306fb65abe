// !此样式文件必须在作用域下引入

* {
    // 无奈之举
    z-index: initial !important;
}

table {
    margin: 20px auto;
    width: 100%;
    border-collapse: collapse;
    border: none !important;

    th {
        background-color: #fafafc;
    }

    th,
    td {
        padding: 10px 5px !important;
        word-break: break-word;
        border: 1px solid #333 !important;
    }
}

p,
table {
    & + .detail-title {
        margin-top: 50px;
    }
}
