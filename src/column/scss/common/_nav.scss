// 主导航栏

@import '../variables';

$color-bg-nav: #5c5c5c;

.page-header-container {
    a {
        display: block;
    }

    .postdoctor-title {
        background: url('../assets/home/<USER>') no-repeat right 27px center / 62px 22px;
        display: flex;
        align-items: center;
        height: 100%;
        position: relative;

        &::after {
            content: '';
            position: absolute;
            display: block;
            width: 27px;
            height: 15px;
            top: 0px;
            right: 8px;
            background: url(../assets/home/<USER>/contain;
        }

        span {
            margin-left: 75px !important;
        }
    }

    .abroad-title {
        background: url('../assets/home/<USER>') no-repeat center / 58px 20px;
        display: flex;
        align-items: center;
        height: 100%;

        span {
            margin-left: 75px !important;
        }
    }

    .nav {
        position: relative;
        width: 100%;
        height: 44px;
        background-color: $color-bg-nav;
        z-index: 1000;

        nav {
            margin: 0 auto;
            width: $view-width;

            ul {
                display: flex;
                justify-content: space-between;

                li {
                    position: relative;

                    .nav-text {
                        &.right {
                            right: 0;
                        }
                    }

                    a {
                        padding: 0 14px;
                        line-height: 44px;
                        color: $color-white;
                        font-weight: bold;
                        white-space: nowrap;

                        &:hover {
                            color: $color-white;
                            background-color: $color-primary;
                        }
                    }

                    span {
                        display: inline-block;
                        margin-left: 8px;
                        width: 8px;
                        height: 4px;
                        vertical-align: middle;
                        background: url('../assets/home/<USER>') no-repeat center / cover;
                    }

                    .nav-text {
                        position: absolute;
                        justify-content: space-between;
                        display: none;
                        padding: 16px;
                        width: 456px;
                        background-color: $color-white;
                        border: 1px solid $color-primary;
                        box-shadow: 0px 4px 10px 0px rgba(102, 102, 102, 0.1);
                        border-radius: 0px 0px 4px 4px;
                        z-index: 110;
                    }

                    .left-title {
                        a {
                            margin-bottom: 15px;
                            padding: 0;
                            width: 195px;
                            line-height: 1.1;
                            font-weight: normal;
                            color: $font-color-basic;
                            background-color: inherit;
                            @include utils-ellipsis;
                        }

                        & > a:hover {
                            color: $color-primary;
                        }

                        & > a:last-child {
                            padding-bottom: 0;
                        }
                    }

                    .right-picture {
                        a {
                            margin-bottom: 10px;
                            padding: 0;
                            width: 188px;
                            height: 54px;
                        }

                        & > a:last-child {
                            margin-bottom: 0;
                        }
                    }

                    &:hover {
                        & > a {
                            background-color: $color-primary;
                            color: $color-white;
                        }

                        .nav-text {
                            display: flex;
                        }

                        .region-container {
                            display: block;
                        }
                    }

                    // 地区
                    .region-container {
                        display: none;
                        position: absolute;
                        top: 44px;
                        right: 0;
                        padding: 20px;
                        width: 1200px;
                        background-color: $color-white;
                        border: 1px solid $color-primary;
                        border-radius: 4px;
                        z-index: 110;
                    }

                    .region-information {
                        display: flex;
                        flex-wrap: wrap;
                        margin-bottom: 10px;

                        &:last-child {
                            margin-bottom: 0;
                        }
                    }

                    .region-title {
                        font-weight: bold !important;
                        color: $color-primary !important;
                    }

                    .region-link {
                        margin-right: 15px;
                        padding: 0;
                        line-height: 2.4;
                        color: $font-color-basic;
                        font-weight: 500;

                        &:hover {
                            background-color: $color-white;
                            color: $color-primary;
                        }
                    }

                    .more-region {
                        margin-right: 0;
                        padding-left: 17px;
                        color: $color-primary;
                        background: url('../assets/home/<USER>') no-repeat center left / 14px;
                    }
                }

                .active {
                    color: $color-primary;
                }
            }
        }
    }

    .fixed {
        position: fixed;
        z-index: $header-index;
        top: 0;
    }
}
