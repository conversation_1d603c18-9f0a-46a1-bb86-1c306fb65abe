// 栏目页头条新闻，广告图

@import '../variables';

$border-color-right: #efefef;

.headlines-content {
    margin: 10px 0;
    height: 80px;
    background-color: $color-white;

    .hot-news {
        position: relative;
        width: 180px;
        border-right: 1px solid $border-color-right;

        &::after {
            content: '';
            position: absolute;
            top: 32px;
            right: -16px;
            width: 0px;
            height: 0px;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-primary;
        }
    }

    .headlines {
        padding: 15px 0;
        width: 1020px;
        line-height: 1.1;
        text-align: center;
    }

    .headlines-title {
        margin-bottom: 15px;
        color: #ffae42;
        font-size: 18px;
        font-weight: bold;

        a {
            width: 900px;
            margin: 0 auto;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .headlines-information {
        a {
            position: relative;
            display: inline-block;
            margin-right: 35px;
            padding-left: 10px;
            width: 288px;
            text-align: left;
            @include utils-ellipsis;
        }

        & > a::after {
            content: '';
            position: absolute;
            width: 6px;
            height: 6px;
            top: 4px;
            left: 0;
            background-color: $color-primary;
            border-radius: 50%;
        }

        & > a:last-child {
            margin-right: 0;
        }
    }
}

.school-display-pictures {
    ul {
        flex-wrap: wrap;

        li {
            margin: 0 10px 10px 0;

            &:nth-child(4n) {
                margin-right: 0;
            }

            a {
                position: relative;
                width: 292px;
                height: 60px;
                color: $font-color-basic;

                span {
                    position: absolute;
                    top: 50%;
                    right: 0;
                    padding: 10px;
                    font-size: 14px;
                    text-align: center;
                    line-height: 20px;
                    transform: translateY(-50%);
                }
            }
        }
    }
}
