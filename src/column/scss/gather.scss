// 每日汇总
@import './variables';

@import './common/headlines-adv';
@import './common/commonTitle';
@import './common/sideBar';

@import './common/currentLocation';
@import './common/component/rangeD';
@import './common/component/rangeL';
@import './common/component/rangeH';
@import './common/component/link';

$border-color-bottom: #ededed;
$color-day: #fffefe;

.everyday-gather-container {
    display: flex;
    margin-bottom: 10px;
}

.left-information-content {
    margin-right: 10px;
    padding-bottom: 30px;
    width: 845px;
    background-color: $color-white;
}

.gather-information {
    margin-bottom: 30px;
    padding: 0 20px 0;

    li {
        display: flex;
        align-items: center;
        padding: 20px 0;
        border-bottom: 1px solid $border-color-bottom;
    }
}

.left-date {
    margin: 0 29px 0 11px;
    width: 67px;
    text-align: center;
    font-weight: bold;

    span {
        display: block;
    }

    .day {
        padding: 4px 0;
        font-size: 16px;
        color: $color-day;
        background-color: $color-primary;
    }

    .year {
        padding: 6px 0;
        font-size: 13px;
        color: $color-primary;
        border: 1px solid $color-primary;
    }
}

.right-news {
    .titile {
        margin-bottom: 11px;
        font-size: inherit;
    }

    .release-news {
        display: flex;
        color: $font-color-label;
    }

    .release-column {
        a {
            display: inline-block;
        }
    }

    .release-column {
        margin-right: 40px;
    }
}

.right-details-content {
    width: 345px;
}

.information-content {
    padding: 20px 20px 30px;
    background-color: $color-white;

    .information {
        padding: 0;
        border: none;
    }
}

.hot-information {
    padding: 0 20px 20px;
    background-color: $color-white;
}

.el-pagination {
    text-align: center;
}
