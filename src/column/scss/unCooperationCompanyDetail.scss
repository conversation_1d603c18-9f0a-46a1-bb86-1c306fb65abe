@import './variables';

@import './common/fixedTool';
@import './common/popper';

$section-width: 858px;
$aside-width: 320px;
$tabs-margin-bottom: 40px;
$auth-tabs-padding: 40px 130px 0;
$auth-tabs-height: 140px;
$auth-tab-pane-size: 60px;
$main-header-content: 260px;
$border: 2px solid $border-color;
$filter-item-width: 160px;
$filter-input-height: 28px;
$filter-input-icon-top: -5px;
$result-item-width: 75%;

body {
    min-height: 100%;
    height: auto;
    .el-header {
        @include utils-fixed;
    }
    .major-popper {
        .arrow {
            bottom: -5px;
            left: 50%;
            top: auto !important;
            transform: translateX(-50%);
        }
    }
}

.fixed-tool {
    display: none;
}

@mixin tags {
    .tags {
        font-size: 16px;

        span {
            display: inline-block;
            margin-right: 10px;
            padding: 5px 12px;
            background-color: rgba($color: $color-white, $alpha: 0.4);
            border-radius: 4px;

            &:last-of-type {
                margin-right: 0;
            }
        }
    }
}

@mixin company-basic {
    display: flex;
    justify-content: space-between;
    margin: 0 auto;
    padding: 0 20px 0 60px;

    section {
        width: 919px;

        h1 {
            margin-bottom: 20px;
            font-size: 34px;
            font-weight: bold;
            @include utils-ellipsis;
        }
        @include tags;
    }

    .wait-auth {
        display: flex;
        flex-direction: column;
        align-items: center;

        .el-button {
            width: 108px;
        }
    }
}

@mixin contact {
    .box {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: center;
        padding-left: 256px;
        padding-right: 20px;
        height: 285px;
        color: $font-color;
        font-size: 14px;
        background: url(../assets/company/contact-bg.png) no-repeat center / cover;

        $box-items: user mobile addr site;
        $box-item-size: 46px;

        @each $item in $box-items {
            .#{$item} {
                display: flex;
                align-items: center;
                margin-bottom: 18px;

                &::before {
                    content: '';
                    margin-right: 8px;
                    width: $box-item-size;
                    height: $box-item-size;
                    background: url(../assets/company/#{$item}.png) no-repeat center / contain;
                }

                span {
                    @if $item != mobile {
                        flex: 1;
                        word-break: break-word;
                    } @else {
                        & + span {
                            margin-left: 15px;
                        }
                    }
                }
            }

            & > li {
                &:last-of-type {
                    margin-bottom: 0;
                }
            }
        }

        .site {
            a:hover {
                color: $color-primary;
            }
        }
    }
}

@mixin filter {
    .filter {
        margin-bottom: 20px;
        padding: 0 30px;

        .el-select,
        .el-cascader {
            margin-right: 20px;
            width: $filter-item-width;

            &.is-select {
                .el-input__inner {
                    color: $color-primary;
                    border-color: $color-primary;
                }

                .el-input {
                    .el-select__caret {
                        color: $color-primary;
                    }
                }
            }
        }

        .el-input__inner {
            height: $filter-input-height;
            line-height: $filter-input-height;

            &::placeholder {
                color: $font-color;
            }
        }

        .el-input__suffix {
            top: $filter-input-icon-top;
        }

        .el-input__icon {
            line-height: $filter-input-height;
        }
    }
}

@mixin notice-list {
    .notice-top {
        display: flex;
        font-size: 14px;
        justify-content: space-between;
        h5 {
            font-size: 14px;
            width: 667px;
            @include utils-ellipsis;
        }
        .date {
            flex-shrink: 0;
            padding-left: 5px;
            text-align: right;
            color: $color-primary;
        }
    }
    .notice-info {
        display: flex;
        margin-top: 11px;
        justify-content: space-between;
        .info {
            span {
                padding-right: 10px;
                & + span {
                    padding-left: 10px;
                    border-left: 1px solid rgba(51, 51, 51, 0.4);
                }
            }
        }
        .address {
            position: relative;
            .text {
                color: rgba($color: #333, $alpha: 0.6);
            }
            #custom-popper {
                display: none;
                position: absolute;
                bottom: calc(100% + 10px);
                left: 50%;
                transform: translateX(-50%);
                min-width: 100%;
                max-width: 450px;
                width: auto;
                .arrow {
                    top: 100%;
                    left: 50%;
                    transform: translateY(-50%);
                }
            }
        }
    }

    &.offline-mark {
        .notice-top {
            .date {
                color: rgba($color: #333, $alpha: 0.4);
            }
        }
        .notice-info {
            .address {
                .text {
                    color: rgba($color: #333, $alpha: 0.4);
                }
            }
        }
    }
}

.el-main {
    padding: 88px 0 20px;
}

.detail-subtitle {
    position: relative;
    margin-bottom: 30px;

    span {
        position: absolute;
        right: 0;
        padding-left: 12px;
        color: $font-color-label;
        font-size: 12px;
        background-color: $color-white;
    }
}

.boon {
    display: inline-block;
    margin: 0 10px;
    padding: 5px 12px;
    background-color: rgba($color: $color-white, $alpha: 0.4);
    border-radius: 4px;
}

.detail-container {
    display: flex;
    justify-content: space-between;
    margin: 0 auto 120px;
    width: $view-width;

    .section {
        width: $section-width;
        background-color: $color-white;
        border: $border;
        border-radius: 10px;

        .main-header {
            position: relative;

            .header-content {
                @include company-basic;
                margin-bottom: 80px;
                padding: 65px 40px 0;
                height: $main-header-content;
                color: $color-white;
                background: url(../assets/detail/none-auth-bg.png) no-repeat center / cover;

                section {
                    width: 612px;

                    h1 {
                        font-size: 28px;
                    }
                }
            }

            .header-main {
                display: flex;
                justify-content: center;
                .header-tabs {
                    display: flex;
                    margin: 0 auto $tabs-margin-bottom;
                    color: $font-color;
                    justify-content: center;
                    font-size: 16px;
                    border-bottom: 1px solid rgba($color: $font-color, $alpha: 0.1);

                    .tab-pane {
                        position: relative;
                        padding-bottom: 12px;
                        text-align: center;
                        cursor: pointer;
                        margin: 0 15px;
                        &:first-child {
                            margin-left: 0;
                        }
                        &:last-child {
                            margin-right: 0;
                        }
                        &.active {
                            &::before {
                                content: '';
                                position: absolute;
                                left: 0;
                                right: 0;
                                bottom: -2px;
                                height: 4px;
                                background: $color-primary;
                            }
                            span {
                                color: $color-primary;
                            }
                        }
                    }
                }
            }
        }

        .detail-content {
            padding: 0 30px 80px;

            .tab-content {
                display: none;

                &.active {
                    display: block;
                }

                @include filter;

                &.notice {
                    a {
                        &:hover {
                            h5 {
                                color: $color-primary;
                            }
                        }
                    }
                }

                &.position {
                    .result {
                        .item {
                            padding: 20px 30px;

                            a {
                                padding: 0;

                                h5 {
                                    &:hover {
                                        color: $color-primary;
                                    }
                                }
                            }

                            .announcement-detail {
                                display: block;
                                max-width: 380px;
                                font-size: 12px;
                                padding-left: 20px;
                                color: $font-color-basic;
                                background: url(../assets/company/attachment.png) no-repeat left/13px 12px;
                                margin-top: 12px;
                                @include utils-ellipsis;

                                &:hover {
                                    color: $color-primary;
                                }
                            }
                        }
                    }
                }

                .result {
                    margin-bottom: 40px;

                    .item {
                        border-bottom: 1px solid $border-color;

                        a {
                            display: flex;
                            justify-content: space-between;
                            padding: 20px 30px;
                        }

                        section {
                            width: $result-item-width;
                            overflow: hidden;

                            h5 {
                                margin-bottom: 12px;
                                font-size: 14px;
                                @include utils-ellipsis;
                            }

                            span {
                                padding-right: 10px;

                                & + span {
                                    padding-left: 10px;
                                    border-left: 1px solid rgba($color: $font-color, $alpha: 0.4);
                                }
                            }

                            .info {
                                display: flex;

                                h5 {
                                    max-width: $result-item-width;
                                }

                                .date {
                                    margin-left: 15px;
                                    margin-bottom: 12px;
                                    color: $color-primary;
                                    font-size: 14px;
                                    display: inline-flex;
                                    align-items: center;
                                }
                                .offline-tag {
                                    margin-bottom: 0;
                                    margin-left: 0;
                                }
                            }
                        }

                        aside {
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                            color: $font-color-basic;
                            text-align: right;

                            .ft14:not(.color-salary) {
                                color: $color-primary;
                            }
                        }

                        &.offline-mark {
                            section {
                                .info {
                                    .date {
                                        color: $font-color;
                                    }
                                }
                            }
                            aside {
                                span {
                                    &:first-child {
                                        color: $font-color;
                                    }
                                }
                            }
                        }
                    }

                    @include utils-offline-mark();
                }

                &.intro {
                    padding: 0 60px;
                    font-size: 14px;
                    line-height: 2;

                    pre {
                        white-space: pre-wrap;
                    }

                    img {
                        display: block;
                        margin: 10px auto;
                        max-width: 100%;
                    }
                }

                .contact-main {
                    display: flex;
                    padding: 10px 30px 60px;

                    @include contact;
                }

                .contact-other {
                    display: flex;
                    flex-wrap: wrap;
                    padding: 0 30px;

                    .item {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        margin-bottom: 30px;
                        padding: 48px 30px;
                        width: 360px;
                        height: 180px;
                        color: $font-color-basic;
                        font-size: 14px;
                        background-color: $color-white;
                        border: 1px solid $border-color;
                        border-top: 2px solid $color-primary;
                        border-radius: 0px 0px 10px 10px;

                        &:nth-of-type(odd) {
                            margin-right: 14px;
                        }

                        h6 {
                            font-size: 16px;
                        }

                        p {
                            &:last-of-type {
                                display: flex;
                                justify-content: space-between;
                            }
                        }
                    }
                }
            }
        }
    }

    .aside {
        width: $aside-width;

        .detail-basic {
            margin-bottom: 20px;
            padding: 0 0 20px 20px;
            width: 320px;
            background: linear-gradient(180deg, #fff8ee, #ffffff 45%);
            border: $border;
            border-radius: 10px;

            .basic-data {
                display: flex;
                align-items: center;
                margin-bottom: 20px;
                padding-top: 30px;
                background: url(../assets/detail/company-card-bg.png) no-repeat right top / 231px 133px;

                .logo {
                    margin-right: 20px;
                    width: 60px;
                    height: 60px;
                    object-fit: contain;
                    border-radius: 50%;
                    flex-shrink: 0;
                }

                .name {
                    flex: auto;
                    padding-right: 20px;

                    h5 {
                        @include utils-ellipsis-lines(2, 1.5, 16px);
                    }

                    h6 {
                        font-weight: inherit;
                        @include utils-ellipsis-lines(2, 1.5);
                    }
                }
            }

            .basic-info {
                display: flex;
                flex-direction: column;
                padding-right: 20px;
                color: $font-color;
                font-size: 14px;
                line-height: 30px;

                span {
                    padding-left: 30px;
                    word-break: break-word;
                    background-size: 15px;
                    background-position: left 8px;
                    background-repeat: no-repeat;
                }

                i {
                    font-style: normal;
                    padding: 0;
                    &::after {
                        content: ' · ';
                    }
                    &:last-child {
                        &::after {
                            content: '';
                        }
                    }
                }

                $infos: (category, category), (nature, type), (scale, number), (site, site);

                @each $label, $value in $infos {
                    .#{$label} {
                        background-image: url(../assets/detail/#{$value}.png);
                    }
                }

                .site {
                    &:hover {
                        color: $color-primary;
                    }
                }
            }
        }

        .company-style,
        .company-address {
            display: none;
            font-size: 14px;
            background-color: $color-white;
            border-radius: 10px;
            padding: 0 20px 20px;
            margin-bottom: 20px;
            .title {
                font-size: 16px;
                font-weight: bold;
                line-height: 1;
                padding: 20px 0 16px;
            }
        }

        .company-style {
            .company-style-swiper {
                border-radius: 6px;
                overflow: hidden;
                height: 140px;
                a {
                    display: block;
                    height: 100%;
                }
                img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    display: block;
                }
                .swiper-button {
                    --swiper-navigation-size: 20px;
                    width: 20px;
                    height: 20px;
                    background-color: rgba($color: #000000, $alpha: 0.35);
                    border-radius: 4px;
                    &::after {
                        font-size: 12px;
                        font-weight: bold;
                        color: #eee;
                    }
                }
            }

            @include utils-swiper-bullet();

            .company-style-pagination {
                bottom: 7px;
            }
        }

        .company-address {
            .address {
                position: relative;
                padding-left: 23px;
                background: url('../assets/icon/address.png') no-repeat left 5px/18px;
                line-height: 28px;
                word-break: break-word;
            }
        }

        .same-wrapper {
            box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);
            border-radius: 10px;
            padding: 20px 20px 18px;
            background-color: $color-white;
            margin-bottom: 20px;

            .title {
                font-size: 16px;
                font-weight: bold;
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                &::before {
                    content: '';
                    width: 4px;
                    height: 16px;
                    background: $color-primary;
                    border-radius: 2px;
                    margin-right: 7px;
                }
            }

            .same-swiper {
                .item {
                    padding: 10px 12px 17px;
                    background-color: #fafafc;
                    border-radius: 4px;

                    & + .item {
                        margin-top: 10px;
                    }

                    .top {
                        display: flex;
                        margin-bottom: 4px;
                        justify-content: space-between;

                        .name {
                            line-height: 21px;
                            width: 153px;
                            @include utils-ellipsis-lines(2, 21px, 14px);

                            &:hover {
                                color: $color-primary;
                            }
                        }

                        .salary {
                            text-align: right;
                            color: $font-color-salary;
                            font-size: 14px;
                            line-height: 21px;
                        }
                    }

                    .middle {
                        font-size: 12px;
                        color: $font-color-basic;
                        margin-bottom: 9px;
                    }

                    .bottom {
                        font-size: 12px;
                        display: flex;
                        justify-content: space-between;

                        .company {
                            width: 168px;
                            @include utils-ellipsis;

                            &:hover {
                                color: $color-primary;
                            }
                        }

                        .address {
                            box-sizing: border-box;
                            max-width: 74px;
                            @include utils-ellipsis;
                            padding-left: 14px;
                            background: url(../assets/icon/address.png) no-repeat left center/12px 12px;
                        }
                    }
                }

                .swiper-pagination {
                    margin-top: 8px;
                    position: static;

                    .swiper-pagination-bullet-active {
                        background: $color-primary;
                    }
                }
            }
        }

        .to-miniprogram {
            position: relative;
            height: 138px;
            background: url(../assets/company/miniprogram.png) no-repeat center / cover;

            img {
                position: absolute;
                top: 10px;
                left: 12px;
                padding: 4px;
                width: 118px;
                height: 118px;
                background-color: $color-white;
                border-radius: 50%;
                pointer-events: none;
            }
        }
    }
}
