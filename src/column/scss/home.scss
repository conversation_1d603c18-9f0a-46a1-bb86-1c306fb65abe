// 颜色变量
@import './variables';

// 首页样式
@import './common/commonTitle';
@import './common/sideBar';

@import './common/component/rangeA';
@import './common/component/rangeB';
@import './common/component/rangeC';
@import './common/component/rangeD';
@import './common/component/rangeE';
@import './common/component/rangeF';
@import './common/component/rangeH';
@import './common/component/rangeL';
@import './common/component/middle';
@import './common/component/link';
@import './common/component/banner';
@import './common/component/job';
@import './common/component/dropnav';
$border-color-zd01: #e5e5e5;
$border-color-bottom: #d4d4d4;

// 电梯导航栏
.fixed-tool {
    position: fixed;
    top: 530px;
    left: 50%;
    width: $view-width;
    transform: translateX(-50%);

    ul {
        position: absolute;
        left: -66px;
        background-color: $color-white;

        li {
            padding: 8px;
            font-size: 12px;
            color: $font-color-basic;
            border-bottom: 1px dashed $border-color-bottom;
            cursor: pointer;
        }
    }

    .current {
        color: $color-white;
        background-color: $color-primary;
    }
    .special:not(.current) {
        color: #486cf5;
        position: relative;
        display: flex;
        align-items: center;
        font-weight: bold;
        &::after {
            content: '';
            display: block;
            width: 4px;
            height: 12px;
            background-color: #486cf5;
            position: absolute;
            left: 0;
        }
    }
}

// 首页banner区域个人信息
.banner-login-container {
    display: flex;
    flex-direction: column;
    padding: 16px 12px;

    .to-resume {
        position: relative;
        display: flex;
        margin-bottom: 24px;

        .vip-logo {
            position: absolute;
            bottom: 0px;
            left: 25px;
            width: 20px;
            height: 15px;
            background: url(https://img.gaoxiaojob.com/uploads/person/vip-logo.png) no-repeat center/contain;
        }

        .avatar {
            flex: none;
            margin-right: 8px;
            width: 46px;
            height: 46px;
            border-radius: 50%;
            object-fit: cover;
            overflow: hidden;
        }

        .data {
            flex: 1;
            max-width: 81%;
            padding-top: 5px;
        }

        .name {
            @include utils-ellipsis;
            margin-bottom: 7px;
            max-width: 50%;
            font-size: 16px;
        }

        .tips {
            color: $font-color-basic;
            font-size: 12px;

            &.is-warning {
                padding-left: 16px;
                color: $color-primary;
                background: url(../assets/home/<USER>/ 13px;
            }
        }

        .mark {
            position: absolute;
            top: 0;
            right: -12px;
            padding: 5px 16px 5px 13px;
            color: $color-white;
            font-size: 14px;
            line-height: 1;
            background: url(../assets/home/<USER>/ 7px 12px, linear-gradient(90deg, #fabe59 0%, #ffa000 100%);

            border-radius: 12px 0px 0px 12px;
        }
    }

    .link-list {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        a {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .num {
            position: relative;
            margin-bottom: 6px;
            color: $font-color;
            font-size: 18px;
            font-weight: bold;
        }

        .txt {
            color: $font-color;
            font-size: 12px;
        }
    }

    .badge {
        position: absolute;
        top: -15px;
        left: 80%;
        padding: 0 3px;
        height: 14px;
        color: $color-white;
        font-size: 11px;
        font-weight: normal;
        line-height: 14px;
        white-space: nowrap;
        background-color: $font-color-salary;
        border-radius: 10px;

        &.is-special {
            left: 60%;
            height: 16px;
            line-height: 16px;
            border-radius: 4px 0px 4px 0px;
        }
    }

    .operate-btn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 4px;

        .el-button {
            width: 94px;
            font-size: 14px;
            font-weight: bold;
        }

        .el-button--default {
            padding: 7px 10px;
            color: $color-primary;
            background-color: $background-color;
            border-color: $color-primary;

            span {
                padding-left: 17px;
                background: url(../assets/home/<USER>/ 14px;
            }
        }

        &.is-modify {
            .el-button--default {
                position: relative;

                span {
                    background-image: url(../assets/home/<USER>
                }

                .badge {
                    top: -11px;
                    height: 16px;
                    line-height: 16px;
                }
            }
        }
    }
}

// t01区样式
.t01 {
    margin: 8px 0;

    a {
        margin-right: 8px;
        width: calc((100% - 32px) / 5);
        height: 55px;

        &:nth-child(5n) {
            margin-right: 0;
        }
    }
}

// zd01区样式
.zd01 {
    margin-bottom: 10px;
    background-color: $color-white;

    ul {
        justify-content: center;

        li {
            padding: 20px 20px 20px 0;

            a {
                position: relative;
                width: 216px;
                height: 220px;
                border: 1px solid $border-color-zd01;

                .picture {
                    height: 144px;
                }

                .bottom {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    background-color: $color-white;

                    .card-txt {
                        display: flex;
                        height: 42px;
                        margin: 15px 12px;

                        h5 {
                            line-height: 1.4;
                            font-size: 16px;
                            text-overflow: -o-ellipsis-lastline;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                        }

                        .info {
                            margin: 4px 0 0 13px;
                            border-width: 4.5px 0 4.5px 7.8px;
                            display: block;
                            width: 0;
                            height: 0;
                            border-style: solid;
                            border-width: 6.5px 0 6.5px 11.3px;
                            border-color: transparent transparent transparent $color-primary;
                        }
                    }

                    .tips {
                        display: none;

                        h6,
                        p {
                            padding-bottom: 10px;
                            padding-left: 10px;
                            width: 100%;
                            font-size: 12px;
                            @include utils-ellipsis;
                        }
                    }
                }

                &:hover {
                    h6,
                    p {
                        color: $font-color;
                    }

                    .tips {
                        display: block;
                    }
                }
            }

            &:last-child {
                padding-right: 0;
            }
        }
    }
}

// 首页精选职位下的热招学科样式
.subject-content {
    flex-wrap: wrap;

    li {
        margin: 10px 10px 0 0;
        width: 211px;
        height: 72px;
        border: 1px solid $border-color;

        .subject-list {
            display: flex;
            align-items: center;
            padding: 17px;
        }

        &:nth-child(4n) {
            margin-right: 0;
        }
    }

    .serial-number {
        flex: none;
        margin-right: 12px;
        font-size: 20px;
        font-weight: bold;
        color: $font-color-tips;
    }

    .number-bg {
        padding-top: 5px;
        width: 26px;
        height: 35px;
        text-align: center;
        font-size: 16px;
        color: $color-white;
    }

    .number-one {
        background: url('../assets/home/<USER>') no-repeat center / cover;
    }

    .number-two {
        background: url('../assets/home/<USER>') no-repeat center / cover;
    }

    .number-three {
        background: url('../assets/home/<USER>') no-repeat center / cover;
    }

    .number-four {
        background: url('../assets/home/<USER>') no-repeat center / cover;
    }

    .subject-information {
        width: 141px;
        @include utils-ellipsis;
    }

    .subject-subtitle {
        display: block;
        padding-top: 6px;
        font-size: 12px;
        color: $font-color-basic;
    }

    @for $i from 1 through 4 {
        li:nth-of-type(#{$i}) {
            margin-top: 0;
        }
    }
}
