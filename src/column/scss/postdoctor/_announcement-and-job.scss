@import '../variablesPostdoctor';

.main-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    padding-top: 20px;
    padding-bottom: 1px;
    background: url(//img.gaoxiaojob.com/uploads/boshihou/company/bg.png) no-repeat left top/100% auto,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-left-bg.png) no-repeat left 169px/380px 264px,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-right-bg.png) no-repeat right 169px/380px 264px, linear-gradient(180deg, #ffe9ea 0, #f8f8f8 326px);

    & > div {
        width: $view-width;
    }

    .showcase-wrapper {
        width: $view-width;

        @import './postdoctor/a1-and-b1';
    }

    .filter-wrapper {
        width: $view-width;
        background: url(//img.gaoxiaojob.com/uploads/boshihou/announcement/filter-bg.png) no-repeat right 20px bottom 1px/530px 47px, $color-white;
        border-radius: 12px;
        padding: 20px 20px 8px;
        margin-bottom: 20px;

        .filter-row {
            display: flex;
            margin-bottom: 2px;

            button {
                border: none;
                background: transparent;
            }

            .filter-label {
                width: 93px;
                line-height: 32px;
                flex-shrink: 0;
                font-size: 16px;
                font-weight: bold;

                .tips {
                    color: $font-color-label;
                    margin-left: 5px;
                    font-size: 14px;
                    line-height: 1;
                    font-weight: normal;
                }
            }

            .filter-value {
                display: flex;
                flex-wrap: wrap;
                flex-grow: 1;
                max-width: 983px;

                .filter-item {
                    margin-right: 23px;
                    padding: 8px 0;
                    border-radius: 4px;
                    line-height: 1;
                    cursor: pointer;

                    &:hover {
                        color: $color-primary;
                    }

                    &.active {
                        color: $color-primary;
                    }
                }

                .select-item {
                    display: flex;
                    margin-right: 10px;
                    padding: 7px 8px;
                    border-radius: 4px;
                    margin-bottom: 12px;
                    line-height: 1;
                    color: $color-primary;
                    background-color: $tag-background;

                    .filter-clear {
                        width: 12px;
                        height: 100%;
                        margin-left: 3px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/clear.png) no-repeat left/12px 12px;
                    }
                }

                .open-login {
                    padding: 0 7px;
                    line-height: 22px;
                    border-radius: 22px;
                    border: 1px solid $color-primary;
                    color: $color-primary;
                    align-self: center;
                    margin-right: 23px;
                }
            }

            .filter-more {
                flex-grow: 1;
                text-align: right;
                flex-shrink: 0;
                color: $color-primary;
                line-height: 28px;
                cursor: pointer;
            }

            .filter-clear-all {
                text-wrap: nowrap;
                align-self: start;
                display: flex;
                line-height: 32px;
                cursor: pointer;
                color: $font-color-basic;
                padding-left: 19px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/delete.png) no-repeat left/15px 15px;

                span {
                    color: $color-primary;
                }
            }
        }

        .kw-row {
            margin-bottom: 14px;

            .filter-value {
                flex-wrap: nowrap;
                max-width: initial;
                overflow: hidden;
            }

            .filter-label {
                line-height: 32px;
            }

            .search {
                flex-shrink: 0;
                background-color: $color-primary;
                border-radius: 8px;
                padding: 1px;
                display: flex;
                margin-right: 20px;
                display: flex;
                width: 282px;

                .search-input {
                    background-color: $color-white;
                    border-radius: 8px;
                    overflow: hidden;
                    width: 220px;
                }

                .kw {
                    width: 100%;
                    height: 30px;
                    line-height: 30px;
                    display: flex;
                    align-items: center;

                    input {
                        border: none;
                        background-color: $color-white;
                        height: 100%;
                        padding-left: 10px;
                        line-height: 30px;
                        height: 30px;
                    }

                    .el-input__suffix {
                        display: flex;
                        align-items: center;
                    }
                }

                .search-btn {
                    color: $color-white;
                    padding: 0 16px;
                    line-height: 30px;
                }
            }

            .search-hot {
                display: flex;
                font-size: 12px;
                align-items: center;
                flex-grow: 1;
                overflow: hidden;

                &-label {
                    flex-shrink: 0;
                    padding-left: 20px;
                    margin-right: 7px;
                    background: url(//img.gaoxiaojob.com/uploads/boshihou/common/hot.png) no-repeat left/18px 18px;
                    color: $font-color-basic;
                }

                &-value {
                    display: flex;
                    flex-wrap: nowrap;
                }

                .item {
                    line-height: 24px;
                    padding: 0 8px;
                    border-radius: 4px;
                    background-color: #f3f8fd;
                    margin-right: 7px;
                    white-space: nowrap;

                    &.active {
                        color: $color-primary;
                        background-color: $tag-background;
                    }
                }
            }
        }

        .more-row {
            margin-bottom: 10px;

            .filter-value {
                align-items: center;
            }

            .custom-select {
                width: 95px;
                position: relative;
                margin-right: 23px;

                .el-select {
                    .el-input__inner {
                        padding-left: 10px;
                        padding-right: 30px;
                        border: none;
                        background-color: #f8f8f8;
                        font-size: 14px;

                        &::placeholder {
                            color: $font-color;
                        }
                    }

                    .el-input__suffix {
                        width: 30px;
                        right: 0;

                        .el-input__suffix-inner {
                            width: 100%;
                            height: 100%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            position: relative;

                            i {
                                position: absolute;
                            }

                            .el-icon-circle-close {
                                display: none;
                            }
                        }
                    }

                    .down {
                        display: inline-block;
                        width: 9px;
                        height: 5px;
                        transition: all ease 0.3s;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/down.png) no-repeat center/9px 5px;
                        transform-origin: center;
                        transform: translateY(-1px);
                        cursor: pointer;
                    }
                }

                .el-select__popper {
                    top: calc(100% + 10px);
                    opacity: 0;
                    transform: scaleY(0);

                    .el-select-dropdown {
                        background-color: $color-white;

                        .el-select-dropdown__item {
                            display: block;

                            &:hover {
                                color: $font-color;
                            }

                            &.selected {
                                color: $color-primary;
                                background-color: var(--el-background-color-base);
                            }
                        }
                    }

                    .el-popper__arrow {
                        top: -5px;
                        left: 47px;
                        transform: translateX(-50%);
                    }
                }

                &.active {
                    .el-select {
                        .down {
                            transform: translateY(-1px) rotate(180deg);
                        }
                    }

                    .el-select__popper {
                        transition: var(--el-transition-md-fade);
                        opacity: 1;
                        transform-origin: center top;
                        transform: scaleY(1);
                    }
                }

                &.is-select {
                    .el-select {
                        .el-input__inner {
                            color: $color-primary;
                        }

                        .down {
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/down-active.png) no-repeat center/9px 5px;
                        }

                        .el-icon-circle-close {
                            color: $color-primary;
                        }
                    }
                    .el-input__inner {
                        background-color: $tag-background;

                        &::placeholder {
                            color: $color-primary;
                        }
                    }
                }
            }

            .checkbox {
                padding-left: 22px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/checkbox.png) no-repeat left/16px 16px;

                &.is-checked {
                    background: url(//img.gaoxiaojob.com/uploads/boshihou/common/checkbox-checked.png) no-repeat left/16px 16px;
                    color: $color-primary;
                }
            }
        }
    }

    .content-wrapper {
        display: flex;
        padding-bottom: 20px;

        .left-content {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            margin-right: 20px;

            .tabs-content {
                display: flex;
                height: 44px;
                background: linear-gradient(180deg, #fce6e1, #ffffff);
                border-radius: 12px 12px 0px 0px;

                .tabs {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    font-size: 20px;
                    height: 100%;
                    color: $font-color-label;

                    .english {
                        position: absolute;
                        top: 4px;
                        font-weight: bold;
                        font-size: 24px;
                        background: linear-gradient(180deg, #fbd3cb 0%, #fef6f6 100%);
                        opacity: 0.4;
                        background-clip: text;
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }

                    .name {
                        z-index: 1;
                    }

                    &:not(.active) {
                        flex-grow: 1;
                    }

                    &.active {
                        width: 484px;
                        font-weight: bold;
                        color: $color-primary;

                        &::after {
                            content: '';
                            width: 38px;
                            height: 4px;
                            background: $color-primary;
                            border-radius: 2px;
                            position: absolute;
                            bottom: 0;
                        }
                    }

                    &:first-child {
                        &.active {
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/announcement/tabs-announcement.png) no-repeat right top/auto 100%;
                        }
                    }

                    &:last-child {
                        &.active {
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/announcement/tabs-job.png) no-repeat right top/auto 100%;
                        }
                    }

                    &:hover {
                        color: $color-primary;
                    }
                }
            }

            .list-content {
                .list {
                    display: block;
                    background: $color-white;
                    border-radius: 12px;
                    padding: 14px 20px 16px 20px;
                    margin-bottom: 12px;
                    border: 1px solid $color-white;
                    position: relative;

                    &:first-child {
                        border-radius: 0px 0px 12px 12px;
                    }

                    .top {
                        display: flex;
                        align-items: baseline;
                        margin-bottom: 9px;
                        justify-content: space-between;

                        .title {
                            line-height: 24px;
                            font-weight: bold;
                            font-size: 16px;
                            max-width: 717px;

                            .tag-content {
                                display: inline-flex;
                                font-weight: normal;

                                .tag {
                                    display: inline-block;
                                    font-size: 12px;
                                    border-radius: 4px;
                                    line-height: 18px;
                                    padding: 0 5px;
                                    margin-right: 5px;

                                    &.offline {
                                        display: none;
                                        border: 1px solid #dddddd;
                                        color: $font-color-basic;
                                    }

                                    &.urgent {
                                        background-color: #fff0ef;
                                        color: $font-color-salary;
                                    }

                                    &.recommend {
                                        display: none;
                                        background-color: #e5edff;
                                        color: #5e84db;
                                    }

                                    &.pi {
                                        background-color: #fde3cb;
                                        color: rgba($color: #6e4843, $alpha: 0.6);
                                    }
                                }
                            }
                        }
                    }

                    .view {
                        font-weight: bold;
                        padding: 0 20px;
                        line-height: 32px;
                        position: absolute;
                        right: 20px;
                        top: calc(50% - 16px);
                        color: $color-white;
                        background: $liner-gradient-primary;
                        border-radius: 20px;
                        display: none;
                    }

                    &.is-recommend {
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/announcement/recommend-bg.png) no-repeat right bottom/151px 115px, $color-white;

                        .top {
                            .title {
                                .tag-content {
                                    .tag {
                                        &.recommend {
                                            display: inline-flex;
                                        }
                                    }
                                }
                            }
                        }

                        .release-time {
                            display: none;
                        }
                    }

                    &:hover {
                        .view {
                            display: block;
                        }
                    }
                }
            }

            .pagination {
                margin-top: 20px;
            }

            .empty-container {
                height: 632px;
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
                padding-top: 415px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/empty.png) no-repeat center 190px/266px 194px, $color-white;
                text-align: center;
                color: $font-color-basic;
                margin-bottom: 20px;
            }
        }

        .aside {
            width: 280px;
            flex-shrink: 0;

            .login-guide {
                height: 146px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/login-guide.png) no-repeat left top/100% 100%;
                cursor: pointer;
                margin-bottom: 20px;
            }

            .ranking {
                padding: 67px 20px 5px 17px;
                border-radius: 12px;
                margin-bottom: 20px;
                counter-reset: item;

                .list {
                    counter-increment: item;

                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;

                    $index: counter(item);

                    &::before {
                        content: '';
                        flex-shrink: 0;
                        width: 20px;
                        height: 22px;
                        line-height: 22px;
                        margin-right: 15px;
                        font-size: 16px;
                        font-weight: bold;
                        color: $font-color-tips;
                    }

                    $icons: ((1, first), (2, second), (3, third));

                    @each $item in $icons {
                        $index: nth($item, 1);
                        $icon: nth($item, 2);

                        &:nth-child(#{$index}) {
                            &::before {
                                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/#{$icon}.png?v=1.0) no-repeat center/18px 23px;
                            }
                        }
                    }

                    @for $i from 4 through 9 {
                        &:nth-child(#{$i}) {
                            &::before {
                                content: '0' counter(item);
                            }
                        }
                    }

                    @for $i from 10 through 20 {
                        &:nth-child(#{$i}) {
                            &::before {
                                content: counter(item);
                            }
                        }
                    }
                }
            }

            .qr-code {
                border-radius: 12px;
                height: 103px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/announcement/qrcode.png) no-repeat 20px 18px/68px 68px,
                    url(//img.gaoxiaojob.com/uploads/boshihou/announcement/qrcode-bg.png) no-repeat left top/100% 100%;
                padding: 20px 22px 10px 108px;

                .title {
                    color: #187ab4;
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 6px;
                }

                .desc {
                    font-size: 13px;
                    line-height: 20px;
                }
            }
        }
    }
}
