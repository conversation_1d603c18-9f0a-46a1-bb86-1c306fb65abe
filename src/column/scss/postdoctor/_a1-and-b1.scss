@import '../variablesPostdoctor';

.showcase-top,
.showcase-bottom {
    display: flex;
}

.showcase-top {
    .item {
        width: 590px;

        & + .item {
            margin-left: 20px;
        }
    }
}

.showcase-bottom {
    flex-wrap: wrap;
    margin-bottom: 7px;

    .item {
        width: 290px;
        margin-right: 13px;
        margin-bottom: 13px;

        &:nth-child(4n) {
            margin-right: 0;
        }
    }
}

.item {
    margin-bottom: 20px;
    border-radius: 12px;
    height: 90px;
    overflow: hidden;

    img {
        display: block;
        width: 100%;
        height: 100%;
    }
}
