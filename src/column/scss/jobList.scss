@import './variables';
@import './common/sideBar';
@import './job-aside';

#component {
    .search-header {
        padding: 20px 0;
        background-color: $color-white;

        &.is-fixed {
            position: fixed;
            top: $header-height;
            left: 0;
            right: 0;
            box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.1);
            // z-index: $header-index;
            // 这里考虑到上面会有需要出现的小程序,所以这里需要比header低一点
            z-index: 1900;

            .recommend-container,
            .hot-search,
            .is-area {
                display: none;
            }

            .filter-area {
                display: block;
            }
        }
    }

    .recommend-container {
        margin: 0 auto 15px;
        width: $view-width;

        .el-carousel {
            margin-bottom: 10px;
        }

        .el-carousel__item {
            display: flex;
            padding: 2px 1px;

            a {
                flex: none;
                margin-right: 11px;
                width: 140px;
                height: 70px;
                background-color: $color-white;
                box-shadow: 0 0 0 1px $border-color;
                border-radius: 8px;
                overflow: hidden;

                &:last-of-type {
                    margin-right: 0;
                }

                & > img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }
        }

        .el-carousel__indicator {
            margin-right: 6px;
            padding: 0;
            width: 6px;
            height: 6px;
            background: #dedede;
            border-radius: 50%;

            &:last-of-type {
                margin-right: 0;
            }

            &.is-active {
                width: 20px;
                height: 6px;
                background: $color-primary;
                border-radius: 3px;
            }

            .el-carousel__button {
                display: none;
            }
        }
    }

    .recommend-tabs {
        padding-bottom: 5px;
        font-size: 14px;
        text-align: center;
        line-height: 16px;

        .label {
            padding-left: 30px;
            background: url(../assets/job/hot.png) no-repeat left center / 28px 17px;
        }

        .value {
            margin-left: 30px;
            color: $font-color-basic;
            cursor: pointer;

            &.is-active {
                position: relative;
                color: $color-primary;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -7px;
                    width: 100%;
                    height: 2px;
                    background-color: $color-primary;
                }
            }
        }
    }

    .search-query {
        margin: 0 auto;
        width: $view-width;
    }

    $search-height: 50px;
    $search-padding-right: 135px;

    .search-input-cell {
        position: relative;

        .miniprogram-cell {
            position: absolute;
            top: -11px;
            right: 0;
            display: flex;
            flex-direction: column;
            padding: 20px 12px;
            width: 210px;
            height: 72px;
            background: url(https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/index.jpg) no-repeat calc(100% - 12px) center / 63px, linear-gradient(180deg, #fff2dc, #fffaf2);
            border-radius: 10px;
        }

        .title {
            margin-bottom: 4px;
            color: $font-color;
            font-size: 16px;
            font-weight: bold;
        }

        .tips {
            color: $color-primary;
            font-size: 14px;
        }
    }

    .search-keyword {
        position: relative;
        display: block;
        width: 974px;
        height: $search-height;
        box-shadow: 0 0 0 1px $color-primary;
        border-radius: 10px;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 18px;
            left: 115px;
            width: 1px;
            height: 15px;
            background-color: #dddddd;
        }

        .el-input__inner {
            padding: 0 $search-padding-right 0 128px;
            height: $search-height;
            font-size: 14px;
            line-height: $search-height;
            border: none;

            &::placeholder {
                color: $font-color-label;
            }
        }

        .el-input__prefix {
            left: 0;

            .el-input__icon {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                color: $font-color;
                cursor: pointer;
            }

            &:hover {
                .job-category,
                .el-input__icon {
                    color: $color-primary;
                }
            }

            .el-icon-circle-close {
                display: none;
                color: $color-primary;
            }
        }

        &.is-checked {
            .el-input__prefix {
                .job-category {
                    color: $color-primary;
                    font-weight: bold;
                }

                .el-icon-arrow-down {
                    color: $color-primary;
                }

                &:hover {
                    .el-icon-arrow-down {
                        display: none;
                    }

                    .el-icon-circle-close {
                        display: block;
                    }
                }
            }
        }

        .el-input__suffix {
            right: -1px;

            .el-input__icon {
                position: absolute;
                right: $search-padding-right;
                font-size: 14px;
                height: $search-height;
            }
        }

        .job-category {
            @include utils-ellipsis;
            display: block;
            padding: 0 15px;
            width: 110px;
            color: $font-color;
            font-size: 14px;
            font-weight: bold;
            text-align: left;
            line-height: $search-height;
            cursor: pointer;
        }

        .search-button {
            padding: 0 20px;
            color: $color-white;
            font-size: 18px;
            font-weight: bold;
            width: 120px;
            height: $search-height;
            border-radius: 0px;

            span {
                display: inline-block;
                padding-left: 34px;
                line-height: 20px;
                background: url(../assets/job/search-button.png) no-repeat left center / 20px;
            }
        }
    }

    .search-params {
        .hot-search {
            margin-top: 20px;
            height: 16px;
            color: $font-color-basic;
            font-size: 14px;
            line-height: 16px;
            overflow: hidden;

            & > a {
                display: inline-block;
                margin-left: 24px;

                &:first-of-type {
                    margin-left: 18px;
                }

                &:hover {
                    color: $color-primary;
                }
            }
        }

        .search-filter {
            display: flex;
            margin-top: 15px;
            line-height: 1;

            &.filter-option {
                justify-content: space-between;
                align-items: center;
            }

            .cell,
            .filter-cell {
                flex: 1;
                display: flex;
            }

            .filter-cell {
                .label {
                    margin-top: 7px;
                }
            }

            .event {
                flex: none;
                cursor: pointer;
                margin-top: 6px;
                font-size: 14px;

                &.is-primary {
                    color: $color-primary;

                    & > i {
                        font-style: normal;
                        font-weight: bold;
                    }
                }

                &.is-arrow {
                    display: flex;

                    &::after {
                        content: '';
                        margin-top: 3px;
                        margin-left: 10px;
                        width: 9px;
                        height: 5px;
                        background: url(../assets/job/expand.png) no-repeat center / contain;
                    }
                }

                &.is-reverse {
                    &::after {
                        transform: rotate(180deg);
                    }
                }
            }

            .label {
                flex: none;
                margin-top: 5px;
                margin-right: 20px;
                font-size: 15px;
                font-weight: bold;
            }

            .value-cell {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                height: 26px;
                overflow: hidden;

                &.show-values {
                    height: auto;
                }
            }

            .value {
                margin-right: 12px;
                margin-bottom: 8px;
                padding: 6px 10px;
                font-size: 14px;
                box-sizing: border-box;
                cursor: pointer;

                &[checked='true'] {
                    @include utils-radius;
                    color: $color-primary;
                    background: $background-color;

                    &::after {
                        content: '';
                        display: inline-block;
                        margin-left: 3px;
                        width: 10px;
                        height: 10px;
                        background: url(../assets/job/remove.png) no-repeat center / contain;
                    }
                }

                &.is-special {
                    &::after {
                        display: none;
                    }
                }

                &:hover {
                    color: $color-primary;
                }
            }

            .el-select,
            .el-cascader {
                margin-right: 16px;
                width: 110px;

                &:last-of-type {
                    margin-right: 0;
                }
            }

            .filter-area {
                @include utils-radius;
                @include utils-ellipsis;
                padding: 0 10px 0 24px;
                width: auto;
                min-width: 62px;
                max-width: 120px;
                font-size: 14px;
                background: #f8f8f8 url(../assets/job/addr.png) no-repeat 10px center / 10px 12px;
                cursor: pointer;

                &.is-checked {
                    color: $color-primary;
                    background-color: $background-color;
                    background-image: url(../assets/job/addr-checked.png);
                }
            }

            .filter-major {
                &.is-checked {
                    width: 124px;

                    &:hover {
                        .el-input__suffix-inner {
                            display: none;
                        }

                        .filter-major-clear {
                            display: block;
                        }
                    }
                }

                .filter-major-clear {
                    display: none;
                }
            }

            .el-input__inner {
                @include utils-ellipsis;
                padding-left: 10px;
                color: $color-primary;
                font-size: 14px;
                background-color: #f8f8f8;
                border: none;
                box-sizing: border-box;

                &::placeholder {
                    color: $font-color;
                }
            }

            .is-checked {
                .el-input__inner {
                    background-color: $background-color;
                }

                .el-input__icon {
                    color: $color-primary;
                }
            }

            .trash-button {
                padding-left: 20px;
                color: $font-color-basic;
                font-size: 14px;
                background: url(../assets/job/trash.png) no-repeat left center / 14px;
                cursor: pointer;

                & > i {
                    color: $color-primary;
                    font-style: normal;
                    font-weight: bold;
                }
            }
        }
    }

    .filter-area {
        display: none;
    }

    .search-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin: 0 auto;
        padding-top: 20px;
        width: $view-width;

        .single-page {
            margin: 0 auto;
            width: 348px;
            color: $font-color-label;
            font-size: 14px;
            text-align: center;
            line-height: 1;
            background: url(../assets/job/no-more.png) no-repeat center / contain;
        }

        .single-page,
        .el-pagination {
            margin-top: 20px;
        }

        .el-pagination.is-background .btn-prev,
        .el-pagination.is-background .btn-next,
        .el-pagination.is-background .el-pager li {
            background-color: $color-white;
        }

        .el-pagination.is-background .el-pager li:not(.disabled).active {
            background-color: $color-primary;
        }
    }

    .search-section {
        flex: none;
        width: 900px;

        .el-loading-mask {
            z-index: initial;
        }

        .search-result {
            padding: 20px 20px 0;
            border-radius: 10px;
            background-color: $color-white;
            overflow: hidden;
        }

        .result-header {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                display: flex;
                align-items: center;
                color: $font-color;
                font-size: 18px;
                font-weight: bold;
                line-height: 1;

                &::before {
                    content: '';
                    margin-right: 12px;
                    width: 4px;
                    height: 16px;
                    background-color: $color-primary;
                    border-radius: 2px;
                }
            }

            .filter {
                .establishment {
                    padding-right: 42px;
                    background: url(../assets/job/establishment.png) no-repeat right/35px;
                    img {
                        width: 15px;
                        height: 15px;
                        vertical-align: top;
                    }
                }
            }

            .el-checkbox__label {
                color: $font-color;
            }

            .el-checkbox__input.is-checked + .el-checkbox__label {
                color: $color-primary;
            }
        }

        .result-guide {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 30px 15px 86px;
            font-size: 14px;
            line-height: 1;
            background: #fff5e7 url(../assets/job/result-guide.png) no-repeat center / contain;
            border-radius: 10px;
            overflow: hidden;

            .question {
                margin-bottom: 10px;
                color: $color-primary;
                font-size: 18px;
                font-weight: bold;
            }

            .button {
                padding: 0 14px 0 34px;
                color: $color-white;
                font-size: 14px;
                line-height: 30px;
                background: $color-primary url(../assets/job/add.png) no-repeat 12px center / 16px;
                border-radius: 15px;
                cursor: pointer;
            }
        }

        .result-list {
            $space: 15px;

            a {
                &:hover {
                    color: $color-primary;
                }
            }

            .result-item {
                display: flex;
                justify-content: space-between;
                position: relative;
                padding: 20px 0;
                min-height: 86px;
                line-height: 1;
                box-shadow: 0 1px 0 0 #ebebeb;

                &::after {
                    content: '';
                    position: absolute;
                    left: 760px;
                    bottom: 0;
                    width: 60px;
                    height: 60px;
                    background: url(../assets/job/result-item.png) no-repeat center / contain;
                }

                &:hover {
                    .type,
                    .tags {
                        opacity: 0;
                        user-select: none;
                    }

                    .operate {
                        display: block;
                    }
                }

                &:last-of-type {
                    box-shadow: none;
                }
            }

            .showcase-item {
                margin-left: -20px;
                margin-right: -20px;
                padding-top: 1px;

                .showcase-link {
                    position: relative;
                    display: flex;

                    &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        bottom: 0px;
                        height: 1px;
                        left: 20px;
                        right: 20px;
                        background-color: #ebebeb;
                    }
                }

                &:last-of-type {
                    .showcase-link::after {
                        display: none;
                    }
                }
            }

            .item-cell {
                display: flex;
                flex-direction: column;
            }

            .item-cell-3 {
                width: 472px;
            }

            .item-cell-2 {
                position: relative;
                width: 254px;
            }

            .item-cell-1 {
                align-self: center;
                width: 44px;
                height: 44px;

                a {
                    display: block;
                }

                img {
                    display: block;
                    width: 44px;
                    height: 44px;
                    object-fit: contain;
                    border-radius: 50%;
                    overflow: hidden;
                }

                .is-special {
                    position: relative;

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: -8px;
                        width: 42px;
                        height: 18px;
                        background: url(../assets/job/auth-mark.png) no-repeat center / contain;
                    }
                }
            }

            .title {
                display: flex;
                align-items: center;
                margin-bottom: $space;
                font-size: 16px;
                font-weight: bold;

                &.offline {
                    a {
                        color: $font-color-label;

                        &::before {
                            content: '已下线';
                            display: inline-block;
                            margin-right: 6px;
                            width: 55px;
                            height: 22px;
                            color: $font-color-label;
                            font-size: 14px;
                            font-weight: normal;
                            text-align: center;
                            line-height: 22px;
                            border: 1px solid #ebebeb;
                            border-radius: 4px;
                        }
                    }
                }

                a {
                    @include utils-ellipsis;
                    line-height: 1.24;
                }

                span {
                    @include utils-radius;
                    flex: none;
                    margin-left: 6px;
                    font-size: 12px;
                }
            }

            .establishment {
                padding: 3px 10px 3px;
                color: #4fbc67;
                background-color: #f0ffdc;
            }

            .help-wanted {
                padding: 3px 10px 3px;
                color: $font-color-salary;
                background-color: #fff0ef;
            }

            .fast-feedback {
                padding: 3px 5px 3px 15px;
                color: $color-primary;
                background: $background-color url(../assets/job/fast-feedback.png) no-repeat 6px 4px / 8px 12px;
            }

            .label {
                display: flex;
                align-items: center;
                font-size: 14px;

                span {
                    flex: none;
                    margin-left: 10px;
                }

                .wage {
                    margin-left: 0;
                    margin-right: 10px;
                    color: $font-color-salary;
                    font-size: 16px;
                    font-weight: bold;
                }

                .area {
                    @include utils-ellipsis;
                    max-width: 120px;
                }

                .is-special {
                    & + .is-special {
                        padding-left: 10px;
                        box-shadow: -1px 0 0 0 $font-color-tips;
                    }
                }

                .refresh {
                    display: flex;
                    align-items: center;
                    color: $font-color-basic;

                    &::before {
                        content: '';
                        margin-right: 6px;
                        width: 14px;
                        height: 14px;
                        vertical-align: text-bottom;
                        background: url(../assets/job/time.png) no-repeat center / contain;
                    }
                }
            }

            .source {
                @include utils-ellipsis;
                margin-top: $space + 2;
                padding-left: 20px;
                color: $font-color-basic;
                font-size: 14px;
                background: url(../assets/job/attachment.png) no-repeat left center / 13px 12px;
            }

            .name {
                @include utils-ellipsis;
                margin-bottom: $space;
                font-size: 16px;
                font-weight: bold;
            }

            .type {
                @include utils-ellipsis;
                margin-top: 2px;
                color: $font-color-basic;
                font-size: 14px;

                span {
                    & + span {
                        &::before {
                            content: '·';
                        }
                    }
                }
            }

            .tags {
                margin-top: $space - 2;
                height: 20px;
                overflow: hidden;

                span {
                    @include utils-radius;
                    display: inline-block;
                    margin-right: 8px;
                    padding: 4px 8px;
                    height: 20px;
                    font-size: 12px;
                    white-space: nowrap;
                    background-color: #f3f8fd;
                }
            }

            .operate {
                display: none;
                position: absolute;
                top: 36px;

                &.is-special {
                    top: 26px;
                }

                .el-button {
                    font-size: 14px;
                    padding-left: 10px;
                    padding-right: 10px;
                    width: 80px;
                    height: 32px;
                    border: none;
                }

                .chat-button {
                    color: $color-primary;
                    border: 1px solid $color-primary;
                    padding-left: 35px;
                    width: 92px;
                    background: url(../assets/common/chat-primary.png) no-repeat left 13px center / 20px;

                    &:hover {
                        background-color: #fff1da;
                    }
                }

                .collect-button {
                    padding-left: 24px;
                    color: $color-primary;
                    text-align: left;
                    background: url(../assets/job/collect.png) no-repeat left center / 20px;

                    &.is-checked {
                        background-image: url(../assets/job/collected.png);
                    }

                    &:hover {
                        color: $color-primary;
                        background-color: transparent;
                    }
                }
            }
        }

        .empty {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 100px 0;
            color: $font-color-basic;
            font-size: 14px;
            line-height: 1;

            &::before {
                content: '';
                margin-bottom: 16px;
                width: 150px;
                height: 111px;
                background: url(../assets/job/empty.png) no-repeat center / contain;
            }
        }
    }

    .search-aside {
        width: 280px;

        .guide-card {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;

            & > img {
                display: block;
                width: 100%;
            }
        }
        .advert-vip {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;

            & > img {
                display: block;
                width: 100%;
            }
        }

        .subscription {
            display: flex;
            flex-direction: column;
            margin-bottom: 20px;
            padding: 20px 20px 24px;
            line-height: 1;
            background: url(../assets/job/subscription.png) no-repeat center / cover;
            border-radius: 10px;
            overflow: hidden;

            p {
                font-size: 14px;
                line-height: 1.5;
            }

            .title {
                position: relative;
                margin-bottom: 20px;
                font-size: 18px;
                font-weight: bold;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -4px;
                    width: 20px;
                    height: 4px;
                    background: linear-gradient(90deg, #ffaf27, #ffe5b9);
                    border-radius: 2px;
                }
            }

            .subscribe {
                margin-top: 20px;
                text-align: center;

                .el-button {
                    padding: 7px 21px;
                    font-size: 14px;
                    border: none;
                }
            }
        }

        .aside-link {
            display: flex;
            flex-direction: column;

            a {
                & + a {
                    margin-top: 20px;
                }

                & > img {
                    display: block;
                    width: 100%;
                    border-radius: 10px;
                    overflow: hidden;
                }
            }
        }
    }

    .select-dialog {
        & > .el-input {
            display: none;
        }
    }

    .superior-filter-global {
        margin-right: 20px;
    }
}
