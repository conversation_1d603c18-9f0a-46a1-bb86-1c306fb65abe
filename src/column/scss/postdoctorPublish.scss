@import './variablesPostdoctor';

.main-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-repeat: no-repeat;
    padding-bottom: 1px;

    // .swiper-t1 {
    // width: 1200px;
    // height: 170px;
    // background-color: $color-white;
    // border-radius: 12px;
    // padding-bottom: 60px;
    // @include utils-swiper-bullet(#d6d3d2, $color-primary, 0.8);

    // .swiper-pagination {
    //     display: flex;
    //     bottom: 12px;
    //     justify-content: center;
    // }

    // .swiper-pagination-bullet {
    //     margin: 0 4px;
    //     height: 4px;
    //     width: 27px;
    //     border-radius: 4px;
    // }
    // }
    .view-content {
        width: 1200px;
        margin: 0 auto;
    }
    .banner {
        width: 100%;
        height: 380px;
        overflow: hidden;
        position: relative;
        img {
            width: 2500px;
            height: 380px;
            position: absolute;
            left: 50%;
            top: 0;
            transform: translate(-50%,0);
        }
    }
    .first-wrap {
        width: 100%;
        background-color: #fff;
        .list-header {
            @include wrapper-title;
            padding-top: 60px;
            margin-bottom: 0;
        }
        .list-content {
            .con {
                text-indent: 2em;
            }
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            line-height: 31px;
            padding: 30px 0 60px;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/first-r.png) no-repeat left top/120px 58px,
                url(//img.gaoxiaojob.com/uploads/boshihou/publish/first-l.png) no-repeat right bottom/192px 138px;
            span {
                color: #b61f22;
                font-weight: bold;
            }
        }
    }

    .about-wrap {
        width: 100%;
        background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/about-bg.png) no-repeat right bottom/742px 543px, #f8f8f8;
        .list-header {
            @include wrapper-title;
            padding-top: 40px;
            margin-bottom: 0;
        }
        .ts {
            text-align: center;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            opacity: 0.8;
        }
        .list-content {
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            line-height: 31px;
            padding: 40px 0;
            display: flex;
            justify-content: space-between;

            .content-l {
                width: 520px;
                height: 415px;
                img {
                    width: 520px;
                    height: 415px;
                }
            }
            .content-r {
                .box {
                    &:first-child {
                        margin: 14px 0 40px;
                    }
                }
                .tit {
                    span {
                        display: inline-block;
                        padding: 0 10px 0 18px;
                        line-height: 24px;
                        background: #b61f22;
                        border-radius: 4px 0px 0px 0px;
                        font-weight: 400;
                        font-size: 14px;
                        color: #ffffff;
                        position: relative;
                        &::after {
                            position: absolute;
                            right: -16px;
                            bottom: 0;
                            content: '';
                            width: 0;
                            height: 0;
                            border-top: 12px solid transparent;
                            border-bottom: 12px solid #b61f22;
                            border-left: 8px solid #b61f22;
                            border-right: 8px solid transparent;
                        }
                    }
                }
                .con {
                    width: 620px;
                    background: #ffffff;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                    border-radius: 0px 10px 10px 10px;
                    padding: 10px 18px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 28px;
                }
            }
        }
    }

    .difficulty-wrap {
        width: 100%;
        background: #ffffff;
        .list-header {
            @include wrapper-title;
            padding-top: 40px;
            margin-bottom: 0;
        }
        .list-content {
            padding: 30px 0 40px;
            ul {
                display: flex;
                justify-content: space-between;
                li {
                    width: 386px;
                    height: 338px;
                    padding: 38px;
                    background: #f6f8fb;
                    border-radius: 12px;
                }
            }
            .icon {
                img {
                    width: 70px;
                    height: 70px;
                    margin: 0 auto 20px;
                }
            }
            .tit {
                height: 45px;
                font-weight: bold;
                font-size: 18px;
                color: #333333;
                text-align: center;
                line-height: 27px;
            }
            .con {
                margin-top: 18px;
                font-weight: 400;
                font-size: 14px;
                color: #333333;
                line-height: 28px;
                text-align: center;
            }
        }
    }

    .advantage-wrap {
        width: 100%;
        background-color: #f8f8f8;
        .list-header {
            @include wrapper-title;
            padding-top: 40px;
            margin-bottom: 0;
        }
        .list-content {
            padding: 35px 0 40px;
        }
        .ts {
            text-align: center;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            opacity: 0.8;
        }
        ul {
            display: flex;
            justify-content: space-between;
            li {
                width: 285px;
                height: 402px;
                position: relative;
                background: #ffffff;
                box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                border-radius: 12px;
                overflow: hidden;
                &:hover {
                    .mask {
                        display: block;
                    }
                }
            }
        }
        .default {
            .pic {
                height: 160px;
            }
            .con {
                padding: 20px 20px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/advantage-bg.png) no-repeat right top/104px 174px;
                .number {
                    font-weight: bold;
                    font-size: 40px;
                    color: #b61f22;
                }
                .tit {
                    margin-top: 6px;
                    font-weight: bold;
                    font-size: 18px;
                    color: #333333;
                    line-height: 26px;
                }
                .desc {
                    margin-top: 10px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 24px;
                    opacity: 0.8;
                }
            }
        }
        .mask {
            display: none;
            padding: 30px 20px;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            z-index: 99;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/advantage-mask-bg.png) no-repeat left bottom/100% 117px, #b61f22;
            .tit {
                font-weight: bold;
                font-size: 18px;
                color: #ffffff;
                line-height: 27px;
            }
            .desc {
                margin-top: 10px;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
                line-height: 28px;
                // opacity: 0.8;
            }
        }
    }

    .swiper-wrapper {
        width: 100%;
        background-color: #f8f8f8;
        .swiper {
            overflow: visible;
        }
        .swiper-box {
            padding: 30px 0;
            overflow: hidden;
        }
        .swiper-button-next,
        .swiper-button-prev {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 28px;
            height: 28px;
            background-repeat: no-repeat;
            background-size: contain;
            background-position: center;
        }

        .swiper-button-next {
            right: -40px;
            background-image: url(//img.gaoxiaojob.com/uploads/boshihou/common/arrow-r.png);
        }

        .swiper-button-prev {
            background-image: url(//img.gaoxiaojob.com/uploads/boshihou/common/arrow-l.png);
            left: -40px;
        }

        .swiper-button-prev:after,
        .swiper-rtl .swiper-button-next:after,
        .swiper-button-next:after,
        .swiper-rtl .swiper-button-prev:after {
            display: none;
        }

        .swiper-slide {
            &:hover {
                .default {
                    opacity: 0;
                    transform: scale(0.8);
                }
                .mask {
                    opacity: 1;
                }
            }
        }
        .list-header {
            @include wrapper-title;
            padding-top: 40px;
            margin-bottom: 0;
        }
        .list-content {
            position: relative;
            padding: 20px 0 30px;
        }

        .default {
            border-radius: 12px;
            box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
            background-color: #fff;
            transition: all 0.3s ease;
            transform: scale(1);
            opacity: 1;
            overflow: hidden;
            .header {
                img {
                    height: 180px;
                }
            }
            .content-box {
                transform: translateY(-20px);
                position: relative;
                z-index: 9;
                &::before {
                    display: block;
                    content: '';
                    width: 100%;
                    height: 20px;
                    border-radius: 12px 12px 0 0;
                    background-color: #fff;
                }
                .content {
                    padding: 0 30px;
                }
                .hd {
                    font-weight: bold;
                    color: #333333;
                    line-height: 27px;
                    height: 54px;
                    p {
                        font-size: 16px;
                        @include text-ellipsis;
                    }
                }
                .bd {
                    margin-top: 20px;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;
                    line-height: 21px;
                    opacity: 0.8;
                    height: 63px;
                    p {
                        @include text-ellipsis(3);
                    }
                }
                .ft {
                    margin-top: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    img {
                        width: 136px;
                        height: 50px;
                        background: #fff;
                        border-radius: 8px;
                        overflow: hidden;
                    }
                    p {
                        margin-left: 17px;
                        flex: 1;
                        font-weight: 400;
                        font-size: 14px;
                        color: #333333;
                        line-height: 21px;
                        opacity: 0.8;
                        text-align: right;
                        @include text-ellipsis(1);
                    }
                }
            }
        }
        .mask {
            height: 450px;
            background: #b61f22;
            border-radius: 12px;
            padding: 30px 30px 0;
            box-sizing: border-box;
            position: absolute;
            left: 0;
            right: 0;
            top: -25px;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(1);
            z-index: 19;
            .hd {
                font-weight: bold;
                font-size: 18px;
                color: #ffffff;
                line-height: 28px;
            }
            .bd {
                height: 300px;
                margin-top: 10px;
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
                line-height: 28px;
                overflow-y: scroll;
                /*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
                &::-webkit-scrollbar {
                    // width: 7px;
                    // height: 7px;
                    background-color: rgba(255, 255, 255, 0);
                }

                /*定义滚动条轨道 内阴影+圆角*/
                &::-webkit-scrollbar-track {
                    border-radius: 10px;
                }

                /*定义滑块 内阴影+圆角*/
                &::-webkit-scrollbar-thumb {
                    background-color: rgba(249, 191, 193, 1);
                }
            }
            .ft {
                display: flex;
                justify-content: center;
                align-items: center;
                background: #b61f22;
                padding: 20px 0;
                .button {
                    width: 121px;
                    height: 32px;
                    line-height: 32px;
                    background: #ffffff;
                    border-radius: 16px;
                    text-align: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: #b61f22;
                }
            }
        }
    }

    .matrix-wrap {
        width: 100%;
        background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/matrix-bg.png) no-repeat right top/351px 323px, #fff;
        .list-header {
            @include wrapper-title;
            padding-top: 40px;
            margin-bottom: 0;
        }
        .ts {
            text-align: center;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            opacity: 0.8;
        }
        .list-content {
            padding: 40px 0;
        }
        .tabs {
            border-radius: 12px;
            box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
            background-color: #fff;
            position: relative;
            &::before {
                position: absolute;
                left: -210px;
                top: 50%;
                content: '';
                width: 379px;
                height: 382px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/matrix-tab-bg.png) no-repeat left top/379px 382px;
                transform: translate(0, -50%);
            }
            .tab-hd {
                display: flex;
                border-radius: 12px 12px 0 0;
                background-color: #f6f6f6;
                overflow: hidden;
                li {
                    flex: 1;
                    height: 80px;
                    line-height: 80px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 28px;
                    color: #333333;
                    background: #f6f6f6;
                    position: relative;
                    z-index: 1;
                    cursor: pointer;
                    &:first-of-type:before {
                        display: none;
                    }
                    &::before {
                        background-color: #caced3cc;
                        content: '';
                        height: 40px;
                        left: 0;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 1px;
                    }
                    &::after {
                        content: attr(data-id);
                        font-weight: bold;
                        font-size: 93px;
                        color: #f2ecec;
                        position: absolute;
                        bottom: -26px;
                        right: 22px;
                        z-index: -1;
                    }
                    &.active {
                        font-size: 28px;
                        color: #b61f22;
                        background: #ffffff;
                        border-radius: 12px 12px 0px 0px;
                        &::after {
                            color: #f7e5e6;
                        }
                        &::before {
                            display: none;
                        }
                        & + li {
                            &::before {
                                display: none;
                            }
                        }
                    }
                }
            }
            .tab-bd {
                position: relative;
                z-index: 1;
                width: 100%;
                height: 504px;
                padding: 30px 40px 10px;
                border-radius: 0 0 12px 12px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/publish/matrix-tab-con-bg.png) no-repeat left bottom/444px 117px, #fff;
                .tab-con-wrap {
                    display: none;
                    &:first-child {
                        display: block;
                    }
                }
                .tab-con-wrap-n2,
                .tab-con-wrap-n3 {
                    .con-r {
                        ul {
                            padding-top: 42px;
                            li {
                                height: 358px;
                            }
                        }
                    }
                }
                .tab-con-wrap-n4 {
                    .con-r {
                        ul {
                            li {
                                padding: 26px 20px 0;
                            }
                        }
                    }
                }
                .tab-con {
                    display: flex;
                    justify-content: space-between;
                }
                .con-l {
                    color: #333333;
                    margin-right: 74px;
                    .con-l-hd {
                        margin-top: 94px;
                        p {
                            display: flex;
                            align-items: center;
                        }
                        span {
                            margin-left: 13px;
                            font-weight: bold;
                            font-size: 24px;
                        }
                        img {
                            width: 58px;
                            height: 58px;
                        }
                    }
                    .con-l-bd {
                        width: 351px;
                        margin-top: 18px;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 28px;
                    }
                }
                .con-r {
                    ul {
                        display: flex;
                        flex-wrap: wrap;
                        li {
                            position: relative;
                            width: 322px;
                            height: 212px;
                            margin: 0 0 20px 20px;
                            padding: 40px 20px 0;
                            color: #333333;
                            background: #f6f8fb;
                            border-radius: 10px;
                            &:hover {
                                .mask {
                                    display: block;
                                }
                            }
                            img {
                                width: 40px;
                                height: 40px;
                            }
                            .tit {
                                margin-top: 16px;
                                font-weight: bold;
                                font-size: 20px;
                                line-height: 30px;
                            }
                            .desc {
                                margin-top: 16px;
                                font-weight: 400;
                                font-size: 14px;
                                line-height: 21px;
                            }
                            .p-box {
                                p {
                                    margin-top: 15px;
                                    font-weight: 400;
                                    font-size: 14px;
                                    line-height: 21px;
                                    span {
                                        display: inline-block;
                                        width: 6px;
                                        height: 6px;
                                        margin: 0 6px 3px 0;
                                        border-radius: 50%;
                                        background-color: #B61F22;
                                    }
                                }
                            }
                        }
                        .mask {
                            display: none;
                            position: absolute;
                            left: 0;
                            top: 0;
                            right: 0;
                            bottom: 0;
                            padding: 20px;
                            color: #ffffff;
                            background: #b61f22;
                            border-radius: 10px;
                            .mask-center-y {
                                height: 100%;
                                display: flex;
                                align-items: center;
                            }
                            .hd {
                                font-weight: bold;
                                font-size: 20px;
                                line-height: 30px;
                            }
                            .bd {
                                p {
                                    margin-top: 14px;
                                    font-weight: 400;
                                    font-size: 14px;
                                    line-height: 21px;
                                    list-style: disc;
                                    span {
                                        display: inline-block;
                                        width: 6px;
                                        height: 6px;
                                        margin: 0 6px 3px 0;
                                        border-radius: 50%;
                                        background-color: #fff;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .btn {
            width: 156px;
            height: 48px;
            margin: 40px auto 0;
            line-height: 48px;
            background: $liner-gradient-primary;
            border-radius: 24px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            a {
                display: block;
                width: 100%;
                height: 100%;
                color: $color-white;
            }
        }
    }

    .department-wrap {
        width: 100%;
        background-color: #fff;
        .list-header {
            @include wrapper-title;
            padding-top: 40px;
            margin-bottom: 0;
        }
        .ts {
            text-align: center;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            opacity: 0.8;
        }
        ul {
            padding-top: 40px;
            li {
                &:nth-child(odd) {
                    background: #fcf9f4;
                }
                &:nth-child(even) {
                    background: #fff;
                }
                .list-wrap {
                    width: 1200px;
                    margin: 0 auto;
                    .list {
                        display: flex;
                        justify-content: space-between;
                    }
                }
                .list-l {
                    display: flex;
                    align-items: center;
                    p {
                        font-weight: bold;
                        font-size: 20px;
                        color: #333333;
                        position: relative;
                        &::after {
                            position: absolute;
                            left: 0;
                            bottom: -4px;
                            content: '';
                            width: 38px;
                            height: 4px;
                            background: linear-gradient(90deg, #b61f22, #e95d60);
                            border-radius: 2px;
                        }
                    }
                }
            }
        }
        .list-n1,
        .list-n2 {
            img {
                width: 1087px;
                height: 202px;
            }
        }

        .list-n3,
        .list-n4 {
            img {
                width: 1087px;
                height: 142px;
            }
        }
        .list-n2 img {
            object-fit: cover !important;
        }
    }

    .cooperation-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: 215px;
        background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/auto 100%, $background-primary;
        padding-top: 30px;

        .top {
            line-height: 30px;
            font-size: 20px;
            padding-left: 47px;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/postdoctor-logo.png) no-repeat left/40px 31px;
            margin-bottom: 18px;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .bottom {
            display: flex;

            .cooperation {
                background: $liner-gradient-primary;
                color: $color-white;
                padding: 0 42px;
                font-weight: bold;
                line-height: 40px;
                border-radius: 40px;
                margin-right: 20px;
            }

            .tel {
                border-radius: 20px;
                border: 1px solid $color-primary;
                padding: 0 30px 0 63px;
                line-height: 38px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/phone.png) no-repeat 30px center/25px 25px;
                color: $color-primary;
                font-weight: bold;
            }
        }
    }
}
