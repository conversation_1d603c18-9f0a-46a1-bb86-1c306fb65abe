@import './variables';

$font-color: #333333;
$font-color-label: rgba(51, 51, 51, 0.6);

.cascader-dialog-template {
    color: $font-color;
    line-height: 1;

    .el-overlay-dialog {
        display: flex;
    }

    .el-dialog {
        width: auto;
        padding: 30px 75px;
        border-radius: 10px;
        align-self: flex-start;

        .el-dialog__header {
            margin-right: 0;
            padding: 0;
        }

        .el-dialog__body {
            padding: 0;
            width: 850px;
            max-height: initial;
            color: $font-color;
        }

        .el-dialog__footer {
            padding: 0px;
        }
    }

    .dialog-center {
        padding: 0;
    }

    .cascader-header {
        display: flex;
        align-items: center;
        padding: 15px 19px;
        border-bottom: 1px solid #ebebeb;

        .title {
            font-size: 18px;
        }

        .tips {
            color: $font-color-label;
            font-size: 14px;
            margin: 0 22px 0 8px;

            span {
                color: $color-primary;
            }
        }

        .search {
            width: 250px;

            .el-input__inner {
                cursor: text;
            }
        }
    }

    .cascader-content {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .select {
            display: flex;
            min-height: 56px;
            align-items: center;
            padding: 13px 20px;
            background-color: #f9fafb;

            .select-label {
                flex-shrink: 0;
            }

            .select-value {
                display: flex;
                flex-wrap: wrap;
                flex-grow: 1;

                .tag {
                    height: 24px;
                    max-width: calc((100% - 40px) / 5);
                    display: flex;
                    overflow: hidden;
                    align-items: center;
                    justify-content: center;
                    background-color: #fff;
                    border-color: $color-primary;
                    color: $color-primary;

                    .el-tag__content {
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }

                    .el-tag__close {
                        width: 13px;
                        height: 13px;
                        font-size: 11px;
                        line-height: 13px;
                        flex-shrink: 0;
                        color: #fff;
                        background-color: $color-primary;
                        position: static;
                        margin-left: 5px;
                    }

                    & + .tag {
                        margin-left: 10px;
                    }
                }
            }
        }

        .data-content {
            display: flex;
            height: 400px;
            overflow: hidden;

            .left-content {
                width: 160px;
                background-color: #f5f5f5;
                overflow-y: auto;
                flex-shrink: 0;
                padding: 0;

                .list {
                    display: block;
                    padding: 13px 8px 13px 35px;
                    position: relative;
                    line-height: 20px;

                    &.active {
                        background-color: #fff;
                        color: $color-primary;
                    }

                    &.has-select {
                        &::after {
                            content: '';
                            display: block;
                            width: 4px;
                            height: 4px;
                            background: #fa635c;
                            border-radius: 50%;
                            position: absolute;
                            left: 20px;
                            top: 21px;
                        }
                    }
                }
            }

            .right-content {
                overflow-y: auto;
                padding: 0 20px 10px;
                min-width: 690px;
                flex-grow: 1;

                .second-content {
                    display: none;

                    &.show {
                        display: block;

                        .second-list {
                            & + .second-list {
                                margin-top: 8px;
                            }
                        }
                    }
                }

                @for $i from 3 to 7 {
                    .column-#{$i} {
                        display: flex;
                        flex-wrap: wrap;

                        .second-item {
                            width: calc((100% - (($i - 1) * 16px)) / $i);
                            margin-right: 16px;

                            &:nth-child(#{$i}n) {
                                margin-right: 0px;
                            }
                        }
                    }
                }

                .second-item {
                    padding: 5px 0;

                    &.only {
                        label {
                            height: auto;
                        }
                    }

                    label {
                        font-weight: normal;
                        display: inline-flex;
                        position: relative;
                        height: 100%;

                        input {
                            cursor: pointer;
                            position: absolute;
                            z-index: 1;
                            top: 0;
                            left: 0;
                            right: 0;
                            bottom: 0;
                            opacity: 0;
                            border: none;
                            outline: none;

                            &.btn-select:checked + span {
                                color: $color-primary;
                                background-color: #fff3e0;
                                border-radius: 4px;
                            }

                            &.btn-active:checked + span {
                                border: 1px solid $color-primary;
                                border-bottom: none;
                                background: #fff;
                                position: relative;
                                z-index: 1;
                                border-radius: 4px 4px 0px 0px;
                            }

                            &.has-select:not(:checked) + span {
                                &::after {
                                    display: block;
                                }
                            }

                            &:hover + span {
                                color: $color-primary;
                            }
                        }

                        span {
                            padding: 5px 11px;
                            border: 1px solid transparent;
                            line-height: 18px;
                            height: 100%;
                            position: relative;
                            display: inline-flex;
                            align-items: flex-start;

                            &::after {
                                content: '';
                                display: none;
                                width: 4px;
                                height: 4px;
                                background: #fa635c;
                                border-radius: 50%;
                                position: absolute;
                                left: 3px;
                                top: 12px;
                            }
                        }
                    }
                }

                .second-list-title {
                    font-size: 16px;
                    color: $font-color-label;
                    padding: 0 10px;
                }

                .second-list-content {
                    .third-content {
                        position: relative;

                        & > div {
                            &:first-child {
                                .third-list {
                                    border-top-left-radius: 0px;
                                }
                            }
                        }

                        .third-list {
                            margin-top: -6px;
                            padding: 10px 6px 5px 6px;
                            border: 1px solid $color-primary;
                            margin-bottom: 8px;
                            border-radius: 4px;

                            &.hidden {
                                display: none;
                            }
                        }

                        @for $i from 3 to 7 {
                            .column-#{$i} {
                                display: flex;
                                flex-wrap: wrap;

                                .third-item {
                                    width: calc((100% - (($i - 1) * 16px)) / $i);
                                    margin-right: 16px;

                                    &:nth-child(#{$i}n) {
                                        margin-right: 0px;
                                    }
                                }
                            }
                        }

                        .third-item {
                            margin-bottom: 5px;

                            label {
                                font-weight: normal;
                                display: inline-block;
                                position: relative;

                                input {
                                    cursor: pointer;
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    bottom: 0;
                                    opacity: 0;
                                    border: none;
                                    outline: none;

                                    &.btn-select:checked + span {
                                        color: $color-primary;
                                        background-color: #fff3e0;
                                        border-radius: 4px;
                                    }

                                    &.btn-active:checked + span {
                                        border: 1px solid $color-primary;
                                        border-bottom: none;
                                        background: #fff;
                                        position: relative;
                                        z-index: 1;
                                        border-radius: 4px 4px 0px 0px;
                                    }

                                    &:hover + span {
                                        color: $color-primary;
                                    }
                                }

                                span {
                                    display: inline-block;
                                    padding: 5px 6px;
                                    border: 1px solid transparent;
                                    line-height: 18px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .cascader-footer {
        display: flex;
        justify-content: center;
        padding: 12px 0;
        border-top: 1px solid #f2f2f2;

        .submit {
            width: 94px;
            border: none;
            padding: 0;
        }
    }
}

@media screen and (max-height: 667px) {
    .cascader-dialog-template {
        .cascader-content {
            .data-content {
                height: 300px;
            }
        }
    }
}
