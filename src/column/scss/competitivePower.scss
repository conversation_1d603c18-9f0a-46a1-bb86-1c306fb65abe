@import './variables';
@import './common/sideBar';

$view-width: 1200px;

.main-wrapper {
    width: $view-width;
    margin: 0 auto;
    padding-top: 60px;
}

#component {
    .el-main {
        padding-bottom: 0;
    }
    .search-header {
        background-color: $color-white;
        background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/competitivePower.png) no-repeat;
        background-size: 100% 100%;
    }

    .recommend-container {
        margin: 0 auto;
        padding: 60px 0 30px 0;
        width: $view-width;
        .title {
            font-size: 50px;
            margin-bottom: 20px;
            font-weight: bold;

            span {
                color: #ff6c02;
            }
        }
        .subheading {
            font-size: 18px;
        }
        .set-meal {
            display: flex;
            margin: 60px 0 20px 0;
            .list {
                display: flex;
                flex-direction: column;
                position: relative;
                transition: all 0.2s;
                overflow: hidden;
                bottom: 0;
                background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/meal.png) no-repeat;
                margin-right: 20px;
                background-size: 100% 100%;
                width: 210px;
                padding: 45px 32px 20px 32px;
                cursor: pointer;
                border: 1px solid transparent;
                border-radius: 5px;

                &.active {
                    bottom: 10px;
                    border-color: #ff8d24;
                }

                .top {
                    position: absolute;
                    top: 0;
                    left: 0;
                    border-radius: 5px 0px 10px 0px;
                    font-size: 12px;
                    color: #fff;
                    padding: 5px 15px;
                    background: linear-gradient(90deg, #f33e3e, #ff6966);
                }

                .content {
                    display: flex;
                    flex-direction: column;
                    .day {
                        font-size: 25px;
                        margin-bottom: 30px;
                        font-weight: bold;
                    }
                    .del {
                        color: #afafaf;
                        font-size: 12px;
                        margin-bottom: 15px;
                        text-decoration: line-through;
                    }
                    .buy-price {
                        color: #f85250;
                        display: flex;
                        align-items: flex-end;
                        font-size: 30px;
                        margin-bottom: 20px;
                        font-weight: bold;
                        span {
                            font-size: 18px;
                        }
                        .amortized {
                            font-size: 13px;
                            font-weight: normal;
                            margin-left: 8px;
                            white-space: nowrap;

                            .discount {
                                display: inline-block;
                                padding: 0 5px;
                                line-height: 14px;
                                background: linear-gradient(90deg, #eab294, #de8855);
                                border-radius: 8px 7px 7px 0px;
                                font-size: 12px;
                                color: #fff;
                                margin-bottom: 9px;
                            }
                        }
                    }
                }
                .buy-btn {
                    background: linear-gradient(90deg, #ff6a00, #ff8c1f);
                    border-radius: 5px;
                    color: #fff;
                    font-size: 16px;
                    width: 100%;
                    text-align: center;
                    padding: 12px 0;
                    &:hover {
                        background: linear-gradient(90deg, #ff842d, #ff9f45);
                    }
                }
                &:hover {
                    bottom: 10px;
                }
                &:last-child {
                    margin-right: 0;
                }
            }
        }
        .service-agreement {
            font-size: 12px;
            cursor: pointer;
            span {
                color: #ff6c02;
            }
        }
    }
    .job-analysis {
        background: #fff;
        padding-top: 50px;
        padding-bottom: 75px;
        .analysis-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            padding-top: 20px;
            width: $view-width;
            .analysis-title {
                font-size: 38px;
                font-weight: bold;
                margin-bottom: 17px;
            }
            .analysis-subheading {
                color: #666666;
                font-size: 16px;
                margin-bottom: 25px;
            }
            .analysis-description {
                display: flex;
                justify-content: space-between;
                .left-content {
                    width: 500px;
                    display: flex;
                    flex-direction: column;
                    padding-top: 60px;
                    .left-catalogue {
                        padding: 30px 0 30px 20px;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        margin-bottom: 20px;
                        img {
                            width: 40px;
                            height: 40px;
                        }
                        .text {
                            color: #333;
                            font-size: 18px;
                            font-weight: bold;
                            display: flex;
                            flex-direction: column;
                            margin-left: 12px;
                            span {
                                color: #666;
                                font-weight: normal;
                                font-size: 14px;
                                padding-top: 10px;
                            }
                        }
                        &.active {
                            border: 1px solid #ff8d24;
                            box-shadow: 0px 0px 20px 0px rgba(32, 33, 35, 0.08);
                            border-radius: 10px;
                            .text {
                                color: #ff6c02;
                            }
                        }
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
                .right-content {
                    width: 688px;
                    height: 460px;
                    position: relative;
                    overflow: hidden;
                    .item {
                        box-sizing: border-box;
                        display: flex;
                        position: absolute;
                        top: 500px;
                        transition: all 1s;
                        opacity: 0;
                        width: 100%;
                        img {
                            width: 100%;
                            height: 100%;
                        }
                        &.active {
                            opacity: 1;
                            top: 0;
                            z-index: 1111;
                        }
                    }
                }
            }
        }
    }
    .announcement-heat {
        background: #f5f6f9;
        padding-top: 60px;
        padding-bottom: 45px;
        .announcement-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            padding-top: 20px;
            width: $view-width;
            .announcement-title {
                font-size: 38px;
                font-weight: bold;
                margin-bottom: 17px;
            }
            .announcement-subheading {
                color: #666666;
                font-size: 16px;
                margin-bottom: 25px;
            }
            .announcement-description {
                display: flex;
                justify-content: space-between;
                .left-content {
                    width: 700px;
                    height: 475px;
                    position: relative;
                    overflow: hidden;
                    .item {
                        box-sizing: border-box;
                        display: flex;
                        position: absolute;
                        top: 500px;
                        transition: all 1s;
                        opacity: 0;
                        width: 100%;
                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                            display: block;
                        }
                        &.active {
                            opacity: 1;
                            top: 0;
                            z-index: 1111;
                        }
                    }
                }
                .right-content {
                    width: 500px;
                    display: flex;
                    flex-direction: column;
                    padding-top: 60px;
                    .right-catalogue {
                        padding: 30px 0 30px 20px;
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        margin-bottom: 20px;
                        img {
                            width: 40px;
                            height: 40px;
                        }
                        .text {
                            color: #333;
                            font-size: 18px;
                            font-weight: bold;
                            display: flex;
                            flex-direction: column;
                            margin-left: 12px;
                            span {
                                color: #666;
                                font-weight: normal;
                                font-size: 14px;
                                padding-top: 10px;
                            }
                        }
                        &.active {
                            border: 1px solid #ff8d24;
                            box-shadow: 0px 0px 20px 0px rgba(32, 33, 35, 0.08);
                            border-radius: 10px;
                            background: #ffffff;
                            .text {
                                color: #ff6c02;
                            }
                        }
                        &:last-child {
                            margin-bottom: 0;
                        }
                    }
                }
            }
        }
    }

    .evaluate-wrapper {
        background-color: #fff;

        .main-wrapper {
            background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/evaluate-bg.png') no-repeat right 36px/124px 100px;
        }

        .wrapper-title {
            font-size: 38px;
            font-weight: bold;
            text-align: center;
            color: #242424;
            margin-bottom: 17px;
        }

        .wrapper-desc {
            color: #666;
            font-size: 16px;
            text-align: center;
        }

        .evaluate-content {
            padding-top: 83px;
            padding-bottom: 10px;
            display: flex;
            justify-content: space-between;

            .list {
                width: calc((1200px - 60px) / 4);
                box-shadow: 0px 0px 10px 0px rgba(51, 51, 51, 0.08);
                border-radius: 16px;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0 17px 30px;
                font-size: 14px;

                .avatar {
                    width: 64px;
                    height: 64px;
                    margin-top: -32px;
                    margin-bottom: 13px;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .name {
                    margin-bottom: 17px;
                    font-size: 18px;
                    font-weight: bold;
                }

                .ask {
                    margin-bottom: 13px;
                    opacity: 0.9;
                }

                .content {
                    line-height: 21px;
                    margin-bottom: 17px;
                }

                .tag {
                    display: flex;
                    width: 100%;
                    justify-content: flex-start;

                    span {
                        line-height: 24px;
                        padding: 0 12px;
                        background: #ffeedb;
                        border-radius: 12px;
                        color: #ed7011;
                        font-size: 13px;
                        margin-right: 8px;
                    }
                }
            }
        }
    }

    .search-footer {
        background: #fff;
        padding-top: 40px;
        padding-bottom: 45px;
        .search-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin: 0 auto;
            padding-left: 50px;
            width: $view-width;
            .search-title {
                font-size: 16px;
                font-weight: bold;
            }
            .text-content {
                display: flex;
                justify-content: space-between;
                line-height: 1.8;
                margin-top: 15px;
                color: #666666;
                span {
                    color: #ff6c02;
                    cursor: pointer;
                }
            }
        }
    }

    .fixed-wrapper {
        position: fixed;
        bottom: 0;
        width: 100%;
        background-color: #ffeedb;
        z-index: 9999;

        .main-wrapper {
            height: 84px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 18px;
            padding: 0;
        }

        .buy-info {
            display: flex;
            align-items: flex-end;
            padding-left: 47px;
            font-size: 28px;
            font-weight: bold;
            background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/compete/rise-icon.png') no-repeat left/35px 35px;

            .single-price {
                font-size: 16px;
                display: flex;
                align-items: flex-end;
                margin-left: 11px;
                font-weight: normal;

                span {
                    font-size: 24px;
                    font-weight: bold;
                    color: #ff6c02;
                }
            }
        }

        .aside {
            display: flex;

            .detail {
                display: flex;
                align-items: baseline;
                color: #ff6c02;
                font-size: 20px;

                .price {
                    font-size: 36px;
                    font-weight: bold;
                    margin-right: 8px;
                }

                .original-price {
                    opacity: 0.6;
                    text-decoration: line-through;
                    margin-left: 10px;
                }
            }

            .pay {
                margin-left: 80px;
                font-size: 20px;
                font-weight: bold;
                color: #fff;
                width: 152px;
                height: 44px;
                background: linear-gradient(90deg, #ff6a00, #ff8c1f);
                border-radius: 22px;
                border: none;
                cursor: pointer;
            }
        }
    }
}
