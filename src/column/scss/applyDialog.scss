@import './variables';

.apply-dialog-template {
    .el-dialog {
        $rowHeight: 35px;

        width: 568px;

        .el-dialog__header {
            margin-right: 16px;
            padding-top: 25px;
            padding-bottom: 15px;
        }

        .el-dialog__title {
            color: $font-color;
            font-weight: bold;
            font-size: 18px;
        }

        .el-dialog__body {
            padding: 0 70px;

            .el-form-item {
                align-items: start;
                margin-bottom: 20px;
            }

            .el-form-item__content {
                line-height: 40px;
            }

            .el-form-item__label {
                color: $font-color;
                line-height: $rowHeight;
                font-size: 14px;

                .el-checkbox__label {
                    display: block;
                }
            }

            .el-form-item__content {
                line-height: $rowHeight;

                .el-input__inner {
                    line-height: $rowHeight;
                    height: $rowHeight;
                }
            }
        }

        .education-tips {
            display: flex;
            justify-content: center;

            .tips-content {
                font-size: 12px;
                margin-bottom: 20px;
                padding-left: 15px;
                color: $font-color-salary;
                line-height: 1.5;
                background: url(../assets/icon/warning.png) no-repeat left center/12px;
            }
        }

        .resume-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: $color-primary;
            font-size: 12px;
            line-height: inherit;

            .resume-status-tips {
                background: #fff9e2;
                padding: 0 6px;
                line-height: 22px;
                border-radius: 4px;
            }

            .resume-operate {
                display: flex;
                flex-grow: 1;
                justify-content: flex-end;
            }

            .color-primary {
                font-size: 14px;
                height: 14px;
                line-height: 14px;

                & + .color-primary {
                    padding-left: 6px;
                    margin-left: 6px;
                    border-left: 1px solid $color-primary;
                }
            }

            .warning {
                padding-left: 26px;
                background: url(../assets/icon/warning.png) no-repeat left center / 16px;
            }
        }

        .resume-options {
            display: flex;
            align-items: center;

            .el-select {
                height: $rowHeight;
                flex-grow: 1;
            }

            .resume-upload {
                flex-shrink: 0;
                padding-left: 10px;
                text-align: right;
            }
        }

        .form-item-resume-online,
        .form-item-attachment {
            .el-form-item__label,
            .el-form-item__content {
                line-height: initial;
                line-height: 22px;
            }
        }

        .form-item-attachment {
            align-items: center;

            .el-form-item__content {
                // padding-top: 4px;

                .el-upload--text {
                    display: flex;
                    align-items: center;
                }

                .file-tips {
                    font-size: 13px;
                    color: $font-color-label;
                    margin-bottom: 12px;
                }

                .upload-btn {
                    padding: 0;
                    width: 92px;
                    height: 28px;
                    line-height: 26px;
                    text-align: center;
                    min-height: initial;
                }
            }
        }

        .form-item-add-top{
            .el-form-item__content{
                display: flex;
            }

            .apply-add-top{
                --el-checkbox-input-height:16px;
                --el-checkbox-input-width:16px;
                --el-checkbox-checked-font-color: $font-color;
                --el-text-color-placeholder: $font-color;
                --el-checkbox-font-size: 12px;

                display: flex;
                align-items: center;
            }

            .el-checkbox{
                height: $rowHeight;
            }

            .el-checkbox__label{
                padding-left: 6px;
            }

            .el-checkbox__inner {
                &::after{
                    top: 2px;
                    left: 5px;
                }
            }

            .go-pay{
                font-size: 12px;
                color: $color-primary;
                border-bottom: 1px solid $color-primary;
                line-height: 15px;
                align-self: center;
            }

        }
    }

    .el-dialog__footer {
        padding: 0;
        .dialog-footer {
            padding-bottom: 30px;
            padding-top: 10px;
        }
        .el-button {
            width: 94px;
            height: 32px;
            padding: 0;
            text-align: center;
            line-height: 30px;
            min-height: initial;

            & + .el-button {
                margin-left: 20px;
            }
        }
        .dialog-tips {
            padding: 8px 24px 14px 37px;
            color: #5c5c5c;
            text-align: left;
            line-height: 2;
            border-radius: 0 10px 10px 10px;
            background: #fff9ee url(../assets/icon/warning.png) no-repeat 20px 12px / 13px;
        }
    }
}
