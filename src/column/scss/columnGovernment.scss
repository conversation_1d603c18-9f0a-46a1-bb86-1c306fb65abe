// 栏目页政府与事业单位年度招聘
@import './variables';

@import './common/headlines-adv';
@import './common/fixedTool';
@import './common/commonTitle';
@import './common/sideBar';

@import './common/currentLocation';
@import './common/component/propaganda';
@import './common/component/banner';
@import './common/component/job';
@import './common/component/rangeD';
@import './common/component/rangeL';
@import './common/component/rangeH';
@import './common/component/link';
@import './common/component/dropnav';

$color-bg: #f4f9ff;
$border-color-bottom: #ededed;

.right-content {
    .list-option {
        margin-bottom: 20px;
    }

    .data {
        span {
            color: $color-primary;
        }
    }

    .amount {
        display: none;
        color: $font-color-basic;
    }

    .recruitment-announcement {
        display: none;
    }

    .changing-over {
        margin-left: 187px;
    }

    .college-information {
        ul {
            display: flex;
            flex-wrap: wrap;

            li {
                margin: 10px 11px 0 0;
                padding: 16px;
                width: 432px;
                border: 1px solid $border-color;
                border-radius: 4px;

                &:nth-child(2n) {
                    margin-right: 0;
                }
            }
        }

        .college-job {
            display: flex;
            justify-content: space-between;

            h4 {
                flex: 1;
                @include utils-column-title;
            }

            span {
                margin-left: 5px;
                color: $font-color-label;
            }
        }

        .requirement {
            span {
                display: inline-block;
                margin: 13px 6px 0 0;
                padding: 4px 8px;
                font-size: 12px;
                color: $font-color-basic;
                background-color: $color-bg;
            }
        }

        .recruit-introduce {
            margin-top: 13px;
            width: 100%;
            color: $font-color-label;
            @include utils-ellipsis;
        }
    }

    @for $i from 1 through 2 {
        .college-information ul li:nth-of-type(#{$i}) {
            margin-top: 0;
        }
    }
}
