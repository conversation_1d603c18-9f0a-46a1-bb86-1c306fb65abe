@use './variables' as *;

body {
    background-color: #f6f6f6;
}

.footer-link-for-spider {
    margin-top: 20px;
    padding: 0 20px;
    background: #ffffff;
    border-radius: 10px;

    &.is-column {
        margin: 0 auto 10px;
        width: $view-width;
        border-radius: 0;
    }

    .tab-nav {
        height: 55px;
        color: $font-color-basic;
        font-size: 16px;
        font-weight: 400;
        box-shadow: 0 1px 0 0 #ebebeb;

        span {
            display: inline-block;
            margin-right: 55px;
            line-height: 55px;
            cursor: pointer;

            &.is-active {
                color: $color-primary;
                box-shadow: 0 2px 0 0 $color-primary;
            }
        }
    }

    .tab-pane {
        padding-bottom: 20px;

        .pane {
            display: none;

            &.is-active {
                display: block;
            }

            a {
                display: inline-block;
                margin-top: 20px;
                margin-right: 20px;
                color: $font-color-basic;
                font-size: 14px;
                font-weight: 400;

                &:hover {
                    color: $color-primary;
                }
            }
        }
    }
}
