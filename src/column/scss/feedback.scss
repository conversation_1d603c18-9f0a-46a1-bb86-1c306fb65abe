@import '../../variables';
@import './common/sideBar';

@media (min-width: 500px) {
    .fixed-aside {
        right: 60px !important;
    }
}
@media (min-width: 1100px) {
    .fixed-aside {
        right: 60px !important;
    }
}

@media (min-width: 1366px) {
    .fixed-aside {
        right: 50px !important;
    }
}

@media (min-width: 1440px) {
    .fixed-aside {
        right: 55px !important;
    }
}

@media (min-width: 1680px) {
    .fixed-aside {
        right: 180px !important;
    }
}

.fixed-aside {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 52px;
    height: 52px;
    box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.2);
    border-radius: 8px;
    box-sizing: border-box;

    &,
    &:hover,
    &:focus {
        background-color: #fff;
    }

    .top {
        font-size: 14px;
        font-weight: bold;
    }

    .backtop-button {
        width: 16px;
        height: 16px;
        background: url(../assets/common/backtop-hover.png) no-repeat center / cover;
    }

    .weixin {
        position: relative;
        background-size: 32px;
        padding-top: 40px;
        background-image: url($qrcode-mpweixin);
        // background-image: url('../assets/home/<USER>');

        &:hover {
            background-image: url($qrcode-mpweixin);
            // background-image: url('../assets/home/<USER>');

            .weixin-hover {
                display: block;
            }
        }
    }

    .weixin-hover {
        display: none;
        position: absolute;
        width: 154px;
        height: 166px;
        top: 50%;
        transform: translateY(-50%);
        right: 60px;
        padding-top: 139px;
        font-size: 13px;
        color: $font-color;
        border-radius: 10px;
        background-color: #fff;
        background-repeat: no-repeat;
        background-position: center 12px;
        background-size: 120px;
        background-image: url($qrcode-mpweixin);
        box-sizing: border-box;

        &::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-white;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.feedback-popover {
    &.el-popover.el-popper {
        padding: 0;
        border-radius: 10px;
    }

    .el-popper__arrow {
        transform: translate(0px, 221px) scale(1.8) !important;
    }

    .feedback-detail {
        padding: 10px 0;
        box-sizing: border-box;

        h6 {
            height: 20px;
            line-height: 20px;
            margin-bottom: 10px;
            font-size: 14px;

            &::before {
                content: '';
                display: inline-block;
                width: 20px;
                height: 20px;
                margin-right: 10px;
                vertical-align: bottom;
                background: url(../assets/common/business-cooperation.png) no-repeat center / cover;
            }
        }

        p {
            line-height: 20px;
            margin-left: 30px;
        }
    }

    .opinion-feedback h6::before {
        background-image: url(../assets/common/opinion-feedback.png);
    }

    .customer-service h6::before {
        background-image: url(../assets/common/customer-service.png);
    }

    .business-cooperation,
    .opinion-feedback,
    .customer-service {
        display: block;
        padding: 20px;
        padding-bottom: 0;
    }

    .business-cooperation:hover,
    .opinion-feedback:hover,
    .customer-service:hover {
        color: var(--el-text-color-regular);
        background: #fafafa;
    }

    .business-cooperation p:last-child,
    .opinion-feedback p:last-child {
        padding-bottom: 20px;
        border-bottom: 1px solid #ebebeb;
    }

    .customer-service p:last-child {
        padding-bottom: 20px;
    }

    .customer-service a:hover {
        color: #ffa000;
    }
}

// 博士后样式
.postdoctor-container {
    .fixed-aside {
        @import './variablesPostdoctor';

        .backtop-button {
            width: 16px;
            height: 16px;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/backtop-hover.png) no-repeat center / cover;
        }

        .top {
            color: $color-primary;
        }
    }
}

// 活动样式
.activity-container {
    .fixed-aside {
        @import './variablesActivity';

        .backtop-button {
            background: url(../assets/activity/common/backtop-hover.png) no-repeat center / cover;
        }
    }
}
