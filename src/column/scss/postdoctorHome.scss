@import './variablesPostdoctor';

.main-container {
    background-repeat: no-repeat;
    padding-top: 20px;
    padding-bottom: 1px;
    background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/100% auto,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-left-bg.png) no-repeat left 169px/380px 264px,
        url(//img.gaoxiaojob.com/uploads/boshihou/common/build-right-bg.png) no-repeat right 169px/380px 264px, linear-gradient(180deg, #ffe9ea 0, #fff 326px);

    .gray-bg {
        background-color: #f8f8f8;
    }

    .view-content {
        width: $view-width;
        margin-left: auto;
        margin-right: auto;
    }

    .common-title {
        @include wrapper-title;
    }

    .view-more {
        font-weight: bold;
        line-height: 40px;
        padding: 0 42px;
        border-radius: 40px;
        color: $color-primary;
        background-color: $tag-background;
    }

    .swiper {
        @include utils-swiper-bullet(#d6d3d2, $color-primary, 0.8);

        .swiper-pagination {
            left: 50%;
            transform: translateX(-50%);
            width: auto;
            padding: 12px 20px;
            display: flex;
            bottom: 0;
            justify-content: center;
        }

        .swiper-pagination-bullet {
            margin: 0 4px;
            height: 4px;
            width: 27px;
            border-radius: 4px;
        }
    }

    .top-wrapper {
        display: flex;
        width: $view-width;
        margin: 0 auto;
        margin-bottom: 33px;

        .swiper-t1 {
            width: 642px;
            height: 170px;
            background-color: $color-white;
            border-radius: 12px 0px 0px 12px;
            margin-right: 6px;
            margin-left: 0px;
        }

        .middle {
            width: 286px;
            height: 170px;
            margin-right: 6px;
            background-color: $color-white;
        }

        .aside {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            a {
                width: 260px;
                height: 82px;
                overflow: hidden;
                background-color: $color-white;

                &:first-child {
                    border-radius: 0px 12px 0px 0px;
                }

                &:nth-child(2) {
                    border-radius: 0px 0px 12px 0px;
                }
            }
        }
    }

    .headline-wrapper {
        margin: 0 auto 20px;
        width: $view-width;
        position: relative;
        padding-top: 13px;

        .wrapper-title {
            width: 1105px;
            margin: 0 auto;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/cover;
            text-align: center;
            font-size: 22px;
            font-weight: bold;
            padding: 0 230px;
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);

            a {
                display: block;
                line-height: 54px;
                @include utils-ellipsis;
            }
        }

        .content {
            background-color: $color-white;
            box-shadow: $box-shadow-default;
            padding: 60px 48px 0 50px;
            border-radius: 12px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            padding-bottom: 5px;

            .list {
                display: inline-block;
                width: 527px;
                align-items: baseline;
                @include utils-ellipsis;
                margin-bottom: 13px;

                &::before {
                    content: '';
                    display: inline-block;
                    width: 5px;
                    height: 5px;
                    border-radius: 50%;
                    margin-right: 5px;
                    flex-shrink: 0;
                    background-color: $color-primary;
                    transform: translateY(-2px);
                }
            }
        }
    }

    .recommend-company-wrapper {
        width: $view-width;
        margin-left: auto;
        margin-right: auto;
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 8px;

        .list {
            display: flex;
            width: calc((100% - 60px) / 4);
            margin-right: 20px;
            margin-bottom: 12px;
            padding: 4px 0 4px 4px;
            border-radius: 12px;
            border: 1px solid $border-color;

            &:nth-child(4n) {
                margin-right: 0;
            }

            &:hover {
                border-color: $color-primary;
            }

            img {
                width: 180px;
                height: 60px;
                border-radius: 8px;
            }

            .aside {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-grow: 1;

                .title {
                    width: 71px;
                    text-align: center;
                    @include utils-ellipsis-lines(2, 18px);
                }
            }
        }
    }

    .pi-wrapper {
        .content {
            display: flex;
            flex-wrap: wrap;

            .list {
                width: calc((100% - 60px) / 5);
                color: $font-color;
                border-radius: 12px;
                margin-bottom: 20px;
                margin-right: 15px;

                &:nth-child(5n) {
                    margin-right: 0px;
                }

                .info {
                    padding: 17px 15px 10px;
                    border-radius: 12px 12px 0 0;
                    border: 1px solid $color-white;
                    border-bottom: none;
                    background-color: $color-white;

                    img {
                        width: 195px;
                        height: 50px;
                        border-radius: 8px;
                        margin-bottom: 10px;
                    }

                    .title {
                        height: 40px;
                        @include utils-ellipsis-lines(2, 20px);
                    }
                }

                .bottom {
                    @include utils-ellipsis;
                    color: #6e4843;
                    height: 25px;
                    line-height: 25px;
                    border-radius: 0px 0px 12px 12px;
                    font-size: 12px;
                    padding-left: 16px;
                    padding-right: 20px;
                    background: linear-gradient(90deg, #f3eaeb, #fffaf7);
                    transition: all 0.3s ease;

                    span {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        transform: translateY(4px);
                        margin-right: 6px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/praise.png) no-repeat center/16px 16px;
                    }
                }

                &:hover {
                    .info {
                        border-color: $color-primary;
                        transition: all 0.3s ease;
                    }

                    .bottom {
                        color: $color-white;
                        background: #b61f22;

                        span {
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/praise-white.png) no-repeat center/16px 16px;
                        }
                    }
                }
            }
        }
    }

    .hot-recommend-wrapper {
        padding-bottom: 20px;

        .view-content {
            position: relative;
        }

        .top-view-more {
            display: flex;
            align-items: center;
            color: $color-primary;
            position: absolute;
            right: 0;
            top: 32px;
            z-index: 1;
        }

        .tabs-content {
            display: flex;
            flex-wrap: wrap;
            padding: 12px 20px 9px;
            border: 1px solid $border-color;
            border-radius: 12px;
            margin-bottom: 14px;
            position: relative;

            &::after {
                display: block;
                content: '';
                width: 20px;
                height: 14px;
                position: absolute;
                bottom: -14px;
                left: 50%;
                transform: translateX(-50%);
                background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/20px 14px;
            }

            .tabs {
                display: flex;
                width: calc(100% / 5);
                margin-bottom: 8px;

                .name {
                    display: flex;
                    cursor: pointer;
                    padding: 7px 0;
                    padding-left: 30px;
                    background-position: left center;
                    font-size: 16px;
                    border-bottom: 2px solid transparent;
                    background-size: 24px 24px;
                    background-repeat: no-repeat;

                    &:hover {
                        color: $color-primary;
                    }

                    &.active {
                        font-weight: bold;
                        color: $color-primary;
                        border-color: $color-primary;
                    }
                }
            }
        }

        .tabs-pane {
            display: none;
            $pane-content-height: 601px;

            &.show {
                display: block;
            }

            .hot-recommend-swiper {
                padding-top: 10px;
                height: calc($pane-content-height + 24px);

                .pane-content {
                    padding-top: 0px;
                }

                .swiper-slide {
                    height: 100%;
                }

                .swiper-pagination {
                    bottom: -12px;
                }

                & + .bottom {
                    margin-top: 20px;
                }
            }

            .pane-content {
                display: flex;
                flex-wrap: wrap;
                align-content: flex-start;
                padding-top: 10px;
                height: $pane-content-height;

                .list {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: calc((100% - 64px) / 5);
                    border: 1px solid $border-color;
                    border-radius: 12px;
                    padding: 13px 20px 11px;
                    color: $font-color;
                    position: relative;
                    margin-bottom: 10px;
                    margin-right: 16px;
                    box-sizing: border-box;

                    &:nth-child(5n) {
                        margin-right: 0px;
                    }

                    .logo {
                        max-width: 187px;
                        min-width: 40px;
                        height: 40px;
                        border-radius: 4px;
                        margin-bottom: 5px;
                    }

                    .name {
                        width: 100%;
                        font-size: 14px;
                        font-weight: bold;
                        margin-bottom: 7px;
                        text-align: center;
                        @include utils-ellipsis;
                    }

                    .type {
                        width: 100%;
                        color: $font-color-basic;
                        text-align: center;
                        @include utils-ellipsis;
                    }

                    &:hover {
                        border-color: $tag-background;

                        .mask {
                            display: flex;
                        }
                    }

                    .mask {
                        display: none;
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        border-radius: 12px;
                        background-color: rgba($color: $tag-background, $alpha: 0.9);
                        padding: 25px 20px 25px;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;

                        .title {
                            font-weight: bold;
                            margin-bottom: 12px;
                            text-align: center;
                            @include utils-ellipsis-lines(2, 21px, 14px);
                        }

                        span {
                            display: block;
                            color: $color-white;
                            border-radius: 24px;
                            width: 84px;
                            text-align: center;
                            margin: 0 auto;
                            line-height: 24px;
                            font-size: 12px;
                            background: $liner-gradient-primary;
                        }
                    }
                }
            }

            .bottom {
                display: flex;
                justify-content: center;
                margin-top: 44px;
            }
        }

        .empty-content {
            color: $font-color-basic;
            text-align: center;
            padding-top: 480px;
            margin-top: 20px;
            height: 764px;
            border: 1px solid #f4c6c7;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/empty.png) no-repeat center 256px/266px auto, $color-white;
            border-radius: 12px;
        }
    }

    .announcement-wrapper {
        background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/632px 232px, $background-primary;
        padding-bottom: 20px;

        .view-content {
            position: relative;
        }

        .refresh {
            position: absolute;
            right: 0;
            top: 25px;
            color: $color-primary;
            z-index: 1;
            border: none;
            background-color: none;
            padding: 0 23px 0 0;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/refresh-primary.png) no-repeat right/20px 20px;
        }

        .common-title {
            .title {
                height: 28px;
                width: 215px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/contain;
            }
        }

        @mixin list {
            .list {
                display: flex;
                width: calc((100% - 44px) / 2);
                margin-right: 44px;
                margin-bottom: 19px;
                align-items: center;

                &::before {
                    content: '';
                    width: 5px;
                    height: 5px;
                    border-radius: 50%;
                    background-color: $color-primary;
                    margin-right: 6px;
                    flex-shrink: 0;
                }

                &:nth-child(2n) {
                    margin-right: 0;
                }

                .title {
                    flex-grow: 1;
                    @include utils-ellipsis;
                }

                .time {
                    flex-shrink: 0;
                    margin-left: 10px;
                    color: $font-color-label;
                }
            }
        }

        .announcement-content {
            display: none;
            flex-wrap: wrap;
            padding: 20px 20px 1px;
            background-color: $color-white;
            border-radius: 12px;
            margin-bottom: 20px;

            &.active {
                display: flex;
            }

            @include list;
        }

        .like-content {
            border-radius: 12px;
            padding: 20px 20px 1px;
            background: linear-gradient(180deg, #ffefee, #ffffff, #ffffff);

            .top {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 18px;

                .left {
                    display: flex;
                    align-items: center;
                    color: $color-primary;
                    padding-left: 40px;
                    font-size: 22px;
                    font-weight: bold;
                    background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/30px 22px;

                    .tips {
                        margin-left: 9px;
                        font-size: 14px;
                        color: $font-color-basic;
                        font-weight: normal;
                    }
                }

                .right {
                    color: #6e4843;
                    display: flex;
                    align-items: center;

                    a {
                        color: $color-primary;
                        margin-left: 8px;
                    }
                }
            }

            .list-content {
                display: flex;
                flex-wrap: wrap;

                @include list;
            }
        }
    }

    .new-release-wrapper {
        padding-bottom: 20px;

        .view-content {
            position: relative;
        }

        .tabs-content {
            display: flex;
            margin-bottom: 20px;

            .tabs {
                margin-right: 20px;
                font-size: 18px;
                padding: 0 20px;
                line-height: 40px;
                border-radius: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;

                span {
                    margin-right: 5px;
                    width: 20px;
                    height: 20px;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: contain;
                }

                .icon-active {
                    display: none;
                }

                &.active,
                &:hover {
                    font-weight: bold;
                    color: $color-white;
                    background: $liner-gradient-primary;

                    .icon-active {
                        display: inline-block;
                    }

                    .icon-default {
                        display: none;
                    }
                }
            }
        }

        .tabs-pane {
            display: none;

            .top-more {
                position: absolute;
                right: 0;
                top: 91px;
                z-index: 1;
                color: $color-primary;
            }

            &.show {
                display: block;
            }

            .list-content {
                display: flex;
                flex-wrap: wrap;

                .list {
                    width: calc((100% - 20px) / 2);
                    height: 90px;
                    margin-right: 20px;
                    margin-bottom: 20px;
                    padding: 4px;
                    border-radius: 12px;
                    border: 1px solid #f4c6c7;
                    display: flex;
                    color: $font-color;

                    &:hover {
                        border-color: $color-primary;

                        .time {
                            color: $color-white;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/48px 48px, $color-primary;
                        }
                    }

                    &:nth-child(2n) {
                        margin-right: 0;
                    }

                    .time {
                        width: 100px;
                        padding: 20px 5px 0px 15px;
                        color: #6e4843;
                        height: 100%;
                        flex-shrink: 0;
                        font-size: 24px;
                        font-weight: bold;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/48px 48px, #fbf7f5;
                        border-radius: 12px 0px 0px 12px;
                        margin-right: 20px;

                        .year {
                            font-size: 14px;
                            opacity: 0.8;
                            margin-top: 5px;
                            font-weight: normal;
                        }
                    }

                    .detail {
                        flex-grow: 1;
                        overflow: hidden;
                        padding: 15px 16px 2px 0;
                    }

                    .title {
                        font-size: 16px;
                        font-weight: bold;
                        margin-bottom: 15px;
                        @include utils-ellipsis;

                        &.is-top {
                            padding-left: 17px;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/10px 14px;
                        }
                    }

                    .bottom {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        line-height: 22px;

                        .welfare {
                            flex-grow: 1;
                            color: #9f6d1a;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/praise-fill.png) no-repeat left/26px 22px;
                            padding-left: 29px;
                            @include utils-ellipsis;
                        }

                        .info {
                            span {
                                color: $color-primary;
                            }
                        }

                        .address {
                            color: $font-color-basic;
                            flex-shrink: 0;
                            max-width: 100px;
                            padding-left: 18px;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/address.png) no-repeat left/12px 12px;
                            margin-left: 18px;
                            @include utils-ellipsis;
                        }
                    }
                }
            }

            .view-more {
                display: block;
                width: 140px;
                margin: 0 auto;
            }
        }
    }

    .region-wrapper {
        background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/378px 127px,
            url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/491px 127px, #f8f8f8;
        padding: 20px 0;

        .view-content {
            display: flex;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/98px 66px, $color-white;
            padding: 18px 20px 1px 155px;
            border-radius: 12px;
            min-height: 87px;

            .region-content {
                display: flex;
                flex-wrap: wrap;
                flex-grow: 1;

                .list {
                    font-size: 15px;
                    margin-right: 22px;
                    margin-bottom: 18px;

                    &:hover {
                        font-weight: bold;
                    }
                }
            }

            .region-more {
                width: 22px;
                height: 50px;
                align-self: center;
                transform: translateY(-9px);
                flex-shrink: 0;
                padding: 11px 0 9px;
                background: $tag-background;
                border-radius: 11px;
                color: $color-primary;
                text-align: center;
                align-self: center;
            }
        }
    }

    .job-wrapper {
        padding-top: 5px;
        padding-bottom: 20px;

        .content {
            position: relative;
        }

        .tabs-content {
            display: flex;
            margin-bottom: 20px;

            .tabs {
                margin-right: 20px;
                font-size: 18px;
                padding: 0 20px;
                line-height: 40px;
                border-radius: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;

                span {
                    margin-right: 5px;
                    width: 20px;
                    height: 20px;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: contain;
                }

                .icon-active {
                    display: none;
                }

                &.active,
                &:hover {
                    font-weight: bold;
                    color: $color-white;
                    background: $liner-gradient-primary;

                    .icon-active {
                        display: inline-block;
                    }

                    .icon-default {
                        display: none;
                    }
                }
            }
        }

        .top-tips {
            display: flex;
            align-items: center;
            color: #6e4843;
            position: absolute;
            right: 0;
            top: 12px;

            a {
                color: $color-primary;
                font-weight: bold;
            }
        }

        .tabs-pane {
            display: none;

            &.show {
                display: block;
            }

            .list-content {
                display: flex;
                flex-wrap: wrap;
            }

            .list {
                width: calc((100% - 40px) / 3);
                margin-right: 20px;
                margin-bottom: 20px;
                box-shadow: $box-shadow-default;
                border-radius: 12px;
                overflow: hidden;

                &:nth-child(3n) {
                    margin-right: 0px;
                }

                &:hover {
                    box-shadow: 0px 0px 0px 1px $color-primary, 0 3px 10px 5px rgba(51, 51, 51, 0.12);
                }

                .detail {
                    padding: 16px 20px 15px;
                }

                .top {
                    display: flex;
                    margin-bottom: 12px;

                    .name {
                        flex-grow: 1;
                        font-size: 16px;
                        line-height: 22px;
                        font-weight: bold;
                        @include utils-ellipsis;
                    }

                    .aside {
                        flex-shrink: 0;
                        margin-left: 22px;
                        width: 22px;
                        height: 22px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/info-off.png?v=1.0) no-repeat center/contain;

                        &.on-line {
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/info-on.png?v=1.0) no-repeat center/contain;
                        }
                    }
                }

                .info {
                    display: flex;
                    margin-bottom: 12px;

                    .welfare {
                        font-size: 16px;
                        font-weight: bold;
                        color: $font-color-salary;
                        margin-right: 11px;
                    }

                    .invite {
                        color: $font-color-basic;
                    }
                }

                .relevance {
                    display: flex;
                    font-size: 12px;
                    color: $font-color;

                    .label {
                        margin-right: 9px;
                        color: $font-color-basic;
                        flex-shrink: 0;
                    }

                    .name {
                        padding-right: 12px;
                        @include utils-ellipsis;
                    }
                }

                .bottom {
                    display: flex;
                    align-items: center;
                    background: linear-gradient(90deg, #f3eaeb, #fffaf7);
                    height: 42px;
                    padding: 0 32px 0 20px;

                    & > [class*='-content'] {
                        display: flex;
                        align-items: center;
                        overflow: hidden;
                    }

                    .tag {
                        font-size: 12px;
                        padding: 0 6px;
                        line-height: 18px;
                        margin-right: 6px;
                        color: $color-white;
                        border-radius: 4px;
                        flex-shrink: 0;
                    }

                    .value {
                        flex-grow: 1;
                        @include utils-ellipsis;
                        color: $font-color-basic;
                    }

                    .welfare-content {
                        .tag {
                            background: linear-gradient(90deg, #f88773, #fbb2a5);
                        }
                    }

                    .major-content {
                        .tag {
                            background: linear-gradient(90deg, #f5bf65, #f7a927);
                        }
                    }

                    .time-content {
                        color: $font-color-basic;
                        padding-left: 20px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/time.png) no-repeat left/12px 12px;
                    }
                }
            }

            .empty-content {
                padding: 265px 0 60px;
                text-align: center;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/empty.png) no-repeat center 40px/266px 195px;
            }
        }

        .view-more {
            display: block;
            width: 140px;
            margin: 0 auto;
        }
    }

    .brand-wrapper {
        padding-bottom: 20px;

        .view-content {
            position: relative;
        }

        .more {
            position: absolute;
            right: 0;
            top: 25px;
            color: $color-primary;
            z-index: 1;
        }

        .content {
            display: flex;
            width: $view-width;

            .list {
                flex-shrink: 0;
                flex-grow: 1;
                width: 226px;
                height: 330px;
                border-radius: 12px;
                overflow: hidden;
                margin-right: 20px;
                transition: width ease 0.1s;

                &:last-child {
                    margin-right: 0;
                }

                .show {
                    display: none;
                }

                &.active {
                    width: 460px;
                    flex-shrink: 0;

                    .cover {
                        display: none;
                    }

                    .show {
                        display: block;
                    }
                }

                &:nth-child(2n) {
                    .cover {
                        background: linear-gradient(180deg, #b61f22, #ffffff);
                    }
                }

                @for $i from 1 through 4 {
                    &:nth-child(#{$i}) {
                        .company {
                            background-image: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>
                        }
                    }
                }

                .cover {
                    height: 100%;
                    background: linear-gradient(180deg, #e07463, #fffcfb);

                    .company {
                        width: 100%;
                        height: 100%;
                        padding: 20px;
                        color: $color-white;
                        background-position: left bottom;
                        background-repeat: no-repeat;
                        background-size: 100% auto;

                        .title {
                            font-weight: bold;
                            @include utils-ellipsis-lines(3, 30px, 20px);
                        }

                        .sub-title {
                            color: rgba($color: $color-white, $alpha: 0.8);
                            margin-top: 5px;
                            @include utils-ellipsis-lines(2, 24px, 14px);
                            padding-top: 17px;
                            max-height: 60px;
                            position: relative;

                            &::before {
                                content: '';
                                display: block;
                                width: 80px;
                                height: 4px;
                                border-radius: 2px;
                                background-color: $color-white;
                                position: absolute;
                                left: 0;
                                top: 0;
                            }
                        }
                    }
                }

                .show {
                    height: 100%;
                    background-color: $color-white;

                    .top {
                        display: flex;
                        padding: 20px;
                        background-color: #e07463;
                        color: $color-white;
                    }

                    .logo {
                        flex-shrink: 0;
                        width: 60px;
                        height: 60px;
                        background-color: $color-white;
                        border-radius: 30px;
                        margin-right: 16px;
                    }

                    .aside {
                        flex-grow: 1;
                        overflow: hidden;
                    }

                    .title {
                        font-size: 20px;
                        font-weight: bold;
                        line-height: 30px;
                        margin-bottom: 7px;
                        @include utils-ellipsis;
                    }

                    .type {
                        font-size: 16px;
                        line-height: 1.01;
                    }

                    .detail {
                        padding: 20px 20px 10px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/244px 121px;
                    }

                    .tag-content {
                        display: flex;
                        margin-bottom: 7px;

                        .tag {
                            color: #6e4843;
                            line-height: 24px;
                            padding: 0 10px;
                            font-size: 12px;
                            margin-right: 8px;
                            background: $tag-background;
                            border-radius: 4px;
                        }
                    }

                    .desc {
                        height: 72px;
                        margin-bottom: 16px;
                        color: $font-color-basic;
                        @include utils-ellipsis-lines(3, 24px, 14px);
                    }

                    .total {
                        display: flex;
                        font-size: 16px;
                        font-weight: bold;
                        align-items: center;
                        margin-bottom: 18px;
                        color: $font-color;

                        .amount {
                            font-size: 20px;
                            margin-right: 22px;
                            color: $color-primary;
                        }
                    }

                    .look-detail {
                        display: inline-block;
                        line-height: 33px;
                        border-radius: 33px;
                        padding: 0 20px;
                        color: $color-white;
                        background: $liner-gradient-primary;
                    }
                }
            }
        }
    }

    .postdoctor-wrapper {
        padding-bottom: 35px;

        .view-content {
            position: relative;
        }

        .more {
            position: absolute;
            right: 0;
            top: 25px;
            color: $color-primary;
            z-index: 1;
        }

        .content {
            display: flex;

            .list {
                width: calc((100% - 60px) / 4);
                background-color: $color-white;
                border-radius: 12px;
                box-shadow: $box-shadow-default;
                overflow: hidden;
                margin-right: 20px;

                &:nth-child(4n) {
                    margin-right: 0;
                }

                &:hover {
                    box-shadow: 0px 0px 0px 1px $color-primary, 0 3px 10px 5px rgba(51, 51, 51, 0.12);
                }

                .top {
                    img {
                        height: 150px;
                        object-fit: contain;
                        margin-bottom: 18px;
                    }

                    .time {
                        color: $font-color-label;
                        margin-bottom: 12px;
                        padding: 0 15px;
                    }

                    .title {
                        font-weight: bold;
                        padding: 0 15px;
                        height: 48px;
                        @include utils-ellipsis-lines(2, 24px, 16px);
                    }
                }

                .bottom {
                    padding: 0 15px;
                    display: flex;
                    align-items: center;
                    margin-top: 5px;
                    padding-bottom: 20px;

                    .address {
                        @include utils-ellipsis;
                        padding-right: 23px;
                        color: $font-color-basic;
                        flex-grow: 1;
                    }

                    a {
                        flex-shrink: 0;
                        color: $color-white;
                        padding: 0 20px;
                        background: $liner-gradient-primary;
                        line-height: 32px;
                        border-radius: 32px;
                        font-weight: bold;

                        &.disabled {
                            background: #bababa;
                        }
                    }
                }
            }
        }
    }

    .cooperation-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: 215px;
        background: url(//img.gaoxiaojob.com/uploads/boshihou/home/<USER>/auto 100%, $background-primary;
        padding-top: 30px;

        .top {
            font-size: 20px;
            line-height: 31px;
            padding-left: 47px;
            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/postdoctor-logo.png) no-repeat left/40px 31px;
            margin-bottom: 18px;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
        }

        .bottom {
            display: flex;

            .cooperation {
                background: $liner-gradient-primary;
                color: $color-white;
                padding: 0 42px;
                font-weight: bold;
                line-height: 40px;
                border-radius: 40px;
                margin-right: 20px;
            }

            .tel {
                border-radius: 20px;
                border: 1px solid $color-primary;
                padding: 0 30px 0 63px;
                line-height: 38px;
                background: url(//img.gaoxiaojob.com/uploads/boshihou/common/phone.png) no-repeat 30px center/25px 25px;
                color: $color-primary;
                font-weight: bold;
            }
        }
    }
}
