@use 'sass:meta';
@import './variables';
@import './common/popper';
@import './mini-code-popup';

$header-margin-bottom: 50px;

$border-color-bg: rgba(
    $color: $color-white,
    $alpha: 0.4
);

@mixin span-boon {
    padding: 4px 10px;
    color: $font-color;
    line-height: 1;
    background: #f8f9fb;
    border: 1px solid $border-color;
    border-radius: 4px;

    &::before {
        content: '';
        display: inline-block;
        margin-top: -3px;
        margin-right: 4px;
        width: 4px;
        height: 4px;
        background: $color-primary;
        border-radius: 4px;
        vertical-align: middle;
    }
}

body {
    .el-main {
        padding-bottom: 0;
    }
}

.detail-container {
    padding-bottom: 20px;

    &.template-two {
        background-color: #e7eafb;
    }

    &.template-three {
        background-color: #e3f0fb;
    }

    .detail-header-template {
        @import './notice/headerTemplate';

        .detail-header {
            .detail-header-container {
                .main {
                    aside {
                        .detail-button {
                            .el-button--collect {
                                background: url(../assets/detail/collect.png) no-repeat left center / 20px;
                            }
                        }
                    }
                }
            }
        }
    }

    .detail-header-template-one {
        @import './notice/headerTemplateOne';

        .detail-header {
            margin-bottom: 37px;
        }
    }

    .detail-header-template-two {
        @import './notice/headerTemplateTwo';

        .detail-header {
            margin-bottom: 53px;
        }
    }

    .detail-header-template-three {
        @import './notice/headerTemplateThree';

        .detail-header {
            margin-bottom: 39px;
        }
    }

    .detail-main {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        width: $view-width;

        section {
            padding: 20px 30px 30px;
            width: 858px;
            background: $color-white;
            border-radius: 10px;
            align-self: flex-start;

            .tips {
                margin-bottom: 20px;
                padding: 10px 30px 10px 45px;
                color: $font-color-basic;
                line-height: 2;
                background: url(../assets/icon/warning.png) no-repeat #fff9e2 20px 14px / 16px;
                @include utils-radius;
            }
        }

        @include meta.load-css('./common/aside');
    }

    .filter {
        $filter-item-width: 160px;
        $filter-input-height: 28px;
        $filter-input-icon-top: -5px;

        display: flex;
        margin-bottom: 20px;

        .el-select,
        .el-cascader {
            margin-right: 20px;
            width: $filter-item-width;

            &.is-select {
                .el-input__inner {
                    color: $color-primary;
                    border-color: $color-primary;
                }

                .el-input {
                    .el-select__caret {
                        color: $color-primary;
                    }
                }
            }
        }

        .el-input__inner {
            height: $filter-input-height;
            line-height: $filter-input-height;

            &::placeholder {
                color: $font-color-basic;
            }
        }

        .el-input__suffix {
            top: $filter-input-icon-top;
        }

        .el-input__icon {
            line-height: $filter-input-height;
        }
    }

    .el-table {
        margin-bottom: 20px;
        width: 100%;
        color: $font-color;
        border-spacing: 0;

        &::before {
            content: none;
        }

        tr {
            display: flex;
            align-items: center;
            border-bottom: 1px solid $border-color;
        }

        th.el-col {
            padding-top: 10px;
            padding-bottom: 10px;
            color: $font-color;
            font-size: 16px;
            font-weight: bold;
            text-align: left;
            line-height: 1;
            background: #fafafc;
        }

        .el-col {
            padding: 20px;
        }

        .data-name {
            display: flex;
            flex-direction: column;
            font-size: 14px;
            line-height: 2;

            a {
                font-weight: bold;

                &:hover {
                    color: $color-primary;
                }
            }

            p {
                span {
                    font-size: 12px;
                    color: $font-color-basic;

                    & + span {
                        margin-left: 10px;
                        padding-left: 10px;
                        border-left: 1px solid $font-color-tips;
                    }
                }

                .salary {
                    margin-right: 10px;
                    color: $font-color-salary;
                    font-size: 14px;
                }
            }
        }

        .data-major {
            @include utils-ellipsis-lines(2, 2);
            padding-top: 0;
            padding-bottom: 0;
            color: $font-color;

            span {
                font-size: inherit;
            }
        }

        .el-button {
            padding: 0;
            width: 72px;
            min-height: 28px;
            text-align: center;

            &.offline {
                background: #cbcbcb;
                border: 1px solid #cbcbcb;
            }

            & + .el-button {
                margin-left: 0;
            }
        }

        .el-button--collect {
            padding-left: 25px;
            color: $font-color;
            font-size: 14px;
            text-align: left;
            background: url(../assets/detail/collect.png) no-repeat left center / 18px;
            border: none;

            &.collected {
                background-image: url(../assets/detail/collected.png);
            }
        }
    }

    .no-data {
        padding-top: 260px;
        padding-bottom: 123px;
        text-align: center;
        font-size: 14px;
        color: $font-color-basic;
        background: url(../assets/job/empty.png) no-repeat center 133px / 150px 111px;
    }
}

.el-popper {
    .boon {
        display: inline-block;
        padding: 3px 8px;
        margin: 8px 6px;
        color: $color-primary;
        font-size: 12px;
        font-weight: 400;
        background: #feebcb;
        border-radius: 4px;
    }
}

.major-popper {
    .arrow {
        bottom: -5px;
        left: 50%;
        top: auto !important;
        transform: translateX(-50%);
    }
}

.template-two-welfare-popover {
    display: flex;
    padding: 6px 8px !important;
    flex-wrap: wrap;

    .boon {
        display: inline-block;
        padding: 3px 8px;
        margin: 6px 4px;
        font-size: 12px;
        font-weight: 400;
        border-radius: 4px;
        color: #6893f1;
        background-color: #ecf5ff;
    }
}
