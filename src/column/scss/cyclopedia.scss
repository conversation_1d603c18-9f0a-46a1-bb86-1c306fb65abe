@import './variables';

.cyclopedia-container {
    background-color: #f6f6f6;
}

.view-content {
    width: 1200px;
    margin: 0 auto;
}

.job-desc-wrapper {
    margin-bottom: 20px;
    padding: 28px 0 26px;
    background: url(../assets/cyclopedia/top-bg.png) no-repeat left center/cover;

    .job-name {
        font-size: 24px;
        font-weight: bold;
        line-height: 40px;
        background: url(../assets/cyclopedia/job.png) no-repeat left/46px 45px;
        padding: 5px 0 8px;
        padding-left: 54px;
    }

    .desc {
        border-top: 1px solid #ebebeb;
        font-size: 16px;
        padding-top: 13px;
        line-height: 24px;
        min-height: 86px;
    }
}

.main-wrapper {
    display: flex;
    padding-bottom: 20px;
}

.main-content {
    flex-grow: 1;

    .list-wrapper {
        .list {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            background-color: $color-white;

            .info {
                padding: 20px;
                background: linear-gradient(90deg, #fffaf2 0%, #ffffff 100%);
            }

            .top {
                display: flex;
                justify-content: space-between;

                .name {
                    font-size: 16px;
                    font-weight: bold;
                    width: 453px;
                    @include utils-ellipsis;

                    &:hover {
                        color: $color-primary;
                    }
                }

                .company {
                    margin-left: 30px;
                    font-size: 14px;
                    width: 337px;
                    text-align: right;
                    @include utils-ellipsis;

                    &:hover {
                        color: $color-primary;
                    }
                }
            }

            .bottom {
                display: flex;
                justify-content: space-between;
                margin-top: 10px;
                font-size: 14px;

                .salary {
                    margin-right: 9px;
                    font-size: 16px;
                    font-weight: bold;
                    color: $font-color-salary;
                }

                .type {
                    color: $font-color-basic;
                }
            }

            .content {
                margin: 20px;
                background: #f6f6f6;
                border-radius: 10px;
                padding: 16px;
                position: relative;

                .desc {
                    font-size: 14px;
                    line-height: 24px;

                    &.up {
                        padding-bottom: 0px;
                        @include utils-ellipsis-lines(6, 24px);
                    }

                    &.down {
                        padding-bottom: 28px;
                    }
                }

                .desc + .trigger {
                    &::after {
                        content: '收起';
                    }
                }

                .up + .trigger {
                    background: linear-gradient(to right, rgba(247, 248, 249, 0) 0%, rgba(247, 248, 249, 0.8) 42%, rgba(247, 248, 249, 1) 100%);

                    &::after {
                        content: '展开';
                    }
                }

                .trigger {
                    cursor: pointer;
                    font-size: 14px;
                    position: absolute;
                    color: $color-primary;
                    right: 16px;
                    bottom: 18px;
                    padding: 2px 0 2px 40px;
                    display: none;

                    &.show {
                        display: block;
                    }
                }
            }
        }
    }

    .more-wrapper {
        background-color: $color-white;
        border-radius: 10px;
        overflow: hidden;

        .title-wrapper {
            padding: 0 20px;
            line-height: 40px;
            background: linear-gradient(0deg, #fff9ed 0%, #fffcf7 100%);
            border-radius: 10px 10px 0px 0px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                font-size: 18px;
                font-weight: bold;
                display: flex;
                align-items: center;

                &::before {
                    content: '';
                    margin-right: 8px;
                    width: 4px;
                    height: 16px;
                    background: $color-primary;
                    border-radius: 2px;
                }
            }

            .arrow {
                font-size: 14px;
                color: $color-primary;
                padding-right: 16px;
                background: url(../assets/cyclopedia/arrow.png) no-repeat right center/10px 10px;
            }
        }

        .list-content {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            column-gap: 16px;
            row-gap: 16px;
            padding: 20px;
        }

        .list {
            padding: 15px 12px 17px;
            border-radius: 10px;
            border: 1px solid #ebebeb;
            overflow: hidden;

            &:hover {
                border-color: $color-primary;
                .top {
                    .name {
                        color: $color-primary;
                    }
                }
            }

            .top {
                display: flex;
                align-items: center;
                font-size: 14px;
                margin-bottom: 12px;

                .name {
                    flex-grow: 1;
                    font-weight: bold;
                    margin-right: 18px;
                    @include utils-ellipsis;
                }

                .salary {
                    flex-shrink: 0;
                    color: $font-color-salary;
                }
            }

            .tag-content {
                display: flex;
                margin-bottom: 12px;

                .tag {
                    font-size: 12px;
                    border-radius: 4px;
                    background-color: #f3f8fd;
                    padding: 0 7px;
                    line-height: 20px;

                    & + .tag {
                        margin-left: 6px;
                    }
                }
            }

            .bottom {
                font-size: 13px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .company {
                    @include utils-ellipsis;
                    width: 156px;
                    color: $font-color-label;
                }

                .address {
                    @include utils-ellipsis;
                    color: $font-color-label;
                    padding-left: 13px;
                    max-width: 65px;
                    background: url(../assets/cyclopedia/address.png) no-repeat left top/12px 12px;
                }
            }
        }
    }
}

.aside-content {
    flex-shrink: 0;
    width: 320px;
    margin-left: 20px;

    .more-wrapper {
        background-color: $color-white;
        border-radius: 10px;

        .title {
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
            padding: 0 20px;
            line-height: 40px;
            background: linear-gradient(0deg, #fff9ed 0%, #fffcf7 100%);
            border-radius: 10px 10px 0px 0px;

            &::before {
                content: '';
                margin-right: 8px;
                width: 4px;
                height: 16px;
                background: $color-primary;
                border-radius: 2px;
            }
        }

        .content {
            padding: 8px 20px 10px;

            .item {
                font-size: 14px;
                display: block;
                padding: 9px 0;
                @include utils-ellipsis;

                &:hover {
                    color: $color-primary;
                }
            }
        }
    }
}
