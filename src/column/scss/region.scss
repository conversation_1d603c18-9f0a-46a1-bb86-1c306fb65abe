// 地区选择页面样式

@import './variables';

$color-bg: #f8f9fb;
$border-color: #ebebee;

.column-wrapper {
    background-color: $color-white;
}

.city-list {
    background-color: $color-bg;

    .common-city {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        padding: 30px 0;
        width: $view-width;
    }

    .hot-city {
        font-size: 18px;
        line-height: 38px;
    }

    .el-form {
        display: flex;
        height: 0;
    }

    .el-form-item {
        margin-right: 12px;

        &:last-child {
            margin-right: 0;
        }
    }

    .el-select .el-input__inner {
        width: 151px;
        height: 38px;
        color: $font-color-label;
    }

    .el-button {
        padding: 11px 20px;
        width: 68px;
        min-height: 38px;
    }

    .city {
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        width: $view-width;

        a {
            display: block;
            margin: 0 25px 30px 0;
            width: 150px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            background-color: $color-white;
            border-radius: 4px;
        }

        & > a:nth-child(7n) {
            margin-right: 0;
        }

        & > a:hover {
            background-color: $color-primary;
            color: $color-white;
        }
    }
}

.main {
    background: url('../assets/region/region-bg.png') no-repeat bottom center;
}

.citieslist-content {
    margin: 0 auto;
    padding-bottom: 70px;
    width: $view-width;

    .citieslist {
        display: flex;
        margin-top: 40px;
        border: 1px solid $border-color;
        border-radius: 8px;
    }

    .region {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 78px;
        font-size: 18px;
        background-color: $color-bg;
    }

    ul {
        width: 1122px;
        padding: 10px 40px;

        li {
            border-bottom: 1px solid $border-color;

            &:last-child {
                border-bottom: none;
            }
        }
    }

    a {
        @include utils-ellipsis;

        display: inline-block !important;
        margin: 7px 40px 7px 0;
        padding: 4px;
        width: 84px;
        line-height: 24px;
    }

    a:first-child {
        font-size: 16px;
        font-weight: bold;
    }
}
