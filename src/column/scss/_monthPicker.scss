@import './variables';

$class-prefix: 'month-picker';
$top-height: 30px;

.#{$class-prefix} {
    &-container {
        min-width: 300px;
    }

    &-top {
        height: $top-height;
        line-height: $top-height;
        margin-bottom: 12px;
        text-align: center;
        &-prev,
        &-next {
            float: left;
            .el-button {
                border: none;
                padding: 0;
                height: auto;
                background-color: transparent;
            }
        }

        &-label {
            font-weight: 600;
            padding: 0 5px;
            line-height: 22px;
            text-align: center;
            cursor: pointer;
        }

        &-next {
            float: right;
        }
    }

    &-content {
        display: flex;
        flex-wrap: wrap;
        border-top: 1px solid var(--el-border-color-lighter);

        &-item {
            width: calc(100% / 4);
            text-align: center;
            padding: 20px 3px;
            cursor: pointer;

            .cell {
                width: 60px;
                height: 36px;
                display: block;
                line-height: 36px;
                font-size: 12px;
                margin: 0 auto;

                &:hover {
                    color: $color-primary;
                }
            }

            &.current {
                .cell {
                    font-weight: bold;
                    color: $color-primary;
                }
            }

            &.selected {
                .cell {
                    color: $color-primary;
                }
            }

            &.disabled {
                cursor: not-allowed;
                .cell {
                    border-radius: 36px;
                    background-color: var(--el-bg-color);
                    color: var(--el-text-color-placeholder);
                }
            }
        }
    }
}
