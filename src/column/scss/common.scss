@import './variables';

html {
    height: 100%;
    box-sizing: border-box;
}

body {
    min-width: $view-width;
    height: 100%;
    color: $font-color;
    font-size: 12px;
    font-family: 'Microsoft YaHei', 'Hiragino Sans GB', tahoma, arial;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background: #f9f9f9;
}

::-webkit-scrollbar-thumb {
    background: #c0c0c0;
    border-radius: 5px;

    &:hover {
        background: #7c7c7c;
    }
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
    margin: 0;
    padding: 0;
    font-size: 12px;
}

ul {
    list-style: none;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

div:focus {
    outline: none;
}

label {
    font-weight: 700;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

$font-sizes: 12 14 16 18 20;

@each $size in $font-sizes {
    .ft#{$size} {
        font-size: #{$size}px;
    }
}

.block {
    display: block;
}

.pointer {
    cursor: pointer;
}

.clearfix {
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: ' ';
        clear: both;
        height: 0;
    }
}

a.color-primary {
    color: $color-primary;
}

$font-colors: (
    default: $font-color,
    primary: $color-primary,
    basic: $font-color-basic,
    salary: $font-color-salary,
    label: $font-color-label,
    tips: $font-color-tips
);

@each $label, $value in $font-colors {
    .color-#{$label} {
        color: $value;
    }
}

.flex {
    display: flex;
}

.flex-1 {
    flex: 1;
}

.inline-block {
    display: inline-block;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.none-select {
    user-select: none;
}

.title-primary {
    padding-left: 12px;
    border-left: 4px solid $color-primary;
    border-radius: 2px;
}

#component {
    display: flex;
    flex: 1;
    flex-direction: column;
    // 由于后端渲染的原因，顶部无法固定，主体设置高度无效
    // height: calc(100% - $header-height);
}

/* 栏目主体样式 */
.column-wrapper {
    font-size: 14px;
    background-color: $background-primary;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    ul {
        font-size: 14px;
    }

    img:not(.rich-img) {
        display: block;
        width: 100%;
        height: 100%;
        font-size: 0;
        object-fit: contain;
    }

    .component {
        display: flex;
        justify-content: space-between;
    }

    .main {
        margin: 0 auto;
        width: $view-width;

        a {
            display: block;

            img {
                background-color: $color-white;
            }

            &:hover {
                color: $color-primary;
            }
        }
    }
}

// 头部(公告&简章)鼠标经过后展开
.header-notice-container {
    position: relative;
    cursor: pointer;

    &:hover {
        color: $color-primary;

        .notice-open-part {
            display: block;
        }
    }

    // 展开部分 栏目页距离顶部 top $page-header-height
    .notice-open-part {
        display: none;
        position: fixed;
        width: 100vw;
        top: $page-header-height;
        left: 50%;
        transform: translateX(-50%);
        line-height: 1;
        font-weight: normal;
        border-top: 1px solid #ffedcf;
        box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);
        background: $color-white url('../assets/region/region-nav-bg.png') no-repeat bottom center;
        z-index: $basic-index;

        // 公告页面距离顶部 top $header-height
        &.is-open {
            top: $header-height;
        }

        &:hover {
            cursor: default;
        }
    }

    .notice-content {
        margin: 0 auto;
        padding: 30px 0 10px;
        width: $view-width;

        a {
            display: inline-block;
            padding: 0 !important;
        }

        .more {
            padding-left: 17px !important;
        }
    }

    .nav-list {
        display: flex;
        margin-bottom: 9px;
    }

    .nav-title {
        width: 109px;
        font-weight: bold;
        color: $font-color;
    }

    .nav-container {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        font-size: 12px;
        color: $font-color;

        li {
            margin-bottom: 20px;
            width: calc(100% / 15);

            &:nth-child(15n) {
                padding-right: 20px;
                text-align: right;
            }

            a:hover {
                color: $color-primary;
            }
        }
    }

    .is-nav-container {
        li {
            margin-right: 5px;
            width: calc(100% / 10);
            @include utils-ellipsis;

            &:nth-child(15n) {
                text-align: left;
            }
        }
    }

    .more {
        position: absolute;
        right: 20px;
        bottom: 20px;
        text-align: right;
        font-weight: bold;
        color: $color-primary;
        background: url('../assets/home/<USER>') no-repeat left center / 14px;
    }
}

// 顶部 广告 轮播滚动通栏
.main-header-showcase__container {
    margin: 0 auto;
    padding-bottom: 20px;
    width: $view-width;

    .el-carousel__item {
        a {
            display: inline-block;
            margin-right: 8px;
            width: 164px;
            height: 70px;
            background-color: $color-white;
            border: 1px solid $border-color;
            border-radius: 8px;
            overflow: hidden;

            &:last-of-type {
                margin-right: 0;
            }

            & > img {
                // display: block;
                width: 100%;
                height: 100%;
                object-fit: contain;
            }
        }
    }

    .el-carousel__indicator {
        &.is-active {
            .el-carousel__button {
                width: 20px;
                height: 6px;
                background-color: $color-primary;
                border-radius: 3px;
            }
        }

        .el-carousel__button {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background-color: #dedede;
            opacity: 1;
        }
    }
}

/*
* Reset ElementPlus Style
*/
body {
    [v-cloak] {
        display: none !important;
    }

    .el-header {
        --el-header-padding: 0;
        height: $header-height;
        background-color: #5c5c5c;

        &.is-fixed {
            @include utils-fixed($header-index);
        }

        .header-container {
            display: flex;
            flex: none;
            justify-content: space-between;
            align-items: center;
            margin: 0 auto;
            width: $view-width;
            height: 100%;

            .header-nav {
                display: flex;
                align-items: center;
                color: $color-white;
                font-size: 15px;
                font-weight: bold;
                line-height: $header-height;

                .nav-link {
                    padding: 0 16px;
                    .gaocai-vip {
                        border-radius: 8px 8px 8px 0px;
                        background-color: #fa635c;
                        color: #fff !important;
                        padding: 0px 6px;
                        font-size: 12px;
                        margin-left: 5px;
                    }

                    &:hover,
                    &.active {
                        color: $color-primary;
                    }
                }

                .header-logo {
                    margin-right: 20px;
                    padding: 0;
                    width: $logo-column-width;
                    height: $logo-column-height;

                    img {
                        display: block;
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                    }
                }

                // vip相关
                .vip {
                    position: relative;
                    a {
                        .arrow {
                            display: inline-block;
                            margin-left: 10px;
                            width: 11px;
                            height: 6px;
                            vertical-align: middle;
                            background: url('../assets/home/<USER>') no-repeat center / cover;
                        }
                        .artifact {
                            width: 32px;
                            height: 14px;
                            background: #fa635c;
                            border-radius: 7px 0px 7px 0px;
                            font-size: 12px;
                            line-height: 14px;
                            text-align: center;
                            display: block;
                            position: absolute;
                            font-family: Source Han Sans CN;
                            // font-weight: 400;
                            color: #ffffff;
                            top: 4px;
                            left: 68px;
                            font-weight: normal;
                            top: 10px;
                            left: 74px;
                        }
                    }
                    &:hover {
                        .arrow {
                            background: url('../assets/home/<USER>') no-repeat center / cover;
                        }
                        .more {
                            display: block;
                        }
                    }
                    .more {
                        display: none;
                        position: absolute;
                        padding: 16px 20px;
                        left: 3px;
                        top: 57px;
                        z-index: 2200;
                        background: #ffffff;
                        box-shadow: 0px 4px 10px 3px rgba(51, 51, 51, 0.09);
                        border-radius: 8px;
                        &::after {
                            content: '';
                            position: absolute;
                            top: -20px;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 10px;
                            height: 10px;
                            border-width: 10px;
                            border-style: solid;
                            border-color: transparent transparent $color-white transparent;
                        }
                        a {
                            text-align: left;
                            display: block;
                            margin-bottom: 26px;
                            padding: 0;
                            font-size: 14px;
                            line-height: 14px;
                            height: 14px;
                            font-weight: 400;
                            color: #000000;
                            background-color: inherit;
                            white-space: nowrap;

                            &:last-child {
                                margin-bottom: 4px;
                            }

                            &:hover {
                                color: $color-primary;
                            }
                        }
                    }
                }

                // 小程序二维码部分
                .join {
                    display: inline-block;
                    position: relative;
                }

                .join:hover {
                    .join-code {
                        display: flex;

                        &.is-join-code {
                            display: flex;
                        }
                    }
                }

                // 其他页面展示(在最前面)
                .join-code {
                    display: none;
                    position: absolute;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0 10px;
                    right: -65px;
                    top: 57px;
                    width: 205px;
                    height: 230px;
                    background: linear-gradient(190deg, #fff8ee, #ffffff); /* 修改渐变方向为 45 度 */
                    border: 1px solid #ebebeb;
                    box-shadow: 0px 0px 10px 0px rgba(51, 51, 51, 0.2);
                    border-radius: 8px;
                    z-index: 2200;

                    &::after {
                        content: '';
                        position: absolute;
                        top: -20px;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 0;
                        height: 0;
                        border-width: 10px;
                        border-style: solid;
                        border-color: transparent transparent $color-white transparent;
                    }
                    // 总体居中
                    .news-part {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        margin: 20px auto;
                        .scan-join {
                            width: 112px;
                            height: 37px;
                            font-size: 16px;
                            font-family: Source Han Sans CN;
                            font-weight: bold;
                            color: $color-primary;
                            line-height: 21px;
                            text-align: center;
                        }
                        .explain {
                            margin-top: 12px;
                            width: 100px;
                            height: 15px;
                            font-size: 14px;
                            font-family: Source Han Sans CN;
                            color: #333333;
                            line-height: 15px;
                            font-weight: 400;
                        }
                        img {
                            margin-top: 9px;
                            width: 120px;
                            height: 120px;
                            background: #ffffff;
                            border-radius: 10px;
                        }
                    }
                }
            }

            .header-main {
                display: flex;
                align-items: center;

                .header-search {
                    margin-right: 30px;

                    .el-input__inner {
                        box-shadow: none !important;
                    }

                    .search-input {
                        width: 242px;
                        line-height: 32px;

                        .el-input__inner {
                            padding: 0 40px 0 75px;
                            height: 32px;
                            color: $color-white;
                            font-size: 12px;
                            line-height: 32px;
                            background: transparent;
                            border: 1px solid $color-primary;
                            border-radius: 32px;
                        }

                        .el-input__prefix {
                            left: 0;
                        }

                        & > .el-input__suffix {
                            top: 4px;
                            right: 12px;
                            height: 16px;
                        }
                    }

                    .search-type {
                        width: 60px;

                        .el-input__inner {
                            padding: 0 20px 0 10px;
                            border-top-right-radius: 0;
                            border-bottom-right-radius: 0;
                            background: #404040;
                        }

                        .el-input__suffix {
                            right: 0;
                            line-height: 32px;
                        }

                        .el-input__icon {
                            width: 25px;
                            line-height: 32px;
                        }
                    }

                    .el-icon-search {
                        width: 16px;
                        height: 16px;
                        background: url(../assets/icon/search.png) no-repeat center center / contain;

                        &::before {
                            content: none;
                        }
                    }
                }

                .header-dropdown {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: rgba($color: $color-white, $alpha: 0.8);
                    cursor: default;
                    position: relative;

                    .el-avatar {
                        margin-right: 7px;

                        & > img {
                            width: 100%;
                        }
                    }
                    .vip-logo {
                        position: absolute;
                        bottom: 0px;
                        left: 11px;
                        width: 17px;
                        height: 13px;
                        background: url(https://img.gaoxiaojob.com/uploads/person/vip-logo.png) no-repeat center/contain;
                    }
                }
            }

            .message {
                margin-right: 30px;
                user-select: none;
            }
        }

        .header-notice-container {
            position: relative;
            cursor: default;

            &:hover {
                color: $color-primary;

                .notice-open-part {
                    display: block;
                }
            }

            // 展开部分 栏目页距离顶部 top $page-header-height
            .notice-open-part {
                display: none;
                position: fixed;
                width: 100vw;
                top: $page-header-height;
                left: 50%;
                transform: translateX(-50%);
                line-height: 1;
                border-top: 1px solid #ffedcf;
                box-shadow: 0px 4px 30px 0px rgba(102, 102, 102, 0.06);
                background: $color-white url('../assets/region/region-nav-bg.png') no-repeat bottom center;
                z-index: $basic-index;

                // 公告页面距离顶部 top $header-height
                &.is-open {
                    top: $header-height;
                }
            }

            .notice-content {
                margin: 0 auto;
                padding: 30px 0 10px;
                width: $view-width;

                .more {
                    padding-left: 17px !important;
                }
            }

            .nav-list {
                display: flex;
                margin-bottom: 9px;
            }

            .nav-title {
                width: 109px;
                font-weight: bold;
                color: $font-color;
            }

            .nav-container {
                position: relative;
                display: flex;
                flex-wrap: wrap;
                flex: 1;
                font-size: 12px;
                color: $font-color-basic;

                li {
                    margin-bottom: 20px;
                    width: calc(100% / 15);

                    &:nth-child(15n) {
                        padding-right: 20px;
                        text-align: right;
                    }

                    a:hover {
                        color: $color-primary;
                    }
                }
            }

            .is-nav-container {
                li {
                    margin-right: 5px;
                    width: calc(100% / 10);
                    @include utils-ellipsis;

                    &:nth-child(15n) {
                        text-align: left;
                    }
                }
            }

            .more {
                position: absolute;
                right: 20px;
                bottom: 20px;
                text-align: right;
                font-weight: bold;
                color: $color-primary;
                background: url('../assets/home/<USER>') no-repeat left center / 14px;
            }
        }

        .login-register-container {
            color: $color-white;
            font-size: 14px;

            .login,
            .register {
                display: inline-block;
                line-height: $header-height;

                &:hover {
                    color: $color-primary;
                }
            }

            .line {
                margin: 0 5px;
            }
        }
    }

    .el-icon-bell {
        width: 19px;
        height: 20px;
        background: url(../assets/icon/bell.png) no-repeat center center / contain;

        &::before {
            content: none;
        }
    }

    .el-badge__content {
        background-color: $font-color-salary;
        border: none;
    }

    .el-main {
        --el-main-padding: 0;
        padding-bottom: 20px;
        background-color: $background-primary;
        overflow-x: hidden;
        scroll-behavior: smooth;
    }

    .el-footer {
        padding: 38px 0;
        height: 233px;
        background-color: #343539;

        .footer-container {
            margin: 0 auto;
            width: $view-width;
            color: $color-white;
            font-weight: 400;
            text-align: center;
            line-height: 2;
        }
    }

    .el-form-item {
        margin-bottom: 30px;
    }

    .el-input-group__prepend,
    .el-input-group__append {
        background-color: inherit;
    }

    .el-form-item__label {
        color: $font-color-basic;
    }

    .el-form-item__content {
        line-height: initial;
    }

    .el-form-item__error--inline {
        margin-left: 0;
    }

    .el-select,
    .el-cascader,
    .el-range-editor {
        width: 100%;
    }

    .el-cascader-panel {
        .el-cascader-menu {
            .el-cascader-node {
                position: relative;

                .el-radio {
                    position: absolute;
                    left: 20px;
                    right: 30px;
                    opacity: 0;
                }
            }

            &:last-child {
                .el-cascader-node {
                    .el-radio {
                        right: 20px;
                    }
                }
            }
        }
    }

    .el-dialog {
        border-radius: 10px;

        .el-dialog__footer {
            .el-button--default {
                color: $font-color-tips;

                &:hover {
                    color: $color-primary;
                }
            }
        }
    }

    .el-dialog__headerbtn,
    .el-message-box__headerbtn {
        top: -18px;
        right: -42px;
        width: 30px;
        height: 30px;
        background: $font-color;
        border-radius: 50%;
        z-index: inherit;

        i.el-dialog__close,
        i.el-message-box__close {
            color: $color-white;
            font-size: 16px;
            font-weight: bold;
        }
    }

    .el-pagination {
        padding: 0;
        text-align: center;

        .el-input__icon {
            height: initial;
        }
    }

    .el-message {
        min-width: auto;
    }

    .el-message-box {
        padding: 0;
        width: 560px;
        border-radius: 10px;

        .el-message-box__header {
            margin-bottom: 18px;
            padding-top: 40px;
            font-size: 20px;
            text-align: center;
        }

        .el-message-box__content {
            margin-bottom: 30px;
            padding: 0 120px;
            color: $font-color-basic;
            text-align: center;
        }

        .el-message-box__message {
            p {
                font-size: inherit;
            }
        }

        .el-message-box__btns {
            margin-bottom: 60px;
            text-align: center;

            .el-button {
                width: 160px;

                &.el-button--default {
                    color: $font-color-tips;

                    &:hover {
                        color: $color-primary;
                    }
                }

                &.el-button--primary {
                    color: $color-white;

                    &:hover {
                        color: $color-white;
                    }
                }

                & + .el-button {
                    margin-left: 40px;
                }
            }
        }
    }

    .el-message__icon {
        font-size: 16px;
    }

    .breadcrumb {
        padding: 12px 0;
        color: rgba($color: $font-color, $alpha: 0.4);
        font-size: 14px;
        line-height: 1;
        box-shadow: 0 1px 0 0 rgba($color: $font-color, $alpha: 0.2);
    }

    .detail-subtitle {
        position: relative;
        display: flex;
        align-items: center;
        padding-left: 30px;
        font-size: 18px;
        font-weight: bold;
        background: url(../assets/icon/title.png) no-repeat left center / 20px 16px;

        $list: basic detail job file analyse heat;

        @each $var in $list {
            &.is-#{$var} {
                padding-left: 45px;
                line-height: 38px;
                background-image: url(../assets/icon/title-#{$var}.png);
                background-size: 38px;
            }
        }
        &.is-analyse::after {
            content: '';
            flex: 1;
            margin-left: 10px;
            margin-right: 90px;
            height: 1px;
            background-color: $border-color;
        }
        &.is-heat::after {
            content: '';
            flex: 1;
            margin-left: 10px;
            margin-right: 90px;
            height: 1px;
            background-color: $border-color;
        }

        &::after {
            content: '';
            flex: 1;
            margin-left: 10px;
            height: 1px;
            background-color: $border-color;
        }
    }
}

// 高级筛选按钮
.superior-filter-global {
    // 用于本地调试 start
    // margin-left: 200px;
    // margin-top: 20px;
    // 用于本地调试 end

    display: inline-flex;
    cursor: pointer;
    position: relative;
    font-size: 14px;
    padding: 0 15px 0 34px;
    line-height: 30px;
    border-radius: 4px;
    border: 1px solid $color-primary;
    background: url(../assets/icon/filter.png) no-repeat 15px center / 14px 14px;

    &.active {
        color: $color-primary;
        background: url(../assets/icon/filter.png) no-repeat 15px center / 14px 14px, #fff3e0;
    }

    &::after {
        content: '';
        position: absolute;
        top: -7px;
        right: -12px;
        width: 30px;
        height: 14px;
        background: url(../assets/icon/vip.png) no-repeat center/contain;
    }

    &:hover {
        .superior-tips {
            display: block;
        }
    }

    .superior-tips {
        display: none;
        transition: all ease-in-out 0.1s;
        position: absolute;
        top: calc(100% + 10px);
        left: 50%;
        border-radius: 4px;
        padding: 10px;
        z-index: 2000;
        font-size: 12px;
        line-height: 1.2;
        min-width: 10px;
        white-space: nowrap;
        word-wrap: break-word;
        visibility: visible;
        color: var(--el-color-white);
        background: var(--el-text-color-primary);
        transform: translate(-50%);

        &::after {
            content: '';
            width: 10px;
            height: 10px;
            position: absolute;
            transform: translate(-50%) rotate(45deg);
            background-color: inherit;
            top: -5px;
            left: 50%;
        }
    }
}

// 公告详情隐藏邮箱地址
.detail-hidden-email-address {
    display: inline-block;
    padding-left: 20px;
    background: url(../assets/icon/email.png) no-repeat left 4px / 18px;
    cursor: pointer;
}

// * 优先上面的样式
@import './common/header';
@import './common/nav';
@import './common/footer';
@import './common/animation';
@import './common/newResumeActivity';
