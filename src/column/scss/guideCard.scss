@use './variables' as *;

.guide-card-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
    padding: 78px 32px 40px;
    background: $color-white url(../assets/common/guide-card-bg.png) no-repeat center top / contain;
    border-radius: 10px;

    $items: edit upload;

    @each $var in $items {
        .#{$var} {
            span {
                display: inline-block;
                padding-left: 30px;
                line-height: 20px;
                background: url(../assets/common/guide-card-#{$var}.png) no-repeat center left / 20px;
            }
        }
    }

    a {
        height: 40px;
        color: $font-color;
        font-size: 14px;
        text-align: center;
        line-height: 40px;
        background-color: $color-white;

        @include utils-radius;

        & + a {
            margin-top: 12px;
        }
    }
}
