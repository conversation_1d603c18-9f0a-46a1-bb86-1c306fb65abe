// 栏目页博士后(B)
@import './variables';

@import './common/headlines-adv';
@import './common/commonTitle';
@import './common/fixedTool';
@import './common/sideBar';

@import './common/currentLocation';
@import './common/component/propaganda';
@import './common/component/rangeD';
@import './common/component/rangeL';
@import './common/component/rangeH';
@import './common/component/link';
@import './common/component/job';
@import './common/component/banner';
@import './common/component/dropnav';
@import './common/component/columnBMiddle';

$border-color-dashed: #ffd285;
$color-bg-title: #fff7ea;
$border-color-solid: #e5e5e5;

.site-tupian-forty {
    margin-bottom: 10px;
    background-color: $color-white;

    .sort-list {
        border-right: 1px dashed $border-color-dashed;

        li {
            padding-left: 20px;
            width: 195px;
            height: 44px;
            line-height: 44px;
            color: $font-color-basic;
            border-bottom: 1px dashed $border-color-dashed;
            cursor: pointer;
        }

        li:last-child {
            border-bottom: none;
        }

        .tab-mouseover {
            position: relative;
            background-color: $color-primary;
            color: $color-white;
        }

        .tab-mouseover::after {
            position: absolute;
            content: '';
            width: 0;
            height: 0;
            border-width: 8px;
            border-style: solid;
            border-color: transparent transparent transparent $color-primary;
            top: 15px;
            right: -16px;
        }
    }

    .tab-card-content {
        flex: 1;
        padding: 0 20px;
        overflow: hidden;
    }

    .pic {
        display: none;
        flex-direction: column;
        height: 100%;
        font-size: 13px;
    }

    .list-title {
        margin: 16px auto 0;
        padding: 4px 20px;
        text-align: center;
        background-color: $color-bg-title;
        color: $color-primary;
        border-radius: 12px;
    }

    .sort-switch {
        flex: 1;
        margin-top: 20px;
        width: 100%;

        .swiper-pagination-bullet {
            width: 6px;
            height: 6px;
        }

        .swiper-pagination-bullet-active {
            width: 34px;
            border-radius: 3px;
            background-color: $color-primary;
        }
    }

    .focus-box {
        display: flex;
        flex-wrap: wrap;
    }

    .list-link {
        margin-bottom: 10px;
        width: calc(100% / 5);
        height: 86px;
        text-align: center;
        border-left: 1px solid $border-color-solid;
        border-top: 1px solid $border-color-solid;
        border-bottom: 1px solid $border-color-solid;
        box-sizing: border-box;
        @include utils-ellipsis;
    }

    .list-link:nth-child(5n) {
        border-right: 1px solid $border-color-solid;
    }

    .list-link:last-child {
        border-right: 1px solid $border-color;
    }

    .school-pictures {
        margin: 5px auto 0;
        max-width: 80%;
        max-height: 40px;
    }

    .title {
        @include utils-ellipsis;
    }

    .subtitle {
        color: $font-color-basic;
        @include utils-ellipsis;
    }
}
