@import './variables';

.share-mini-code-container {
    position: relative;
    display: inline-block;

    .share-mini-code-trigger {
        padding-left: 25px;
        line-height: 20px;
        background: url(../assets/common/wechat-default.png) no-repeat left/20px 20px;
        color: $color-white;
        cursor: pointer;
        font-size: 14px;

        &.share-mini-code-trigger--primary {
            color: $font-color;
            background: url(../assets/common/wechat-primary.png) no-repeat left/20px 20px;
        }

        &.share-mini-code-trigger--info {
            color: #2a6bff;
            background: url(../assets/common/wechat-info.png) no-repeat left/20px 20px;
        }

        &.font-color {
            color: $font-color;
        }

        &.font-color-white {
            color: $color-white;
        }

        &:hover {
            & + .share-mini-code-popup {
                display: flex;
            }
        }
    }

    .share-mini-code-popup {
        top: calc(100% + 13px);
        position: absolute;
        background: linear-gradient(180deg, #fff8ee, #ffffff);
        box-shadow: 0px 0px 10px 0px rgba(51, 51, 51, 0.2);
        border-radius: 8px;
        z-index: $header-index;
        flex-direction: column;
        display: none;
        align-items: center;
        justify-content: center;
        padding: 24px 24px 21px;
        left: 50%;
        transform: translateX(-50%);

        &::before {
            content: '';
            position: absolute;
            top: -12px;
            left: calc(50% - 6px);
            border-width: 6px;
            border-style: solid;
            border-color: transparent transparent #fff8ee transparent;
            transform: scaleY(1.5);
        }

        .share-mini-code {
            width: 140px;
            height: 140px;
            margin-bottom: 8px;

            .share-mini-code-img {
                width: 100%;
                height: 100%;
            }
        }

        .share-mini-code-title {
            line-height: 1.01;
            font-weight: bold;
            margin-bottom: 9px;
            color: $font-color;
            font-size: 14px;
        }

        .share-mini-code-tips {
            line-height: 1.01;
            font-size: 12px;
            color: $font-color-basic;
        }
    }
}
