@import './variables';

body {
    color: $color-white;

    font: {
        size: 14px;
    }
}

.container {
    background: url('../assets/application/picture.png') no-repeat center / cover;

    .header {
        display: flex;
        justify-content: space-between;
        padding: 40px 60px 24px;

        a {
            display: block;
            width: 154px;
            height: 42px;

            img {
                display: block;
                max-width: 100%;
            }
        }
    }

    .main {
        display: flex;
        padding-bottom: 106px;
        margin: 0 auto;
        width: 1160px;

        .tips {
            border-radius: 10px 0 0 10px;
            background-color: rgba(92, 92, 92, 0.9);

            .tips-application {
                .tips-title {
                    margin: 80px 0 41px 34px;
                    height: 38px;
                    font-size: 20px;
                    font-weight: bold;
                    background: url('../assets/application/background.png') no-repeat left center / 209px;

                    span {
                        padding-left: 21px;
                        line-height: 38px;
                    }
                }

                .tips-news {
                    width: 378px;
                    margin: 0 52px 0 40px;

                    p {
                        padding-bottom: 30px;
                        line-height: 2em;

                        &:last-child {
                            padding-bottom: 80px;
                            border-bottom: 1px dashed rgba(255, 255, 255, 0.6);
                        }
                    }
                }
            }

            .tips-contact {
                padding-left: 38px;

                .tips-more {
                    padding: 39px 0 39px 0;
                }

                p {
                    padding-bottom: 20px;

                    img {
                        display: inline-block;
                        margin-right: 12px;
                        vertical-align: middle;
                        width: 24px;
                        height: 24px;
                    }

                    &:last-child {
                        padding-bottom: 40px;
                    }
                }
            }
        }
    }

    #component {
        background: $color-white;
        border-radius: 0px 10px 10px 0px;

        .form-container {
            label {
                color: $font-color;
            }

            .el-form {
                padding: 73px 75px 5px 77px;
                width: 690px;
            }

            .el-input__inner::placeholder,
            .el-textarea__inner::placeholder {
                font-size: 12px;
            }

            .el-textarea__inner {
                height: 94px;
                resize: none;
            }

            .el-button--primary {
                width: 280px;
                height: 44px;
                font-size: 16px;
            }

            .el-button + .el-button {
                width: 160px;
                height: 44px;
                color: $font-color-tips;
                font-size: 16px;
            }
        }

        .explain {
            .el-form-item__content {
                display: flex;
                justify-content: space-between;
                color: $color-primary;
            }
        }
    }
}
