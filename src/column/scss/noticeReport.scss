@use './variables' as *;
@use './common/popper';

.notice-report {
    margin: 20px auto 0;
    padding: 104px 50px 45px;
    width: $view-width;
    background: $color-white url(../assets/report/notice-header.png) no-repeat top left / 100%;
    border-radius: 16px;

    .report-header {
        padding: 56px 0 0;
        background: url(../assets/report/notice-title.png) no-repeat top left / 405px 36px;

        .datetime {
            margin-bottom: 50px;
            color: #373ab1;
            font-size: 14px;
        }

        .intro {
            display: flex;
            align-items: flex-end;
            justify-content: space-between;
            padding: 30px 20px 20px;
            background: $color-white;
            box-shadow: 0px 3px 7px 0px rgba(51, 51, 51, 0.1);
            border-radius: 10px;
        }

        .intro-section {
            flex: 1 0 auto;
            margin-right: 30px;
            width: 680px;

            .tips {
                margin-bottom: 15px;
                color: $font-color-label;
                font-size: 14px;
            }

            .title {
                display: flex;
                align-items: center;
                margin-bottom: 20px;

                span {
                    @include utils-ellipsis;

                    font-size: 18px;
                    font-weight: bold;
                }

                &.is-new {
                    &::after {
                        content: '新发布';
                        flex: none;
                        margin-left: 10px;
                        width: 48px;
                        height: 16px;
                        color: $color-white;
                        font-size: 12px;
                        text-align: center;
                        line-height: 16px;
                        background-color: $font-color-salary;
                        border-radius: 8px;
                    }
                }
            }

            .desc {
                margin-bottom: 30px;
                font-size: 14px;
                font-weight: 400;

                span {
                    color: $color-primary;
                    font-weight: bold;
                }
            }

            .link {
                margin-left: 15px;
                padding-right: 15px;
                color: $color-primary;
                font-weight: bold;
                background: url(../assets/report/notice-forward.png) no-repeat right center / 9px;
            }

            .list {
                display: flex;
                justify-content: space-between;
                margin-top: 20px;

                li {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                    padding-left: 58px;
                    height: 48px;
                    background-repeat: no-repeat;
                    background-position: left center;
                    background-size: 48px;

                    @for $i from 1 through 3 {
                        &:nth-of-type(#{$i}) {
                            background-image: url(../assets/report/notice-#{$i}.png);
                        }
                    }

                    span {
                        &:first-of-type {
                            color: $color-primary;
                            font-size: 20px;
                            font-weight: bold;
                        }

                        &:last-of-type {
                            color: $font-color;
                            font-size: 13px;
                        }
                    }
                }
            }
        }

        .intro-aside {
            width: 356px;
            height: 203px;
        }

        &__chart {
            margin-top: 30px;

            .chart-item {
                height: auto;

                .title {
                    font-size: 16px;
                    font-weight: bold;

                    span {
                        color: $color-primary;
                    }

                    &.has-help {
                        span {
                            display: inline-block;
                            padding-right: 20px;
                            background: url(../assets/report/job-help.png) no-repeat right center / 14px;
                        }
                    }
                }

                &__trending {
                    height: auto;

                    .chart-title {
                        &__trending {
                            flex-direction: row;
                            justify-content: space-between;
                            align-items: center;
                            padding-left: 0;
                            margin-bottom: 18px;
                            height: auto;

                            span {
                                width: auto;
                            }

                            .tooltip-trigger {
                                flex: none;
                                padding-right: 20px;
                                background: url(../assets/report/job-help.png) no-repeat right 1px / 14px;
                            }
                        }

                        &__tab {
                            display: flex;
                            color: $color-white;
                            font-size: 14px;
                            line-height: 24px;
                            background-color: #c5c5c5;
                            border-radius: 8px;
                            overflow: hidden;

                            &-item {
                                width: 60px;
                                text-align: center;
                                user-select: none;
                                cursor: pointer;

                                &.is-active {
                                    background-color: $color-primary;
                                }
                            }
                        }
                    }

                    &-content {
                        padding-bottom: 20px;
                        background-color: $color-white;
                        border-radius: 16px;
                    }

                    &-stat {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 12px 20px 0;
                        padding: 0 20px;
                        height: 30px;
                        background: #f9fafc;
                        border-radius: 15px;
                        font-size: 16px;
                        font-weight: bold;

                        span {
                            position: relative;
                            padding-left: 11px;
                            color: $color-primary;

                            &::before {
                                content: '';
                                position: absolute;
                                top: calc(50% - (5px / 2));
                                left: 0;
                                width: 5px;
                                height: 5px;
                                background-color: $color-primary;
                                border-radius: 50%;
                            }
                        }
                    }
                }
            }

            .stat-cell {
                padding: 20px;
                background-color: $color-white;
                border-radius: 16px;

                .item {
                    padding-bottom: 15px;

                    &:nth-of-type(2) {
                        padding-bottom: 0;
                    }
                }

                .title {
                    position: relative;
                    margin-bottom: 15px;
                    padding-left: 11px;
                    color: $color-primary;
                    font-size: 16px;

                    &.has-help {
                        span {
                            display: inline-block;
                            padding-right: 20px;
                            background: url(../assets/report/job-help.png) no-repeat right center / 14px;
                        }
                    }

                    &::before {
                        content: '';
                        position: absolute;
                        top: 50%;
                        left: 0;
                        width: 5px;
                        height: 5px;
                        background-color: $color-primary;
                        border-radius: 50%;
                        transform: translateY(-50%);
                    }
                }

                .desc {
                    color: $font-color-basic;
                    font-size: 14px;

                    .num {
                        color: $font-color;
                        font-size: 16px;
                        font-weight: bold;
                    }

                    strong {
                        color: $font-color;
                    }
                }
            }
        }
    }

    .subtitle-cell {
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-left: 16px;
        padding-right: 20px;
        height: 36px;
        background: #fff7ea;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 6px;
            background-color: $color-primary;
        }

        .title {
            padding-right: 20px;
            color: $color-primary;
            font-size: 18px;
            font-weight: bold;
        }
    }

    .chart-cell {
        display: flex;
        flex-wrap: wrap;

        .chart-item {
            &:nth-of-type(2n) {
                margin-left: 30px;
            }
        }
    }

    .chart-item {
        margin-bottom: 30px;
        padding: 30px 20px;
        width: 535px;
        background: #f9fafc;
        border-radius: 16px;

        .chart-title {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;
            padding-left: 52px;
            height: 40px;
            background-repeat: no-repeat;
            background-position: left center;
            background-size: 40px;

            $list: addr education major overseas title work;

            @each $item in $list {
                &.is-#{$item} {
                    background-image: url(../assets/report/chart-title-#{$item}.png);
                }
            }

            .no-data {
                &::after {
                    content: '暂无应聘数据';
                    padding-left: 18px;
                    color: $font-color-basic;
                    font-size: 14px;
                    font-weight: normal;
                    background: url(../assets/report/title-tips.png) no-repeat left center / 13px;
                }
            }

            span {
                width: 100%;

                &:nth-of-type(1) {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    color: $font-color;
                    font-size: 16px;
                    font-weight: bold;
                }

                &:nth-of-type(2) {
                    @include utils-ellipsis;

                    color: $font-color-basic;
                    font-size: 14px;
                }
            }
        }

        .chart-slot {
            width: 500px;
            height: 250px;
        }
    }

    .chart-status {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        margin-bottom: 40px;
        height: 140px;
        color: $font-color-label;

        .item {
            display: flex;
            flex-direction: column;
            align-items: center;

            & + .item {
                margin-left: 5px;
            }

            &::before {
                content: '';
                margin-bottom: 8px;
                width: 112px;
                height: 39px;
                background-repeat: no-repeat;
                background-position: center;
                background-size: contain;
            }

            &:nth-of-type(1) {
                &::before {
                    background-image: url(../assets/report/status-1.png);
                }
            }

            &:nth-of-type(2) {
                &::before {
                    background-image: url(../assets/report/status-2.png);
                }
            }

            &:nth-of-type(3) {
                &::before {
                    background-image: url(../assets/report/status-2.png);
                }
            }

            &:nth-of-type(4) {
                &::before {
                    background-image: url(../assets/report/status-4.png);
                }
            }

            &.is-active {
                &::before {
                    height: 91px;
                    background-image: url(../assets/report/status-active.png);
                }
            }
        }
    }

    .chart-item {
        margin-bottom: 30px;
        padding: 30px 20px;
        width: 535px;
        background: #f9fafc;
        border-radius: 16px;

        .chart-title {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding-left: 52px;
            height: 40px;
            background-repeat: no-repeat;
            background-position: left center;
            background-size: 40px;

            $list: addr education major overseas title work age political;

            @each $item in $list {
                &.is-#{$item} {
                    background-image: url(../assets/report/chart-title-#{$item}.png);
                }
            }

            span {
                &:nth-of-type(1) {
                    color: $font-color;
                    font-size: 16px;
                    font-weight: bold;
                }

                &:nth-of-type(2) {
                    color: $font-color-basic;
                    font-size: 14px;
                }
            }
        }

        .chart-slot {
            width: 500px;
            height: 250px;
        }
    }

    .report-tips {
        position: relative;
        margin-top: 10px;
        font-size: 12px;
        border-bottom: 1px dashed #ebebeb;

        span {
            position: absolute;
            left: 50%;
            bottom: 0;
            padding: 0 5px;
            background-color: $color-white;
            transform: translate(-50%, 50%);
        }
    }
}
