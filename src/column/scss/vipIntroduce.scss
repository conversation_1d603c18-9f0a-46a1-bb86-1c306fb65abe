@use 'sass:meta';
@import './variables';

$red: #d84d4d;
$price: #ee3c3c;
$view-width: 1200px;

#component {
    font-size: 14px;
}

@mixin privilege-escalation {
    .privilege-escalation-tag {
        color: $color-primary;
        font-size: 12px;
        font-weight: normal;
        padding: 0 5px;
        line-height: 14px;
        border-radius: 8px 8px 8px 0px;
        border: 1px solid #ffa000;
        margin-left: 5px;
        align-self: center;
    }
}

.banner {
    height: 400px;
    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/banner.jpg?v=c3.0') no-repeat center/auto 100%;
}

.main-wrapper {
    width: $view-width;
    margin: 0 auto;
}

.wrapper-title {
    font-size: 32px;
    color: #4c3214;
    margin-bottom: 10px;
    font-weight: bold;
    display: flex;
    align-items: flex-end;
    letter-spacing: 2px;
    justify-content: center;

    &::after {
        content: '';
        margin-left: 15px;
        width: 29px;
        height: 33px;
        background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/title-right.png) no-repeat center / contain;
    }
    &::before {
        content: '';
        margin-right: 15px;
        width: 29px;
        height: 33px;
        background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/title-left.png) no-repeat center / contain;
    }
}

.tabs-wrapper {
    padding-bottom: 54px;
    padding-top: 26px;

    .main-wrapper {
        width: 1040px;
    }

    .tabs {
        display: flex;
        align-items: flex-end;
        margin: 0 175px 65px;
        width: 650px;
        height: 78px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-bg.png') no-repeat left bottom/100% 68px;

        .item {
            height: 100%;
            display: flex;
            align-items: flex-end;
            padding-bottom: 13px;
            box-sizing: border-box;
            font-size: 15px;
            color: #d4d4d4;
            width: 300px;
            cursor: pointer;
            position: relative;
            font-weight: bold;

            .amount {
                color: $red;
            }

            &:first-child {
                padding-left: 107px;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-diamond-title.png') no-repeat left 110px bottom 38px/124px 26px;

                .tag {
                    font-size: 12px;
                    position: absolute;
                    left: 239px;
                    top: 5px;
                    color: #fff;
                    display: inline-block;
                    padding: 0 12px;
                    line-height: 22px;
                    background: linear-gradient(90deg, #ff6114 0%, #fa6573 100%);
                    border-radius: 17px 13px 13px 2px;
                    white-space: nowrap;
                    z-index: 1;
                }

                &.active {
                    color: #321279;
                    width: 350px;
                    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-diamond-title-active.png') no-repeat left 110px bottom 38px/124px 26px,
                        url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-diamond.png') no-repeat left -1px bottom -1px/100% 100%;
                }
            }

            &:last-child {
                padding-right: 94px;
                justify-content: flex-end;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-gold-title.png') no-repeat right 94px bottom 38px/124px 26px;

                &.active {
                    width: 350px;
                    color: #845009;
                    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-gold-title-active.png') no-repeat right 94px bottom 40px/124px 26px,
                        url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/tabs-gold.png') no-repeat right -1px bottom -1px/100% 100%;
                }
            }
        }
    }

    .tab-panes {
        margin-bottom: 27px;

        .pane {
            display: flex;
        }
        .list {
            width: calc((100% - 80px) / 3);
            margin-right: 40px;
            padding: 50px 45px 10px;
            text-align: center;
            border: 1px solid #d8d8d8;
            border-radius: 15px;
            position: relative;
            transition: all ease 0.1s;
            cursor: pointer;

            &:last-child {
                margin-right: 0;
            }

            &.active {
                transform: translateY(-26px);
                padding: 42px 43px 8px;
            }
        }

        .tag {
            position: absolute;
            color: #fff;
            left: -9px;
            top: 10px;
            height: 26px;
            background: linear-gradient(90deg, #ff6114 0%, #fa6573 100%);
            line-height: 26px;
            font-size: 15px;
            font-weight: bold;
            padding: 0 19px 0 20px;
            border-radius: 0px 13px 13px 0px;

            &::after {
                position: absolute;
                content: '';
                display: block;
                width: 0;
                height: 0;
                border-top: 4px solid #e6444f;
                border-bottom: 3px solid transparent;
                border-left: 4px solid transparent;
                border-right: 4px solid #e6444f;
                left: 0;
            }
        }

        .title {
            font-size: 30px;
            font-weight: bold;
            margin-bottom: 14px;
        }

        .desc {
            font-size: 16px;
            color: #757575;
            margin-bottom: 20px;
        }

        .price {
            display: flex;
            justify-content: center;
            margin-bottom: 12px;
            align-items: baseline;
        }

        .real-price {
            display: flex;
            align-items: baseline;
            font-size: 46px;
            font-weight: bold;
            color: $price;
            margin-right: 9px;

            &::before {
                content: '￥';
                font-size: 20px;
            }
        }

        .original-price {
            font-size: 20px;
            text-decoration: line-through;
            color: #818181;
        }

        .avg-price {
            display: inline-block;
            padding: 0 14px;
            line-height: 26px;
            border-radius: 5px;
            margin: 0 auto 19px;
            font-size: 14px;
        }

        .deduction-content {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 25px;

            .deduction {
                display: flex;
                align-items: center;
                padding: 4px 7px 4px 6px;
                background: #ffffff;
                border: 1px solid #ffe5e5;
                border-radius: 5px;
                margin-right: 5px;
                color: $price;

                .money {
                    font-size: 16px;
                    margin-right: 6px;
                }
            }

            .label {
                margin-right: 4px;
                background: #ffe5e5;
                border-radius: 5px;
                padding: 0 10px;
                font-size: 14px;
                line-height: 20px;
            }

            .what {
                width: 13px;
                height: 13px;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/what.png') no-repeat center/contain;
            }
        }

        .vip-status {
            min-width: 226px;
            padding: 0 10px;
            font-size: 18px;
            font-weight: bold;
            height: 48px;
            border-radius: 48px;
            margin: 0 auto 29px;
            border: none;
            cursor: pointer;
        }

        .detail-item {
            height: 20px;
            align-items: center;
            display: flex;
            color: #484848;
            font-size: 15px;
            justify-content: space-between;
            margin-bottom: 12px;

            .value {
                flex-grow: 1;
                display: flex;
                justify-content: flex-end;

                .primary {
                    color: $color-primary;
                }

                .icon {
                    width: 16px;
                    height: 16px;
                    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/right.png') no-repeat center/contain;
                    margin-left: 6px;
                }
            }
        }

        .diamond {
            .list {
                &.active {
                    border: 3px solid #7f68d4;
                    padding: 42px 43px 8px;
                    background: linear-gradient(0deg, #f3ecfd 0%, #ffffff 48%);
                }
            }

            .title {
                color: #674399;
            }

            .avg-price {
                color: #ee3c3c;
                background: #ffe5e5;
            }

            .deduction-content {
                display: none;
            }

            .vip-status {
                color: #48425a;
                background: linear-gradient(90deg, #fde4be 0%, #ffd58e 62%, #efba67 98%);

                .update {
                    display: none;
                }
            }
        }

        .gold {
            .list {
                &.active {
                    border: 3px solid #fcd38c;
                    padding: 42px 43px 8px;
                    background: linear-gradient(0deg, #fff2db 0%, #ffffff 46%);
                }
            }

            .avg-price {
                color: #845009;
                background: #ffdfad;
            }

            .vip-status {
                color: #ffe9bf;
                background: linear-gradient(90deg, #49494b 0%, #42415c 100%);

                .forbidden {
                    display: none;
                }
            }
        }

        &.forbidden-status {
            .gold {
                .vip-status {
                    background: #ebebeb;
                    color: #a2a2a2;
                    cursor: no-drop;

                    .open {
                        display: none;
                    }

                    .forbidden {
                        display: block;
                        color: #5c5c5c;
                    }

                    .reason {
                        font-size: 13px;
                        margin-top: 2px;
                        font-weight: normal;
                    }
                }
            }
        }

        &.update-status {
            .diamond {
                .avg-price {
                    display: none;
                }

                .deduction-content {
                    display: flex;
                }

                .vip-status {
                    color: #49425a;

                    .open {
                        display: none;
                    }

                    .update {
                        display: block;
                    }

                    .update {
                        display: flex;
                        align-items: baseline;
                        justify-content: center;
                        font-size: 16px;
                        font-weight: normal;
                    }

                    .money {
                        display: flex;
                        align-items: baseline;
                        font-weight: bold;
                        font-size: 24px;

                        &::before {
                            content: '￥';
                            font-size: 16px;
                            font-weight: normal;
                        }
                    }
                }
            }
        }
    }

    .agreement {
        text-align: center;

        a {
            color: $color-primary;
        }
    }
}

.contrast-wrapper {
    $radius: 10px;
    background: #f6f6f6;
    padding-top: 44px;

    .main-wrapper {
        width: $view-width;
        position: relative;
    }

    .subheading {
        color: #757575;
        font-size: 14px;
        margin-bottom: 28px;
        text-align: center;
    }

    .detail {
        border-radius: $radius;
        border: 1px solid #f1c290;
        position: relative;

        .hot {
            position: absolute;
            right: 35px;
            top: 4px;
            width: 46px;
            height: 54px;
            color: #fff;
            padding-top: 28px;
            padding-left: 6px;
            background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/hot.png') no-repeat center/contain;
        }

        table {
            border-radius: $radius;
            width: 100%;
            display: block;
            border-spacing: 0;
            overflow: hidden;
            height: 640px;
        }

        th {
            height: 75px;
            width: 260px;
            overflow: hidden;

            &:first-child {
                width: 418px;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/instructions.png') no-repeat left 30px center/84px 21px, linear-gradient(90deg, #ffffff 0%, #f4f4f6 100%);
            }

            &:nth-child(2) {
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/normal.png') no-repeat center/84px 21px, linear-gradient(90deg, #f6f7fc 0%, #e5e5e5 99%);
            }

            &:nth-child(3) {
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/gold-vip.png') no-repeat center/84px 21px, linear-gradient(90deg, #fdf4dd 0%, #fbe8b6 100%);
            }

            &:nth-child(4) {
                border-top-right-radius: $radius;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/diamond-vip.png') no-repeat center/84px 21px, linear-gradient(90deg, #f1effd 0%, #d9c4ff 100%);
            }
        }

        tr {
            height: 65px;

            &:nth-child(2n-1) {
                background-color: #fff;
            }

            &:nth-child(2n) {
                background-color: #f7f4f1;
            }
        }

        th,
        td {
            text-align: center;
            font-size: 16px;
            color: #666666;

            &:first-child {
                text-align: left;
                padding-left: 30px;
            }
        }

        td {
            border-right: 1px solid #d8d8d8;
            padding: 0;

            &:last-child {
                border-right: none;
            }

            .error {
                display: inline-block;
                width: 14px;
                height: 14px;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/error.png') no-repeat center/contain;
            }

            .contain {
                display: inline-block;
                width: 22px;
                height: 19px;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/correct.png') no-repeat center/contain;
            }
        }

        .name {
            color: #484848;
            font-weight: bold;
            margin-bottom: 9px;
            display: flex;

            @include privilege-escalation;
        }

        .instructions {
            font-size: 13px;
            color: #999999;
        }

        &.diamond {
            th {
                &:last-child {
                    border: 2px solid #7f68d4;
                    border-bottom: none;
                    border-radius: $radius $radius 0 0;
                    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/diamond-vip.png') no-repeat center/84px 21px, linear-gradient(90deg, #f1effd 0%, #d9c4ff 100%);
                }
            }

            tr {
                &:nth-child(2n-1) {
                    td {
                        &:last-child {
                            color: #563c7d;
                            background-color: #f9f9ff;
                            border-left: 2px solid #7f68d4;
                            border-right: 2px solid #7f68d4;
                        }
                    }
                }

                &:nth-child(2n) {
                    td {
                        &:last-child {
                            color: #563c7d;
                            background-color: #f0edfd;
                            border-left: 2px solid #7f68d4;
                            border-right: 2px solid #7f68d4;
                        }
                    }
                }

                &:last-child {
                    td {
                        &:last-child {
                            color: #563c7d;
                            border: 2px solid #7f68d4;
                            border-top: none;
                            border-radius: 0 0 $radius $radius;
                        }
                    }
                }
            }
        }

        &.gold {
            th {
                &:nth-child(3) {
                    border: 2px solid #ffc043;
                    border-bottom: none;
                    border-radius: $radius $radius 0 0;
                    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/gold-vip.png') no-repeat center/84px 21px, linear-gradient(90deg, #fdf4dd 0%, #fbe8b6 100%);
                }
            }

            tr {
                &:nth-child(2n-1) {
                    td {
                        &:nth-child(3) {
                            color: #a35f1c;
                            background-color: #fffcf4;
                            border-left: 2px solid #ffc043;
                            border-right: 2px solid #ffc043;
                        }
                    }
                }

                &:nth-child(2n) {
                    td {
                        &:nth-child(3) {
                            color: #a35f1c;
                            background-color: #fdf2d8;
                            border-left: 2px solid #ffc043;
                            border-right: 2px solid #ffc043;
                        }
                    }
                }

                &:last-child {
                    td {
                        &:nth-child(3) {
                            color: #a35f1c;
                            border: 2px solid #ffc043;
                            border-top: none;
                            border-radius: 0 0 $radius $radius;
                        }
                    }
                }
            }
        }
    }

    .show-more {
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 110px 0 40px;
        background: linear-gradient(180deg, RGBA(246, 246, 246, 0) 0%, RGBA(246, 246, 246, 1) 110px, #f6f6f6);

        .more {
            display: flex;
            align-items: center;
            border: 0;
            font-size: 16px;
            color: #757575;
            background: none;
            cursor: pointer;

            &::after {
                content: '';
                margin-left: 6px;
                width: 15px;
                height: 9px;
                transform: rotate(180deg);
                opacity: 0.75;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/up-down.png') no-repeat right/contain;
            }
        }
    }

    .show {
        .detail {
            table {
                height: auto;
            }
        }

        .show-more {
            position: static;
            padding: 25px 0 30px;

            .more {
                &::after {
                    transform: rotate(0deg);
                }
            }
        }
    }
}

.introduce-wrapper {
    padding: 43px 0 30px;

    $picture: (
        (
            'parent': 1,
            'children': 1,
            'bg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/resume-top.png',
            'titleBg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/resume-top-icon.png'
        ),
        (
            'parent': 1,
            'children': 2,
            'bg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/deliver-top.png',
            'titleBg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/deliver-top-icon.png'
        ),
        (
            'parent': 2,
            'children': 1,
            'bg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/refreseh.png',
            'titleBg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/refreseh-icon.png'
        ),
        (
            'parent': 2,
            'children': 2,
            'bg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/query-v3.png',
            'titleBg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/query-icon.png'
        ),
        (
            'parent': 3,
            'children': 1,
            'bg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/analyze.png',
            'titleBg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/analyze-icon.png'
        ),
        (
            'parent': 3,
            'children': 2,
            'bg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/heat.png',
            'titleBg': '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/heat-icon.png'
        )
    );

    @each $item in $picture {
        &:nth-child(#{map-get($item, 'parent')}) {
            .list {
                &:nth-child(#{map-get($item, 'children')}) {
                    $bg: #{map-get($item, 'bg')};
                    background-image: url($bg);

                    .title {
                        $titleBg: #{map-get($item, 'titleBg')};
                        background: url($titleBg) no-repeat left/48px 48px;
                    }
                }
            }
        }
    }

    .wrapper-title {
        margin-top: 10px;
        margin-bottom: 60px;
    }

    .list {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 417px;
        padding-left: 754px;
        background-position: left;
        background-size: 623px 417px;
        background-repeat: no-repeat;

        & + .list {
            margin-top: 52px;
        }

        .title {
            display: flex;
            align-items: center;
            height: 48px;
            font-size: 30px;
            font-weight: bold;
            padding-left: 58px;
            margin-bottom: 10px;
            color: #976a35;

            &.diamond-vip {
                &::after {
                    content: '钻石专享';
                    padding: 0 15px;
                    line-height: 26px;
                    color: #4e2b13;
                    font-size: 14px;
                    margin-left: 15px;
                    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/diamond-tag-bg.png') no-repeat left/cover;
                }
            }

            @include privilege-escalation;
            .privilege-escalation-tag {
                padding: 0 15px;
                line-height: 24px;
                font-size: 14px;
                margin-left: 8px;
                border-radius: 17px 13px 13px 2px;
                border: 1px solid #ffc86b;
            }
        }

        .desc {
            width: 390px;
            font-size: 18px;
            line-height: 36px;
        }
    }

    &.reverse {
        background-color: #f6f6f6;

        .list {
            padding-left: 42px;
            background-position: right;
        }
    }
}

.more-contrast-wrapper {
    background-color: #f6f6f6;
    padding: 54px 0;

    .main-wrapper {
        display: flex;
        align-items: center;
    }

    .title {
        display: flex;
        position: relative;
        height: 40px;
        width: 150px;
        flex-shrink: 0;
        margin-right: 25px;

        &::before {
            content: '';
            position: absolute;
            width: 150px;
            height: 16px;
            bottom: 0;
            left: -8px;
            background: linear-gradient(90deg, #ffdbc3 0%, #fee6c6 40%, #f4ce9b 100%);
        }

        &::after {
            content: '更多权益';
            white-space: nowrap;
            position: absolute;
            color: #2b2b2b;
            font-size: 30px;
            font-weight: bold;
        }
    }

    .content {
        display: flex;
        flex-grow: 1;
        align-items: center;
        overflow: hidden;
    }

    .scroll-content {
        flex-grow: 1;
        flex-wrap: nowrap;
        width: 100%;
        overflow: hidden;
    }

    .contrast {
        display: flex;
        transition: all ease 0.5s;
    }

    .next {
        cursor: pointer;
        width: 23px;
        height: 22px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/next.png') no-repeat center/contain;
        margin-left: 16px;

        &.disabled {
            cursor: no-drop;
            background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/next-disabled.png') no-repeat center/contain;
        }
    }

    .pre {
        cursor: pointer;
        width: 23px;
        height: 22px;
        margin-right: 16px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/previous.png') no-repeat center/contain;

        &.disabled {
            cursor: no-drop;
            background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/previous-disabled.png') no-repeat center/contain;
        }
    }

    .item {
        flex-shrink: 0;
        width: 210px;
        height: 210px;
        box-shadow: 0px 4px 15px 1px rgba(198, 198, 198, 0.2);
        border-radius: 5px;
        margin: 0 14px;
        padding-top: 93px;
        text-align: center;

        $icon: (1, template), (2, footmark), (3, collect), (4, job), (5, exclusive), (6, course);

        @each $index, $name in $icon {
            &:nth-child(#{$index}) {
                background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/#{$name}.png) no-repeat center 32px/50px 50px, #ffffff;
            }
        }

        .name {
            font-size: 18px;
            color: #484848;
            margin-bottom: 20px;
        }

        .sub {
            line-height: 22px;
            padding: 0 20px;
            font-size: 14px;
            white-space: wrap;

            span {
                font-size: 14px;
            }
        }
    }
}

.user-wrapper {
    background: #ffffff;
    padding-top: 38px;
    padding-bottom: 110px;
    color: #252532;

    .user-description {
        display: flex;
        justify-content: space-between;
        margin-bottom: 33px;

        .right-content {
            display: flex;
            padding: 36px 0 0 0;
            flex-wrap: wrap;
            width: 1200px;

            .right-catalogue {
                width: calc((1200px - 60px) / 3);
                height: 195px;
                padding: 25px 0 0 20px;
                display: flex;
                flex-direction: column;
                background: #fff;
                margin-right: 30px;
                box-shadow: 0px 0px 20px 0px rgba(32, 33, 35, 0.1);
                border-radius: 10px;
                margin-bottom: 30px;

                .head {
                    display: flex;
                    margin-bottom: 25px;

                    .avatar {
                        position: relative;

                        .male {
                            position: absolute;
                            right: 0;
                            top: -10px;
                            background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male.png) no-repeat;
                            width: 26px;
                            height: 26px;
                            background-size: 100% 100%;
                        }

                        .female {
                            position: absolute;
                            right: 0;
                            top: -10px;
                            background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/female.png) no-repeat;
                            width: 26px;
                            height: 26px;
                            background-size: 100% 100%;
                        }
                    }

                    .user-content {
                        color: #4c3214;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        margin-left: 8px;

                        .name {
                            font-size: 16px;
                            margin-left: 10px;
                        }

                        .category {
                            font-size: 13px;
                            display: flex;

                            .icon {
                                padding: 6px 16px;
                                background: #fbf4e4;
                                border-radius: 13px;
                                margin-right: 10px;
                            }
                        }
                    }
                }
                img {
                    width: 57px;
                    height: 57px;
                }
                .text {
                    color: #252532;
                    font-size: 14px;
                    display: flex;
                    flex-direction: column;
                    line-height: 1.8;
                    padding-right: 15px;
                    span {
                        color: #666;
                        font-weight: normal;
                        font-size: 14px;
                        padding-top: 10px;
                    }
                }
                &.active {
                    border: 1px solid #ff8d24;
                    box-shadow: 0px 0px 20px 0px rgba(32, 33, 35, 0.08);
                    border-radius: 10px;
                    .text {
                        color: #ff6c02;
                    }
                }
                &:nth-child(3n) {
                    margin-right: 0;
                }
            }
        }
    }

    .search-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin: 0 auto;
        width: $view-width;
        .search-title {
            font-size: 16px;
            font-weight: bold;
        }
        .text-content {
            display: flex;
            justify-content: space-between;
            line-height: 2;
            margin-top: 15px;
            color: #666666;
            span {
                color: #e6bd7a;
                cursor: pointer;
            }
        }
    }
}

.fixed-wrapper {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #272833;
    color: #fff;

    .main-wrapper {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 18px;
    }

    .place {
        padding-left: 130px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/vip-text.png') no-repeat left/105px 26px;

        a {
            margin: 0 10px;
        }
    }

    .buy-info {
        display: flex;
        align-items: center;
    }

    .price {
        display: inline-flex;
        align-items: baseline;
        color: #fde2be;
        font-size: 30px;
        font-weight: bold;

        .tag {
            font-size: 20px;
        }

        &.through {
            text-decoration: line-through;
        }
    }

    .pay {
        min-width: 200px;
        padding: 0 8px;
        height: 45px;
        margin-left: 25px;
        background: #f23f3f;
        border-radius: 5px;
        border: none;
        color: #fff;
        position: relative;
        font-size: 24px;
        cursor: pointer;

        & > * {
            display: none;
        }

        & > .open {
            display: block;
        }
    }

    .forbidden-status {
        .price {
            text-decoration: line-through;
        }

        .pay {
            background: #7f7f7f;
            font-size: 18px;
            font-weight: bold;
            cursor: no-drop;

            .reason {
                font-size: 12px;
            }

            & > * {
                display: none;
            }

            & > .forbidden {
                display: block;
            }
        }
    }

    .update-status {
        .price {
            text-decoration: line-through;
        }

        .pay {
            .update {
                font-size: 18px;
                align-items: baseline;

                .real {
                    display: flex;
                    align-items: baseline;
                    font-weight: bold;
                    font-size: 30px;

                    &::before {
                        content: '￥';
                        font-size: 24px;
                    }
                }

                .deduction {
                    position: absolute;
                    font-size: 12px;
                    color: #322725;
                    padding: 0 6px;
                    right: -30px;
                    top: -10px;
                    line-height: 24px;
                    background: linear-gradient(90deg, #fde3c0 0%, #f3d3a7 100%);
                    border-radius: 8px 8px 8px 0px;
                }
            }

            & > * {
                display: none;
            }

            & > .update {
                display: flex;
            }
        }
    }
}

.el-popper {
    max-width: 270px;
}
