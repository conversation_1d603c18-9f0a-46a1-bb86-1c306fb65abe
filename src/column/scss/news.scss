@import './variables';

@import './common/commonTitle';
@import './common/component/hot-topics';
@import './common/sideBar.scss';
@import './common/currentLocation';

$border-color-contact: #efefef;

.current-location {
    margin: 10px auto 0;
    width: $view-width;
}

.main {
    padding: 10px 0;
    display: flex;

    .left {
        width: 845px;
        margin-right: 10px;
        flex-shrink: 0;

        .showcase {
            display: flex;
            margin-bottom: 10px;

            .banner {
                width: 600px;
                height: 300px;
                margin-right: 10px;
                background-color: $color-white;

                .banner-pagination {
                    width: auto;
                    right: 30px;
                    bottom: 0;
                    line-height: 34px;
                    left: auto;

                    .swiper-pagination-bullet {
                        background-color: rgba(255, 255, 255, 0.7);
                        width: 6px;
                        height: 6px;
                    }

                    .swiper-pagination-bullet-active {
                        background-color: #fff;
                    }

                    @include utils-swiper-bullet();
                }

                .bottom {
                    position: absolute;
                    width: 100%;
                    left: 0;
                    bottom: 0;
                    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(139, 139, 139, 0.8));

                    .title {
                        padding-left: 20px;
                        width: 70%;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                        color: #fff;
                        line-height: 34px;
                    }
                }
            }

            .mini {
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                a {
                    height: calc((100% - 10px) / 2);
                    position: relative;

                    .title {
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        width: 100%;
                        min-height: 34px;
                        background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(139, 139, 139, 1));
                        color: $color-white;
                        padding: 7px 11px;
                    }
                }
            }
        }

        .new {
            background-color: $color-white;
            padding: 20px;

            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid #efefef;

                .more {
                    color: $color-primary;
                }
            }

            .tabs-new {
                padding-top: 20px;

                li {
                    display: inline-block;
                    min-width: 80px;
                    line-height: 26px;
                    border: 1px solid #f2f2f2;
                    border-radius: 4px;
                    text-align: center;
                    color: rgba(51, 51, 51, 0.8);
                    margin-right: 12px;
                    cursor: pointer;

                    &.active {
                        background-color: $color-primary;
                        color: #fff;
                        border-color: $color-primary;
                    }
                }
            }

            .tabs-new-content {
                .list {
                    &:not(:first-child) {
                        display: none;
                    }

                    li {
                        border-bottom: 1px solid #f2f2f2;

                        &:last-child {
                            border-bottom: none;
                        }

                        a {
                            display: flex;
                            padding: 20px 0;

                            .cover {
                                width: 220px;
                                height: 120px;
                                border-radius: 4px;
                                overflow: hidden;
                                margin-right: 19px;
                                flex-shrink: 0;
                            }

                            .detail {
                                display: flex;
                                flex-direction: column;
                                justify-content: space-between;
                                padding: 8px 0;
                                flex-grow: 1;

                                h3 {
                                    font-size: 16px;
                                }

                                .info {
                                    opacity: 0.8;
                                    color: initial;
                                    margin-top: 5px;
                                    flex-grow: 1;

                                    .intro {
                                        white-space: normal;
                                        overflow: hidden;
                                        text-overflow: ellipsis;
                                        display: -webkit-box;
                                        -webkit-line-clamp: 2;
                                        -webkit-box-orient: vertical;
                                        word-break: break-word;
                                        line-height: 1.6;
                                    }
                                }

                                .bottom {
                                    display: flex;
                                    justify-content: space-between;
                                    opacity: 0.6;
                                    font-size: 13px;
                                    color: initial;
                                }
                            }
                        }
                    }

                    .more {
                        a {
                            margin: 0 auto 0;
                            padding: 5px 0;
                            text-align: center;
                            width: 120px;
                            border: 1px solid $color-primary;
                            @include utils-radius;
                            color: $color-primary;
                            font-size: 12px;
                        }
                    }
                }
            }

            .pagination {
                margin-top: 20px;
            }
        }
    }

    .right {
        width: 345px;

        .hot-topics {
            background-color: #fff;
            padding: 20px;
            min-height: 300px;
            margin-bottom: 10px;

            .job-information {
                padding: 0;
                border: none;

                a {
                    line-height: 1.1;
                }

                .job-announcement {
                    .meeting-title {
                        width: 186px;
                    }
                }
            }
        }

        @import './common/component/circle';

        .exchange {
            margin-bottom: 10px;
            background-color: $color-white;
            padding: 20px;

            .dialogue {
                padding-bottom: 0;
            }
        }

        .recommend {
            background-color: $color-white;
            padding: 20px;
            width: 100%;
            min-height: 1081px;

            .news {
                .cover {
                    width: 100%;
                    border-radius: 4px;
                    overflow: hidden;
                }

                .title {
                    white-space: normal;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    word-break: break-word;
                    line-height: 1.5;
                    margin-top: 12px;
                }

                .date {
                    opacity: 0.6;
                    color: initial;
                }
            }

            .recommend-list {
                padding-top: 8px;

                a {
                    display: flex;
                    padding: 10px 0;

                    .cover {
                        width: 108px;
                        height: 66px;
                        border-radius: 4px;
                        overflow: hidden;
                        margin-right: 8px;
                        flex-shrink: 0;
                    }

                    .detail {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                    }

                    .title {
                        white-space: normal;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        word-break: break-word;
                        line-height: 1.5;
                    }

                    .date {
                        opacity: 0.6;
                        font-size: 13px;
                        color: initial;
                    }
                }
            }

            .hot-news {
                margin-top: 20px;
                padding-top: 12px;
                padding-bottom: 6px;

                h2 {
                    padding-left: 30px;
                    background-image: url(../assets/home/<USER>
                    background-size: 24px;
                    background-repeat: no-repeat;
                }
            }

            .hot-list {
                @each $index, $color in 1 #ff3333, 2 #ff9000, 3 #ff9000 {
                    li:nth-child(#{$index}) {
                        a {
                            span {
                                color: #{$color};
                            }
                        }
                    }
                }

                a {
                    display: flex;
                    padding: 7px 0;

                    span {
                        margin-right: 19px;
                        color: #aeaeae;
                        font-weight: bold;
                    }

                    .title {
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
}
