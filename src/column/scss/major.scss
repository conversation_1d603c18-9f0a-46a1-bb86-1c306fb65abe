// http://www.gaoxiaojob.com/major.html

@import './variables';

$color-bg: #f8f9fb;
$border-color: #ebebee;

.column-wrapper {
    background-color: $color-white;
}

.city-list {
    background-color: $color-bg;

    .common-city {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        padding: 30px 0;
        width: $view-width;
    }

    .hot-city {
        font-size: 18px;
        line-height: 38px;
    }

    .el-form {
        display: flex;
        height: 0;
    }

    .el-form-item {
        margin-right: 12px;

        &:last-child {
            margin-right: 0;
        }
    }

    .el-select .el-input__inner {
        width: 151px;
        height: 38px;
        color: $font-color-label;
    }

    .el-button {
        padding: 11px 20px;
        width: 68px;
        min-height: 38px;
    }

    .city {
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        width: $view-width;

        a {
            display: block;
            margin: 0 25px 30px 0;
            width: 150px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            background-color: $color-white;
            border-radius: 4px;
        }

        & > a:nth-child(7n) {
            margin-right: 0;
        }

        & > a:hover {
            background-color: $color-primary;
            color: $color-white;
        }
    }
}

.citieslist-content {
    margin: 0 auto;
    padding-bottom: 70px;
    width: $view-width;

    .citieslist {
        display: flex;
        margin-top: 40px;
        border: 1px solid $border-color;
        border-radius: 8px;
    }

    .region {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 78px;
        font-size: 18px;
        background-color: $color-bg;
    }

    ul {
        width: 1122px;
        padding: 10px 40px;

        li {
            line-height: 45px;
            border-bottom: 1px solid $border-color;

            &:last-child {
                border-bottom: none;
            }
        }
    }

    a {
        display: inline-block !important;
        margin-right: 40px;
        padding: 4px 11px;
        line-height: 24px;
        text-align: center;
    }

    a:hover {
        color: $color-white !important;
        background-color: $color-primary;
        border-radius: 4px;
    }
}
