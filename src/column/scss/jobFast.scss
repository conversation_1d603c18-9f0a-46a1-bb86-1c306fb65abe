@use 'sass:meta';
@import './variables';

$red: #ed7011;
$gray-bg: #f5f6f9;
$view-width: 1200px;

#component {
    font-size: 14px;
}

.banner {
    height: 400px;
    background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/banner.jpg') no-repeat center/auto 100%;
}

.gray-bg {
    background-color: $gray-bg;
}

.main-wrapper {
    width: $view-width;
    margin: 0 auto;
    padding-top: 75px;
}

.wrapper-title {
    font-size: 36px;
    margin-bottom: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    letter-spacing: 2px;
    justify-content: center;

    &::after {
        content: '';
        margin-left: 19px;
        width: 100px;
        height: 20px;
        background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/title-right.png) no-repeat center / contain;
    }
    &::before {
        content: '';
        margin-right: 19px;
        width: 100px;
        height: 20px;
        background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/title-left.png) no-repeat center / contain;
    }
}

.introduce-wrapper {
    padding-bottom: 80px;

    .compare {
        width: 100%;
        height: 316px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/compare.png') no-repeat center/contain;
        margin-top: 56px;
    }
}

.privilege-wrapper {
    padding: 22px 0 12px;

    .main-wrapper {
        padding-top: 53px;
    }

    .privilege-content {
        display: flex;
        align-items: center;
        height: 576px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/privilege-bg.png') no-repeat 13px 0px / contain;

        &.reverse {
            flex-direction: row-reverse;
            background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/privilege-bg.png') no-repeat right 23px top 0px / contain;

            .review {
                margin-right: 0;
                margin-left: 67px;
            }
        }
    }

    .review {
        width: 720px;
        height: 450px;
        margin-right: 50px;
        flex-shrink: 0;
    }

    .swiper {
        width: 100%;
        height: 100%;

        .swiper-slide {
            border-radius: 8px;
            box-shadow: 0px 0px 20px 0px rgba(32, 33, 35, 0.1);

            .effect {
                position: absolute;
                width: 645px;
                height: 98px;
                transform: scale(1);
                opacity: 0;
                transition: all ease-in-out 0.3s;
                transition-delay: 1s;
            }
        }
    }

    .swiper-pagination {
        position: static;
        padding-top: 10px;

        .swiper-pagination-bullet {
            width: 30px;
            height: 5px;
            border-radius: 5px;
            background: #e6e6e6;
            opacity: 1;

            &.swiper-pagination-bullet-active {
                background: $color-primary;
            }
        }
    }

    .resume-top-swiper {
        $effect: (1, resume-top-slide1, 54px, 109px), (2, resume-top-slide2, 54px, 109px), (3, resume-top-slide3, 56px, 85px);

        @each $index, $item, $left, $top in $effect {
            .slide#{$index} {
                background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/#{$item}.png) no-repeat center/contain;

                .effect {
                    top: $top;
                    left: $left;
                    background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/#{$item}-effect.png) no-repeat center/contain;
                }
            }
        }

        .swiper-slide-active {
            .effect {
                transform: scale(1.05);
                opacity: 1;
            }
        }
    }

    .deliver-top-swiper {
        $effect: (1, deliver-top-slide1, 54px, 109px), (2, deliver-top-slide2, 146px, 97px);

        @each $index, $item, $left, $top in $effect {
            .slide#{$index} {
                background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/#{$item}.png) no-repeat center/contain;

                .effect {
                    top: $top;
                    left: $left;
                    background: url(//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/#{$item}-effect.png) no-repeat center/contain;
                }
            }
        }

        .slide2 {
            .effect {
                width: 565px;
                height: 24px;
            }

            .effect-next {
                position: absolute;
                right: 0;
                bottom: 0;
                width: 220px;
                height: 375px;
                opacity: 0;
                transform: translateX(100%);
                transition: all ease-in-out 0.3s;
                transition-delay: 2.5s;
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/deliver-top-slide2-effect-next.png') no-repeat left/contain;
            }
        }

        .swiper-slide-active {
            .effect {
                transform: scale(1.05);
                opacity: 1;
            }

            .effect-next {
                opacity: 1;
                transform: translateX(0);
            }
        }
    }

    .refresh-gif {
        border-radius: 8px;
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/resume-refresh.gif') no-repeat left/100% auto;
    }

    .introduce {
        flex-grow: 1;

        $title-icon: resume-top, deliver-top, refresh;
        @each $name in $title-icon {
            .#{$name} {
                background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/#{$name}-icon.png') no-repeat left/48px 48px;
            }
        }

        .title {
            display: flex;
            align-items: center;
            line-height: 48px;
            font-size: 28px;
            font-weight: bold;
            padding-left: 64px;
            margin-bottom: 18px;
        }

        .desc {
            word-break: break-all;
            font-size: 18px;
            line-height: 36px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .item {
            font-size: 16px;
            line-height: 36px;
            position: relative;
            padding-left: 12px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 15px;
                width: 6px;
                height: 6px;
                background: $color-primary;
                border-radius: 50%;
                margin-right: 6px;
            }

            .primary {
                font-weight: bold;
                color: $color-primary;
                display: inline;
            }
        }
    }
}

.evaluate-wrapper {
    .main-wrapper {
        background: url('//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/evaluate-bg.png') no-repeat right 40px/124px 100px;
    }

    .evaluate-content {
        padding-top: 90px;
        padding-bottom: 10px;
        display: flex;
        justify-content: space-between;

        .list {
            width: calc((1200px - 60px) / 4);
            box-shadow: 0px 0px 10px 0px rgba(51, 51, 51, 0.08);
            border-radius: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0 17px 30px;

            .avatar {
                width: 64px;
                height: 64px;
                margin-top: -32px;
                margin-bottom: 13px;

                img {
                    width: 100%;
                    height: 100%;
                }
            }

            .name {
                margin-bottom: 17px;
                font-size: 18px;
                font-weight: bold;
            }

            .ask {
                margin-bottom: 13px;
                opacity: 0.9;
            }

            .content {
                line-height: 21px;
                margin-bottom: 17px;
            }

            .tag {
                display: flex;
                width: 100%;
                justify-content: flex-start;

                span {
                    line-height: 24px;
                    padding: 0 12px;
                    background: #ffeedb;
                    border-radius: 12px;
                    color: #ed7011;
                    font-size: 13px;
                    margin-right: 8px;
                }
            }
        }
    }
}

.package-wrapper {
    .package {
        display: flex;
        justify-content: space-between;
        padding: 56px 80px 40px;
    }

    .list {
        width: 320px;
        background: linear-gradient(180deg, #ffffff, #fff5eb);
        border: 2px solid #ffcda7;
        border-radius: 10px;
        text-align: center;
        padding: 56px 0 57px;
        transition: all ease 0.3s;
        position: relative;
        cursor: pointer;

        .tag {
            font-size: 14px;
            color: #fff;
            font-weight: bold;
            position: absolute;
            left: -2px;
            top: -11px;
            width: 53px;
            line-height: 26px;
            background: linear-gradient(90deg, #ff6f37, #fa635c);
            border: 1px solid #fde3c0;
            border-radius: 5px 5px 5px 0px;
        }

        .name {
            font-size: 26px;
            font-weight: bold;
            margin-bottom: 22px;
        }

        .detail {
            padding: 0 50px;
            font-size: 18px;

            .item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 22px;
                align-items: center;

                span {
                    font-weight: bold;
                    color: #ed7011;
                }

                .value {
                    color: $font-color-basic;
                }
            }
        }

        .price {
            display: flex;
            align-items: baseline;
            justify-content: center;
            font-size: 20px;
            padding-top: 8px;
            margin-bottom: 10px;

            .real {
                font-size: 56px;
                display: flex;
                align-items: baseline;

                &::before {
                    content: '￥';
                    color: inherit;
                    font-size: 20px;
                }
            }

            .original {
                color: #61647e;
                margin-left: 7px;
                text-decoration: line-through;
            }
        }

        .avg-price {
            display: inline-block;
            padding: 0 12px;
            height: 20px;
            line-height: 20px;
            background: #ffeedb;
            border-radius: 5px;
            margin: 0 auto 30px;
            font-size: 14px;
            color: $red;
        }

        .open {
            width: 144px;
            height: 40px;
            background: $red;
            border-radius: 20px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            line-height: 40px;
            margin: 0 auto 0px;
            color: #fff;
        }

        &.active {
            transform: translateY(-30px);
            border: 2px solid #ed7011;

            .name {
                color: $red;
            }

            .price {
                .real {
                    font-weight: bold;
                    color: $red;
                }
            }
        }
    }
}

.instructions-wrapper {
    padding: 20px 0;

    .main-wrapper {
        padding-top: 0;
    }

    .title {
        font-size: 16px;
        font-weight: bold;
    }
    .instructions-content {
        line-height: 2;
        margin-top: 12px;
        color: #322725;

        span {
            color: $red;
            cursor: pointer;
        }
    }
}

.fixed-wrapper {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #040d57;
    color: #fff;
    z-index: 9999;

    .main-wrapper {
        height: 84px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 18px;
        padding: 0;
    }

    .buy-info {
        display: flex;
        align-items: baseline;
        color: #fff;

        .real {
            font-size: 35px;
            font-weight: bold;
            color: #fdd6ad;
            margin-right: 5px;
        }

        .original {
            margin-left: 10px;
            font-size: 20px;
            opacity: 0.6;
            color: #e2e2e2;
            text-decoration: line-through;
        }
    }

    .pay {
        font-size: 20px;
        font-weight: bold;
        color: #795021;
        border: none;
        width: 152px;
        height: 44px;
        background: linear-gradient(90deg, #ffe8cf, #fdd6ad);
        border-radius: 22px;
        cursor: pointer;
        margin-left: 80px;
    }
}
