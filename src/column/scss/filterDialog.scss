@import './variables';

$font-color: #333333;
$font-color-label: rgba(51, 51, 51, 0.6);

.filter-dialog-template {
    color: $font-color;
    line-height: 1;

    .el-overlay {
        display: none;
    }

    .el-overlay-dialog {
        display: flex;
    }

    .el-dialog {
        width: auto;
        padding: 30px 75px;
        border-radius: 10px;
        align-self: flex-start;

        .el-dialog__header {
            margin-right: 0;
            padding: 0;
        }

        .el-dialog__body {
            padding: 0;
            width: 850px;
            max-height: initial;
            color: $font-color;
        }

        .el-dialog__footer {
            padding: 0px;
        }
    }

    .dialog-center {
        padding: 0;
    }

    .filter-header {
        display: flex;
        align-items: center;
        padding: 15px 19px;
        border-bottom: 1px solid #ebebeb;

        .title {
            font-size: 18px;
            margin-right: 8px;
        }

        .tips {
            color: $font-color-label;
            font-size: 14px;
            margin-right: 22px;

            span {
                color: $color-primary;
            }
        }

        .search {
            width: 250px;

            .el-input__inner {
                cursor: text;
            }
        }
    }

    .filter-content {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .select {
            display: flex;
            min-height: 56px;
            align-items: center;
            padding: 13px 20px;
            background-color: #f9fafb;

            .select-label {
                flex-shrink: 0;
            }

            .select-value {
                display: flex;
                flex-wrap: wrap;
                flex-grow: 1;

                .tag {
                    height: 24px;
                    max-width: calc((100% - 40px) / 5);
                    display: flex;
                    overflow: hidden;
                    align-items: center;
                    justify-content: center;
                    background-color: #fff;
                    border-color: $color-primary;
                    color: $color-primary;

                    .el-tag__content {
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }

                    .el-tag__close {
                        width: 13px;
                        height: 13px;
                        font-size: 11px;
                        line-height: 13px;
                        flex-shrink: 0;
                        color: #fff;
                        background-color: $color-primary;
                        position: static;
                        margin-left: 5px;
                    }

                    & + .tag {
                        margin-left: 10px;
                    }
                }
            }
        }

        .data-content {
            display: flex;
            height: 400px;
            overflow: hidden;

            .left-content {
                width: 160px;
                background-color: #f5f5f5;
                overflow-y: auto;
                flex-shrink: 0;
                padding: 0;

                .list {
                    display: block;
                    padding: 13px 8px 13px 35px;
                    position: relative;
                    line-height: 20px;
                    cursor: pointer;

                    &.active {
                        background-color: #fff;
                        color: $color-primary;
                    }

                    &.has-select {
                        &::after {
                            content: '';
                            display: block;
                            width: 4px;
                            height: 4px;
                            background: #fa635c;
                            border-radius: 50%;
                            position: absolute;
                            left: 20px;
                            top: 21px;
                        }
                    }
                }
            }

            .right-content {
                overflow-y: auto;
                padding: 0 20px 10px;
                min-width: 690px;
                flex-grow: 1;

                .second-content {
                    display: none;

                    &.show {
                        display: flex;

                        .second-list {
                            & + .second-list {
                                margin-top: 8px;
                            }
                        }
                    }
                }

                @for $i from 3 to 7 {
                    .column-#{$i} {
                        flex-wrap: wrap;

                        .second-item {
                            width: calc((100% - (($i - 1) * 16px)) / $i);
                            margin-right: 16px;

                            &:nth-child(#{$i}n) {
                                margin-right: 0px;
                            }
                        }
                    }
                }

                .second-item {
                    padding: 5px 0;

                    a {
                        padding: 5px 11px;
                        line-height: 18px;
                        display: inline-block;

                        &.active {
                            color: $color-primary;
                            background-color: #fff3e0;
                            border-radius: 4px;
                        }
                    }
                }
            }
        }
    }
}

@media screen and (max-height: 667px) {
    .filter-dialog-template {
        .filter-content {
            .data-content {
                height: 300px;
            }
        }
    }
}

.postdoctor-container {
    .filter-dialog-template {
        @import './variablesPostdoctor';

        --el-color-primary: #{$color-primary};

        .filter-content {
            .data-content {
                .left-content {
                    .list {
                        &.active {
                            color: $color-primary;
                        }

                        &.has-select {
                            &::after {
                                background: $color-primary;
                            }
                        }
                    }
                }

                .right-content {
                    .second-content {
                        a {
                            &.active {
                                color: $color-primary;
                                background-color: #f7e8e9;
                            }
                        }
                    }
                }
            }
        }
    }
}
