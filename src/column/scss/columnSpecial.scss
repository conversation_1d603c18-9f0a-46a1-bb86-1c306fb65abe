//  专场栏目
@import './variables';

@import './common/headlines-adv';
@import './common/commonTitle';
@import './common/fixedTool';
@import './common/sideBar';

@import './common/currentLocation';
@import './common/component/propaganda';
@import './common/component/rangeD';
@import './common/component/rangeL';
@import './common/component/rangeH';
@import './common/component/link';
@import './common/component/specialAnnouncement';
@import './common/component/banner';
@import './common/component/dropnav';
@import './common/popper';

$border-color-contact: #efefef;
$color-bg: #f4f9ff;
$color-border-bottom: #f2f2f2;
$color-bg-title: #fafafc;

.contact {
    padding: 16px 0;
    border: 1px solid $border-color-contact;

    p {
        padding-left: 38px;
        line-height: 20px;
    }

    span {
        color: $font-color-basic;
    }

    .telephone-hotline {
        background-image: url('../assets/home/<USER>');
        background-repeat: no-repeat;
        background-position: 12px;
        background-size: contain;
    }

    .hotline-number {
        padding-left: 6px;
    }

    .hotline {
        padding: 18px 0 18px 101px;
        color: $font-color-basic;
    }

    .consult-qq {
        background-image: url('../assets/home/<USER>');
        background-repeat: no-repeat;
        background-position: 12px;
        background-size: contain;
    }

    .qq-number {
        padding-left: 11px;
    }
}

.type-select {
    .el-form-item {
        margin-bottom: 16px;
    }

    .type-select-tips {
        margin-bottom: 16px;
        color: $font-color-basic;
        font-size: 13px;

        span {
            display: inline-block;
            padding: 5px 18px 5px 39px;
            background: url('../assets/icon/type-select-tips.png') no-repeat #fff9e2 20px 5px /13px;
            @include utils-radius;
        }
    }
}

.right-content {
    @import './common/component/latestAnnouncementTiled';
}
