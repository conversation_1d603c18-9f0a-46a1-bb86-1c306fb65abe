@use 'sass:meta';
@import './variables';
@import './common/component/detailShare';
@import './mini-code-popup';

@mixin establishment {
    .establishment-tag {
        padding: 0 10px;
        line-height: 18px;
        border-radius: 4px;
        border: 1px solid #4fbc67;
        color: #4fbc67;
        font-size: 12px;
    }
}

@mixin span-boon {
    padding: 4px 10px;
    color: $font-color;
    line-height: 1;
    background: #f8f9fb;
    border: 1px solid $border-color;
    border-radius: 4px;

    &::before {
        content: '';
        display: inline-block;
        margin-top: -3px;
        margin-right: 4px;
        width: 4px;
        height: 4px;
        background: $color-primary;
        border-radius: 4px;
        vertical-align: middle;
    }
}

.detail-container {
    .detail-header {
        background: url(../assets/detail/detail-header.jpg) no-repeat center / cover;

        &.is-fixed {
            @include utils-fixed;
            @include utils-detail-header-plain-fixed;
            top: $header-height;

            .detail-header-container {
                .breadcrumb {
                    display: none;
                }

                .main {
                    margin-bottom: 0;
                    padding-bottom: 24px;

                    section {
                        .title {
                            h1 {
                                display: block;
                                @include utils-ellipsis();
                            }

                            .job-tag-cell {
                                display: none;
                            }
                        }

                        .info {
                            margin-bottom: 0;

                            @include establishment;
                            .establishment-tag {
                                margin-right: 7px;
                                display: inline-block;
                            }
                        }

                        .tips {
                            margin-bottom: 0;
                        }

                        .boons {
                            display: none;
                        }

                        .job-tag-cell {
                            margin-right: 12px;
                            display: inline-flex;
                        }
                    }

                    .view-button {
                        display: none;
                    }
                }
            }

            .view-relation {
                display: inline-block;
            }
        }

        .detail-header-container {
            margin: 0 auto;
            width: $view-width;

            .main {
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 45px;
                padding: 24px 0 40px;

                section {
                    width: 900px;
                    color: $font-color;
                    font-size: 14px;

                    .title {
                        display: flex;
                        align-items: flex-start;
                        margin-bottom: 20px;

                        h1 {
                            margin-right: 20px;
                            max-width: 710px;
                            font-size: 22px;
                            font-weight: bold;
                            line-height: 30px;
                            /*
                            * Version 1.2
                            * 产品需求 放开行数限制（已告知放开限制异常情况，依然坚持）
                            * @include utils-ellipsis-lines(2, 30px, 22px);
                            */
                        }

                        .job-tag-cell {
                            display: inline-block;
                            position: relative;
                            inset: -3px 0 0;
                        }

                        .color-salary {
                            font-size: 16px;
                            font-weight: bold;
                            line-height: 30px;
                        }
                    }

                    .info {
                        .establishment-tag {
                            display: none;
                        }
                    }

                    .tips {
                        display: flex;
                        align-items: center;
                        margin-bottom: 20px;
                        line-height: 1;
                        .job-tag-cell {
                            .fast-feedback {
                                margin-left: 6px;
                            }
                            .establishment {
                                margin-left: 6px;
                            }
                        }

                        & > span {
                            line-height: 1;

                            & + span {
                                margin-left: 10px;
                                padding-left: 10px;
                                border-left: 1px solid $font-color-tips;
                            }
                        }
                    }

                    .boons {
                        display: flex;
                        align-items: center;
                        font-size: 12px;

                        span {
                            & + span {
                                margin-left: 10px;
                                padding-left: 10px;
                                border-left: 1px solid $font-color-tips;
                            }

                            &.boon {
                                @include span-boon;
                            }
                        }

                        .boon-more {
                            margin-left: 10px;
                            color: #bcbcbc;
                            font-size: 18px;
                            cursor: pointer;
                        }

                        @include establishment;
                    }

                    .job-tag-cell {
                        display: none;
                    }

                    .help-wanted {
                        @include utils-radius;
                        flex: none;
                        padding: 3px 10px 3px;
                        color: $font-color-salary;
                        font-size: 12px;
                        line-height: 1;
                        border: 1px solid $font-color-salary;

                        // & + .fast-feedback + .establishment {
                        //     margin-left: 6px;
                        // }
                    }

                    .establishment {
                        @include utils-radius;
                        flex: none;
                        padding: 3px 10px 3px;
                        color: #4fbc67;
                        font-size: 12px;
                        line-height: 1;
                        border: 1px solid #4fbc67;
                    }

                    .fast-feedback {
                        @include utils-radius;
                        flex: none;
                        padding: 3px 5px 3px 15px;
                        color: $color-primary;
                        font-size: 12px;
                        line-height: 1;
                        background: url(../assets/job/fast-feedback.png) no-repeat 6px 4px / 8px 12px;
                        border: 1px solid $color-primary;
                    }
                }

                aside {
                    .el-button {
                        width: 175px;

                        &.el-button--collect {
                            color: $color-primary;
                            background-color: transparent;
                            border-color: $color-primary;
                            padding-top: 0;
                            padding-bottom: 0;
                            display: inline-block;
                            padding-left: 30px;
                            background: url(../assets/detail/collect.png) no-repeat left center / 20px;
                            line-height: 38px;
                            // span {

                            // }
                            &.collected {
                                background-image: url(../assets/detail/collected.png);
                            }
                        }
                    }
                    .el-button--collect {
                        // color: $color-primary;
                        background-color: transparent;
                        border-color: $color-primary;
                        padding-top: 0;
                        padding-bottom: 0;
                        display: inline-block;
                        padding-left: 25px;
                        background: url(../assets/detail/collect.png) no-repeat left center / 20px;
                        line-height: 38px;
                        text-align: left;
                        // span {

                        // }
                        &.collected {
                            background-image: url(../assets/detail/collected.png);
                        }
                    }
                    .el-button--analyse {
                        // color: $color-primary;
                        background-color: transparent;
                        border-color: $color-primary;
                        padding-top: 0;
                        padding-bottom: 0;
                        display: inline-block;
                        padding-left: 25px;
                        background: url(../assets/detail/analyse.png) no-repeat left center / 20px;
                        line-height: 38px;
                        text-align: right;
                    }

                    .emit {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-end;

                        .detail-button {
                            font-size: 14px;
                            display: flex;
                            justify-content: flex-end;
                            align-items: center;
                            cursor: pointer;

                            & > div:not(:first-child) {
                                margin-left: 30px;
                            }

                            .see-analysis {
                                margin-left: 30px;
                            }
                            &.announcement-button {
                                justify-content: space-between;
                            }
                        }

                        .button-group {
                            display: flex;

                            button {
                                width: 120px;
                            }

                            .chat-button {
                                color: $color-primary;
                                border-color: $color-primary;
                                padding-left: 40px;
                                background: $color-white url(../assets/common/chat-primary.png) no-repeat left 22px center/20px;
                            }
                        }
                    }
                }

                .view-button {
                    position: absolute;
                    left: 0;
                    padding: 0 20px;
                    min-height: 35px;
                    line-height: 35px;
                    bottom: calc(-35px / 2);
                    border-radius: calc(35px / 2);
                    border: none;

                    span {
                        padding-right: 25px;
                        background: url(../assets/detail/view-button-arrow.png) no-repeat right center / 20px 11px;
                    }
                }
            }
        }

        .view-relation {
            display: none;
            margin-left: 20px;
            padding-right: 15px;
            color: $color-primary;
            font-weight: bold;
            background: url(../assets/detail/view-relation.png) no-repeat right center / 8px;
        }
    }

    .detail-main {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        width: $view-width;

        section {
            padding: 20px 30px 0;
            width: 858px;
            background: $color-white;
            border-radius: 10px;

            .tips {
                margin-bottom: 20px;
                padding: 10px 20px 10px 45px;
                color: $font-color-basic;
                line-height: 2;
                background: url(../assets/icon/warning.png) no-repeat #fff9e2 20px 14px / 16px;
                @include utils-radius;
            }

            .detail-list {
                .detail-item {
                    padding: 20px 0 15px;
                    border-bottom: 1px solid $border-color;

                    &:last-of-type {
                        border: none;
                    }

                    .detail-title {
                        margin-bottom: 20px;
                        font-size: 16px;
                        font-weight: bold;
                        background: url(../assets/detail/title.png) no-repeat left bottom / 80px 8px;
                    }

                    .detail-content {
                        color: $font-color;
                        font-size: 14px;
                        font-weight: 400;

                        p {
                            font-size: inherit;
                            margin-left: 5px;
                            cursor: pointer;
                            display: inline-block;
                        }
                        .disinblock {
                            display: inline-block;
                        }
                        .el-col {
                            padding: 7px 0;
                            line-height: 1.5;
                            word-break: break-word;
                        }

                        &.single {
                            line-height: 2;
                        }

                        .attention {
                            display: flex;
                            padding: 0px 10px 0px 12px;
                            margin-top: 20px;
                            color: $color-primary;
                            background: #fff9e2;
                            border-radius: 15px;
                            align-items: center;

                            p {
                                flex: 1;
                            }

                            a {
                                width: 18px;
                                height: 18px;
                                line-height: 16px;
                                border-radius: 50%;
                                text-align: center;
                                color: $color-white;
                                background: $color-primary;
                            }
                        }

                        .el-row {
                            width: 100%;
                            .el-col {
                                width: 100% !important;
                            }
                        }

                        .show-complete {
                            display: flex;
                            .label {
                                flex-shrink: 0;
                                line-height: 1.5;
                            }
                            .value {
                                flex-wrap: wrap;
                                flex-grow: 1;
                                line-height: 1.5;
                            }
                            a.color-primary {
                                margin-left: 3px;
                                white-space: pre-wrap;
                                word-break: break-all;
                            }
                        }

                        .job-detail-major-tips {
                            margin-top: 8px;

                            span {
                                @include utils-radius;
                                padding: 4px 10px 4px 32px;
                                font-size: 13px;
                                line-height: 1;
                                background: #fff9e2 url(../assets/icon/type-select-tips.png) no-repeat 10px 50% / 13px;
                            }
                        }
                    }

                    .el-col {
                        @include utils-ellipsis;

                        &:nth-of-type(n) {
                            width: 50%;
                        }

                        &:nth-of-type(2n) {
                            margin-left: 5%;
                            width: 45%;
                        }

                        &.is-complete {
                            white-space: normal;
                        }
                    }

                    .announcement {
                        .el-col {
                            @include utils-ellipsis;

                            &.is-complete {
                                white-space: normal;
                            }

                            &:nth-of-type(n) {
                                margin-left: calc(100% / 24 * 1.5);
                                width: calc(100% / 24 * 7);
                            }

                            &:nth-of-type(3n + 1) {
                                margin-left: 0;
                            }

                            &.el-col-8 {
                                width: calc(100% / 24 * 7);
                                max-width: calc(100% / 24 * 7);
                            }

                            &.el-col-16 {
                                width: calc(100% / 24 * 15);
                                max-width: calc(100% / 24 * 15);
                            }

                            a {
                                & + a {
                                    margin-left: 1em;
                                }
                            }
                        }
                    }
                }
            }

            .analyse {
                margin-top: 20px;
                cursor: pointer;
                .open-analysis {
                    cursor: pointer;
                    position: absolute;
                    font-weight: 400;
                    right: 0;
                    padding-right: 10px;
                    font-size: 14px;
                    color: $color-primary;
                    background: url(../assets/detail/to-next.png) no-repeat right center/5px 7px;
                }
            }

            .heat {
                margin-top: 20px;
                cursor: pointer;
                .open-heat {
                    cursor: pointer;
                    position: absolute;
                    font-weight: 400;
                    right: 0;
                    padding-right: 10px;
                    font-size: 14px;
                    color: $color-primary;
                    background: url(../assets/detail/to-next.png) no-repeat right center/5px 7px;
                }
            }

            .power-heat {
                margin: 15px 0 35px 0;
                font-size: 14px;
                .text-heat {
                    margin-bottom: 35px;
                    span {
                        font-size: 16px;
                        color: $color-primary;
                        font-weight: 700;
                    }
                    .active {
                        color: transparent;
                        text-shadow: #ffa000 0 0 10px;
                    }
                }
            }
            .power-analyse {
                margin: 15px 0 70px 0;
                font-size: 14px;
                .text-heat {
                    margin-bottom: 35px;
                    span {
                        font-size: 16px;
                        color: $color-primary;
                        font-weight: 700;
                    }
                    .active {
                        color: transparent;
                        text-shadow: #ffa000 0 0 10px;
                    }
                }
                .text-rate {
                    margin-bottom: 50px;
                    span {
                        font-size: 16px;
                        color: $color-primary;
                        font-weight: 700;
                    }
                    .active {
                        color: transparent;
                        text-shadow: #ffa000 0 0 10px;
                    }
                }
                .not-login {
                    text-align: center;
                    color: #5c5c5c;
                    margin-bottom: 18px;
                    margin-top: -23px;
                }
                .matching-rate {
                    height: 10px;
                    display: flex;
                    text-align: center;
                    color: #5c5c5c;
                    span {
                        width: 100%;
                        height: 100%;
                        display: inline-block;
                        margin-bottom: 5px;
                    }
                    .lower {
                        width: 198px;
                        margin-right: 5px;
                        position: relative;
                        .active {
                            position: absolute;
                            top: -283%;
                            left: 46%;
                            background: url(../assets/detail/positioning.png) no-repeat center/28px;
                            width: 18px;
                            height: 50px;
                        }
                        span {
                            background: linear-gradient(90deg, #fdde60, #fcc854);
                            border-radius: 5px 0px 0px 5px;
                        }
                    }
                    .general {
                        width: 198px;
                        margin-right: 5px;
                        position: relative;
                        .active {
                            position: absolute;
                            top: -283%;
                            left: 46%;
                            background: url(../assets/detail/positioning.png) no-repeat center/28px;
                            width: 18px;
                            height: 50px;
                        }
                        span {
                            background: linear-gradient(90deg, #fcc854, #fda92c);
                        }
                    }
                    .matching {
                        width: 198px;
                        margin-right: 5px;
                        position: relative;
                        .active {
                            position: absolute;
                            top: -283%;
                            left: 46%;
                            background: url(../assets/detail/positioning.png) no-repeat center/28px;
                            width: 18px;
                            height: 50px;
                        }
                        span {
                            background: linear-gradient(90deg, #fda92c, #ff8c00);
                        }
                    }
                    .closely-match {
                        width: 198px;
                        position: relative;
                        .active {
                            position: absolute;
                            top: -283%;
                            left: 46%;
                            background: url(../assets/detail/positioning.png) no-repeat center/28px;
                            width: 18px;
                            height: 50px;
                        }
                        span {
                            background: linear-gradient(90deg, #ff8c00, #ff6600);
                            border-radius: 0px 5px 5px 0px;
                        }
                    }
                }
            }

            .detail-emit {
                margin: 30px 0;
                text-align: center;

                .el-button {
                    & + .el-button {
                        margin-left: 30px;
                    }
                }
            }

            @import './common/jobFileList';

            @import './common/component/qrCode';

            .detail-tips {
                margin: 0 auto 20px;
                padding: 15px;
                color: #fa635c;
                line-height: 2;
                background: #fffcea;
                border-radius: 4px;
            }

            .qr {
                padding-top: 25px;
                text-align: center;
                color: #ffa000;
                font-weight: bold;
                border-top: 1px solid #ededed;
            }
        }

        @include meta.load-css('./common/aside');
    }
}

.el-popper {
    .boon {
        display: inline-block;
        padding: 3px 8px;
        margin: 8px 6px;
        color: $color-primary;
        font-size: 12px;
        font-weight: 400;
        background: #feebcb;
        border-radius: 4px;
    }
}
