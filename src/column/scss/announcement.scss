@use 'sass:meta';
@use './footerLink';

@import './detail';

@import './mini-code-popup';

$color-secondary: rgba(
    $color: $color-primary,
    $alpha: 0.6
);
$color-main: rgba(
    $color: #ffa917,
    $alpha: 0.4
);
$border-color-bg: rgba(
    $color: $color-white,
    $alpha: 0.4
);

.detail-container {
    .detail-header {
        .detail-header-container {
            .main {
                section {
                    .title {
                        h1 {
                            margin-right: 0;
                            max-width: 100%;
                        }
                    }

                    .info {
                        margin-bottom: 20px;
                        line-height: 1;
                    }
                }
            }
        }
    }

    .detail-main {
        section {
            padding: 0;
            background: initial !important;
            border-radius: 0;

            .section-main {
                padding: 30px 30px;
                background: $color-white;
                border-radius: 10px;

                .detail-list {
                    margin-bottom: 15px;
                }
            }
        }

        .feedback-link {
            margin-bottom: 45px;
            font-size: 14px;
            line-height: 1;
        }

        .feedback-source-tips {
            margin-bottom: 30px;

            &.is-extend {
                a::after {
                    transform: rotate(180deg);
                }

                p {
                    display: block;
                }
            }

            a {
                margin-bottom: 16px;
                display: flex;
                align-items: center;

                &::after {
                    content: '';
                    margin-left: 8px;
                    width: 8px;
                    height: 4px;
                    background: url(../assets/icon/filter-arrow.png) no-repeat center / contain;
                }
            }

            p {
                display: none;
                color: $font-color-basic;
                font-size: 14px;
                line-height: 22px;
            }
        }
    }

    .detail-main-content {
        margin: 20px auto 18px;

        .detail-title {
            margin-bottom: 38px;
            border-bottom: 1px solid $color-primary;

            span {
                position: relative;
                display: inline-block;
                padding: 9px 18px;
                color: $color-white;
                font-size: 18px;
                font-style: italic;
                font-weight: bold;
                transform: skew(-10deg);
                border-radius: 5px;
                letter-spacing: 3px;
                background-color: $color-primary;

                &::before,
                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    right: -10px;
                    width: 100%;
                    height: 100%;
                    border-radius: 5px;
                    background-color: $color-secondary;
                    z-index: -1;
                }

                &::after {
                    right: -20px;
                    background-color: $color-main;
                }
            }
        }

        p {
            color: $font-color;
            font-size: 14px;
            line-height: 2;
            word-break: break-word;
        }

        img {
            display: block;
            margin: 20px auto;
            max-width: 100%;
        }

        .detail-images {
            display: flex;
            justify-content: space-between;
            margin: 20px auto;

            li {
                width: 49%;
            }

            img {
                margin: 0 auto;
            }
        }

        @include meta.load-css('./common/detailTable');
    }
}
