@import './variables';

body {
    margin: 0;
    padding: 0;
}

/* animate.css - https://animate.style start */

:root {
    --animate-duration: 1s;
    --animate-delay: 1s;
    --animate-repeat: 1;
}

.animate__animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.animate__animated.animate__infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
}

@-webkit-keyframes shakeY {
    0%,
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0);
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0);
    }
}

@keyframes shakeY {
    0%,
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translate3d(0, -10px, 0);
        transform: translate3d(0, -10px, 0);
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translate3d(0, 10px, 0);
        transform: translate3d(0, 10px, 0);
    }
}

.animate__shakeY {
    -webkit-animation-name: shakeY;
    animation-name: shakeY;
}

/* animate.css end */

.wrapper {
    position: relative;
    padding: 163px 0 202px;
    width: 100%;

    .left-bottom {
        position: absolute;
        width: 200px;
        height: 86px;
        bottom: 0;
        left: 44px;
        background: url('../assets/404/left-bottom.png') no-repeat center / cover;
    }

    .right-top {
        position: absolute;
        width: 63px;
        height: 27px;
        top: 180px;
        right: 286px;
        background: url('../assets/404/right-top.png') no-repeat center / cover;
    }

    .main {
        position: relative;
        margin: 0 auto;
        width: 801px;
        height: 582px;
        background: url('../assets/404/404.png') no-repeat center / cover;
    }

    .animation {
        position: absolute;
        width: 178px;
        height: 217px;
        top: 78px;
        left: 53px;
        background: url('../assets/404/animation.png') no-repeat center / cover;
        animation-duration: 15s;
    }

    .button-container {
        margin: 33px auto 0;
        width: 140px;
        height: 35px;
        line-height: 35px;
        text-align: center;
        background-color: $color-primary;
        border-radius: 10px;

        a {
            display: block;
            text-decoration: none;
            font-size: 18px;
            color: $color-white;
        }
    }
}
