@import './variablesActivity';

.show {
    display: block !important;
}

.banner-wrapper {
    position: relative;
    // background: url(../assets/activity/singleDetail/banner.jpg) no-repeat center top/auto 100%;
    // height: 600px;
    display: flex;
    justify-content: center;
    overflow: hidden;

    // &::after {
    //     content: '';
    //     position: absolute;
    //     bottom: 0;
    //     left: 0;
    //     right: 0;
    //     height: 40px;
    //     background-color: rgba($color: #e0e8fd, $alpha: 0.7);
    // }

    img {
        width: auto !important;
        height: auto !important;
        object-fit: cover; /* 图片覆盖整个容器，可能会被裁剪 */
        object-position: center; /* 图片居中 */
    }
}

.main-container {
    background: url(../assets/activity/singleDetail/bg.png) repeat-y center top / cover, linear-gradient(180deg, #e3e7ff, #cfeeff);
    padding-bottom: 40px;

    .view-content {
        width: $view-width;
        margin: 0 auto;
    }

    .countdown-content {
        display: flex;
        justify-content: center;
        height: 70px;
        line-height: 70px;
        font-size: 26px;
        font-weight: bold;
        transform: translateY(-35px);

        .countdown {
            display: flex;
            align-items: center;
            padding: 0 34px 0 74px;
            border-radius: 35px;
            background: url(../assets/activity/singleDetail/countdown-icon.png) no-repeat 31px center/32px #ffffff;
            .box {
                display: inline-block;
                color: $color-primary;
                font-size: 28px;
                min-width: 42px;
                height: 42px;
                line-height: 42px;
                // padding: 0 6px;
                text-align: center;
                background: $color-white;
                border-radius: 4px;
                border: 1px solid #296aff;
            }
        }
    }

    .detail-wrapper {
        padding: 28px 46px 14px 30px;
        background-color: $color-white;
        border-radius: 12px;
        position: relative;
        z-index: 1;
        display: flex;
        .activity-connect {
            position: absolute;
            left: 30px;
            top: -14px;
            // display: flex;
            // align-items: center;
            max-width: 605px;
            height: 28px;
            padding: 0 12px;
            line-height: 28px;
            color: $color-primary;
            font-weight: bold;
            background: #eef1ff;
            border-radius: 14px;
            border: 1px solid #c8d8fd;
            .connect {
                // display: flex;
                // flex-wrap: nowrap;
                @include utils-ellipsis;
                p {
                    display: inline;
                    font-size: 16px;
                    span {
                        color: #3477fa;
                    }
                }

                a {
                    &:hover {
                        color: inherit;
                    }
                }
            }
        }
        .status {
            position: absolute;
            top: -15px;
            right: 0;
            padding: 5px 0px 0;
            height: 40px;
            font-size: 18px;
            font-weight: bold;
            color: $color-white;
            width: 96px;
            text-align: center;

            $status: await, await-start, start, ended;

            @each $name in $status {
                @if $name == start {
                    &.#{$name} {
                        text-align: left;
                        padding-left: 30px;
                        background: url(../assets/activity/singleDetail/start-icon.gif) no-repeat 10px 8px/13px 14px, url(../assets/activity/singleDetail/#{$name}-bg.png) no-repeat left top/auto 39px;
                    }
                } @else {
                    &.#{$name} {
                        background: url(../assets/activity/singleDetail/#{$name}-bg.png) no-repeat left top/auto 39px;
                    }
                }
            }
        }

        .detail {
            flex-grow: 1;
            margin-right: 65px;
            overflow: hidden;

            .tag {
                display: flex;
                margin-bottom: 14px;

                .item {
                    padding: 0 6px;
                    font-size: 12px;
                    line-height: 20px;
                    color: $color-primary;
                    background-color: #e5edff;
                    border-radius: 4px;

                    & + .item {
                        margin-left: 6px;
                    }

                    &.yellow {
                        border-radius: 4px;
                        border: 1px solid #ffa000;
                        color: #ffa000;
                        background-color: #fff;
                    }
                }
            }

            .title {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 10px;
                line-height: 1.5;
            }

            .detail-item {
                display: flex;
                font-size: 16px;
                line-height: 28px;
                margin-bottom: 2px;

                .label {
                    flex-shrink: 0;
                    padding-left: 26px;
                }

                .value {
                    flex-grow: 1;
                }

                &.organization,
                &.type {
                    .value {
                        @include utils-ellipsis;
                    }
                }
            }

            $icons: organization, time, address, type;

            @each $name in $icons {
                .#{$name} {
                    .label {
                        background: url(../assets/activity/singleDetail/#{$name}.png) no-repeat left 6px/16px 16px;
                    }
                }
            }

            .welfare {
                margin-top: 14px;
                margin-bottom: 16px;

                display: flex;
                line-height: 22px;
                padding-right: 10px;
                color: #714d10;

                .value {
                    background: url(../assets/activity/singleDetail/praise.png) no-repeat left/26px 22px, #fff8f1;
                    padding-left: 32px;
                    @include utils-ellipsis;
                    border-radius: 4px 0 0 4px;
                }

                &.has-detail {
                    .arrow {
                        cursor: pointer;
                        flex-shrink: 0;
                        font-weight: bold;
                        color: #714d10;
                        flex-shrink: 0;
                        padding-left: 6px;
                        padding-right: 28px;
                        background: url(../assets/activity/singleDetail/arrow.png) no-repeat right 10px center/13px 13px, #fff8f1;
                        border-radius: 0 4px 4px 0;
                        position: relative;

                        &::after {
                            content: '';
                            display: block;
                            position: absolute;
                            left: 6px;
                            right: 10px;
                            height: 1px;
                            background-color: #714d10;
                            bottom: 0px;
                        }
                    }
                }
            }
        }

        .aside {
            position: relative;
            flex-shrink: 0;
            position: relative;
            background: #f7f9fe;
            border-radius: 10px;
            padding: 24px 40px;
            display: flex;
            align-self: center;
            margin-top: -6px;

            .update-status {
                position: absolute;
                right: 0;
                top: -10px;
                transform: translateX(50%);
                padding: 0 9px;
                line-height: 20px;
                color: #ffa000;
                font-size: 12px;
                background: #fff2db;
                border-radius: 12px 0px 10px 0px;
            }

            .item {
                padding: 0 20px 0 0;
                text-align: center;

                .amount {
                    padding-bottom: 6px;
                    font-size: 28px;
                    font-weight: bold;
                    color: $color-primary;
                }

                span {
                    margin-top: 11px;
                    color: $font-color-label;
                    font-size: 16px;
                }

                &:last-child {
                    padding: 0 0 0 20px;
                }

                & + .item {
                    position: relative;

                    &::before {
                        display: block;
                        position: absolute;
                        left: 0;
                        top: 6px;
                        content: '';
                        width: 1px;
                        height: 50px;
                        background: #ebebeb;
                    }
                }
            }
        }
    }

    .tips-wrapper {
        margin-top: 30px;
        border-radius: 12px;
        padding: 2px;
        background: linear-gradient(to bottom, #608ef9, #bacfff);
        overflow: hidden;
        .tips-content {
            display: flex;
            overflow: hidden;
            min-height: 136px;
            border-radius: 12px;
            background: #ffffff;
        }
        .left {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            width: 138px;
            background: linear-gradient(90deg, #ffffff, #dfecfc);
            img {
                width: 54px;
                height: 43px;
                margin: 0 auto 10px;
            }
            p {
                font-weight: bold;
                font-size: 18px;
                color: #333333;
            }
        }
        .right {
            display: flex;
            // align-items: center;
            flex: 1;
            padding: 28px;
            .text {
                font-size: 18px;
                color: #333333;
                line-height: 30px;
                p {
                    font-size: 18px;
                }
            }
        }
    }

    .hot-company {
        position: relative;
        overflow: hidden;
        padding: 35px 20px 18px;
        margin-top: 30px;
        border-radius: 12px;
        background-color: #fff;
        .tag {
            position: absolute;
            left: 0;
            top: 0;
            height: 25px;
            padding: 0 14px;
            line-height: 25px;
            font-weight: bold;
            font-size: 18px;
            color: #ffffff;
            background: linear-gradient(90deg, #2a6bff, #25c9e6);
            border-radius: 12px 0px 12px 0px;
        }
        .hot-company-swiper {
            padding-top: 10px;
            .company-list {
                width: 216px;
                height: 118px;
                padding: 10px 16px;
                background: #ffffff;
                border-radius: 12px;
                border: 1px solid #d5e1fd;
                text-align: center;

                &:hover {
                    border: 1px solid #296aff;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                    transform: translateY(-5px);
                    transition: all 0.3s ease;
                    .title {
                        color: #296bff !important;
                    }
                }
                img {
                    width: 49px;
                    height: 49px;
                    border-radius: 50%;
                    margin: 0 auto;
                }
                .title {
                    margin-top: 10px;
                    font-weight: bold;
                    color: #333333;
                    @include utils-ellipsis;
                }
                .text {
                    margin-top: 7px;
                    font-size: 12px;
                    color: #333333;
                    opacity: 0.8;
                    @include utils-ellipsis;
                }
            }
        }
        .swiper-pagination {
            position: static;
            padding-top: 10px;

            .swiper-pagination-bullet {
                width: 20px;
                height: 6px;
                border-radius: 3px;
                background: #296aff;

                &.swiper-pagination-bullet-active {
                    background: $color-primary;
                }
            }
        }
    }

    .tabs-wrapper {
        margin-top: 30px;

        .tabs-header-wrapper {
            padding: 2px;
            background: linear-gradient(to top, #296aff, #bacfff);
            border-radius: 12px;
            margin-bottom: 20px;
            height: 74px;

            &.is-fixed {
                background: transparent;

                .tabs-header-content {
                    position: fixed;
                    z-index: 99;
                    left: 0;
                    right: 0;
                    top: 68px;
                    display: flex;
                    justify-content: center;
                    background-color: $color-white;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);

                    .tabs-header {
                        width: $view-width;
                        padding: 0px;

                        .tabs-nav {
                            color: $font-color;
                            font-weight: normal;
                            padding: 0;

                            & + .tabs-nav {
                                margin-left: 80px;
                            }

                            &.active {
                                color: $color-primary;
                                background: transparent;
                                font-weight: bold;
                            }

                            &.tabs-switch-item2 {
                                .update-status {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
        }

        .tabs-header {
            display: flex;
            padding: 10px 30px 0;
            border-radius: 12px 12px 10px 10px;
            background: #ffffff;

            .tabs-nav {
                font-size: 20px;
                padding: 0 45px 10px;
                line-height: 50px;
                border-radius: 6px;
                cursor: pointer;

                &.active {
                    font-weight: bold;
                    color: $color-primary;
                    background: url(../assets/activity/singleDetail/tabs-active.png) no-repeat center 42px/52px 13px;
                }

                &.tabs-switch-item2 {
                    position: relative;
                    .update-status {
                        position: absolute;
                        right: 0;
                        top: 8px;
                        transform: translateX(50%);
                        padding: 0 8px;
                        line-height: 20px;
                        color: #ffa000;
                        font-size: 12px;
                        background: #fff2db;
                        border-radius: 12px 0px 10px 0px;
                    }
                }
            }

            @import './mini-code-popup';

            .share-mini-code-container {
                margin-left: auto;
                // align-self: center;

                .share-mini-code-trigger {
                    line-height: 50px;
                    font-size: 18px;
                    color: $font-color;
                    background: url(../assets/common/wechat-gray.png) no-repeat left/20px 20px;
                    &:hover {
                        color: #296aff;
                        background: url(../assets/common/wechat-info.png) no-repeat left/20px 20px;
                    }
                }
            }
        }

        .tabs-pane {
            display: none;

            &.advanced-template {
                // 高级模板class
                background: transparent;
            }

            &.show {
                display: block;
            }
        }

        @mixin common-wrapper {
            .not-style-wrapper {
                padding: 17px 20px 30px;
            }
            .common-wrapper {
                display: flex;
                flex-direction: column;
                position: relative;
                padding: 17px 20px 30px;
                margin-bottom: 75px;
                background: #abc9fe;
                border-radius: 12px;

                &:last-child {
                    margin-bottom: 0;
                }

                .common-title-content {
                    position: absolute;
                    left: 50%;
                    top: 0;
                    z-index: 10;
                    transform: translate(-50%, -17px) skewX(-20deg);
                    height: 34px;

                    &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        top: 0;
                        left: -8px;
                        width: 4px;
                        height: 34px;
                        background: linear-gradient(to top, #ffa000, #ff5a00);
                    }

                    &::after {
                        content: '';
                        display: block;
                        width: 100%;
                        height: 100%;
                        position: relative;
                        z-index: -1;
                        background: linear-gradient(90deg, #2cc6af, #f7faff);
                        transform: translate(10px, -44px);
                    }

                    .common-title {
                        position: relative;
                        z-index: 1;
                        line-height: 34px;
                        color: $color-white;
                        padding: 0 30px;
                        background: linear-gradient(90deg, #2a55e3, #1fbdae);
                        span {
                            display: block;
                            font-weight: bold;
                            font-size: 24px;
                            transform: skewX(20deg);
                        }
                    }
                }

                .common-content--border {
                    background: linear-gradient(to bottom, #296bff, #e3ecff);
                    padding: 2px;
                    border-radius: 12px;

                    & + .common-content--border {
                        margin-top: 30px;
                    }
                }

                .common-content {
                    position: relative;
                    background: linear-gradient(to bottom, rgba(208, 223, 255, 0.6), rgba(240, 248, 255, 0.6)), $color-white;
                    border-radius: 10px 10px 12px 12px;
                    padding: 20px 30px;
                    font-size: 18px;
                    line-height: 2;
                    white-space: wrap;

                    p {
                        font-size: 18px;
                    }

                    &::after {
                        content: '';
                        display: block;
                        position: absolute;
                        top: 0;
                        right: 0;
                        width: 186px;
                        height: 270px;
                        background: url(../assets/activity/singleDetail/triangle.png) no-repeat center / cover;
                    }

                    .common-content-tittle {
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 20px;
                        margin-bottom: 10px;
                        font-weight: bold;

                        &::before {
                            content: '';
                            display: block;
                            width: 16px;
                            height: 16px;
                            background: linear-gradient(45deg, #ffa000, #ff5a00);
                            border-radius: 2px;
                            transform: rotate(45deg);
                        }

                        &::after {
                            content: '';
                            display: block;
                            width: 16px;
                            height: 16px;
                            background: linear-gradient(45deg, #ffa000, #ff5a00);
                            border-radius: 2px;
                            transform: rotate(45deg);
                        }

                        .common-content-sort {
                            padding-left: 8px;
                        }

                        .common-content-name {
                            padding-right: 8px;
                        }
                    }
                }
            }
        }

        .detail-pane {
            font-size: 18px;
            padding-top: 22px;
            background-color: #fff;
            border-radius: 12px;
            @include common-wrapper;

            p {
                font-size: 18px;
            }
        }

        .company-pane {
            .filter-wrapper {
                margin-bottom: 24px;
                border-radius: 12px;
                background-color: rgba($color: $color-white, $alpha: 1);
                padding: 40px 20px 20px;

                .filter-list {
                    display: flex;
                    align-items: baseline;

                    & + .filter-list {
                        margin-top: 5px;
                    }

                    .label {
                        flex-shrink: 0;
                        font-size: 16px;
                        font-weight: bold;
                        margin-right: 21px;
                        position: relative;
                        .pop {
                            position: absolute;
                            left: 0;
                            top: -34px;
                            width: 241px;
                            height: 23px;
                            line-height: 23px;
                            background: #e5edff;
                            border-radius: 4px;
                            font-size: 12px;
                            font-weight: 400;
                            color: #5c5c5c;
                            text-align: center;
                            &::before {
                                position: absolute;
                                bottom: -6px;
                                left: 26px;
                                // transform: translateX(-50%);
                                width: 0;
                                height: 0;
                                content: '';
                                border-left: 6px solid transparent;
                                border-right: 6px solid transparent;
                                border-top: 6px solid #e5edff; /* 设置三角形的颜色 */
                            }
                        }
                    }

                    .value {
                        flex-grow: 1;
                        display: flex;
                        flex-wrap: wrap;

                        .item {
                            margin-right: 6px;
                            margin-bottom: 8px;
                            padding: 0px 9px;
                            line-height: 26px;
                            font-size: 14px;
                            cursor: pointer;

                            &[checked='true'] {
                                border-radius: 4px;
                                color: $color-primary;
                                background: #e5edff;

                                &::after {
                                    content: '';
                                    display: inline-block;
                                    margin-left: 3px;
                                    width: 10px;
                                    height: 10px;
                                    background: url(../assets/activity/common/close-primary.png) no-repeat center / contain;
                                }
                            }

                            &.is-special {
                                &::after {
                                    display: none;
                                }
                            }

                            &:hover {
                                color: $color-primary;
                            }
                        }

                        .el-select {
                            margin-right: 16px;
                            width: 110px;
                            overflow: hidden;

                            &:last-of-type {
                                margin-right: 0;
                            }

                            .el-select__tags {
                                display: none;
                            }
                        }

                        .filter-item {
                            &.is-checked {
                                width: 124px;

                                &:hover {
                                    .el-input__suffix-inner {
                                        display: none;
                                    }

                                    .filter-major-clear {
                                        display: block;
                                    }
                                }
                            }

                            .filter-major-clear {
                                display: none;
                            }
                        }

                        .company-type-item {
                            position: relative;
                            display: flex;
                            align-items: center;
                            background-color: #f8f8f8;

                            .el-select,
                            .el-input {
                                height: 100%;
                            }

                            .select-trigger {
                                height: 100%;
                                width: calc(100%);
                            }

                            &.is-select {
                                background-color: #e5edff;

                                .label {
                                    color: $color-primary;
                                }

                                .el-select {
                                    width: 124px;
                                }
                            }

                            input {
                                position: absolute;
                                left: 0;
                                right: 0;
                                height: 100%;
                                z-index: 1;
                                opacity: 0;
                            }

                            .el-input__suffix {
                                z-index: 2;
                            }

                            .label {
                                color: $font-color;
                                white-space: nowrap;
                                font-size: 14px;
                                font-weight: normal;
                                position: absolute;
                                left: 0;
                                right: 35px;
                                padding: 0 0 0 10px;
                                z-index: 0;
                            }
                        }

                        .el-input__inner {
                            @include utils-ellipsis;
                            padding-left: 10px;
                            color: $color-primary;
                            font-size: 14px;
                            background-color: #f8f8f8;
                            border: none;
                            box-sizing: border-box;

                            &::placeholder {
                                color: $font-color;
                            }
                        }

                        .is-checked {
                            .el-input__inner {
                                background-color: #e5edff;
                            }

                            .el-input__icon {
                                color: $color-primary;
                            }
                        }
                    }

                    .filter-aside {
                        flex-shrink: 0;

                        .clear-all {
                            cursor: pointer;
                            padding-left: 20px;
                            background: url(../assets/activity/common/delete.png) no-repeat left/14px 14px;
                        }
                    }
                }

                .tips {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $color-primary;
                    margin-top: 18px;

                    &::before,
                    &::after {
                        content: '';
                        width: 60px;
                        height: 1px;
                        background: #333333;
                        opacity: 0.5;
                        margin: 0 15px;
                    }
                }
            }

            .select-dialog > .el-input {
                display: none;
            }

            .company-wrapper {
                .company-data {
                    padding: 30px 20px;
                    background: rgba(255, 255, 255, 0.4);
                    border: 2px solid rgba(255, 255, 255, 0.24);
                    margin-bottom: 30px;
                    display: grid;
                    grid-template-columns: repeat(3, 373px);
                    gap: 20px;
                    border-radius: 12px;

                    .item {
                        height: 156px;
                        box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                        background: #ffffff;
                        position: relative;
                        border-radius: 12px;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        .diamond {
                            display: none;
                        }

                        .top {
                            border-radius: 12px 12px 0 0;
                            display: flex;
                            padding: 17px 20px 0;
                        }

                        .logo {
                            flex-shrink: 0;
                            width: 56px;
                            height: 56px;
                            border-radius: 50%;
                            margin-right: 10px;
                            overflow: hidden;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: contain;
                                display: block;
                            }
                        }

                        .grow {
                            flex-grow: 1;
                            overflow: hidden;
                        }

                        .name {
                            @include utils-ellipsis;
                            font-weight: bold;
                            font-size: 16px;
                            line-height: 28px;
                        }

                        .type {
                            @include utils-ellipsis;
                            margin-top: 7px;
                            color: $font-color-basic;
                        }

                        .middle {
                            @include utils-ellipsis;
                            font-size: 12px;
                            padding: 11px 17px 17px;
                            color: $font-color;

                            span {
                                color: $font-color-label;
                            }
                        }

                        .bottom {
                            @include utils-ellipsis;
                            padding-right: 20px;
                            color: $font-color-basic;
                            border-radius: 0 0 12px 12px;
                            line-height: 42px;
                            background: linear-gradient(90deg, #e6eeff, #f5fdff);
                            padding-left: 41px;

                            $icons: praise, announcement, site;

                            @each $name in $icons {
                                &.#{$name} {
                                    background: url(../assets/activity/common/#{$name}.png) no-repeat 20px/13px 13px, linear-gradient(90deg, #e6eeff, #f5fdff);
                                }
                            }

                            &.major {
                                padding-left: 32px;
                                position: relative;

                                &::before {
                                    content: '';
                                    display: inline-block;
                                    position: absolute;
                                    top: 19px;
                                    left: 20px;
                                    width: 6px;
                                    height: 6px;
                                    background: $color-primary;
                                    border-radius: 50%;
                                }
                            }
                        }

                        .aside {
                            position: absolute;
                            padding-left: 40px;
                            right: 20px;
                            bottom: 9px;
                            background: linear-gradient(to right, rgba(247, 248, 249, 0) 0%, rgba(247, 248, 249, 1) 41px, #f7f8f9 100%);

                            .btn {
                                border-radius: 12px;
                                width: 72px;
                                line-height: 24px;
                                padding: 0;
                                border: none;

                                &.apply {
                                    color: $color-white;
                                    background: linear-gradient(90deg, #296aff, #29a7ff);
                                }

                                &.applied {
                                    display: none;
                                    color: $color-white;
                                    background: #d7d7d7;
                                }
                            }
                        }

                        &:hover {
                            transform: translateY(-5px);

                            box-shadow: 0 0 0 1px $color-primary, $box-shadow-default;
                        }

                        &.is-apply {
                            &:hover {
                                .applied {
                                    display: block;
                                }
                            }
                        }

                        &.is-top {
                            background: url(../assets/activity/singleDetail/diamond-bg.png) no-repeat right 10px top 47px/107px 82px, $color-white;

                            .diamond {
                                display: block;
                                position: absolute;
                                width: 22px;
                                height: 17px;
                                top: -6px;
                                right: -9px;
                                background: url(../assets/activity/singleDetail/diamond.png) no-repeat center/ 22px 17px;
                            }
                        }
                    }
                }

                .tips {
                    margin-bottom: 20px;
                    text-align: center;
                    color: $font-color-label;
                }

                .empty {
                    display: none;
                    border-radius: 12px;
                    padding: 285px 15px 60px;
                    text-align: center;
                    border: 2px solid rgba(255, 255, 255, 0.24);
                    background: url(../assets/activity/common/empty.png) no-repeat center 61px/266px 194px, rgba(255, 255, 255, 1);
                }
            }
        }

        .apply-way-pane,
        .review-pane {
            font-size: 18px;
            padding-top: 22px;
            background-color: #fff;
            border-radius: 12px;
            @include common-wrapper;

            p {
                font-size: 18px;
            }
        }
    }

    .connect-company {
        position: relative;
        overflow: hidden;
        padding: 35px 60px 25px 20px;
        margin-top: 30px;
        border-radius: 12px;
        background-color: #fff;

        .hd-tag {
            position: absolute;
            left: 0;
            top: 0;
            height: 25px;
            padding: 0 14px;
            line-height: 25px;
            font-weight: bold;
            font-size: 18px;
            color: #ffffff;
            background: linear-gradient(90deg, #2a6bff, #25c9e6);
            border-radius: 12px 0px 12px 0px;
        }

        .company-wrapper {
            .company-list {
                flex-shrink: 0;
                display: block;
                width: 219px;
                height: 197px;
                margin-right: 16px;
                background: #ffffff;
                border-radius: 12px;
                border: 1px solid #d5e1fd;

                &:hover {
                    border: 1px solid #296aff;
                    box-shadow: 0px 3px 10px 0px rgba(51, 51, 51, 0.12);
                    transform: translateY(-5px);
                    transition: all 0.3s ease;
                    .title {
                        color: #296bff !important;
                    }
                }

                .hd {
                    position: relative;
                    .tag {
                        position: absolute;
                        left: -1px;
                        top: -1px;
                        height: 20px;
                        line-height: 20px;
                        padding: 0 7px 0 17px;
                        font-size: 12px;
                        border-radius: 8px 0px 8px 0px;

                        &::before {
                            position: absolute;
                            left: 7px;
                            top: 50%;
                            content: '';
                            width: 6px;
                            height: 6px;
                            border-radius: 50%;
                            transform: translateY(-50%);
                        }

                        $font-colors: (-1, #ffa000, #fff3e0), (1, #5386ff, #e5edff), (2, #fa635c, #fff4f3), (3, #ffa000, #fff3e0), (4, #999, #f8f8f8);

                        @each $index, $color, $bg in $font-colors {
                            &.tag#{$index} {
                                color: $color;
                                background: $bg;
                                &::before {
                                    position: absolute;
                                    top: 50%;
                                    content: '';
                                    width: 6px;
                                    height: 6px;
                                    border-radius: 50%;
                                    transform: translateY(-50%);
                                    background-color: $color;
                                }
                            }
                        }
                    }

                    .cover {
                        width: 100%;
                        height: 100px;
                        border-radius: 12px 12px 0 0;
                    }
                    .hot {
                        position: absolute;
                        left: 0;
                        bottom: 0;
                        height: 18px;
                        padding: 0 8px 0 21px;
                        line-height: 18px;
                        font-size: 12px;
                        color: #ffffff;

                        background: url(../assets/activity/common/hot.png) no-repeat 8px center/8px 9px, rgba($color: $font-color, $alpha: 0.6);
                        border-radius: 7px 9px 9px 0px;
                        opacity: 0.6;
                    }
                }

                .bd {
                    padding: 10px;
                    .title {
                        min-height: 44px;
                        line-height: 22px;
                        font-weight: bold;
                        color: #333333;
                        line-height: 22px;
                        @include text-ellipsis;
                    }
                    .desc {
                        display: flex;
                        margin-top: 10px;
                        color: rgba($color: $font-color, $alpha: 0.6);
                        .date {
                            margin-right: 10px;
                        }
                        .address {
                            flex: 1;
                            text-align: right;
                            @include utils-ellipsis;
                        }
                    }
                }
            }
        }

        /* 包裹内容的容器 */
        .scroll-wrapper {
            position: relative;
            overflow: hidden; /* 隐藏默认滚动条 */
        }

        /* 内容区域 */
        .scroll-content {
            width: 100%;
            padding: 10px 0 16px 0;
            margin-right: 44px;
            display: inline-flex;
        }

        /* 自定义的横向滚动条 */
        .scrollbar {
            width: 200px; /* 设置滚动条宽度为200px */
            height: 8px;
            position: absolute;
            bottom: 15px;
            left: 0;
            left: 50%; /* 设置滚动条在页面居中 */
            border-radius: 4px;
            transform: translateX(-50%); /* 确保滚动条居中 */
            background-color: #c6d5ff;
            cursor: pointer;
        }

        .scrollbar-thumb {
            position: absolute;
            height: 100%;
            width: 50%; /* 滑块宽度为滚动条宽度的一半 */
            background-color: #5f8ff9;
            border-radius: 4px;
        }

        .more {
            position: absolute;
            top: 0;
            right: 0;
            width: 44px;
            height: 100%;
            background: #ffffff;
            box-shadow: 0px 7px 6px 0px rgba(51, 51, 51, 0.08);
            border-radius: 0px 12px 12px 0px;
            a {
                height: 100%;
                padding: 0 14px;
                flex-wrap: wrap;
                display: flex;
                align-items: center;
                justify-content: center;
                color: rgba($color: $font-color, $alpha: 0.6);
                span {
                    display: block;
                    transform: rotate(90deg);
                }
            }
        }
    }
}

#welfareDialog {
    display: none;

    .el-dialog {
        width: 568px;
        background: linear-gradient(180deg, #dfe9fe, #ffffff, #ffffff);
        border-radius: 10px;
        padding: 0 8px;

        &__header {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 30px 30px 20px;
        }

        &__body {
            color: $font-color;
            line-height: 1.5;
            padding: 0 23px 0 30px;
            height: 280px;
            max-height: 280px;
            overflow-y: auto;

            &::-webkit-scrollbar-thumb {
                background: #5f8ff9;
            }

            &::-webkit-scrollbar {
                width: 6px;
            }

            .welfare-wrapper {
                & + .welfare-wrapper {
                    margin-top: 15px;
                }

                .welfare-title {
                    position: relative;
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 8px;
                    line-height: 1.5;
                    padding-left: 13px;

                    &::before {
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 9px;
                        width: 6px;
                        height: 6px;
                        background: $color-primary;
                        border-radius: 50%;
                    }
                }

                .welfare-content {
                    line-height: 21px;
                }
            }
        }

        &__footer {
            padding: 15px 30px 30px;
            display: flex;
            justify-content: center;

            .close-dialog {
                font-size: 16px;
                font-weight: bold;
                width: 320px;
                height: 40px;
                background: $liner-gradient-primary;
                border-radius: 4px;
                text-align: center;
                border: none;
                color: $color-white;
            }
        }
    }
}

.fixed-apply-enter {
    padding-top: 13px;
    &.applied {
        // 已报名样式
        .apply-enter-person {
            padding-top: 50px;
            color: rgba($color: #333, $alpha: 0.8);
            background: url(../assets/activity/apply-enter/applied.png) no-repeat center top/49px 45px;
        }
    }
}

.share-mini-code-container {
    .share-mini-code-popup {
        top: calc(100% - 13px) !important;
    }
}
