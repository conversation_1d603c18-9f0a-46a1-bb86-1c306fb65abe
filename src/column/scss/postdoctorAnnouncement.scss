@import './variablesPostdoctor';

@import './postdoctor/announcement-and-job';

.main-container {
    .content-wrapper {
        .left-content {
            .list-content {
                .list {
                    .top {
                        .release-time {
                            flex-shrink: 0;
                            color: $font-color-basic;
                            padding-left: 18px;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/time.png) no-repeat left/12px 12px;
                            margin-left: 22px;
                        }
                    }

                    .bright-spot {
                        color: #9f6d1a;
                        @include utils-ellipsis;
                        max-width: 592px;
                        padding-left: 33px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/praise-fill.png) no-repeat left/26px 22px;
                        margin-bottom: 8px;
                    }

                    .bottom {
                        display: flex;
                        align-items: center;
                        height: 24px;
                        color: $font-color;

                        .detail {
                            flex-grow: 1;
                            display: flex;
                            align-items: center;

                            .major {
                                font-size: 12px;
                                border-radius: 4px;
                                background-color: #f8f8f8;
                                padding: 0 7px;
                                line-height: 24px;
                                margin-right: 8px;
                                max-width: 220px;
                                @include utils-ellipsis;
                            }

                            .primary {
                                color: $color-primary;
                            }
                        }

                        .address {
                            flex-shrink: 0;
                            color: $font-color-basic;
                            padding-left: 19px;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/address.png) no-repeat left/12px 12px;
                        }
                    }

                    .view {
                        right: 20px;
                        top: calc(50% - 16px);
                    }

                    &.is-offline {
                        .top {
                            .title {
                                color: $font-color;

                                .tag-content {
                                    .tag {
                                        &.offline {
                                            display: inline-flex;
                                        }
                                    }
                                }
                            }
                        }

                        .title,
                        .bright-spot,
                        .detail {
                            opacity: 0.6;
                        }

                        .release-time,
                        .address {
                            opacity: 0.8;
                        }
                    }

                    &:hover {
                        border-color: $color-primary;

                        .release-time,
                        .address {
                            opacity: 0;
                        }
                    }
                }
            }
        }

        .aside {
            .ranking {
                background: url(//img.gaoxiaojob.com/uploads/boshihou/announcement/announcement-title.png) no-repeat 16px 16px/152px 34px, linear-gradient(180deg, #fce6e1 0, #fff 70px, #fff 100%);

                .list {
                    .name {
                        @include utils-ellipsis-lines(2, 22px, 14px);
                    }
                }
            }
        }
    }
}
