@use 'sass:meta';
@import './variables';

$border-radius: 10px;
$border-color: #ebebeb;
$rich-line-height: 1.4;
$column-width: 486px;

body {
    background-color: #efefef;
    font-size: 14px;
    color: $font-color;

    div {
        word-break: break-all;
    }
}

.activity-form-main {
    width: $view-width;
    margin: 20px auto 60px;
    border-radius: $border-radius;
    overflow: hidden;
    background-color: #fff;
    padding: 0;

    .banner {
        width: 100%;
        height: 300px;
        background-size: cover;
        background-repeat: no-repeat;
    }

    .form-container {
        padding: 0 60px;

        .form-wrapper {
            margin-bottom: 16px;
        }

        .form-content {
            padding-top: 30px;
        }
        .el-radio {
            --el-radio-input-height: 16px;
            --el-radio-input-width: 16px;
            --el-radio-font-color: $font-color;
            display: inline-flex;
            align-items: center;
            line-height: 24px;
            height: 24px;
            margin-right: 0;

            & + .el-radio {
                margin-left: 30px;
            }
        }

        .el-radio__input {
            &.is-checked {
                .el-radio__inner {
                    background-color: transparent;

                    &::after {
                        width: 8px;
                        height: 8px;
                        background-color: $color-primary;
                    }
                }
            }
        }

        .el-checkbox {
            --el-checkbox-input-height: 16px;
            --el-checkbox-input-width: 16px;
            --el-checkbox-font-color: $font-color;
            display: flex;
            align-items: center;
            line-height: 24px;
            height: 24px;
            margin-right: 0;
        }

        .el-checkbox__label {
            flex-grow: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .el-checkbox__inner::after {
            top: 2px;
            left: 5px;
        }
    }

    .common-title {
        font-size: 18px;
        font-weight: bold;
        display: flex;
        padding: 13px 0;
        line-height: 28px;
        border-bottom: 1px solid $border-color;

        &::before {
            content: '';
            width: 4px;
            height: 18px;
            background: $color-primary;
            border-radius: 2px;
            margin-right: 12px;
            transform: translateY(5px);
        }

        .required {
            color: $font-color-salary;
        }

        .required-tips {
            font-size: 14px;
            font-weight: normal;
            line-height: 28px;
            flex-shrink: 0;
        }
    }

    .form-header {
        .title {
            font-size: 22px;
            font-weight: bold;
            text-align: center;
            word-break: break-all;
            color: $color-primary;
            padding: 36px 2em 25px;
        }

        .introduction {
            line-height: $rich-line-height;

            p {
                font-size: 14px;
            }
        }
    }

    @mixin checkbox-group {
        .checkbox-group {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            align-items: flex-start;

            .checkbox-list {
                width: $column-width;
                margin-bottom: 22px;
            }

            .checkbox-label {
                display: flex;
                font-size: 16px;
                align-items: center;
                height: 100%;
            }

            .el-checkbox {
                max-width: 100%;
            }
        }
    }

    .intention-choose {
        .form-content {
            padding-top: 15px;
        }

        .tips {
            margin-bottom: 30px;
            word-break: break-all;
        }

        @include checkbox-group;

        .el-checkbox {
            height: auto;
            line-height: 24px;
            display: flex;
            align-items: flex-start;
            color: inherit !important;
            overflow: hidden;

            .el-checkbox__input {
                transform: translateY(4px);
            }

            .el-checkbox__label {
                display: flex;
                align-items: center;
                color: inherit !important;
                white-space: normal;
            }
        }

        .name {
            line-height: 24px;
        }

        .attended {
            flex-shrink: 0;
            align-self: flex-start;
            font-size: 12px;
            line-height: 18px;
            padding: 0 9px;
            color: $color-primary;
            border-radius: 20px;
            border: 1px solid $color-primary;
            margin-left: 6px;
            transform: translateY(2px);
        }

        .bottom {
            padding-left: 26px;
            font-size: 14px;
            color: $font-color-basic;
        }

        .detail {
            display: inline-block;
            line-height: 16px;
            margin-top: 10px;
            margin-bottom: 3px;
            border-bottom: 1px solid $font-color-basic;

            &:hover {
                color: $color-primary;
                border-color: $color-primary;
            }
        }

        .other {
            line-height: 20px;
            margin-top: 8px;
            word-break: break-all;
        }
    }

    .user-info {
        .form-content {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            padding-top: 20px;

            .el-form-item {
                width: $column-width;
                margin-bottom: 23px;

                .el-form-item__label {
                    padding-bottom: 12px;
                    line-height: 1.01;
                    color: $font-color;
                    font-weight: normal;
                }

                .el-form-item__content {
                    width: 340px;
                }
            }
        }

        .education-wrapper {
            .study-time {
                display: flex;

                .line {
                    width: 8px;
                    height: 1px;
                    background-color: #6d6d6d;
                    margin: 0 6px;
                    margin-top: 15px;
                }

                .el-form-item,
                .el-form-item__content {
                    width: auto;
                }
            }
        }

        .switch-user-info {
            text-align: center;
            padding-bottom: 42px;

            button {
                cursor: pointer;
                font-size: 16px;
                color: $color-primary;
                border: none;
                padding: 0;
                background-color: transparent;
                display: inline-flex;
                align-items: center;

                &::after {
                    content: '';
                    margin-left: 6px;
                    width: 10px;
                    height: 7px;
                    background: url(../assets/icon/filter-arrow.png) no-repeat right/10px 7px;
                }

                &.reverse {
                    &::after {
                        transform: rotate(180deg);
                    }
                }
            }
        }
    }

    .intention-type,
    .channel {
        @include checkbox-group;

        .checkbox-list {
            margin-bottom: 22px;
        }
    }

    .accept-recommend {
        .el-radio {
            width: $column-width;

            & + .el-radio {
                margin-left: 0px;
            }
        }
    }

    .upload-resume {
        .form-content {
            padding-top: 18px;

            .tips {
                margin-bottom: 20px;
                word-break: break-all;
            }
        }

        .file-select {
            display: flex;
            margin-bottom: 20px;

            .upload {
                background-color: #fff3e0;
                width: 280px;
                height: 40px;
                padding: 0;
                border: none;
                font-size: 14px;
                color: $color-primary;

                span {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .upload-icon {
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    background: url('../assets/icon/upload.png') no-repeat center/contain;
                    margin-left: 8px;
                }
            }

            .el-popover {
                padding: 18px 10px 20px 0px;
            }

            .exist-file {
                max-height: 128px;
                overflow-y: scroll;
                padding-left: 20px;
                padding-right: 10px;

                .el-checkbox {
                    margin-bottom: 8px;
                }
            }

            .exist-bottom {
                display: flex;
                justify-content: center;
                padding-top: 12px;

                .el-button {
                    width: 68px;

                    & + .el-button {
                        margin-left: 20px;
                    }
                }
            }

            .select {
                display: inline-flex;
                width: 280px;
                height: 40px;
                border: 1px solid $color-primary;
                border-radius: 4px;
                justify-content: center;
                align-items: center;
                color: $color-primary;
                margin-left: 20px;
                cursor: pointer;

                .select-icon {
                    width: 12px;
                    height: 12px;
                    background: url('../assets/icon/filter-arrow.png') no-repeat center/contain;
                    margin-left: 8px;
                }
            }
        }

        .file-upload-tips {
            color: $font-color-basic;
            font-size: 12px;
            margin-top: 7px;
        }

        .file-list {
            width: 480px;

            .list {
                margin-top: 12px;
                background-color: #fafafc;
                display: flex;
                align-items: center;
                padding-right: 10px;

                i {
                    flex-shrink: 0;
                }
            }

            .file-type {
                width: 32px;
                height: 32px;

                &.pdf {
                    background: url('../assets/icon/pdf.png') no-repeat center/22px, #ffeceb;
                }
                &.doc,
                &.docx {
                    background: url('../assets/icon/word.png') no-repeat center/22px, #eff2ff;
                }
            }

            .name {
                flex-grow: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                padding: 0 13px;
                line-height: 32px;
                height: 32px;
            }

            .remove {
                width: 20px;
                height: 20px;
                background: url('../assets/icon/delete.png') no-repeat center/20px, #eff2ff;
                cursor: pointer;
            }
        }
    }

    .wechat-wrapper,
    .reference-wrapper {
        .wechat,
        .reference {
            margin-top: 10px;
            margin-bottom: 30px;
            width: 340px;
        }
    }

    .employment-wrapper,
    .working-hours-wrapper {
        .el-radio-group {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }
        .el-radio {
            width: 486px;
            margin-top: 22px;

            & + .el-radio {
                margin-left: 0;
            }

            &:nth-child(1),
            &:nth-child(2) {
                margin-top: 0;
            }
        }

        .tips {
            margin-bottom: 10px;
        }
    }

    .postdoctor-wrapper {
        .form-item {
            width: 340px;
        }
        .tips {
            margin-bottom: 10px;
        }
    }

    .end-wrapper {
        padding: 24px 0px 0;
        border-top: 1px solid $border-color;
        line-height: $rich-line-height;
    }

    .submit-wrapper {
        display: flex;
        align-items: center;
        flex-direction: column;
        padding-top: 38px;
        padding-bottom: 38px;

        .submit {
            width: 200px;
            line-height: 40px;
            padding: 0;
            border: none;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: bold;
        }

        .submit-tips {
            font-size: 12px;
            padding-left: 15px;
            background: url('../assets/icon/warning.png') no-repeat left/11px;
        }
    }

    .form-item-checkbox {
        margin-top: -11px;
        .el-form-item__content {
            line-height: 1;
            height: auto;

            .checkbox-content {
                display: inline-flex;

                & + .checkbox-content {
                    margin-left: 20px;
                }

                .checkbox-label {
                    padding-left: 9px;
                    font-size: 14px;
                }
            }
        }
    }

    .non-login {
        display: flex;
        justify-content: center;
        padding: 40px 0;

        .el-button {
            padding-left: 71px;
            padding-right: 71px;
        }
    }
}
