@import './variablesPostdoctor';

@import './postdoctor/announcement-and-job';

.main-container {
    .content-wrapper {
        .left-content {
            .list-content {
                .list {
                    .top {
                        display: flex;
                        justify-content: space-between;
                        margin-bottom: 12px;

                        .title {
                            max-width: 685px;
                            @include utils-ellipsis;

                            &:hover {
                                color: $color-primary;
                            }
                        }
                        .salary {
                            flex-shrink: 0;
                            color: $font-color-salary;
                            font-size: 16px;
                            font-weight: bold;
                        }
                    }

                    .detail {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;

                        .tag {
                            color: $font-color;
                            font-size: 12px;
                            line-height: 20px;
                            background-color: #f3f8fd;
                            padding: 0 6px;
                            border-radius: 4px;
                            margin-right: 6px;
                            max-width: 208px;
                            @include utils-ellipsis;
                        }

                        .bright-spot {
                            font-size: 12px;
                            color: #9f6d1a;
                            @include utils-ellipsis;
                            max-width: 345px;
                            padding-left: 23px;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/praise-fill.png) no-repeat left/20px 17px;
                        }
                    }

                    .announcement {
                        display: inline-block;
                        padding-left: 18px;
                        background: url(//img.gaoxiaojob.com/uploads/boshihou/common/announcement-icon.png) no-repeat left/13px 13px;
                        @include utils-ellipsis;
                        max-width: 633px;
                        margin-bottom: 15px;
                        color: $font-color-basic;
                    }

                    .bottom {
                        display: flex;
                        align-items: center;
                        height: 24px;
                        color: $font-color;
                        justify-content: space-between;

                        .company {
                            display: flex;
                            align-items: center;

                            .name {
                                color: $font-color;
                                max-width: 352px;
                                @include utils-ellipsis;
                            }

                            .logo {
                                width: 24px;
                                height: 24px;
                                object-fit: contain;
                                border-radius: 50%;
                                margin-right: 7px;
                            }

                            .type {
                                font-size: 12px;
                                color: $font-color-basic;
                                margin-left: 9px;
                            }
                        }

                        .release-time {
                            flex-shrink: 0;
                            color: $font-color-basic;
                            padding-left: 19px;
                            background: url(//img.gaoxiaojob.com/uploads/boshihou/common/time.png) no-repeat left/12px 12px;
                        }
                    }

                    .view {
                        right: 20px;
                        top: initial;
                        bottom: 20px;
                    }

                    &.is-offline {
                        .top {
                            .title {
                                color: $font-color;

                                .tag-content {
                                    .tag {
                                        &.offline {
                                            display: inline-flex;
                                        }
                                    }
                                }
                            }
                        }

                        .title,
                        .detail,
                        .salary {
                            opacity: 0.6;
                        }

                        .company {
                            color: $font-color-label;

                            a,
                            .name,
                            .type {
                                color: $font-color-label;
                            }
                        }

                        .logo {
                            opacity: 0.6;
                        }

                        .announcement,
                        .release-time {
                            opacity: 0.75;
                        }
                    }

                    &:not(.is-offline) {
                        .announcement:hover {
                            color: $color-primary;
                        }

                        .company:hover {
                            .name,
                            .type {
                                color: $color-primary;
                            }
                        }
                    }

                    &:hover {
                        border-color: $color-primary;

                        .release-time {
                            opacity: 0;
                        }
                    }
                }
            }
        }

        .aside {
            .ranking {
                background: url(//img.gaoxiaojob.com/uploads/boshihou/job/job-title.png) no-repeat 16px 16px/152px 34px, linear-gradient(180deg, #ffe9c7 0, #fff 70px, #fff 100%);

                .list {
                    margin-bottom: 29px;

                    &:last-child {
                        margin-bottom: 15px;
                    }

                    a {
                        font-size: 12px;
                        flex-grow: 1;
                        overflow: hidden;
                    }

                    .name {
                        font-size: 14px;
                        margin-bottom: 9px;
                        @include utils-ellipsis;
                    }

                    .info {
                        color: $font-color-basic;
                        margin-bottom: 8px;
                        @include utils-ellipsis;

                        .salary {
                            color: $font-color-salary;
                        }
                    }

                    .bottom {
                        display: flex;
                        justify-content: space-between;
                        color: $font-color;

                        .company-name {
                            max-width: 144px;
                            @include utils-ellipsis;
                        }

                        .address {
                            max-width: 60px;
                            text-align: right;
                            color: $font-color-basic;
                            @include utils-ellipsis;
                        }
                    }
                }
            }
        }
    }
}
