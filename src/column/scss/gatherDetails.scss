// 每日汇总详情页

@import './variables';

@import './common/sideBar.scss';
@import './gather.scss';

$border-color-contact: #efefef;
$border-color-bottom: #ededed;

.article-body {
    padding: 40px 30px 0;
    .pagination {
        margin-top: 25px;
        padding-bottom: 26px;
        .prev,
        .next {
            display: flex;
        }
        .prev {
            margin-bottom: 12px;
        }
        .label {
            opacity: 0.8;
        }
    }
    @import './common/component/qrCode';
}

.article-content {
    padding: 40px 0 20px;
    text-align: center;

    .article-title {
        font-size: 18px;
    }

    .article-information {
        padding: 20px 0;
        border-bottom: 1px solid $border-color-bottom;
    }

    .detailed {
        padding-right: 20px;
        font-size: 13px;
        color: $font-color-label;
    }

    .detailed:last-child {
        padding-right: 0;
    }
}

.content {
    * {
        line-height: 2;
    }

    h3 {
        color: $color-primary;
    }

    a {
        font-weight: bold;
    }
}

.article {
    border-bottom: 1px solid $border-color-bottom;
}

.information-exhibition {
    padding: 20px;
    background-color: $color-white;

    .eliminate {
        padding-top: 0;
        border-top: none;
    }

    .school-picturepicture {
        margin-bottom: 20px;

        a {
            width: 305px;
            height: 120px;
        }
    }

    .national-examination {
        padding: 13px 10px 20px 10px;

        .main-link {
            width: 285px;
            height: 167px;
        }
    }
}
