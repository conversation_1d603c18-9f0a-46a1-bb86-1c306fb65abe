@import './variables';

.payment-dialog-alert-template {
    .el-dialog {
        width: 568px !important;
    }

    .el-dialog__header {
        padding: 0;

        .el-dialog__title {
            font-size: 20px;
            font-weight: 700;
        }
    }

    .el-dialog__body {
        padding: 0;
        .el-form-item__label {
            color: $font-color-basic;
            font-size: 14px;
        }
        .sussess-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            div {
                text-align: left;
                line-height: 2;
                color: $font-color;
            }
            img {
                width: 100px;
                height: 100px;
                margin: 20px auto;
            }
            .el-button {
                width: 200px;
            }
        }
    }
    .wechat-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 14px;
        .paymentTips {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            color: #333;
            width: 100%;
            border-radius: 10px 10px 0 0;
            font-size: 22px;
            .online-tips {
                text-align: center;
                width: 168px;
                line-height: 24px;
                margin-top: 15px;
                margin-bottom: -5px;
                font-size: 12px;
                color: $color-primary;
                background-color: #fff3e0;
            }
        }
        .payment-text {
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            padding: 0 30px 30px 30px;
            .qr-code {
                margin: 20px 0 25px 0;
                position: relative;
                width: 150px;
                height: 150px;
                img {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                    // border: 1px solid #ebebeb;
                    // border-radius: 10px;
                }
                .logo {
                    position: absolute;
                    top: 55px;
                    left: 55px;
                    width: 40px;
                    height: 40px;
                    border: none;
                    border-radius: 4px;
                    object-fit: contain;
                }
            }
            .text-mid {
                font-size: 14px;
                color: #333;
                margin-top: 10px;
                padding: 0 55px;
                display: flex;
                flex-direction: column;
                text-align: left;
                span {
                    line-height: 1.5;
                }
            }
        }

        .wechat-footer {
            width: 300px;
        }
    }

    .el-dialog__footer {
        padding: 0;

        .el-button {
            & + .el-button {
                margin-left: 20px;
            }
        }
    }
}
