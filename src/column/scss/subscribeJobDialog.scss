@import './variables';
.subscribe-dialog-template {
    .el-dialog {
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 1200px;
        min-width: 960px;
    }

    .education {
        align-items: center;
    }

    .el-form {
        padding: 30px 10% 0 10%;
    }

    .el-dialog__body {
        padding-bottom: 0;
    }

    .el-select--large {
        width: 100%;
    }

    .el-dialog__header {
        padding: 0;
    }

    .el-input__inner {
        // 特殊处理
        height: 35px !important;
    }

    .el-input__suffix {
        top: -2px;
    }

    .el-form-item__label {
        font-weight: normal;
        color: $font-color;
    }

    .el-dialog__footer {
        padding: 0 0 40px;
    }

    .title {
        color: $font-color;
        text-align: center;
        font-weight: bold;
        font-size: 20px;
        margin: 25px 0;
    }

    .tips {
        color: $font-color;
        text-align: center;
        font-size: 14px;
    }

    .email {
        display: flex;
        align-items: center;

        .el-input__inner {
            border: none;
            border-bottom: 1px solid #d7dae2;
            border-radius: 0;
            height: 19px;
            margin-left: 6px;
            padding: 0;
        }
    }

    .wechat-tips {
        display: flex;
        text-align: left;
        color: $font-color-basic;
        font-size: 12px;
        line-height: 14px;
        margin-bottom: 5px;

        &::before {
            content: '';
            width: 14px;
            margin-right: 5px;
            background: url('../assets/icon/warning.png') no-repeat center / 14px;
        }

        .follow {
            position: relative;
            cursor: pointer;
            margin-left: 8px;
            color: $color-primary;
            padding-right: 3px;

            &::after {
                content: '_________';
                position: absolute;
                top: 3px;
                left: 0;
            }
        }
    }

    .wechat-dialog {
        display: block;
        max-width: 568px;
        min-width: 568px;

        .el-dialog__header {
            position: relative;
        }

        .wechat-title {
            color: $font-color;
            text-align: left;
            font-size: 18px;
            padding-left: 16px;
            &::before {
                content: '';
                position: absolute;
                top: 31px;
                left: 20px;
                width: 4px;
                height: 17px;
                background-color: $color-primary;
                border-radius: 2px;
            }
        }
    }

    .relogin-dialog {
        max-width: 568px;
        min-width: 568px;
        .relogin-title {
            margin-bottom: 30px;
            font-size: 18px;
            text-align: center;
        }

        .relogin-content {
            color: $font-color-basic;
            text-align: center;
            line-height: 1.8;
            padding: 0 40px;

            p {
                font-size: 14px;
            }

            .el-button {
                margin-top: 40px;
                width: 300px;
            }
        }

        .footer {
            text-align: center;
            margin: 30px 0;
        }
    }

    .wechat-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 20px;

        .qr-code {
            position: relative;
            margin-bottom: 15px;
            width: 180px;
            height: 180px;

            img {
                max-width: 100%;
                max-height: 100%;
                object-fit: contain;
                border: 1px solid #ebebeb;
                border-radius: 10px;
            }
            .logo {
                position: absolute;
                top: 70px;
                left: 70px;
                width: 40px;
                height: 40px;
                border: none;
                border-radius: 4px;
                object-fit: contain;
            }
        }

        .wechat-footer {
            width: 300px;

            .tips {
                display: flex;
                flex-direction: column;
                align-items: center;
                line-height: 1.5;
                font-size: 13px;
                color: $font-color;
                .online-tips {
                    text-align: center;
                    width: 168px;
                    line-height: 24px;
                    margin-top: 15px;
                    margin-bottom: 35px;
                    font-size: 12px;
                    color: $color-primary;
                    background-color: #fff3e0;
                }
            }
        }
    }

    .select-dialog {
        & > .el-input {
            display: none;
        }
        .el-dialog {
            align-items: flex-start;
        }

        .el-dialog__header {
            width: 100%;
        }
        .el-dialog__body {
            width: 100%;
        }
    }
}
