.job-search-box {
    border-bottom: 1px solid #efefef;
    padding-bottom: 20px;
    .area {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }
    .area .jobs {
        max-width: 170px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        text-align: left;
        cursor: pointer;
    }
    #educationa {
        .is-checked {
            .el-input__inner {
                color: #ffa000;
                border-color: #ffa000;
            }
            .el-icon-arrow-up {
                color: #ffa000;
            }
        }
        .el-input__inner:focus {
            color: #ffa000 !important;
        }
        .el-input__inner {
            padding-left: 14px;
            .el-icon-arrow-down:focus {
                color: #ffa000 !important;
            }
        }
        .el-input__suffix {
            right: 9px;
            .el-icon-circle-close {
                color: #ffa000;
            }
        }
        .el-input__inner:hover {
            border-color: #dcdfe6;
        }
    }
    .search-keyword {
        position: relative;
        display: block;
        height: 40px;
        box-shadow: 0 0 0 1px #ffa000;
        overflow: hidden;
        .el-input__prefix {
            left: 1px;
            .el-input__icon {
                position: absolute;
                top: 0;
                right: 0;
                bottom: 0;
                cursor: pointer;
            }

            .el-icon-circle-close {
                display: none;
                color: #ffa000;
            }
        }
        &.is-checked {
            .el-input__inner {
                border-color: #ffa000;
            }
            .el-input__prefix {
                .job-category {
                    color: #ffa000;
                }

                .el-icon-arrow-down {
                    color: #ffa000;
                }

                &:hover {
                    .el-icon-arrow-down {
                        display: none;
                    }

                    .el-icon-circle-close {
                        display: block;
                        color: #ffa000;
                    }
                }
            }
        }
    }
    .area .job-category {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: block;
        padding: 0 15px;
        width: 160px;
        font-size: 14px;
        text-align: left;
        cursor: pointer;
    }

    .job-search {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 28px;
        background: url(../assets/home/<USER>/20px;

        a {
            width: 81px;
            height: 14px;
            background: url(../assets/home/<USER>/ contain;
        }
    }

    .select-dialog > .el-input {
        display: none;
    }
}
