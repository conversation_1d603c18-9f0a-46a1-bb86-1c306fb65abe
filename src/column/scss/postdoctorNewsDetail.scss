@import './variablesPostdoctor';
@import './common/currentLocation';

$border-color-contact: #efefef;
$border-color-title: #efefef;

.main-container {
    margin: 0 auto;
    width: 1200px;
    &::after {
        display: block;
        content: '';
        clear: both;
    }

    // .current-location {
    //     display: flex;
    //     margin: 10px 0;
    //     padding: 10px 0 10px 20px;
    //     background-color: #fff;
    //     color: #b61f22;
    //     font-size: 16px;
    //     .current {
    //         padding-left: 20px;
    //         background-image: url('../assets/home/<USER>');
    //         background-repeat: no-repeat;
    //         background-position: left center;
    //         background-size: contain;
    //     }
    // }

    .left {
        width: 845px;
        float: left;
        margin-right: 10px;
        padding: 0 30px;
        background-color: #fff;

        .article-top {
            text-align: center;

            .title {
                font-size: 18px;
                font-weight: bold;
                margin: 30px 0 12px;
                padding: 0 15px;
            }

            .info {
                color: rgba(51, 51, 51, 0.6);
                text-align: center;
                padding-bottom: 20px;
                border-bottom: 1px solid #ededed;

                small {
                    font-size: 13px;
                }

                .origin {
                    margin: 0 20px;
                    display: inline-block;
                }

                a {
                    color: $color-primary;
                    display: inline-block;
                    font-size: 13px;
                    margin-left: 20px;
                    border-bottom: 1px solid $color-primary;
                }
            }
        }

        .content {
            .abstract {
                font-weight: normal;
                padding: 7px 14px 7px;
                border-radius: 4px;
                font-size: 14px;
                background-color: #ededed;
                margin: 20px 0;
                line-height: 2;
            }

            .article {
                @import './common/wangeditor';

                a {
                    display: revert;
                    color: revert;

                    &:hover {
                        color: revert;
                        text-decoration: revert;
                    }
                }
            }

            .origin {
                padding: 30px 0;
                border-bottom: 1px solid #ededed;
            }

            .qr {
                padding-top: 25px;
                text-align: center;
                color: $color-primary;
                font-weight: bold;

                img {
                    width: 150px;
                    height: 150px;
                    margin: 20px auto;
                }
            }
        }
    }

    .right {
        float: right;
        width: 345px;

        .recommend {
            background-color: $color-white;
            padding: 20px;
            width: 100%;
            overflow: hidden;

            .common-title {
                position: relative;
                padding: 10px 0 18px 0;
                border-top: 1px solid $border-color-title;

                &::after {
                    content: '';
                    position: absolute;
                    top: -1px;
                    left: 0;
                    width: 72px;
                    height: 3px;
                    background-color: $color-primary;
                }

                h2 {
                    color: $color-primary;
                    font-size: 18px;
                }

                .subtitle {
                    padding-left: 28px;
                    background: url('../assets/home/<USER>') no-repeat center left / 24px;
                }
            }

            ul {
                a {
                    position: relative;
                    padding: 4px 0 4px 9px;
                    display: flex;
                    align-items: center;

                    &::before {
                        position: absolute;
                        content: '';
                        left: 0;
                        top: 12px;
                        display: block;
                        width: 4px;
                        height: 4px;
                        border-radius: 50%;
                        background-color: $color-primary;
                        margin-right: 5px;
                        flex-shrink: 0;
                    }

                    .title {
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        padding-right: 9px;
                    }
                }
            }
        }
    }

    .share-custom {
        display: flex;
        justify-content: space-between;
        padding: 20px 0;
        border-top: 1px solid #ededed;

        .bshare-custom {
            a {
                display: inline-block;
            }
        }

        .custom-more-btn {
            background: url(http://static.bshare.cn/frame/images/logos/s4/more-style-addthis.png) no-repeat;
        }
    }
}
