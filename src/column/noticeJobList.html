<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>公告_职位列表</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/list.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main">
                <!-- 模板二添加 template-two，模板三添加 template-three -->
                <div class="detail-container template-two">
                    <!-- 普通模板 -->
                    <div style="display: none" class="detail-header-template">
                        <div class="detail-header">
                            <div class="detail-header-container">
                                <div class="breadcrumb">
                                    位置：
                                    <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞
                                    <a>北京大学珠海分校2021年度招聘公告</a>
                                </div>

                                <div class="main">
                                    <section>
                                        <div class="title">
                                            <h1>中国人民解放军空军特色医学中心2022年博士后研究人员招收简章招收简章招收简章</h1>
                                        </div>

                                        <div class="info">
                                            共计<span class="color-primary">12</span>个岗位，招 <span class="color-primary">29</span>人

                                            <a class="view-relation" href="">查看公告详情</a>
                                        </div>

                                        <div id="boonsTemplate" class="boons">
                                            <span class="boon">全勤奖</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">五险一金</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">绩效奖金</span>

                                            <el-popover placement="bottom" :width="330" trigger="hover" v-cloak>
                                                <template #reference>
                                                    <i class="el-icon boon-more">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path
                                                                fill="currentColor"
                                                                d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                            ></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <span class="boon">全勤奖</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">五险一金</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">绩效奖金</span>
                                            </el-popover>
                                        </div>
                                    </section>

                                    <aside>
                                        <div class="detail-button">
                                            <div class="share-mini-code-container">
                                                <div class="share-mini-code-trigger share-mini-code-trigger--primary">分享</div>

                                                <div class="share-mini-code-popup">
                                                    <div class="share-mini-code">
                                                        <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                    </div>
                                                    <div class="share-mini-code-title">微信扫一扫</div>
                                                    <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                                </div>
                                            </div>
                                            <!-- <div class="el-button--collect">收藏</div> -->
                                            <div class="el-button--collect collected">收藏</div>
                                            <div class="el-button--analyse see-heat">公告热度</div>
                                        </div>
                                        <div class="aside-bottom">
                                            <button class="el-button el-button--info is-plain is-disabled" disabled>
                                                <span>已下线</span>
                                            </button>
                                            <a href="www.baidu.com" target="_blank" class="company-home el-button el-button--primary">
                                                <span>单位主页</span>
                                            </a>
                                        </div>
                                    </aside>

                                    <a class="el-button el-button--primary view-button" href="">
                                        <span>查看公告详情</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级模板一 -->
                    <div style="display: none" class="detail-header-template-one">
                        <!-- 自定义背景图需添加 is-custom-background 类名 -->
                        <div class="detail-header">
                            <div class="detail-header-container">
                                <div class="breadcrumb">
                                    位置：
                                    <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞
                                    <a>北京大学珠海分校2021年度招聘公告</a>
                                </div>

                                <div class="main">
                                    <section>
                                        <h1 class="title">
                                            北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学
                                        </h1>

                                        <div class="info">
                                            <span class="establishment-tag superior">部分编制</span>
                                            <div>共计<span class="color-primary">4</span>个岗位，招 <span class="color-primary">29</span>人</div>

                                            <ul>
                                                <li>发布时间：2021-06-25</li>
                                                <li>截止时间：详见正文</li>
                                                <li>工作地点：四川省成都市</li>
                                            </ul>

                                            <a class="view-relation" href="">查看公告详情</a>
                                        </div>

                                        <div class="tips" id="tipsTemplate">
                                            <span class="establishment-tag superior">部分编制</span>
                                            <span class="boon">全勤奖</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">五险一金</span>
                                            <span class="boon">节日福利</span>
                                            <span class="boon">绩效奖金</span>

                                            <el-popover placement="bottom" :width="330" trigger="hover" v-cloak>
                                                <template #reference>
                                                    <i class="el-icon boon-more">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path
                                                                fill="currentColor"
                                                                d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                            ></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <span class="boon">全勤奖</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">五险一金</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">绩效奖金</span>
                                            </el-popover>
                                        </div>
                                    </section>

                                    <aside>
                                        <div class="detail-button">
                                            <div class="share-mini-code-container">
                                                <div class="share-mini-code-trigger share-mini-code-trigger--primary font-color-white">分享</div>

                                                <div class="share-mini-code-popup">
                                                    <div class="share-mini-code">
                                                        <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                    </div>
                                                    <div class="share-mini-code-title">微信扫一扫</div>
                                                    <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                                </div>
                                            </div>

                                            <!-- <div class="el-button--collect">收藏</div> -->
                                            <div class="el-button--collect collected">收藏</div>
                                            <div class="el-button--analyse see-heat">公告热度</div>
                                        </div>
                                        <div class="aside-bottom">
                                            <button class="offline">已下线</button>
                                            <a href="www.baidu.com" target="_blank" class="company-home el-button el-button--primary">
                                                <span>单位主页</span>
                                            </a>
                                        </div>
                                    </aside>

                                    <a class="el-button el-button--primary view-button" href="">
                                        <span>查看公告详情</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级模板二 -->
                    <div style="display: none" class="detail-header-template-two">
                        <div class="detail-header">
                            <div class="detail-header-container">
                                <div class="breadcrumb">
                                    位置：
                                    <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞
                                    <a>北京大学珠海分校2021年度招聘公告</a>
                                </div>

                                <div class="main">
                                    <div class="detail">
                                        <h1
                                            class="title"
                                            title="北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学"
                                        >
                                            北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学
                                        </h1>
                                        <div class="detail-bottom">
                                            <a class="el-button view-button" href=""> 查看公告详情 </a>
                                            <div class="share-mini-code-container">
                                                <div class="share-mini-code-trigger">分享</div>

                                                <div class="share-mini-code-popup">
                                                    <div class="share-mini-code">
                                                        <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                    </div>
                                                    <div class="share-mini-code-title">微信扫一扫</div>
                                                    <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                                </div>
                                            </div>

                                            <div class="el-button--collect">收藏</div>
                                            <div class="el-button--analyse see-heat">公告热度</div>
                                        </div>
                                    </div>

                                    <a class="company-home" href="" target="_blank">单位主页</a>
                                </div>
                            </div>

                            <div class="wave"></div>

                            <div class="detail-header-container--fixed">
                                <div class="detail">
                                    <div
                                        class="title"
                                        title="北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学"
                                    >
                                        北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学
                                    </div>
                                    <div class="bottom">
                                        <span class="tag">部分有编</span>
                                        <div>招<span class="bold">29</span>人，共计<span class="bold">212</span>个岗位</div>
                                        <a class="el-button view-button" href="">查看公告详情</a>
                                    </div>
                                </div>
                                <div class="aside">
                                    <div class="detail-button">
                                        <div class="share-mini-code-container">
                                            <div class="share-mini-code-trigger">分享</div>

                                            <div class="share-mini-code-popup">
                                                <div class="share-mini-code">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                </div>
                                                <div class="share-mini-code-title">微信扫一扫</div>
                                                <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                            </div>
                                        </div>

                                        <div class="el-button--collect">收藏</div>
                                        <div class="el-button--analyse see-heat">公告热度</div>
                                    </div>
                                    <div class="bottom">
                                        <button class="offline">已下线</button>
                                        <a class="company-home" href="" target="_blank">单位主页</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 高级模板三 -->
                    <div style="display: block" class="detail-header-template-three">
                        <div class="detail-header">
                            <div class="detail-header-container">
                                <div class="breadcrumb">
                                    位置：
                                    <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞
                                    <a>北京大学珠海分校2021年度招聘公告</a>
                                </div>

                                <div class="main">
                                    <div class="detail">
                                        <h1
                                            class="title"
                                            title="北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学"
                                        >
                                            北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学
                                        </h1>

                                        <div class="job-info">
                                            <div class="amount">共计 <span class="color-main">4</span> 个岗位，招 <span class="color-main">4</span> 人</div>

                                            <div class="other">
                                                <span class="time">发布时间：2021-06-25 | 截止时间：详见正文 |</span>

                                                <span class="address">四川，湖南，北京，湖南，北京，湖南，北京，湖南，北京</span>
                                            </div>
                                        </div>

                                        <div class="welfare-tag" id="welfareTag">
                                            <span class="establishment-tag boon">部分编制</span>
                                            <span class="boon">全勤奖</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">五险一金</span>
                                            <span class="boon">节日福利</span>
                                            <span class="boon">绩效奖金</span>

                                            <el-popover placement="bottom" :width="307" trigger="hover" popper-class="template-two-welfare-popover" v-cloak>
                                                <template #reference>
                                                    <i class="el-icon boon-more">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                            <path
                                                                fill="currentColor"
                                                                d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                            ></path>
                                                        </svg>
                                                    </i>
                                                </template>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">五险一金</span>
                                                <span class="boon">带薪年假</span>
                                                <span class="boon">绩效奖金</span>
                                            </el-popover>
                                        </div>

                                        <div class="detail-bottom">
                                            <div class="share-mini-code-container">
                                                <div class="share-mini-code-trigger share-mini-code-trigger--info">分享</div>

                                                <div class="share-mini-code-popup">
                                                    <div class="share-mini-code">
                                                        <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                    </div>
                                                    <div class="share-mini-code-title">微信扫一扫</div>
                                                    <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                                </div>
                                            </div>
                                            <div class="el-button--collect">收藏</div>
                                            <div class="el-button--analyse see-heat">公告热度</div>
                                            <a class="company-home" href="" target="_blank">单位主页</a>
                                        </div>
                                    </div>

                                    <div class="cover">
                                        <img src="https://img.gaoxiaojob.com/uploads/announcement/detail-header-3.png" alt="" />
                                    </div>

                                    <a class="view-job" href="">查看公告详情</a>
                                </div>
                            </div>

                            <div class="detail-header-container--fixed">
                                <div class="detail">
                                    <div
                                        class="title"
                                        title="北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学"
                                    >
                                        北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学
                                    </div>
                                    <div class="bottom">
                                        <span class="tag">部分有编</span>
                                        <div>招<span class="bold">29</span>人，共计<span class="bold">212</span>个岗位</div>
                                        <a class="view-button" href="">查看公告详情</a>
                                    </div>
                                </div>
                                <div class="aside">
                                    <div class="detail-button">
                                        <div class="share-mini-code-container">
                                            <div class="share-mini-code-trigger share-mini-code-trigger--info font-color">分享</div>

                                            <div class="share-mini-code-popup">
                                                <div class="share-mini-code">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                </div>
                                                <div class="share-mini-code-title">微信扫一扫</div>
                                                <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                            </div>
                                        </div>

                                        <div class="el-button--collect">收藏</div>
                                        <div class="el-button--analyse see-heat">公告热度</div>
                                    </div>
                                    <div class="bottom">
                                        <button class="offline">已下线</button>
                                        <a class="company-home" href="" target="_blank">单位主页</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <section>
                            <div class="tips">
                                声明：本站部分公告与职位内容由本站根据官方招聘公告进行整理编辑。由于用人单位需求专业、学历学位、资格条件、备注内容等内容情况复杂且有变化可能，是否符合招聘条件以用人单位公告为准或请联系用人单位确认。本站整理编辑的职位信息仅供求职者参考，如因此造成的损失本站不承担任何责任！
                            </div>

                            <div class="filter" id="filterTemplate">
                                <el-select :class="{'is-select': jobType}" v-model="jobType" @change="(value) => handleFilter(value, 'jobType')" :clearable="true" placeholder="职位类型">
                                    <el-option v-for="item in jobCategoryList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': educationId}" v-model="educationId" @change="(value) => handleFilter(value, 'educationId')" :clearable="true" placeholder="学历">
                                    <el-option v-for="item in educationOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>

                                <el-select :class="{'is-select': majorId}" v-model="majorId" @change="(value) => handleFilter(value, 'majorId')" :clearable="true" placeholder="专业">
                                    <el-option v-for="item in majorOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                                </el-select>
                            </div>

                            <div>
                                <table class="el-table">
                                    <tr>
                                        <th class="el-col el-col-10">职位信息</th>
                                        <th class="el-col el-col-8">需求专业</th>
                                        <th class="el-col el-col-6">操作</th>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测哲学-测试超出测测试超出测测试超出测</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled" disabled>
                                                <span>已申请</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button" data-id="12">
                                                <span>收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled offline" disabled>
                                                <span>已下线</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary job-apply-button" data-id="12">
                                                <span>申请职位</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测哲学-测试超出测测试超出测测试超出测</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled" disabled>
                                                <span>已申请</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button" data-id="12">
                                                <span>收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled offline" disabled>
                                                <span>已下线</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary job-apply-button" data-id="12">
                                                <span>申请职位</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测哲学-测试超出测测试超出测测试超出测</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled" disabled>
                                                <span>已申请</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button" data-id="12">
                                                <span>收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled offline" disabled>
                                                <span>已下线</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary job-apply-button" data-id="12">
                                                <span>申请职位</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测哲学-测试超出测测试超出测测试超出测</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled" disabled>
                                                <span>已申请</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button" data-id="12">
                                                <span>收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary is-disabled offline" disabled>
                                                <span>已下线</span>
                                            </button>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="data-name el-col el-col-10">
                                            <a href="" target="_blank">职位名称-测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示测试超出显示</a>

                                            <p>
                                                <strong class="salary">20K-30K/月</strong>
                                                <span>广东-广州</span><span>硕士</span><span>全职</span>
                                            </p>
                                        </td>

                                        <td class="data-major el-col el-col-8">
                                            <span>哲学-测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出测试超出</span>
                                        </td>

                                        <td class="data-operate el-col el-col-6">
                                            <button class="el-button el-button--collect job-collect-button collected" data-id="12">
                                                <span>已收藏</span>
                                            </button>

                                            <button class="el-button el-button--primary job-apply-button" data-id="12">
                                                <span>申请职位</span>
                                            </button>
                                        </td>
                                    </tr>
                                </table>

                                <div id="paginationTemplate">
                                    <el-pagination
                                        background
                                        :layout="'total, sizes, prev, pager, next, jumper'"
                                        :current-page="page"
                                        :page-size="pageSize"
                                        :total="count"
                                        :total="count"
                                        @size-change="(val) => handleFilter(val, 'pageSize')"
                                        @current-change="(val) => handleFilter(val, 'page')"
                                    >
                                    </el-pagination>
                                </div>
                            </div>

                            <div class="no-data">暂无相关职位，请修改条件试试</div>
                        </section>

                        <aside>
                            <div class="unit">
                                <a class="unit-name" href="" target="_blank">
                                    <img class="logo" src="http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png" alt="" />

                                    <div class="name">
                                        <h3>湖南师范大学</h3>
                                        <h5>Hunan Normal University</h5>
                                    </div>
                                </a>

                                <div class="unit-tips category">教育/培训/院校</div>
                                <div class="unit-tips number">1000人以上</div>
                                <div class="unit-tips type">科研院所</div>

                                <div class="unit-data el-row">
                                    <a class="el-col el-col-8" href="" target="_blank">
                                        <strong class="color-primary">100</strong>
                                        <span>在招公告</span>
                                    </a>
                                    <a class="el-col el-col-8" href="" target="_blank">
                                        <strong class="color-primary">66</strong>
                                        <span>在招职位</span>
                                    </a>
                                    <span class="el-col el-col-8 to-home">
                                        <a href="" target="_blank">
                                            <span>单位主页</span>
                                        </a>
                                    </span>
                                </div>
                            </div>

                            <div id="recommendContainer">
                                <!-- <div class="recommend is-notice">
                                <div class="title">
                                    <h5>专享推荐</h5>
                                </div>

                                <div id="detailRecommend" class="swiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="recommend-list">
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="recommend-list">
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <div class="tips">
                                                            <i>硕士研究生</i>
                                                            <i>18个职位</i>
                                                        </div>

                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                            <script>
                                $(function () {
                                    new Swiper("#detailRecommend", {
                                        autoplay: {
                                            delay: 7000,
                                            pauseOnMouseEnter: true,
                                            disableOnInteraction: false
                                        },
                                        delay: 7000,
                                        loop: true,
                                        pagination: {
                                            el: ".swiper-pagination",
                                            clickable: true
                                        }
                                    })
                                })
                            </script> -->
                            </div>
                        </aside>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                <a href="/" target="_blank">资质证明</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                    <a href="/" target="_blank">粤ICP备13048400号-2</a>
                    粤公网安备：
                    <a href="/" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="/" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <script src="./js/detailService.js"></script>
        <script src="./lib/popper/popper.min.js"></script>
        <script>
            $(function () {
                // 合作
                Vue.createApp({}).use(ElementPlus).mount('#tipsTemplate')
                // 非合作
                Vue.createApp({}).use(ElementPlus).mount('#boonsTemplate')

                Vue.createApp({}).use(ElementPlus).mount('#welfareTag')

                function updQuery(data) {
                    const base = window.location.href
                    const hasParams = base.indexOf('?') > -1
                    const baseUrl = base + (hasParams ? '' : '?')
                    const keys = Object.keys(data)

                    const result = keys.reduce((previous, current) => {
                        const value = data[current]
                        const isValid = value === null ? false : value !== ''
                        const isExist = new RegExp(`(${current}=[^&]*)`).test(previous)
                        const keyValue = isExist ? RegExp.$1 : ''

                        if (isValid) {
                            if (isExist) {
                                previous = previous.replace(keyValue, `${current}=${value}`)
                            } else {
                                previous += `&${current}=${encodeURIComponent(value)}`
                            }
                        } else {
                            previous = previous.replace(new RegExp(`&?${keyValue}`), '')
                        }

                        return previous.replace(/\?&/, '?')
                    }, baseUrl)

                    return result.replace(/\?$/, '')
                }

                const paginationOptions = {
                    data() {
                        return {
                            page: 1,
                            pageSize: 20,
                            count: 100
                        }
                    },
                    mounted() {
                        const search = window.location.search
                        const [mark, query] = search.split('?')
                        const getParams = (key, feature = '') => {
                            if (new RegExp(`${key}=([^&]*)`).test(query)) {
                                const value = decodeURIComponent(RegExp.$1)
                                const toNumber = (val) => (/^-?\d+$/.test(val) ? val * 1 : val)
                                if (feature) {
                                    this[key] = value.split(feature).map((item) => toNumber(item))
                                } else {
                                    this[key] = toNumber(value)
                                }
                            }
                        }

                        if (query) {
                            getParams('page')
                            getParams('pageSize')
                        }
                    },
                    methods: {
                        handleFilter(val, key) {
                            const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                            if (key !== 'page') {
                                query.page = 1
                            }
                            window.location.href = updQuery(query)
                        }
                    }
                }

                Vue.createApp(paginationOptions)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#paginationTemplate')

                const filterOptions = {
                    data() {
                        return {
                            jobType: '',
                            majorId: '',
                            educationId: '',

                            jobCategoryList: [{ label: '技术', value: '1' }],

                            majorOptions: [{ label: '技术', value: '1' }],

                            educationOptions: [
                                { label: '不限', value: '1' },
                                { label: '大专', value: '2' },
                                { label: '本科', value: '3' },
                                { label: '博士', value: '4' }
                            ]
                        }
                    },
                    mounted() {
                        const search = window.location.search
                        const [mark, query] = search.split('?')
                        const getParams = (key, feature = '') => {
                            if (new RegExp(`${key}=([^&]*)`).test(query)) {
                                const value = decodeURIComponent(RegExp.$1)
                                if (feature) {
                                    this[key] = value.split(feature)
                                } else {
                                    this[key] = value
                                }
                            }
                        }

                        if (query) {
                            getParams('jobType')
                            getParams('majorId')
                            getParams('educationId')
                        }
                    },
                    methods: {
                        handleFilter(val, key) {
                            const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                            if (key !== 'page') {
                                query.page = 1
                            }
                            window.location.href = updQuery(query)
                        }
                    }
                }

                Vue.createApp(filterOptions).use(ElementPlus).mount('#filterTemplate')

                var id = '12'
                var $headerCollectButtons = $('.detail-header .el-button--collect')
                var $jobCollectButtons = $('.job-collect-button')
                var $jobApplyButtons = $('.job-apply-button')
                var $noticeJobList = $('.notice-job-list-btn')
                var $recommendContainer = $('#recommendContainer')
                var seeHeat = $('.see-heat')

                var params = {
                    apiPull: '/api/person/announcement/check-generate-report',
                    apiCreate: '/api/person/announcement/create-report',
                    param: { announcementId: '<?=$info["id"]?>' }
                }

                seeHeat.on('click', function () {
                    window.globalComponents.PromptDialogComponent.pull(params)
                })

                httpPost('/api/person/announcement/get-recommend-list', { id: id }).then(function (data) {
                    if (data.html) {
                        $recommendContainer.html(data.html)
                    }
                })

                $headerCollectButtons.on('click', function () {
                    var isCollected = $headerCollectButtons.hasClass('collected')

                    httpPost('/api/person/announcement/collect', { announcementId: id }).then(function () {
                        $headerCollectButtons.toggleClass('collected').text(isCollected ? '收藏' : '已收藏')
                    })
                })

                $noticeJobList.on('click', function () {
                    window.globalComponents.applyDialogComponent.announcementApply(id)
                })

                $jobCollectButtons.on('click', function () {
                    var $this = $(this)
                    var jobId = $this.attr('data-id')
                    var isCollected = $this.hasClass('collected')

                    httpPost('/api/person/job/collect', { jobId: jobId }).then(function () {
                        $this
                            .toggleClass('collected')
                            .find('span')
                            .text(isCollected ? '收藏' : '已收藏')
                    })
                })

                $jobApplyButtons.on('click', function () {
                    var $this = $(this)
                    var jobId = $this.attr('data-id')

                    window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                        $this.prop('disabled', true).addClass('is-disabled').find('span').text('已申请')
                    })
                })

                // 职位需求专业气泡
                !(function majorPopper() {
                    const el = document.createElement('div')
                    el.className = 'major-popper'
                    el.id = 'custom-popper'
                    el.innerHTML = '<span class="arrow"></span><div class="content"></div>'
                    document.body.appendChild(el)

                    let popover = null

                    $('body').on('mouseover', '.data-major', function () {
                        const $major = $(this)
                        const $majorSpan = $major.find('span')
                        const isOverflow = $majorSpan.height() > $major.height()

                        if (isOverflow) {
                            const $popperEl = $('.major-popper')

                            $('.major-popper .content').text($majorSpan.text())

                            popover = Popper.createPopper($major[0], $popperEl[0], {
                                placement: 'top',
                                modifiers: [
                                    {
                                        name: 'offset',
                                        options: {
                                            offset: [0, -10]
                                        }
                                    }
                                ]
                            })
                        }
                    })

                    $('body').on('mouseout', '.data-major', function () {
                        if (popover) popover.destroy()
                    })
                })()
            })
        </script>
    </body>
</html>
