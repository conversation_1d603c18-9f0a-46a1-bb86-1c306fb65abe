<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>专场详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/commonActivity.css" />
        <link rel="stylesheet" href="./css/activitySpecial.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <link rel="stylesheet" href="./lib/animate/animate.min.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./lib/wow/wow.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <div class="activity-container">
            <header class="activity-header-container">
                <div class="activity-header-content">
                    <div class="activity-menu-content">
                        <a href="/" class="logo"></a>
                        <a href="/" class="activity-menu-item active">招聘会首页</a>
                        <a href="/" class="activity-menu-item">全国巡回现场招聘会</a>
                        <a href="/" class="activity-menu-item">2025RPO线上面试会</a>
                        <a href="/" class="activity-menu-item">出海引才</a>
                        <a href="/" class="activity-menu-item">归国活动</a>
                    </div>
                    <div class="activity-nav-content">
                        <a href="" class="activity-nav-item">合作申请</a>

                        <button class="activity-login-btn open-login-dialog">人才登录</button>
                        <!-- <a class="person-avatar" href="" target="_blank">
                            <img src="https://picsum.photos/seed/picsum/36/36" alt="" />
                        </a> -->
                    </div>
                </div>
            </header>

            <!-- 默认模板：is-default -->
            <div class="main-container">
                <div class="banner-wrapper">
                    <div class="banner-box">
                        <img src="./assets/activity/special/banner.jpg" alt="" />
                    </div>

                    <div class="detail-wrapper view-content">
                        <!-- <div class="status await">待举办</div> -->
                        <!-- <div class="status await-start">即将开始</div> -->
                        <div class="status start">进行中</div>
                        <!-- <div class="status ended">已结束</div> -->

                        <div class="detail">
                            <div class="tag">
                                <span class="item offline">线下</span>
                                <span class="item">校园场</span>
                                <span class="item">交通补贴</span>
                                <span class="item">签到福利</span>
                            </div>
                            <h1 class="title">博士·博士后人才全国巡回完整展示完整展示完整展示完整展示展示换行博士·博士后人才全国巡回完整展示完整展示完整展示完整展示展示换行</h1>
                            <div class="detail-item organization">
                                <div class="label">活动组织：</div>
                                <div class="value" title="高校人才网高校人才网">
                                    高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网高校人才网...
                                </div>
                            </div>
                            <div class="detail-item time">
                                <div class="label">活动时间：</div>
                                <div class="value">2025年3月10日~2025年4月15日</div>
                            </div>
                            <div class="detail-item address">
                                <div class="label">活动地点：</div>
                                <div class="value">重庆,成都,西安,哈尔滨，长春，北京，济南，南京，上海，杭州，长沙，广州，广州，深圳</div>
                            </div>
                            <div class="detail-item type">
                                <div class="label">活动系列：</div>
                                <div class="value">
                                    全国巡回现场招聘会全国巡回现场招聘会全国巡回现场招聘会全国巡回现场招聘会全国巡回 全国巡回现场招聘会全国巡回现场招聘会全国巡回现场招聘会全国巡回现场招聘会全国巡回
                                </div>
                            </div>

                            <div class="welfare has-detail">
                                <div class="value">提供年薪80-100万、.</div>
                                <div class="arrow open-dialog-btn" data-target="#welfareDialog">点击查看更多</div>
                            </div>
                        </div>

                        <aside class="aside">
                            <div class="update-status">持续更新中</div>
                            <div class="item">
                                <div class="amount">128</div>
                                <span>活动场次</span>
                            </div>
                            <div class="item">
                                <div class="amount">100+</div>
                                <span>参会单位</span>
                            </div>
                        </aside>
                    </div>
                </div>

                <div class="tabs-wrapper">
                    <div class="tabs-header-wrapper view-content" id="page-change-fixed">
                        <div class="tabs-header-content">
                            <div class="tabs-header">
                                <div class="tabs-nav tabs-switch-item" open-pane="detail-pane">活动详情</div>
                                <div class="tabs-nav tabs-switch-item active" open-pane="session-pane">场次安排</div>
                                <div class="tabs-nav tabs-switch-item" open-pane="company-pane">参会单位</div>
                                <div class="tabs-nav tabs-switch-item" open-pane="apply-way-pane">参会方式</div>
                                <div class="tabs-nav tabs-switch-item" open-pane="review-pane">往届回顾</div>

                                <div class="share-mini-code-container">
                                    <div class="share-mini-code-trigger">微信扫码分享</div>

                                    <div class="share-mini-code-popup">
                                        <div class="share-mini-code">
                                            <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                        </div>
                                        <div class="share-mini-code-title">微信扫一扫</div>
                                        <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tabs-content view-content">
                        <!-- 活动详情 -->
                        <div class="tabs-pane detail-pane">
                            <div class="common-wrapper wow animate__animated animate__fadeInUp animated">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>活动背景</span>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="common-wrapper wow animate__animated animate__fadeInUp animated">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>参会对象</span>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <p style="text-indent: 24pt; mso-char-indent-count: 2; text-autospace: ideograph-numeric; mso-pagination: none; line-height: 200%">
                                            <span style="font-family: 微软雅黑; line-height: 200%; font-size: 12pt"
                                                >为积极响应国家“稳就业、促发展”的战略部署，助力毕业生实现高质量就业，高校人才网联合100+优质单位，推出“智汇未来 ·
                                                职引前程”高校人才网2025年度春季线上招聘。</span
                                            ><span style="font-family: 微软雅黑; line-height: 200%; font-size: 12pt"></span>
                                        </p>

                                        <div class="common-content-tittle">
                                            <div class="common-content-sort">01</div>
                                            <div class="common-content-name">参会人才</div>
                                        </div>

                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <div class="common-content-tittle">
                                            <div class="common-content-sort">02</div>
                                            <div class="common-content-name">参会人才</div>
                                        </div>

                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="common-wrapper wow animate__animated animate__fadeInUp animated">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>参会方式</span>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <div class="common-content-tittle">
                                            <div class="common-content-sort">01</div>
                                            <div class="common-content-name">参会人才</div>
                                        </div>

                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <div class="common-content-tittle">
                                            <div class="common-content-sort">02</div>
                                            <div class="common-content-name">参会人才</div>
                                        </div>

                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tabs-pane session-pane show">
                            <div class="common-wrapper hot-session-wrapper">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>热门场次</span>
                                    </div>
                                </div>

                                <div class="common-content">
                                    <div class="swiper-container hot-session-swiper">
                                        <div class="swiper-wrapper">
                                            <a class="swiper-slide hot-list" href="" target="_blank">
                                                <div class="cover-content">
                                                    <div class="name">广州站</div>
                                                    <img class="cover" src="https://picsum.photos/id/870/364/140" alt="" />
                                                    <div class="view">1.2 w</div>
                                                </div>
                                                <div class="detail">
                                                    <div class="date">2024年03月16日（周一）14:00-16:00</div>
                                                    <div class="address">广州，深圳，北京，上海，重庆庆， 。。。</div>

                                                    <button class="apply" data-link="www.baidu.com">立即报名</button>
                                                </div>
                                            </a>

                                            <a class="swiper-slide hot-list" href="" target="_blank">
                                                <div class="cover-content">
                                                    <div class="name">广州站广州站广州站广州站广州站广州站广州站</div>
                                                    <img class="cover" src="https://picsum.photos/id/870/364/140" alt="" />
                                                    <div class="view">1.2 w</div>
                                                </div>
                                                <div class="detail">
                                                    <div class="date">2024年03月16日（周一）14:00-16:00</div>
                                                    <div class="address">广州，深圳，北京，上海，重庆庆， 。。。</div>

                                                    <button class="apply" disabled>已报名</button>
                                                </div>
                                            </a>

                                            <a class="swiper-slide hot-list" href="" target="_blank">
                                                <div class="cover-content">
                                                    <div class="name">广州站</div>
                                                    <img class="cover" src="https://picsum.photos/id/870/364/140" alt="" />
                                                    <div class="view">1.2 w</div>
                                                </div>
                                                <div class="detail">
                                                    <div class="date">2024年03月16日（周一）14:00-16:00</div>
                                                    <div class="address">广州，深圳，北京，上海，重庆庆， 。。。</div>

                                                    <button class="apply">立即报名</button>
                                                </div>
                                            </a>
                                        </div>

                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="common-wrapper session-wrapper">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>全部场次</span>
                                    </div>
                                </div>

                                <div class="common-content">
                                    <!-- 举办维度---还没开始 -->
                                    <div class="session-order-wrapper">
                                        <!-- 月份维度 -->
                                        <div class="month-list">
                                            <div class="month-title-content">
                                                <div class="aside-circle">
                                                    <span class="circle"></span>
                                                </div>

                                                <div class="month-title">
                                                    <div class="month">1月</div>
                                                    <div class="year">/2024</div>
                                                    <div class="amount">
                                                        共计
                                                        <span class="number">3</span>
                                                        场待举办
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 日维度 -->
                                            <div class="day-session">
                                                <!-- 场次维度 -->
                                                <div class="session-item">
                                                    <div class="aside-date">
                                                        <div class="date">01.15</div>
                                                    </div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <div class="special-tips">火热报名中</div>

                                                            <!-- 待举办：await-status
                                                            即将开始：await-start-status
                                                            进行中：start-status
                                                            已结束：end-status -->
                                                            <div class="status await-status">即将开始</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学--1</a>、 <a href="" target="_blank">广州大学--2</a>、
                                                                            <a href="" target="_blank">广州大学--3</a>、 <a href="" target="_blank">广州大学--4</a>、
                                                                            <a href="" target="_blank">广州大学--5</a>、 <a href="" target="_blank">广州大学--6</a>、
                                                                            <a href="" target="_blank">广州大学--7</a>、 <a href="" target="_blank">广州大学--8</a>、
                                                                            <a href="" target="_blank">广州大学--9</a>、 <a href="" target="_blank">广州大学--10</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply" data-link="http://www.baidu.com">立即报名</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="day-session">
                                                <div class="session-item">
                                                    <div class="aside-date">
                                                        <div class="date">
                                                            02.28
                                                            <span class="number">2场</span>
                                                        </div>
                                                    </div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <!-- 待举办：await-status
                                                            即将开始：await-start-status
                                                            进行中：start-status
                                                            已结束：end-status -->
                                                            <div class="status await-status">即将开始</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply" disabled>立即报名</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="session-item">
                                                    <div class="aside-date"></div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <!-- 待举办：await-status
                                                            即将开始：await-start-status
                                                            进行中：start-status
                                                            已结束：end-status -->
                                                            <div class="status await-status">即将开始</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply" disabled>立即报名</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="month-list">
                                            <div class="month-title-content">
                                                <div class="aside-circle">
                                                    <span class="circle"></span>
                                                </div>

                                                <div class="month-title">
                                                    <div class="month">2月</div>
                                                    <div class="year">/2024</div>
                                                    <div class="amount">
                                                        共计
                                                        <span class="number">2</span>
                                                        场待举办
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="day-session">
                                                <div class="session-item">
                                                    <div class="aside-date">
                                                        <div class="date">01.15</div>
                                                    </div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <div class="status start-status">进行中</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply" disabled>立即报名</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="day-session">
                                                <div class="session-item">
                                                    <div class="aside-date">
                                                        <div class="date">
                                                            02.28
                                                            <span class="number">2场</span>
                                                        </div>
                                                    </div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <div class="status await-start-status">即将开始</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply" disabled>立即报名</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="session-order-wrapper review-session-wrapper">
                                        <div class="tips">以下场次已结束啦，还有更多精彩场次等着你，快来一起参与吧！</div>

                                        <div class="month-list">
                                            <div class="month-title-content">
                                                <div class="aside-circle">
                                                    <span class="circle"></span>
                                                </div>

                                                <div class="month-title">
                                                    <div class="month">1月</div>
                                                    <div class="year">/2024</div>
                                                    <div class="amount">
                                                        共计
                                                        <span class="number">3</span>
                                                        场待举办
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="day-session">
                                                <!-- 场次维度 -->
                                                <div class="session-item">
                                                    <div class="aside-date">
                                                        <div class="date">01.15</div>
                                                    </div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <div class="status end-status">已结束</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply gray-status" disabled>已报名</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="day-session">
                                                <div class="session-item">
                                                    <div class="aside-date">
                                                        <div class="date">
                                                            02.28
                                                            <span class="number">2场</span>
                                                        </div>
                                                    </div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <div class="status end-status">已结束</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply gray-status" disabled>精彩回顾</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="session-item">
                                                    <div class="aside-date"></div>
                                                    <div class="session-content">
                                                        <div class="session-detail">
                                                            <div class="status end-status">已结束</div>

                                                            <div class="info">
                                                                <a class="detail-link" href="" target="_blank">
                                                                    <div class="name">广州站-2025全国博士巡回招聘会广州站-2025全国博士巡回招聘会广州站招聘会招聘会招聘...</div>
                                                                    <div class="center">
                                                                        <div class="time">2024年1月15日（周一） 15:30-17:30</div>
                                                                        <div class="address">广州阳光酒店—1楼国际大厅1楼国际大厅国际大厅国际厅国际大厅国际</div>
                                                                    </div>
                                                                </a>

                                                                <div class="company-content">
                                                                    <div class="amount">参会单位：<span>6000+</span>家</div>
                                                                    <div class="company">
                                                                        <span class="company-name">
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、 <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>、
                                                                            <a href="" target="_blank">广州大学</a>
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <button class="apply gray-status" data-link="activitySpecial.html?show-tab=review">精彩回顾</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tabs-pane company-pane">
                            <div class="site-wrapper">
                                <div class="site-item active" data-specialId="1" data-activityId="201">
                                    <div class="name">全部场次</div>
                                </div>
                                <div class="site-item" data-specialId="2" data-activityId="202">
                                    <div class="status start-status">进行中</div>

                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                                <div class="site-item" data-specialId="3" data-activityId="203">
                                    <div class="status await-start-status">即将开始</div>

                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                                <div class="site-item is-end" data-specialId="4" data-activityId="204">
                                    <div class="status end-status">已结束</div>

                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                                <div class="site-item" data-specialId="5" data-activityId="205">
                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                                <div class="site-item" data-specialId="6" data-activityId="206">
                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                                <div class="site-item" data-specialId="7" data-activityId="207">
                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                                <div class="site-item" data-specialId="8" data-activityId="208">
                                    <div class="name">深圳香港·出站博士后...</div>
                                    <div class="date">2025年03月10日（周一）</div>
                                </div>
                            </div>

                            <div id="companyComponent">
                                <div v-if="isShowCurrentActivityInfo" class="detail-wrapper">
                                    <div class="detail-content" :class="currentActivityInfo.applyStatus == 1 ? 'is-apply': ''">
                                        <div class="title">{{currentActivityInfo.activityShort ? currentActivityInfo.activityShort :currentActivityInfo.name}}</div>

                                        <div class="countdown-content">
                                            <template v-if="currentActivityInfo.activityChildStatus == 1 || currentActivityInfo.activityChildStatus == 2">
                                                <div v-if="currentActivityInfo.startCountDown != null && currentActivityInfo.startCountDown" class="countdown">
                                                    距活动开始还有：<span class="box day">{{day}}</span>&nbsp;天&nbsp;<span class="box hours">{{hours}}</span>&nbsp;时&nbsp;<span class="box min"
                                                        >{{min}}</span
                                                    >&nbsp;分&nbsp;<span class="box second">{{second}}</span>&nbsp;秒
                                                </div>
                                                <div v-else class="text">待举办，敬请期待</div>
                                            </template>
                                            <div v-if="currentActivityInfo.activityChildStatus == 3" class="text">正在进行中</div>
                                            <div v-if="currentActivityInfo.activityChildStatus == 4" class="text">已结束</div>
                                        </div>

                                        <div class="time">活动时间：{{currentActivityInfo.dateText}}</div>
                                        <div class="address">{{currentActivityInfo.toHoldType == 1 ? '活动平台：':'活动地点：'}}{{currentActivityInfo.addressText}}</div>
                                    </div>
                                </div>

                                <div v-if="isShowSearchParams" class="filter-wrapper">
                                    <div v-if="calcAreaList.length" class="filter-list">
                                        <div class="label">
                                            所在地区
                                            <div class="area-tips">仅展示参会单位所在省份及部分热门城市</div>
                                        </div>
                                        <div class="value">
                                            <span
                                                v-for="{ name, id, checked } in calcAreaList"
                                                class="item"
                                                :class="id ? '' : 'is-special'"
                                                :checked="checked"
                                                @click="handleCheck('areaId', { name, id })"
                                                >{{name}}</span
                                            >
                                        </div>
                                    </div>

                                    <div v-if="majorList.length || jobTypeList.length || companyTypeList.length" class="filter-list">
                                        <div class="label">更多筛选</div>
                                        <div class="value">
                                            <div v-if="majorList.length" class="el-select el-select--mini filter-item" :class="majorId.length ? 'is-checked' : ''">
                                                <div class="select-trigger">
                                                    <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('majorList')">
                                                        <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="需求学科" v-model="majorLabel" />
                                                        <span class="el-input__suffix">
                                                            <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('majorId')">
                                                                <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                            </span>
                                                            <span class="el-input__suffix-inner">
                                                                <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="jobTypeList.length" class="el-select el-select--mini filter-item" :class="jobTypeId.length ? 'is-checked' : ''">
                                                <div class="select-trigger">
                                                    <div class="el-input el-input--mini el-input--suffix" @click="handleDialogActive('jobTypeList')">
                                                        <input class="el-input__inner" type="text" readonly="" autocomplete="off" placeholder="职位类型" v-model="jobTypeLabel" />
                                                        <span class="el-input__suffix">
                                                            <span class="el-input__suffix-inner filter-major-clear" @click.stop="handleDialogClear('jobTypeId')">
                                                                <i class="el-select__caret el-input__icon el-icon-circle-close"></i>
                                                            </span>
                                                            <span class="el-input__suffix-inner">
                                                                <i class="el-select__caret el-input__icon el-icon-arrow-up"></i>
                                                            </span>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div v-if="companyTypeList.length" class="filter-item company-type-item" :class="companyTypeId.length ? 'is-select' : ''">
                                                <div class="label">{{companyTypeLabel}}</div>

                                                <el-select
                                                    popper-class="company-select"
                                                    :class="companyTypeId.length ? 'is-checked' : ''"
                                                    v-model="companyTypeId"
                                                    placeholder=" "
                                                    size="mini"
                                                    multiple
                                                    :multiple-limit="5"
                                                    collapse-tags
                                                    @change="handleSearchAgain"
                                                    clearable
                                                >
                                                    <el-option v-for="{ name, code } in companyTypeList" :label="name" :value="code" />
                                                </el-select>
                                            </div>
                                        </div>
                                        <div class="filter-aside">
                                            <div class="clear-all" @click="clearAll">清空筛选条件</div>
                                        </div>
                                    </div>

                                    <div class="tips">当前仅展示部分单位及招聘需求，欲知更多单位详情，请关注后续更新或现场了解</div>
                                </div>

                                <select-dialog
                                    ref="selectDialogRef"
                                    :title="dialogTitle"
                                    :search-placeholder="dialogPlaceholder"
                                    :list="dialogList"
                                    :name="dialogName"
                                    multiple
                                    :multiple-limit="5"
                                    v-model="dialogValue"
                                    @update="handleDialogChange"
                                ></select-dialog>
                            </div>

                            <div class="company-wrapper">
                                <div class="company-content">
                                    <div class="company-data">
                                        <!-- 单场活动：is-single
                                            已报名：is-apply
                                            置顶单位： is-top -->
                                        <a class="item animation-transition is-top is-apply" href="" title="" target="_blank" data-announcement-id="1">
                                            <div class="diamond"></div>
                                            <div class="top">
                                                <div class="logo">
                                                    <img src="https://img.gaoxiaojob.com/na1833.gif" alt="" />
                                                </div>
                                                <div class="grow">
                                                    <div class="name">单位名称单位名称单位名称单位称单位名称单位名称单位名称单位称 ...</div>
                                                    <div class="type">双一流丨北京北京北京北京北京北京北京北京北京北京北京北京北京北京</div>
                                                </div>
                                            </div>
                                            <div class="middle"><span>招 </span>博士后、专任教师、学科带头人、专任教师学科带、专任...</div>
                                            <!-- 福利：praise
                                            场次：site
                                            公告：announcement
                                            专业：major -->
                                            <div class="bottom praise">定期免费体检，免费班车，五险二金带福利-定期免费体检，免费班车，五险二金带福利…</div>
                                            <div class="aside">
                                                <!-- 立即报名： apply
                                                已报名：applied -->
                                                <button class="btn apply" data-link="www.baidu.com" disabled>立即报名</button>
                                            </div>
                                        </a>
                                        <a class="item animation-transition is-apply" href="" title="" target="_blank">
                                            <div class="top">
                                                <div class="logo">
                                                    <img src="https://img.gaoxiaojob.com/na1833.gif" alt="" />
                                                </div>
                                                <div class="grow">
                                                    <div class="name">单位名称单位名称单位名称单位称单位名称单位名称单位名称单位称 ...</div>
                                                    <div class="type">双一流丨北京北京北京北京北京北京北京北京北京北京北京北京北京北京</div>
                                                </div>
                                            </div>
                                            <div class="middle"><span>招 </span>博士后、专任教师、学科带头人、专任教师学科带、专任...</div>
                                            <div class="bottom site">定期免费体检，免费班车，五险二金带福利-定期免费体检，免费班车五险二金，带福利…</div>
                                            <div class="aside">
                                                <button class="btn applied">已报名</button>
                                            </div>
                                        </a>
                                        <a class="item animation-transition" href="" title="" target="_blank">
                                            <div class="top">
                                                <div class="logo">
                                                    <img src="https://img.gaoxiaojob.com/na1833.gif" alt="" />
                                                </div>
                                                <div class="grow">
                                                    <div class="name">单位名称单位名称单位名称单位称单位名称单位名称单位名称单位称 ...</div>
                                                    <div class="type">双一流丨北京北京北京北京北京北京北京北京北京北京北京北京北京北京</div>
                                                </div>
                                            </div>
                                            <div class="middle"><span>招 </span>博士后、专任教师、学科带头人、专任教师学科带、专任...</div>
                                            <div class="bottom major">定期免费体检，免费班车，五险二金，带福利-定期免费体检，免费班车五险二金，带福利…</div>
                                        </a>
                                        <a class="item animation-transition" href="" title="" target="_blank">
                                            <div class="top">
                                                <div class="logo">
                                                    <img src="https://img.gaoxiaojob.com/na1833.gif" alt="" />
                                                </div>
                                                <div class="grow">
                                                    <div class="name">单位名称单位名称单位名称单位称单位名称单位名称单位名称单位称 ...</div>
                                                    <div class="type">双一流丨北京北京北京北京北京北京北京北京北京北京北京北京北京北京</div>
                                                </div>
                                            </div>
                                            <div class="middle"><span>招 </span>博士后、专任教师、学科带头人、专任教师学科带、专任...</div>
                                            <div class="bottom announcement">定期免费体检，免费班车，五险二金，带福利-定期免费体检，免费班车，五险二金，带福利…</div>
                                        </a>
                                    </div>

                                    <div class="tips">更多单位持续更新中，敬请关注...</div>
                                </div>

                                <div class="pagination-cotnent" id="paginationComponent">
                                    <el-pagination v-show="count>0" background layout="prev, pager, next" :page-size="18" @current-change="change" :total="count" v-model:current-page="page" />
                                </div>

                                <div class="empty">单位持续更新中，敬请关注...</div>
                            </div>
                        </div>

                        <div class="tabs-pane apply-way-pane">
                            <div class="common-wrapper">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>参会方式</span>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tabs-pane review-pane">
                            <div class="common-wrapper">
                                <div class="common-title-content">
                                    <div class="common-title">
                                        <span>往届回顾</span>
                                    </div>
                                </div>

                                <div class="common-content--border">
                                    <div class="common-content">
                                        <div>
                                            上半年已顺利举办了十余场线上线下引才活动，人才与单位纷纷表示获益匪浅， 为帮助更多海内外高层次人才与优质单位广泛对接，
                                            下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！下半年线下招聘会将拓展至海外地区，覆盖全球人才！
                                            以更国际化、专业化、高效化的形式搭建线上线下开放交流的广阔舞台， 助力用人单位广聚天下英才，促进求职人才收获优质的就业发展机会。
                                            步履不停，载梦前行，更多招聘场次“职”等您来解锁！“职”等您来解锁！
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 福利详情dialog -->
            <div class="el-overlay" id="welfareDialog" style="z-index: 2003">
                <div class="el-overlay-dialog">
                    <div class="el-dialog dialog-center" aria-modal="true" role="dialog" aria-label="dialog">
                        <div class="el-dialog__header">
                            参会福利

                            <button class="el-dialog__headerbtn close-dialog" type="button">
                                <i class="el-dialog__close el-icon el-icon-close"></i>
                            </button>
                        </div>

                        <div class="el-dialog__body">
                            <div class="welfare-wrapper">
                                <div class="welfare-title">参会享交通福利</div>
                                <div class="welfare-content">
                                    人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）
                                </div>
                            </div>
                            <div class="welfare-wrapper">
                                <div class="welfare-title">参会享交通福利参会享交通福利参会享交通福利参会享交通福利参会享交通福利参会享交通福利参会享交通福利</div>
                                <div class="welfare-content">
                                    人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）
                                </div>
                            </div>
                            <div class="welfare-wrapper">
                                <div class="welfare-title">参会享交通福利</div>
                                <div class="welfare-content">
                                    人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）
                                </div>
                            </div>
                            <div class="welfare-wrapper">
                                <div class="welfare-title">参会享交通福利参会享交通福利参会享交通福利参会享交通福利参会享交通福利参会享交通福利参会享交通福利</div>
                                <div class="welfare-content">
                                    人才参会享市区内交通报销（60元内/人），地铁、公交、打车都可以，轻松出行无负担！参会时现场扫码填写相关报销信息、上传报销凭证，会后10个工作日内完成报销。（具体操作方式可以现场找工作人员了解）
                                </div>
                            </div>
                        </div>

                        <footer class="el-dialog__footer">
                            <button class="close-dialog">我知道了</button>
                        </footer>
                    </div>
                </div>
            </div>

            <!-- 侧边报名入口 -->
            <div class="fixed-apply-enter">
                <a class="apply-enter-item apply-enter-person" href="" target="_blank" rel="nofollow">人才报名</a>
                <a class="apply-enter-item apply-enter-company" href="" target="_blank" rel="nofollow">单位报名</a>
                <span class="apply-enter-item apply-enter-info">
                    活动进群

                    <div class="code-popup">
                        <img src="https://img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" />
                    </div>
                </span>
            </div>

            <!-- 返回顶部 start -->
            <div id="backtopTemplate">
                <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                    <span class="backtop-button"></span>
                    <div class="top">TOP</div>
                </el-backtop>
            </div>
            <script>
                $(function () {
                    const backtopOptions = {
                        computed: {
                            viewportHeight() {
                                return window.innerHeight / 2
                            }
                        }
                    }

                    Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
                    Vue.createApp({}).use(ElementPlus).mount('#statusTemplate')
                })
            </script>
            <!-- 返回顶部 end -->

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                    <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>
        </div>

        <script src="./js/activity/common.js"></script>
        <script>
            $(function () {
                var specialActivityId = ''
                var activityId = ''

                var $headerTabs = $('.tabs-header-wrapper')
                var $headerTabsItem = $headerTabs.find('.tabs-switch-item')

                var $openDialogBtn = $('.open-dialog-btn')
                var $closeDialog = $('.close-dialog')

                var $sessionCard = $(".session-order-wrapper:not('.review-session-wrapper') .session-detail")
                var $sessionCompany = $sessionCard.find('.info .company-content .company')
                var $sessionApplyBtn = $('.session-order-wrapper .session-item .apply')

                // function getParams(key) {
                //     const search = window.location.search
                //     const [mark, ...queryArr] = search.split('?')
                //     const query = queryArr.join('?')
                //     if (!query) return ''
                //     if (new RegExp(`${key}=([^&]*)`).test(query)) {
                //         const value = decodeURIComponent(RegExp.$1)
                //         return /^-?\d+$/.test(value) ? value * 1 : value
                //     }
                // }

                // function openTab() {
                //     var tabKey = getParams('how-tab')
                //     if (tabKey === 'review') {
                //         setTimeout(() => {
                //             $headerTabs.find('.tabs-switch-item[open-pane="review-pane"]').click()
                //         }, 10)
                //     }
                // }
                // openTab()

                $(window).on('scroll', function () {
                    var scrollTop = $(this).scrollTop()
                    var offsetTop = $headerTabs.offset().top
                    const isFixed = scrollTop >= offsetTop
                    isFixed ? $headerTabs.addClass('is-fixed') : $headerTabs.removeClass('is-fixed')
                })

                var activeTab = 'session-pane'

                $headerTabsItem.on('click', function () {
                    var target = $(this).attr('open-pane')

                    if (activeTab === target) return

                    activeTab = target

                    if (target !== 'session-pane') {
                        new WOW({
                            boxClass: 'wow',
                            animateClass: 'animated',
                            offset: 300,
                            mobile: true,
                            live: true
                        }).init()
                    }

                    $(`.${target}`).addClass('show').siblings('.tabs-pane').removeClass('show')
                    $(this).addClass('active').siblings().removeClass('active')

                    scrollToTarget('page-change-fixed')
                })

                $openDialogBtn.on('click', function () {
                    $('body').addClass('el-popup-parent--hidden')
                    var target = $(this).attr('data-target')
                    $(target).fadeIn()
                })

                $closeDialog.on('click', function () {
                    $('body').removeClass('el-popup-parent--hidden')
                    $(this).parents('.el-overlay').fadeOut()
                })

                var swiper = new Swiper('.hot-session-swiper', {
                    slidesPerView: 3,
                    spaceBetween: 10,
                    slidesPerGroup: 3,
                    loop: true,
                    loopFillGroupWithBlank: true,
                    navigation: {
                        nextEl: '.hot-session-swiper .swiper-button-next',
                        prevEl: '.hot-session-swiper .swiper-button-prev'
                    }
                })

                $('.session-pane').on('click', '.hot-list .apply', function (e) {
                    e.preventDefault()
                    const link = $(this).attr('data-link')
                    window.open(link, '_blank')
                })

                $('.company-pane').on('click', '.company-data a.item', function (e) {
                    const announcementId = $(this).attr('data-announcement-id')
                    if (!announcementId) {
                        e.preventDefault()
                        ElementPlus.ElMessage({
                            message: '该单位招聘简章更新中，敬请关注！',
                            type: 'warning'
                        })
                    }
                })

                $('.company-pane').on('click', '.company-data .item .apply', function (e) {
                    const link = $(this).attr('data-link')
                    e.preventDefault()
                    e.stopPropagation()
                    window.open(link, '_blank')
                })

                var sessionCompanyScrollTimer = []
                function sessionCompanyScroll(sessionCard) {
                    var speed = 60
                    var $company = $(sessionCard).find('.info .company-content .company')
                    var $companyName = $company.find('.company-name')
                    var length = $companyName.length

                    var contentWidth = $company.width()
                    var contentNameWidth = $companyName.width()

                    if (contentNameWidth <= contentWidth) return

                    if (length === 1) {
                        var html = $company.html()
                        $company.append(html)
                    }

                    const timerIndex = setInterval(function () {
                        var width = $companyName.width()
                        var scrollLeft = $company.scrollLeft()
                        if (width - scrollLeft < 3) {
                            $company.scrollLeft(0)
                        } else {
                            $company.scrollLeft(scrollLeft + 1)
                        }
                    }, 20)
                    sessionCompanyScrollTimer.push(timerIndex)
                }

                function clearTimer() {
                    sessionCompanyScrollTimer.forEach((item) => clearInterval(item))
                }

                $sessionCard.hover(
                    function () {
                        $(this).find('.info .company-content .company').css('text-overflow', 'initial')
                        sessionCompanyScroll(this)
                    },
                    function () {
                        $(this).find('.info .company-content .company').css('text-overflow', 'ellipsis')
                        $(this).find('.info .company-content .company').scrollLeft(0)
                        clearTimer()
                    }
                )

                $sessionCompany.hover(
                    function () {
                        clearTimer()
                    },
                    function () {
                        sessionCompanyScroll($(this).parents('.session-detail'))
                    }
                )

                $sessionApplyBtn.on('click', function () {
                    var openLink = $(this).attr('data-link')
                    window.open(openLink, '_blank')
                })

                $('.company-pane').on('click', '.site-item', function () {
                    $(this).addClass('active').siblings().removeClass('active')

                    companyComponentVue.areaId = []
                    companyComponentVue.majorId = []
                    companyComponentVue.jobTypeId = []
                    companyComponentVue.companyTypeId = []

                    specialActivityId = $(this).attr('data-specialId')
                    activityId = $(this).attr('data-activityId')

                    getSearchParams()
                })

                function getSearchParams() {
                    httpGet('/special-activity/get-search-params', { specialActivityId, activityId }).then((resp) => {
                        const { companyTabList, cityParams, typeParams, majorSelect, jobTypeList, currentActivityTab = {}, isShowSearchParams } = resp

                        $('.company-pane .site-wrapper').html(companyTabList)

                        companyComponentVue.areaList = cityParams
                        companyComponentVue.majorList = majorSelect
                        companyComponentVue.jobTypeList = jobTypeList
                        companyComponentVue.companyTypeList = typeParams
                        companyComponentVue.isShowCurrentActivityInfo = !!currentActivityTab.activityId
                        companyComponentVue.currentActivityInfo = currentActivityTab
                        companyComponentVue.isShowSearchParams = isShowSearchParams == 1

                        companyComponentVue.formatTimeCountdown()
                        companyComponentVue.handleSearchAgain()
                    })
                }

                const companyComponent = {
                    data() {
                        return {
                            dialogActive: '',

                            areaId: [],
                            majorId: [],
                            jobTypeId: [],
                            companyTypeId: [],

                            isShowSearchParams: true,
                            areaList: [
                                {
                                    name: '不限',
                                    id: ''
                                },
                                {
                                    name: '北京',
                                    id: '2'
                                },
                                {
                                    name: '上海',
                                    id: '802'
                                },
                                {
                                    name: '天津',
                                    id: '20'
                                },
                                {
                                    name: '重庆',
                                    id: '2324'
                                },
                                {
                                    name: '广州',
                                    id: '1965'
                                },
                                {
                                    name: '深圳',
                                    id: '1988'
                                },
                                {
                                    name: '武汉',
                                    id: '1710'
                                },
                                {
                                    name: '南京',
                                    id: '821'
                                },
                                {
                                    name: '西安',
                                    id: '2899'
                                },
                                {
                                    name: '成都',
                                    id: '2368'
                                },
                                {
                                    name: '杭州',
                                    id: '934'
                                },
                                {
                                    name: '长沙',
                                    id: '1828'
                                },
                                {
                                    name: '合肥',
                                    id: '1047'
                                },
                                {
                                    name: '郑州',
                                    id: '1533'
                                },
                                {
                                    name: '济南',
                                    id: '1376'
                                }
                            ],
                            majorList: [
                                {
                                    k: '1',
                                    v: '哲学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '1',
                                            v: '哲学（全部）',
                                            parentId: '0',
                                            topParentId: '1'
                                        },
                                        {
                                            k: '16',
                                            v: '哲学',
                                            parentId: '1',
                                            topParentId: '1'
                                        }
                                    ]
                                },
                                {
                                    k: '2',
                                    v: '经济学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '2',
                                            v: '经济学（全部）',
                                            parentId: '0',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '17',
                                            v: '理论经济学',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '18',
                                            v: '应用经济学',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '748',
                                            v: '数字经济',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '802',
                                            v: '金融学',
                                            parentId: '2',
                                            topParentId: '2'
                                        }
                                    ]
                                },
                                {
                                    k: '3',
                                    v: '法学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '3',
                                            v: '法学（全部）',
                                            parentId: '0',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '19',
                                            v: '法学与法律',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '20',
                                            v: '政治学',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '21',
                                            v: '社会学',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '22',
                                            v: '民族学',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '23',
                                            v: '马克思主义理论',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '24',
                                            v: '公安/警务/纪检监察学',
                                            parentId: '3',
                                            topParentId: '3'
                                        }
                                    ]
                                },
                                {
                                    k: '4',
                                    v: '教育学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '4',
                                            v: '教育学（全部）',
                                            parentId: '0',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '25',
                                            v: '教育学',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '26',
                                            v: '心理学',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '27',
                                            v: '体育学',
                                            parentId: '4',
                                            topParentId: '4'
                                        }
                                    ]
                                },
                                {
                                    k: '5',
                                    v: '文学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '5',
                                            v: '文学（全部）',
                                            parentId: '0',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '28',
                                            v: '中国语言文学',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '29',
                                            v: '外国语言文学',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '30',
                                            v: '新闻传播学',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '750',
                                            v: '翻译',
                                            parentId: '5',
                                            topParentId: '5'
                                        }
                                    ]
                                },
                                {
                                    k: '6',
                                    v: '历史学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '6',
                                            v: '历史学（全部）',
                                            parentId: '0',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '31',
                                            v: '考古学及博物馆',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '32',
                                            v: '中国史',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '33',
                                            v: '世界史',
                                            parentId: '6',
                                            topParentId: '6'
                                        }
                                    ]
                                },
                                {
                                    k: '7',
                                    v: '理学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '7',
                                            v: '理学（全部）',
                                            parentId: '0',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '34',
                                            v: '数学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '35',
                                            v: '物理学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '36',
                                            v: '化学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '37',
                                            v: '天文学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '38',
                                            v: '地理学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '39',
                                            v: '大气科学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '40',
                                            v: '海洋科学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '41',
                                            v: '地球物理学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '42',
                                            v: '地质学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '43',
                                            v: '生物学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '44',
                                            v: '系统科学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '45',
                                            v: '科学技术史',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '46',
                                            v: '生态学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '47',
                                            v: '统计学',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '712',
                                            v: '理科大类（未明确具体学科）',
                                            parentId: '7',
                                            topParentId: '7'
                                        }
                                    ]
                                },
                                {
                                    k: '8',
                                    v: '工学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '8',
                                            v: '工学（全部）',
                                            parentId: '0',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '48',
                                            v: '力学',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '49',
                                            v: '机械工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '50',
                                            v: '光学工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '51',
                                            v: '仪器科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '52',
                                            v: '材料科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '53',
                                            v: '冶金工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '54',
                                            v: '动力工程及工程热物理',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '55',
                                            v: '电气工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '56',
                                            v: '电子科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '57',
                                            v: '信息与通信工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '58',
                                            v: '控制科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '59',
                                            v: '计算机科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '60',
                                            v: '建筑学',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '61',
                                            v: '土木工程与土木水利',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '62',
                                            v: '水利工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '63',
                                            v: '测绘科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '64',
                                            v: '化学工程与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '65',
                                            v: '地质资源与地质工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '66',
                                            v: '矿业工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '67',
                                            v: '石油与天然气工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '68',
                                            v: '纺织科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '69',
                                            v: '轻工技术与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '70',
                                            v: '交通运输工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '71',
                                            v: '船舶与海洋工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '72',
                                            v: '航空宇航科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '73',
                                            v: '兵器科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '74',
                                            v: '核科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '75',
                                            v: '农业工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '76',
                                            v: '林业工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '77',
                                            v: '环境科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '78',
                                            v: '生物医学工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '79',
                                            v: '食品科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '80',
                                            v: '城乡规划学',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '81',
                                            v: '风景园林学',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '82',
                                            v: '软件工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '83',
                                            v: '生物工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '84',
                                            v: '安全科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '85',
                                            v: '公安技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '86',
                                            v: '网络空间安全',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '713',
                                            v: '工科大类（未明确具体学科）',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '719',
                                            v: '生物与医药',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '774',
                                            v: '纳米科学与工程',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '776',
                                            v: '智能科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '778',
                                            v: '人工智能',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '780',
                                            v: '遥感科学与技术',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '804',
                                            v: '能源动力',
                                            parentId: '8',
                                            topParentId: '8'
                                        }
                                    ]
                                },
                                {
                                    k: '9',
                                    v: '农学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '9',
                                            v: '农学（全部）',
                                            parentId: '0',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '88',
                                            v: '作物学',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '89',
                                            v: '园艺学',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '90',
                                            v: '农业与农业资源利用',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '91',
                                            v: '植物保护',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '92',
                                            v: '畜牧学',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '93',
                                            v: '兽医学',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '94',
                                            v: '林学',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '95',
                                            v: '水产',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '96',
                                            v: '草学',
                                            parentId: '9',
                                            topParentId: '9'
                                        }
                                    ]
                                },
                                {
                                    k: '10',
                                    v: '医学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '10',
                                            v: '医学（全部）',
                                            parentId: '0',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '97',
                                            v: '基础医学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '98',
                                            v: '临床医学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '99',
                                            v: '口腔医学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '100',
                                            v: '公共卫生与预防医学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '101',
                                            v: '中医学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '102',
                                            v: '中西医结合',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '103',
                                            v: '药学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '104',
                                            v: '中药学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '105',
                                            v: '特种医学',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '106',
                                            v: '医学技术',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '107',
                                            v: '护理学',
                                            parentId: '10',
                                            topParentId: '10'
                                        }
                                    ]
                                },
                                {
                                    k: '11',
                                    v: '军事学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '11',
                                            v: '军事学（全部）',
                                            parentId: '0',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '108',
                                            v: '军事思想及军事历史',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '109',
                                            v: '战略、作战学',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '110',
                                            v: '战役学',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '111',
                                            v: '战术学',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '112',
                                            v: '军队指挥、作战指挥与军事智能',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '113',
                                            v: '军事管理学',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '114',
                                            v: '军队政治工作学与战时政治工作',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '115',
                                            v: '军事后勤学（含后勤与装备保障）',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '116',
                                            v: '军事装备学',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '117',
                                            v: '军事训练学（含军事训练与管理）',
                                            parentId: '11',
                                            topParentId: '11'
                                        }
                                    ]
                                },
                                {
                                    k: '12',
                                    v: '管理学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '12',
                                            v: '管理学（全部）',
                                            parentId: '0',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '118',
                                            v: '管理科学与工程',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '119',
                                            v: '工商管理',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '120',
                                            v: '农林经济管理',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '121',
                                            v: '公共管理学',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '122',
                                            v: '信息资源管理（图书馆、情报与档案管理）',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '782',
                                            v: '旅游与酒店管理',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '812',
                                            v: '审计',
                                            parentId: '12',
                                            topParentId: '12'
                                        }
                                    ]
                                },
                                {
                                    k: '13',
                                    v: '艺术学',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '13',
                                            v: '艺术学（全部）',
                                            parentId: '0',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '123',
                                            v: '艺术学理论',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '124',
                                            v: '音乐与舞蹈学',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '125',
                                            v: '戏剧、戏曲、影视学',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '126',
                                            v: '美术与书法',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '127',
                                            v: '设计学',
                                            parentId: '13',
                                            topParentId: '13'
                                        }
                                    ]
                                },
                                {
                                    k: '14',
                                    v: '交叉学科',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '14',
                                            v: '交叉学科（全部）',
                                            parentId: '0',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '128',
                                            v: '集成电路科学与工程',
                                            parentId: '14',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '129',
                                            v: '国家安全学',
                                            parentId: '14',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '785',
                                            v: '区域国别学',
                                            parentId: '14',
                                            topParentId: '14'
                                        }
                                    ]
                                },
                                {
                                    k: '15',
                                    v: '其他',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '15',
                                            v: '其他（全部）',
                                            parentId: '0',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '130',
                                            v: '专业未分类',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '716',
                                            v: '专业不限',
                                            parentId: '15',
                                            topParentId: '15'
                                        }
                                    ]
                                }
                            ],
                            jobTypeList: [
                                {
                                    k: '0',
                                    v: '热门类型',
                                    children: [
                                        {
                                            k: '34',
                                            v: '辅导员岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '29',
                                            v: '博士后',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '33',
                                            v: '专职教师/教学科研岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '36',
                                            v: '实验技术岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '32',
                                            v: '教授/副教授',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '35',
                                            v: '教务岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '37',
                                            v: '图书馆岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '38',
                                            v: '党务行政岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '61',
                                            v: '专职科研岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '62',
                                            v: '科研助理岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '49',
                                            v: '中小学普通教师岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '48',
                                            v: '中小学骨干教师岗/学科带头人',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '85',
                                            v: '主治医师/住院医师/医生',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '84',
                                            v: '主任医师/副主任医师',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '92',
                                            v: '普通医技岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '88',
                                            v: '普通药师岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        },
                                        {
                                            k: '90',
                                            v: '普通护理岗',
                                            parentId: '0',
                                            topParentId: '0'
                                        }
                                    ]
                                },
                                {
                                    k: '1',
                                    v: '博士后',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '29',
                                            v: '博士后',
                                            parentId: '1',
                                            topParentId: '1'
                                        }
                                    ]
                                },
                                {
                                    k: '2',
                                    v: '教学岗（高等院校）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '30',
                                            v: '学术领军人才/顶尖人才',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '31',
                                            v: '学科带头人/学术骨干',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '32',
                                            v: '教授/副教授',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '33',
                                            v: '专职教师/教学科研岗',
                                            parentId: '2',
                                            topParentId: '2'
                                        },
                                        {
                                            k: '259',
                                            v: '助理教授/助理副教授',
                                            parentId: '2',
                                            topParentId: '2'
                                        }
                                    ]
                                },
                                {
                                    k: '3',
                                    v: '教学支撑岗（高等院校）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '34',
                                            v: '辅导员岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '35',
                                            v: '教务岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '36',
                                            v: '实验技术岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '37',
                                            v: '图书馆岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '38',
                                            v: '党务行政岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '39',
                                            v: '技术支撑岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        },
                                        {
                                            k: '40',
                                            v: '其他支撑岗',
                                            parentId: '3',
                                            topParentId: '3'
                                        }
                                    ]
                                },
                                {
                                    k: '4',
                                    v: '中高级管理岗（教育/科研机构）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '41',
                                            v: '高校校长/校领导/单位负责人',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '42',
                                            v: '中小学校长/校领导/单位负责人',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '43',
                                            v: '幼儿园园长',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '44',
                                            v: '二级学院院长/副院长',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '45',
                                            v: '系/研究所/实验室负责人',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '46',
                                            v: '高校/科研机构中层党政部门负责人',
                                            parentId: '4',
                                            topParentId: '4'
                                        },
                                        {
                                            k: '47',
                                            v: '中小学校中层党政部门负责人',
                                            parentId: '4',
                                            topParentId: '4'
                                        }
                                    ]
                                },
                                {
                                    k: '5',
                                    v: '教学岗（中小学及幼儿园）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '48',
                                            v: '中小学骨干教师岗/学科带头人',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '49',
                                            v: '中小学普通教师岗',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '50',
                                            v: '中职教师岗（公共课类）',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '51',
                                            v: '中职教师岗（专业课类）',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '52',
                                            v: '学前教师/幼师',
                                            parentId: '5',
                                            topParentId: '5'
                                        },
                                        {
                                            k: '260',
                                            v: '特殊教育教师/康复教师/其他教师岗',
                                            parentId: '5',
                                            topParentId: '5'
                                        }
                                    ]
                                },
                                {
                                    k: '6',
                                    v: '教学支撑岗（中小学及幼儿园）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '53',
                                            v: '教务岗',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '54',
                                            v: '实验技术岗',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '55',
                                            v: '生活老师/保育员',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '56',
                                            v: '专职班主任/辅导员',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '57',
                                            v: '其他教学/行政支撑岗',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '58',
                                            v: '教研员',
                                            parentId: '6',
                                            topParentId: '6'
                                        },
                                        {
                                            k: '261',
                                            v: '教练员',
                                            parentId: '6',
                                            topParentId: '6'
                                        }
                                    ]
                                },
                                {
                                    k: '7',
                                    v: '科学研究岗（教育/科研/卫生单位）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '59',
                                            v: '科研领军人才/杰出人才',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '60',
                                            v: '高级研究人员（正/副研究员）',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '61',
                                            v: '专职科研岗',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '62',
                                            v: '科研助理岗',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '63',
                                            v: '实验技术岗',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '64',
                                            v: '医学教学/科研人才',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '65',
                                            v: '其他科研支撑岗',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '262',
                                            v: '助理研究员/研究实习员',
                                            parentId: '7',
                                            topParentId: '7'
                                        },
                                        {
                                            k: '263',
                                            v: '特别研究助理',
                                            parentId: '7',
                                            topParentId: '7'
                                        }
                                    ]
                                },
                                {
                                    k: '8',
                                    v: '科学研究岗（企事业单位）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '66',
                                            v: '科研领军人才/杰出人才',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '67',
                                            v: '高级研究人员（正/副研究员）',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '68',
                                            v: '专职科研岗',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '69',
                                            v: '科研助理岗',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '70',
                                            v: '实验技术岗',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '71',
                                            v: '其他科研支撑岗',
                                            parentId: '8',
                                            topParentId: '8'
                                        },
                                        {
                                            k: '264',
                                            v: '助理研究员/研究实习员',
                                            parentId: '8',
                                            topParentId: '8'
                                        }
                                    ]
                                },
                                {
                                    k: '9',
                                    v: '公务员/事业单位/军队工作人员',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '72',
                                            v: '领导干部岗（处级以上）',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '73',
                                            v: '公务员',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '74',
                                            v: '选调生（毕业生）',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '75',
                                            v: '军队文职人员',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '76',
                                            v: '工作人员（综合类）',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '77',
                                            v: '工作人员（技术人员岗类）',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '78',
                                            v: '工作人员（工勤技能类）',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '79',
                                            v: '辅助协助岗人员',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '80',
                                            v: '社区工作人员/村官/网格员',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '81',
                                            v: '其他政府与事业单位岗',
                                            parentId: '9',
                                            topParentId: '9'
                                        },
                                        {
                                            k: '265',
                                            v: '高层次人才',
                                            parentId: '9',
                                            topParentId: '9'
                                        }
                                    ]
                                },
                                {
                                    k: '10',
                                    v: '医疗卫生专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '82',
                                            v: '院长/副院长',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '83',
                                            v: '学科带头人/学术骨干',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '84',
                                            v: '主任医师/副主任医师',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '85',
                                            v: '主治医师/住院医师/医生',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '86',
                                            v: '规培岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '87',
                                            v: '中高级药师岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '88',
                                            v: '普通药师岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '89',
                                            v: '中高级护理岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '90',
                                            v: '普通护理岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '91',
                                            v: '中高级医技岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '92',
                                            v: '普通医技岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '93',
                                            v: '医务医辅人员',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '94',
                                            v: '党务行政岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '95',
                                            v: '校医人员',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '96',
                                            v: '其他医疗卫生岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        },
                                        {
                                            k: '266',
                                            v: '科室主任/副主任中高层管理岗',
                                            parentId: '10',
                                            topParentId: '10'
                                        }
                                    ]
                                },
                                {
                                    k: '11',
                                    v: '中高级管理岗（企业）',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '97',
                                            v: '总裁/总经理/CEO',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '98',
                                            v: '副总裁/副总经理/VP/高级合伙人',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '99',
                                            v: '总监/部门高级经理',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '100',
                                            v: '区域/分公司/代表处负责人',
                                            parentId: '11',
                                            topParentId: '11'
                                        },
                                        {
                                            k: '101',
                                            v: '其他中高级管理岗',
                                            parentId: '11',
                                            topParentId: '11'
                                        }
                                    ]
                                },
                                {
                                    k: '12',
                                    v: '人力/行政/财务岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '102',
                                            v: '人力资源管理岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '103',
                                            v: '人力资源专业岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '104',
                                            v: '招聘岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '105',
                                            v: '薪酬福利岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '106',
                                            v: '绩效岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '107',
                                            v: '员工培训岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '108',
                                            v: '其他人力资源岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '109',
                                            v: '行政管理岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '110',
                                            v: '行政事务岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '111',
                                            v: '党务党群岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '112',
                                            v: '秘书文员岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '113',
                                            v: '档案管理岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '114',
                                            v: '其他行政支撑岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '115',
                                            v: '财务管理岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '116',
                                            v: '会计/出纳岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '117',
                                            v: '审计/税务岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '118',
                                            v: '数据分析与统计岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '119',
                                            v: '成本管理岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '120',
                                            v: '资产管理岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        },
                                        {
                                            k: '121',
                                            v: '其他财务岗',
                                            parentId: '12',
                                            topParentId: '12'
                                        }
                                    ]
                                },
                                {
                                    k: '13',
                                    v: '法务/翻译/咨询/教培岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '122',
                                            v: '律师',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '123',
                                            v: '法律顾问',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '124',
                                            v: '法务',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '125',
                                            v: '翻译',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '126',
                                            v: '商务咨询顾问',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '127',
                                            v: '心理/婚恋咨询师',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '128',
                                            v: '经纪人/中介',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '129',
                                            v: '猎头顾问',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '130',
                                            v: '知识产权与专利',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '131',
                                            v: '培训产品研发与支持',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '132',
                                            v: '中小学学科培训教师',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '133',
                                            v: '社会职业培训教师',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '134',
                                            v: '兴趣特长培训教师',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '135',
                                            v: '其他培训岗',
                                            parentId: '13',
                                            topParentId: '13'
                                        },
                                        {
                                            k: '267',
                                            v: '其他法律岗',
                                            parentId: '13',
                                            topParentId: '13'
                                        }
                                    ]
                                },
                                {
                                    k: '14',
                                    v: '销售/商务/客服/招生岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '136',
                                            v: '销售管理',
                                            parentId: '14',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '137',
                                            v: '销售顾问/商务拓展',
                                            parentId: '14',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '138',
                                            v: '销售支持及商务支撑',
                                            parentId: '14',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '139',
                                            v: '客服及支持',
                                            parentId: '14',
                                            topParentId: '14'
                                        },
                                        {
                                            k: '140',
                                            v: '招生顾问',
                                            parentId: '14',
                                            topParentId: '14'
                                        }
                                    ]
                                },
                                {
                                    k: '15',
                                    v: '市场/公关/广告/会展岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '141',
                                            v: '营销策划岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '142',
                                            v: '市场研究岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '143',
                                            v: '市场渠道推广岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '144',
                                            v: '媒介公关岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '145',
                                            v: '品牌形象岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '146',
                                            v: '广告事务岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '147',
                                            v: '会展会务岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        },
                                        {
                                            k: '268',
                                            v: '其他相关岗',
                                            parentId: '15',
                                            topParentId: '15'
                                        }
                                    ]
                                },
                                {
                                    k: '16',
                                    v: '编辑/出版/传媒/文化岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '148',
                                            v: '社长/总编/副总编',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '149',
                                            v: '编辑/作家/撰稿人',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '150',
                                            v: '记者采编岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '151',
                                            v: '出版发行岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '152',
                                            v: '设计排版印刷岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '153',
                                            v: '其他编辑出版岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '154',
                                            v: '影视制作策划岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '155',
                                            v: '主播/主持人岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '156',
                                            v: '拍摄与后期制作岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '157',
                                            v: '导演/编导岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '158',
                                            v: '其他影视传媒岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '159',
                                            v: '图书馆岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '160',
                                            v: '演艺演出岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '161',
                                            v: '文宣策划岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '162',
                                            v: '文物保护与考古岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        },
                                        {
                                            k: '163',
                                            v: '文博展览岗',
                                            parentId: '16',
                                            topParentId: '16'
                                        }
                                    ]
                                },
                                {
                                    k: '17',
                                    v: '互联网产品/设计/运营岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '164',
                                            v: '产品管理岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '165',
                                            v: '产品经理岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '166',
                                            v: '视觉/交互设计岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '167',
                                            v: '工业/艺术设计岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '168',
                                            v: '运营管理岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '169',
                                            v: '电商运营岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '170',
                                            v: '新媒体/内容运营岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '171',
                                            v: '产品/用户/活动运营岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        },
                                        {
                                            k: '172',
                                            v: '其他运营岗',
                                            parentId: '17',
                                            topParentId: '17'
                                        }
                                    ]
                                },
                                {
                                    k: '18',
                                    v: '技术研究/开发/测试/运维岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '173',
                                            v: '技术管理岗',
                                            parentId: '18',
                                            topParentId: '18'
                                        },
                                        {
                                            k: '174',
                                            v: '项目经理/专员岗',
                                            parentId: '18',
                                            topParentId: '18'
                                        },
                                        {
                                            k: '175',
                                            v: '前后端程序开发岗',
                                            parentId: '18',
                                            topParentId: '18'
                                        },
                                        {
                                            k: '176',
                                            v: '人工智能岗/算法工程师',
                                            parentId: '18',
                                            topParentId: '18'
                                        },
                                        {
                                            k: '177',
                                            v: '数据库开发管理岗',
                                            parentId: '18',
                                            topParentId: '18'
                                        },
                                        {
                                            k: '178',
                                            v: '测试/运维/网络支持岗',
                                            parentId: '18',
                                            topParentId: '18'
                                        },
                                        {
                                            k: '269',
                                            v: '其他计算机专业人员岗',
                                            parentId: '18',
                                            topParentId: '18'
                                        }
                                    ]
                                },
                                {
                                    k: '19',
                                    v: '电子/通信/硬件/半导体岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '179',
                                            v: '电子工程师/专业人员岗',
                                            parentId: '19',
                                            topParentId: '19'
                                        },
                                        {
                                            k: '180',
                                            v: '通信工程师/专业人员岗',
                                            parentId: '19',
                                            topParentId: '19'
                                        },
                                        {
                                            k: '181',
                                            v: '硬件设计与开发测试岗',
                                            parentId: '19',
                                            topParentId: '19'
                                        },
                                        {
                                            k: '182',
                                            v: '仪器仪表计量专业人员岗',
                                            parentId: '19',
                                            topParentId: '19'
                                        },
                                        {
                                            k: '183',
                                            v: '半导体/芯片专业人员岗',
                                            parentId: '19',
                                            topParentId: '19'
                                        }
                                    ]
                                },
                                {
                                    k: '20',
                                    v: '生产制造/机械/汽车专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '184',
                                            v: '生产管理岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '185',
                                            v: '设备管理专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '186',
                                            v: '质量管理专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '187',
                                            v: '机械设计专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '188',
                                            v: '机械制造专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '189',
                                            v: '机电工程师/专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '190',
                                            v: '自动化/电气工程师专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '191',
                                            v: '飞行器设计与制造专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '192',
                                            v: '船舶设计与制造专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '193',
                                            v: '兵器工程师/专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '194',
                                            v: '汽车设计与制造专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '195',
                                            v: '新能源汽车专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '196',
                                            v: '轨道交通专业人员岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '197',
                                            v: '技术工人',
                                            parentId: '20',
                                            topParentId: '20'
                                        },
                                        {
                                            k: '198',
                                            v: '其他制造业专业岗',
                                            parentId: '20',
                                            topParentId: '20'
                                        }
                                    ]
                                },
                                {
                                    k: '21',
                                    v: '能源/矿产/电力/环保专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '199',
                                            v: '石油/天然气专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '200',
                                            v: '水利水电工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '201',
                                            v: '热能与动力工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '202',
                                            v: '材料工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '203',
                                            v: '冶金工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '204',
                                            v: '光伏及新能源工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '205',
                                            v: '地质勘察/选矿/采矿专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '206',
                                            v: '电力工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '207',
                                            v: '环保工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '208',
                                            v: '环保检测与环境评价专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '209',
                                            v: '气象/测绘/水文工程师',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '210',
                                            v: '安全工程师/专业人员岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        },
                                        {
                                            k: '211',
                                            v: '其他相关岗',
                                            parentId: '21',
                                            topParentId: '21'
                                        }
                                    ]
                                },
                                {
                                    k: '22',
                                    v: '化工/轻工/食品专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '212',
                                            v: '化工技术专业人员岗',
                                            parentId: '22',
                                            topParentId: '22'
                                        },
                                        {
                                            k: '213',
                                            v: '轻化工专业人员岗',
                                            parentId: '22',
                                            topParentId: '22'
                                        },
                                        {
                                            k: '214',
                                            v: '印刷/包装专业人员岗',
                                            parentId: '22',
                                            topParentId: '22'
                                        },
                                        {
                                            k: '215',
                                            v: '纺织/服装专业人员岗',
                                            parentId: '22',
                                            topParentId: '22'
                                        },
                                        {
                                            k: '216',
                                            v: '食品/粮食专业人员岗',
                                            parentId: '22',
                                            topParentId: '22'
                                        }
                                    ]
                                },
                                {
                                    k: '23',
                                    v: '生物/医药/医疗器械专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '217',
                                            v: '生物工程/生物制药专业人员岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '218',
                                            v: '医药研发管理岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '219',
                                            v: '药品研发岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '220',
                                            v: '药品生产/质量管理岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '221',
                                            v: '临床测试与研究岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '222',
                                            v: '药品/医疗器械注册岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '223',
                                            v: '医疗器械研发岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '224',
                                            v: '医疗器械生产/质量管理岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        },
                                        {
                                            k: '225',
                                            v: '医疗市场事务岗',
                                            parentId: '23',
                                            topParentId: '23'
                                        }
                                    ]
                                },
                                {
                                    k: '24',
                                    v: '金融专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '226',
                                            v: '金融管理岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '227',
                                            v: '行业研究分析岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '228',
                                            v: '金融产品岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '229',
                                            v: '风控岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '230',
                                            v: '投融资岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '231',
                                            v: '证券期货岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '232',
                                            v: '保险岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '233',
                                            v: '基金岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '234',
                                            v: '银行岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '235',
                                            v: '互联网金融岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        },
                                        {
                                            k: '236',
                                            v: '其他金融岗',
                                            parentId: '24',
                                            topParentId: '24'
                                        }
                                    ]
                                },
                                {
                                    k: '25',
                                    v: '房地产/建筑/物业专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '237',
                                            v: '房地产开发岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '238',
                                            v: '建筑规划设计与市政建设岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '239',
                                            v: '建筑工程技术人员岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '240',
                                            v: '工程管理与质量安防岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '241',
                                            v: '物业运营与管理岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '242',
                                            v: '其他房地产/建筑/物业岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '270',
                                            v: '项目管理',
                                            parentId: '25',
                                            topParentId: '25'
                                        },
                                        {
                                            k: '271',
                                            v: '建筑行业招投标岗',
                                            parentId: '25',
                                            topParentId: '25'
                                        }
                                    ]
                                },
                                {
                                    k: '26',
                                    v: '供应链/物流/运输/采购/贸易专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '243',
                                            v: '供应链岗',
                                            parentId: '26',
                                            topParentId: '26'
                                        },
                                        {
                                            k: '244',
                                            v: '物流与仓储岗',
                                            parentId: '26',
                                            topParentId: '26'
                                        },
                                        {
                                            k: '245',
                                            v: '交通运输岗',
                                            parentId: '26',
                                            topParentId: '26'
                                        },
                                        {
                                            k: '246',
                                            v: '采购岗',
                                            parentId: '26',
                                            topParentId: '26'
                                        },
                                        {
                                            k: '247',
                                            v: '进出口贸易岗',
                                            parentId: '26',
                                            topParentId: '26'
                                        },
                                        {
                                            k: '272',
                                            v: '招投标岗',
                                            parentId: '26',
                                            topParentId: '26'
                                        }
                                    ]
                                },
                                {
                                    k: '27',
                                    v: '零售/生活服务/农林牧渔专业岗',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '248',
                                            v: '百货零售相关岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        },
                                        {
                                            k: '249',
                                            v: '休闲娱乐体育相关岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        },
                                        {
                                            k: '250',
                                            v: '酒店旅游餐饮相关岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        },
                                        {
                                            k: '251',
                                            v: '家政婚庆美容相关岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        },
                                        {
                                            k: '252',
                                            v: '其他生活服务岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        },
                                        {
                                            k: '253',
                                            v: '农林牧渔管理岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        },
                                        {
                                            k: '254',
                                            v: '农林牧渔技术人员岗',
                                            parentId: '27',
                                            topParentId: '27'
                                        }
                                    ]
                                },
                                {
                                    k: '28',
                                    v: '管培生/其他',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '255',
                                            v: '管理培训生/储备干部',
                                            parentId: '28',
                                            topParentId: '28'
                                        },
                                        {
                                            k: '256',
                                            v: '社工/志愿者',
                                            parentId: '28',
                                            topParentId: '28'
                                        },
                                        {
                                            k: '257',
                                            v: '其他',
                                            parentId: '28',
                                            topParentId: '28'
                                        },
                                        {
                                            k: '258',
                                            v: '实习生/见习岗',
                                            parentId: '28',
                                            topParentId: '28'
                                        },
                                        {
                                            k: '273',
                                            v: '企业综合类岗位',
                                            parentId: '28',
                                            topParentId: '28'
                                        },
                                        {
                                            k: '274',
                                            v: '企业类高层次人才',
                                            parentId: '28',
                                            topParentId: '28'
                                        },
                                        {
                                            k: '275',
                                            v: '工勤支撑岗',
                                            parentId: '28',
                                            topParentId: '28'
                                        }
                                    ]
                                },
                                {
                                    k: '276',
                                    v: '海外优青',
                                    parentId: '0',
                                    children: [
                                        {
                                            k: '277',
                                            v: '海外优青',
                                            parentId: '276',
                                            topParentId: '276'
                                        }
                                    ]
                                }
                            ],
                            companyTypeList: [
                                {
                                    name: '双一流院校',
                                    code: '1'
                                },
                                {
                                    name: '普通本科院校',
                                    code: '2'
                                },
                                {
                                    name: '高职高专院校',
                                    code: '3'
                                },
                                {
                                    name: '党校与行政学院',
                                    code: '4'
                                },
                                {
                                    name: '机关单位',
                                    code: '5'
                                },
                                {
                                    name: '军队武警',
                                    code: '6'
                                },
                                {
                                    name: '事业单位',
                                    code: '7'
                                },
                                {
                                    name: '政府国有企业',
                                    code: '8'
                                },
                                {
                                    name: '知名企业',
                                    code: '9'
                                },
                                {
                                    name: '中小成长型企业',
                                    code: '10'
                                },
                                {
                                    name: '银行、信用社等金融机构',
                                    code: '11'
                                },
                                {
                                    name: '中小学',
                                    code: '13'
                                },
                                {
                                    name: '中专&职业中学&技师学院',
                                    code: '14'
                                },
                                {
                                    name: '幼儿园',
                                    code: '15'
                                },
                                {
                                    name: '中国科学院系统',
                                    code: '16'
                                },
                                {
                                    name: '人文社科研究机构（事业单位类型）',
                                    code: '17'
                                },
                                {
                                    name: '自然与应用科研机构（事业单位类型）',
                                    code: '18'
                                },
                                {
                                    name: '企业研发机构',
                                    code: '19'
                                },
                                {
                                    name: '医院',
                                    code: '21'
                                },
                                {
                                    name: '其他医疗机构',
                                    code: '22'
                                }
                            ],

                            day: '',
                            hours: '',
                            min: '',
                            second: '',

                            timer: null,
                            isShowCurrentActivityInfo: false,
                            currentActivityInfo: {
                                // 剩余时间(秒)
                                startCountDown: 0,
                                name: '重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站重庆站(博士专场)',
                                dateText: '2025年03月10日（周一）14:30-17:30',
                                addressText: '重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学重庆大学...',
                                applyStatus: '',
                                // 1:待举办；2:即将开始；3:进行中；4:已结束
                                activityChildStatus: ''
                            }
                        }
                    },
                    computed: {
                        majorLabel() {
                            const { majorId } = this
                            const len = majorId.length

                            if (len) {
                                return `学科分类(${len})`
                            }
                            return ''
                        },

                        jobTypeLabel() {
                            const { jobTypeId } = this
                            const len = jobTypeId.length

                            if (len) {
                                return `职位类型(${len})`
                            }
                            return ''
                        },

                        companyTypeLabel() {
                            const { companyTypeId } = this
                            const len = companyTypeId.length

                            if (len) {
                                return `单位类型(${len})`
                            }
                            return '单位类型'
                        },

                        calcAreaList() {
                            const { areaId, areaList } = this
                            return this.getCalcDataList(areaId, areaList)
                        },

                        dialogTitle() {
                            const { dialogActive } = this
                            const options = {
                                majorList: '请选择学科',
                                jobTypeList: '请选择职位类型'
                            }

                            return options[dialogActive] || '请选择'
                        },

                        dialogPlaceholder() {
                            const { dialogActive } = this
                            const options = {
                                majorList: '请输入学科关键词',
                                jobTypeList: '请输入职位类型关键词'
                            }

                            return options[dialogActive] || '请输入关键词'
                        },

                        dialogList() {
                            const { dialogActive } = this
                            return this[dialogActive] || []
                        },

                        dialogName() {
                            return 'area'
                        },

                        dialogValueTextKey() {
                            const { dialogActive } = this
                            const options = {
                                jobTypeList: 'jobTypeId',
                                majorList: 'majorId'
                            }
                            const value = options[dialogActive]

                            if (value) {
                                return value
                            }
                            return ''
                        },

                        dialogValue: {
                            get() {
                                const valKey = this.dialogValueTextKey

                                if (valKey) {
                                    return this[valKey]
                                }
                                return []
                            },
                            set(val) {
                                const valKey = this.dialogValueTextKey

                                if (valKey) {
                                    this[valKey] = val
                                }
                            }
                        }
                    },

                    mounted() {},

                    methods: {
                        formatTimeCountdown() {
                            clearTimeout(this.timer)
                            const {
                                currentActivityInfo: { startCountDown }
                            } = this

                            if (!startCountDown) return

                            const day = Math.floor(startCountDown / (24 * 3600))
                            const hours = Math.floor((startCountDown % (24 * 3600)) / 3600)
                            const min = Math.floor((startCountDown % 3600) / 60)
                            const second = startCountDown % 60

                            this.day = day
                            this.hours = hours
                            this.min = min
                            this.second = second

                            this.currentActivityInfo.startCountDown = startCountDown - 1

                            if (startCountDown - 1 === 0) {
                                getSearchParams()
                                return
                            }

                            this.timer = setTimeout(() => {
                                this.formatTimeCountdown()
                            }, 1000)
                        },

                        getCalcDataList(checkedList, dataList) {
                            const defaultChecked = checkedList.length ? checkedList : ['']

                            return dataList.map(function (item) {
                                const { name, id } = item
                                const checked = defaultChecked.includes(id)
                                return { name, id, checked }
                            })
                        },

                        handleSearchAgain(query = {}) {
                            const { areaId, majorId, jobTypeId, companyTypeId } = this
                            const { page, ...other } = query

                            if (!page) {
                                paginationComponentVue.initPage()
                            }

                            const param = {
                                areaId: areaId.join(),
                                majorId: majorId.join(),
                                categoryId: jobTypeId.join(),
                                type: companyTypeId.join(),
                                ...other,
                                specialActivityId,
                                activityId,
                                page: page ? page : 1
                            }

                            httpGet('/special-activity/get-company-list', param).then((resp) => {
                                const { list, count, isEnd } = resp
                                const $companyContent = $('.company-pane .company-wrapper .company-content')
                                $companyContent.html(`
                                    <div class="company-data">
                                        ${list}
                                    </div>`)
                                if (isEnd == 1) {
                                    $companyContent.append('<div class="tips">更多单位持续更新中，敬请关注...</div>')
                                }

                                if (list == '') {
                                    $companyContent.hide()
                                    $('.company-pane .company-wrapper .empty').addClass('show')
                                } else {
                                    $companyContent.show()
                                    $('.company-pane .company-wrapper .empty').removeClass('show')
                                }

                                if (page) {
                                    const headerHeight = $('.activity-header-container').height()
                                    const tabSHeight = $('.tabs-header-content').height()

                                    const offsetTop = $('.company-pane .filter-wrapper').offset().top

                                    $('html, body').animate({
                                        scrollTop: offsetTop - headerHeight - tabSHeight
                                    })
                                }

                                const total = Number(count)

                                paginationComponentVue.count = total
                            })
                        },

                        handleCheck(key, data) {
                            const { name, id, limit } = data
                            const keyValue = this[key]
                            const _this = this

                            const index = keyValue.findIndex(function (item) {
                                return item === id
                            })
                            const size = limit || 5

                            let needFetchData = true

                            const validHandler = function () {
                                if (index > -1) {
                                    keyValue.splice(index, 1)
                                } else {
                                    if (keyValue.length < size) {
                                        keyValue.push(id)
                                    } else {
                                        needFetchData = false
                                        return ElementPlus.ElMessage.warning(`您最多可选${size}项`)
                                    }
                                }
                                _this[key] = keyValue
                            }

                            const invalidHandler = function () {
                                _this[key] = []
                                _this.handleSearchAgain()
                            }

                            id ? validHandler() : invalidHandler()

                            needFetchData && this.handleSearchAgain()
                        },

                        handleDialogClear(name) {
                            const _this = this

                            this.$nextTick(function () {
                                _this[name] = []
                                _this.handleSearchAgain()
                            })
                        },

                        clearAll() {
                            this.areaId = []
                            this.majorId = []
                            this.jobTypeId = []
                            this.companyTypeId = []
                            this.handleSearchAgain()
                        },

                        handleDialogChange(data) {
                            this.handleSearchAgain()
                        },

                        handleDialogActive(name) {
                            this.dialogActive = name
                            this.$refs.selectDialogRef.handleOpen()
                        }
                    }
                }

                const companyComponentVue = Vue.createApp(companyComponent)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                },
                                select: {
                                    loading: '\u52A0\u8F7D\u4E2D',
                                    noMatch: '\u65E0\u5339\u914D\u6570\u636E',
                                    noData: '\u65E0\u6570\u636E',
                                    placeholder: '\u8BF7\u9009\u62E9'
                                }
                            }
                        }
                    })
                    .component('select-dialog', selectDialogComponent)
                    .mount('#companyComponent')

                const paginationComponent = {
                    data() {
                        return {
                            page: 1,
                            count: 100
                        }
                    },
                    mounted() {},
                    methods: {
                        initPage() {
                            this.page = 1
                        },
                        change(page) {
                            companyComponentVue.handleSearchAgain({ page })
                        }
                    }
                }

                const paginationComponentVue = Vue.createApp(paginationComponent)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#paginationComponent')
            })
        </script>
    </body>
</html>
