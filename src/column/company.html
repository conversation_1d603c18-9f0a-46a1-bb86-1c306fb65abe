<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>单位中心</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/company.css" />
        <style>
            .empty-state {
                text-align: center;
                padding: 60px 20px;
                color: #999;
                font-size: 16px;
            }

            .result-list[v-loading] {
                min-height: 200px;
            }
        </style>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main">
                <div class="company-container">
                    <div class="search-container">
                        <div class="search-content">
                            <!-- 由后端渲染 -->
                            <div class="main-header-showcase__container">
                                <!-- 7 个 showcase-browse 为一组 carousel-item 。如果只有一组：indicator-position=“none”；否则 indicator-position=“outside” -->
                                <el-carousel height="70px" arrow="never" trigger="click" indicator-position="outside" :autoplay="true" :interval="5000">
                                    <el-carousel-item>
                                        <a class="showcase-browse" href="" target="_blank" data-showcase-id="" data-showcase-number="">
                                            <img src="//zt.gaoxiaojob.com/lanmuT1new_10.jpg" alt="" />
                                        </a>
                                    </el-carousel-item>
                                </el-carousel>
                            </div>

                            <div class="search-main">
                                <div class="el-input el-input-group el-input--prefix el-input-group--append">
                                    <input v-model.trim="keyword" class="search-input el-input__inner" type="text" autocomplete="off" placeholder="请输入搜索关键词" @keydown.enter="handleSearch" />

                                    <span class="search-options el-input__prefix">
                                        <span class="el-input__suffix-inner">
                                            <el-cascader
                                                class="search-type"
                                                v-model="industryId"
                                                :options="industryOptions"
                                                placeholder="行业类别"
                                                :clearable="true"
                                                :show-all-levels="false"
                                                :props="{ checkStrictly: false, emitPath: false, expandTrigger: 'hover' }"
                                            />
                                        </span>
                                    </span>

                                    <div class="el-input-group__append">
                                        <button class="search-button el-button el-button--primary" @click="handleSearch"></button>
                                    </div>
                                </div>

                                <div class="search-hot">
                                    热门搜索：
                                    <a>大学教师</a>
                                    <a>高校辅导员</a>
                                    <a>博士后</a>
                                    <a>科研人员</a>
                                    <a>领军人才</a>
                                    <a>高校教</a>
                                </div>
                            </div>

                            <!-- <div class="swiper mySwiper recommend-company" v-cloak>
                                <div class="swiper-wrapper company-pane">
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[0]">
                                        <el-carousel :height="paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[0]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[1]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[1]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[2]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[2]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[3]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[3]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[4]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[4]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[5]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[5]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                </div>

                                <div class="recommend-tabs">
                                    <div class="label"><span>HOT</span>推荐单位：</div>
                                    <div class="swiper-pagination tabs" @click="handleRecommend"></div>
                                </div>
                            </div> -->
                        </div>
                    </div>

                    <div class="company-content">
                        <div class="search-filter">
                            <div class="filter-pane area special">
                                <h6 class="filter-label">工作地点</h6>
                                <div class="filter-value">
                                    <span class="el-tag" :class="{ active: areaFilter.length === 0 }" @click="handleAreaFilter('')" style="cursor: pointer">
                                        全部
                                        <i v-if="areaFilter.length === 0" class="el-icon el-icon-close el-tag__close" @click.stop="handleAreaFilter('')"></i>
                                    </span>
                                    <span
                                        v-for="area in areaOptions"
                                        :key="area.value"
                                        class="el-tag"
                                        :class="{ active: areaFilter.includes(area.value) }"
                                        @click="handleAreaFilter(area.value)"
                                        style="cursor: pointer"
                                    >
                                        {{ area.label }}
                                        <i v-if="areaFilter.includes(area.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleAreaFilter(area.value)"></i>
                                    </span>
                                </div>
                                <span class="filter-more" @click="handleShow">更多</span>
                            </div>

                            <div class="filter-pane nature special">
                                <h6 class="filter-label">单位性质</h6>
                                <div class="filter-value">
                                    <span class="el-tag" :class="{ active: natureFilter.length === 0 }" @click="handleNatureFilter('')" style="cursor: pointer">
                                        全部
                                        <i v-if="natureFilter.length === 0" class="el-icon el-icon-close el-tag__close" @click.stop="handleNatureFilter('')"></i>
                                    </span>
                                    <span
                                        v-for="nature in natureOptions"
                                        :key="nature.value"
                                        class="el-tag"
                                        :class="{ active: natureFilter.includes(nature.value) }"
                                        @click="handleNatureFilter(nature.value)"
                                        style="cursor: pointer"
                                    >
                                        {{ nature.label }}
                                        <i v-if="natureFilter.includes(nature.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleNatureFilter(nature.value)"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="filter-pane type special">
                                <h6 class="filter-label">单位类型</h6>
                                <div class="filter-value">
                                    <span class="el-tag" :class="{ active: typeFilter.length === 0 }" @click="handleTypeFilter('')" style="cursor: pointer">
                                        全部
                                        <i v-if="typeFilter.length === 0" class="el-icon el-icon-close el-tag__close" @click.stop="handleTypeFilter('')"></i>
                                    </span>
                                    <span
                                        v-for="type in typeOptions"
                                        :key="type.value"
                                        class="el-tag"
                                        :class="{ active: typeFilter.includes(type.value) }"
                                        @click="handleTypeFilter(type.value)"
                                        style="cursor: pointer"
                                    >
                                        {{ type.label }}
                                        <i v-if="typeFilter.includes(type.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleTypeFilter(type.value)"></i>
                                    </span>
                                </div>
                                <span class="filter-more" @click="handleShow">更多</span>
                            </div>

                            <div class="filter-pane weflare special">
                                <h6 class="filter-label">职位福利</h6>
                                <div class="filter-value">
                                    <span class="el-tag" :class="{ active: welfareFilter.length === 0 }" @click="handleWelfareFilter('')" style="cursor: pointer">
                                        全部
                                        <i v-if="welfareFilter.length === 0" class="el-icon el-icon-close el-tag__close" @click.stop="handleWelfareFilter('')"></i>
                                    </span>
                                    <span
                                        v-for="welfare in welfareOptions"
                                        :key="welfare.value"
                                        class="el-tag"
                                        :class="{ active: welfareFilter.includes(welfare.value) }"
                                        @click="handleWelfareFilter(welfare.value)"
                                        style="cursor: pointer"
                                    >
                                        {{ welfare.label }}
                                        <i v-if="welfareFilter.includes(welfare.value)" class="el-icon el-icon-close el-tag__close" @click.stop="handleWelfareFilter(welfare.value)"></i>
                                    </span>
                                </div>
                                <span class="filter-clear" @click="handleClear">清空筛选条件</span>
                            </div>

                            <div class="filter-pane filter-pane-more">
                                <h6 class="filter-label">更多筛选</h6>
                                <div class="filter-value">
                                    <el-select v-model="companyScaleType" placeholder="单位规模" clearable="true" @change="(val) => handleFilter(val, 'companyScaleType')">
                                        <el-option v-for="{label, value} in scaleOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>
                                </div>
                            </div>
                        </div>

                        <div class="search-result">
                            <div class="result-header">
                                <h4 class="result-title">单位列表</h4>

                                <!-- <div class="result-sort">
                                    <span :class="sort === 'default' ? 'active' : ''" @click="handleSort('default')">综合排序</span>
                                    <span :class="sort === 'update' ? 'active' : ''" @click="handleSort('update')">更新时间</span>
                                </div> -->
                            </div>

                            <div class="result-list" v-loading="loading">
                                <div class="result-item" v-for="company in companyList" :key="company.companyId">
                                    <a :href="company.url" target="_blank">
                                        <div class="company-data">
                                            <img class="logo" :src="company.logo" :alt="company.name" />

                                            <div class="data">
                                                <h5>{{ company.name }}</h5>
                                                <span class="nature">{{ company.nature }}</span>
                                            </div>
                                        </div>

                                        <div class="company-info">
                                            <span>
                                                <strong>{{ company.announcementCount }}</strong>
                                                招聘公告
                                            </span>
                                            <span>
                                                <strong>{{ company.jobCount }}</strong>
                                                招聘职位
                                            </span>
                                            <span>
                                                <strong>{{ company.activeTime }}</strong>
                                                活跃时间
                                            </span>
                                        </div>
                                    </a>
                                    <div class="collect-button" :class="{ 'is-collected': company.isCollected }" :data-id="company.companyId" @click="handleCollectClick(company.companyId)">
                                        <span>{{ company.isCollected ? '已收藏' : '收藏' }}</span>
                                    </div>
                                </div>

                                <!-- 空状态 -->
                                <div v-if="!loading && companyList.length === 0" class="empty-state">
                                    <p>暂无相关单位信息</p>
                                </div>
                            </div>

                            <el-pagination
                                v-if="!loading && companyList.length > 0"
                                background
                                :layout="'total, sizes, prev, pager, next, jumper'"
                                :current-page="currentPage"
                                :page-sizes="[20, 40, 60, 80, 100]"
                                :page-size="pageSize"
                                :total="total"
                                @size-change="handlePageSizeChange"
                                @current-change="handleCurrentPageChange"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            // 搜索相关
                            keyword: '',
                            industryId: '',
                            industryOptions: [],
                            companyScaleType: '',
                            scaleOptions: [
                                { label: '不限', value: 0 },
                                { label: '0-199人', value: 1 },
                                { label: '200-499人', value: 2 },
                                { label: '500-999人', value: 3 },
                                { label: '1000-1999人', value: 4 },
                                { label: '2000-4999人', value: 5 },
                                { label: '5000-9999人', value: 6 },
                                { label: '10000人以上', value: 7 }
                            ],

                            // 筛选相关
                            areaFilter: [],
                            natureFilter: [],
                            typeFilter: [],
                            welfareFilter: [],

                            // 筛选选项
                            areaOptions: [
                                { label: '北京', value: 'beijing' },
                                { label: '上海', value: 'shanghai' },
                                { label: '广州', value: 'guangzhou' },
                                { label: '西安', value: 'xian' },
                                { label: '杭州', value: 'hangzhou' },
                                { label: '南京', value: 'nanjing' },
                                { label: '武汉', value: 'wuhan' },
                                { label: '成都', value: 'chengdu' },
                                { label: '江苏', value: 'jiangsu' },
                                { label: '河北', value: 'hebei' }
                            ],
                            natureOptions: [
                                { label: '公立（国有）', value: 'public' },
                                { label: '民营（私营）', value: 'private' },
                                { label: '公私混合', value: 'mixed' },
                                { label: '中外合资（合营）', value: 'joint_venture' },
                                { label: '外企（外商）独资', value: 'foreign' },
                                { label: '非盈利组织及其他', value: 'nonprofit' }
                            ],
                            typeOptions: [
                                { label: '双一流院校', value: 'top_university' },
                                { label: '普通本科院校', value: 'university' },
                                { label: '高职高专院校', value: 'college' },
                                { label: '党校与行政学院', value: 'party_school' },
                                { label: '行政机关', value: 'government' },
                                { label: '军队武警', value: 'military' },
                                { label: '事业单位', value: 'institution' },
                                { label: '政府国有企业', value: 'state_enterprise' },
                                { label: '知名企业', value: 'famous_company' },
                                { label: '中小成长型企业', value: 'growing_company' },
                                { label: '银行、信用社等金融机构', value: 'financial' },
                                { label: '教育系统', value: 'education_system' },
                                { label: '中小学', value: 'primary_secondary' },
                                { label: '中专&职业中学&技师学院', value: 'vocational' },
                                { label: '幼儿园', value: 'kindergarten' },
                                { label: '中科院系统', value: 'cas' },
                                { label: '人文社科研究机构', value: 'humanities_research' },
                                { label: '自然与应用科研机构', value: 'science_research' },
                                { label: '企业研发机构', value: 'enterprise_rd' },
                                { label: '卫健委', value: 'health_commission' },
                                { label: '医院', value: 'hospital' },
                                { label: '其他医疗机构', value: 'medical_institution' }
                            ],
                            welfareOptions: [
                                { label: '安家费', value: 'settlement_fee' },
                                { label: '科研启动费', value: 'research_fund' },
                                { label: '科研奖励', value: 'research_bonus' },
                                { label: '提供住房公寓', value: 'housing' },
                                { label: '住房补贴', value: 'housing_allowance' },
                                { label: '五险一金', value: 'insurance' },
                                { label: '带薪年假', value: 'paid_leave' },
                                { label: '节日福利', value: 'holiday_benefits' },
                                { label: '周末双休', value: 'weekend' },
                                { label: '寒暑假', value: 'vacation' },
                                { label: '绩效奖金', value: 'performance_bonus' }
                            ],

                            // 排序和分页
                            sort: 'default',
                            currentPage: 1,
                            pageSize: 20,
                            total: 0,

                            // 数据状态
                            loading: false,
                            companyList: [],

                            // 用户状态
                            isLogin: false,
                            isVip: false,

                            // 轮播相关（保持原有功能）
                            paneHeight: '70px',
                            runTime: 3000,
                            running: [true, false, false, false, false, false],
                            delayTime: []
                        }
                    },

                    async mounted() {
                        // 初始化参数
                        this.initParams()

                        // 获取用户信息
                        await this.getUserInfo()

                        // 获取配置数据
                        this.getConfigData()

                        // 获取公司列表
                        this.getCompanyList()

                        // 初始化轮播（保持原有功能）
                        this.initCarousel()
                    },

                    methods: {
                        // 初始化URL参数
                        initParams() {
                            const search = window.location.search
                            const [mark, query] = search.split('?')
                            const getParams = (key) => {
                                if (new RegExp(`${key}=([^&]*)`).test(query)) {
                                    const value = decodeURIComponent(RegExp.$1)
                                    if (key === 'currentPage' || key === 'pageSize') {
                                        this[key] = /^-?\d+$/.test(value) ? value * 1 : this[key]
                                    } else {
                                        this[key] = /^-?\d+$/.test(value) ? value * 1 : value
                                    }
                                }
                            }

                            if (query) {
                                getParams('keyword')
                                getParams('industryId')
                                getParams('companyScaleType')
                                getParams('sort')
                                getParams('currentPage')
                                getParams('pageSize')
                            }
                        },

                        // 获取用户信息
                        async getUserInfo() {
                            try {
                                const data = await httpGet('/company/user-info')
                                this.isLogin = data.isLogin || false
                                this.isVip = data.isVip || false
                            } catch (error) {
                                console.error('获取用户信息失败:', error)
                            }
                        },

                        // 获取配置数据
                        async getConfigData() {
                            try {
                                const data = await httpGet('/company/config')
                                this.industryOptions = data.industryOptions || []
                            } catch (error) {
                                console.error('获取配置数据失败:', error)
                                // 使用默认数据
                                this.industryOptions = [
                                    {
                                        label: '教育/培训',
                                        value: 1,
                                        children: [
                                            { label: '学前教育', value: 18 },
                                            { label: '初等教育', value: 19 },
                                            { label: '中等教育', value: 20 },
                                            { label: '高等教育', value: 21 },
                                            { label: '职业教育', value: 22 },
                                            { label: '教育培训', value: 23 }
                                        ]
                                    }
                                ]
                            }
                        },

                        // 获取公司列表
                        async getCompanyList() {
                            this.loading = true
                            try {
                                const params = this.getSearchParams()
                                const data = await httpGet('/company/list', params)

                                this.companyList = data.list || []
                                this.total = data.total || 0

                                // 更新URL
                                this.updateURL()
                            } catch (error) {
                                console.error('获取公司列表失败:', error)
                                // 使用 mock 数据
                                this.loadMockData()
                            } finally {
                                this.loading = false
                            }
                        },

                        // 获取搜索参数
                        getSearchParams() {
                            return {
                                keyword: this.keyword,
                                industryId: this.industryId,
                                companyScaleType: this.companyScaleType,
                                sort: this.sort,
                                currentPage: this.currentPage,
                                pageSize: this.pageSize,
                                areaFilter: this.areaFilter,
                                natureFilter: this.natureFilter,
                                typeFilter: this.typeFilter,
                                welfareFilter: this.welfareFilter
                            }
                        },

                        // 加载 Mock 数据（用于演示）
                        loadMockData() {
                            const mockCompanies = [
                                {
                                    companyId: 1,
                                    name: '北京协和医学院医学中心',
                                    nature: '科研院所',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png',
                                    url: '/company/detail/1',
                                    announcementCount: '999+',
                                    jobCount: '999+',
                                    activeTime: '近30天',
                                    isCollected: false
                                },
                                {
                                    companyId: 2,
                                    name: '清华大学',
                                    nature: '双一流院校',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw311.png',
                                    url: '/company/detail/2',
                                    announcementCount: '500+',
                                    jobCount: '800+',
                                    activeTime: '近7天',
                                    isCollected: true
                                },
                                {
                                    companyId: 3,
                                    name: '北京大学',
                                    nature: '双一流院校',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw312.png',
                                    url: '/company/detail/3',
                                    announcementCount: '600+',
                                    jobCount: '900+',
                                    activeTime: '近3天',
                                    isCollected: false
                                },
                                {
                                    companyId: 4,
                                    name: '中科院计算技术研究所',
                                    nature: '科研院所',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw313.png',
                                    url: '/company/detail/4',
                                    announcementCount: '200+',
                                    jobCount: '300+',
                                    activeTime: '近15天',
                                    isCollected: false
                                },
                                {
                                    companyId: 5,
                                    name: '华为技术有限公司',
                                    nature: '知名企业',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw314.png',
                                    url: '/company/detail/5',
                                    announcementCount: '1000+',
                                    jobCount: '2000+',
                                    activeTime: '近1天',
                                    isCollected: true
                                }
                            ]

                            this.companyList = mockCompanies
                            this.total = 1000
                        },

                        updQuery(data) {
                            const base = window.location.href
                            const hasParams = base.indexOf('?') > -1
                            const baseUrl = base + (hasParams ? '' : '?')
                            const keys = Object.keys(data)

                            const result = keys.reduce((previous, current) => {
                                const value = data[current]
                                const isValid = value === null ? false : value !== ''
                                const isExist = new RegExp(`(${current}=[^&]*)`).test(previous)
                                const keyValue = isExist ? RegExp.$1 : ''

                                if (isValid) {
                                    if (isExist) {
                                        previous = previous.replace(keyValue, `${current}=${value}`)
                                    } else {
                                        previous += `&${current}=${encodeURIComponent(value)}`
                                    }
                                } else {
                                    previous = previous.replace(new RegExp(`&?${keyValue}`), '')
                                }

                                return previous.replace(/\?&/, '?')
                            }, baseUrl)

                            return result.replace(/\?$/, '')
                        },

                        // 更新URL
                        updateURL() {
                            const params = this.getSearchParams()
                            const url = this.updQuery(params)
                            window.history.replaceState(params, '', url)
                        },

                        // 搜索处理
                        handleSearch() {
                            this.currentPage = 1
                            this.getCompanyList()
                        },

                        // 分页大小变化
                        handlePageSizeChange(val) {
                            this.pageSize = val
                            this.currentPage = 1
                            this.getCompanyList()
                        },

                        // 当前页变化
                        handleCurrentPageChange(val) {
                            this.currentPage = val
                            this.getCompanyList()
                        },

                        // 收藏处理
                        async handleCollectClick(companyId) {
                            if (!this.isLogin) {
                                // 显示登录弹窗
                                window.globalComponents?.loginDialogComponent?.showLoginDialog()
                                return
                            }

                            try {
                                const data = await httpPost('/api/person/company/collect', { companyId })

                                // 更新本地状态
                                const company = this.companyList.find((item) => item.companyId === companyId)
                                if (company) {
                                    company.isCollected = !company.isCollected
                                }

                                ElementPlus.ElMessage.success(company.isCollected ? '收藏成功' : '取消收藏成功')
                            } catch (error) {
                                console.error('收藏操作失败:', error)
                            }
                        },

                        // 筛选处理
                        handleFilter(val, key) {
                            this[key] = val
                            if (key !== 'currentPage') {
                                this.currentPage = 1
                            }
                            this.getCompanyList()
                        },

                        // 地区筛选处理
                        handleAreaFilter(value) {
                            if (value === '') {
                                this.areaFilter = []
                            } else {
                                const index = this.areaFilter.indexOf(value)
                                if (index > -1) {
                                    this.areaFilter.splice(index, 1)
                                } else {
                                    this.areaFilter.push(value)
                                }
                            }
                            this.currentPage = 1
                            this.getCompanyList()
                        },

                        // 性质筛选处理
                        handleNatureFilter(value) {
                            if (value === '') {
                                this.natureFilter = []
                            } else {
                                const index = this.natureFilter.indexOf(value)
                                if (index > -1) {
                                    this.natureFilter.splice(index, 1)
                                } else {
                                    this.natureFilter.push(value)
                                }
                            }
                            this.currentPage = 1
                            this.getCompanyList()
                        },

                        // 类型筛选处理
                        handleTypeFilter(value) {
                            if (value === '') {
                                this.typeFilter = []
                            } else {
                                const index = this.typeFilter.indexOf(value)
                                if (index > -1) {
                                    this.typeFilter.splice(index, 1)
                                } else {
                                    this.typeFilter.push(value)
                                }
                            }
                            this.currentPage = 1
                            this.getCompanyList()
                        },

                        // 福利筛选处理
                        handleWelfareFilter(value) {
                            if (value === '') {
                                this.welfareFilter = []
                            } else {
                                const index = this.welfareFilter.indexOf(value)
                                if (index > -1) {
                                    this.welfareFilter.splice(index, 1)
                                } else {
                                    this.welfareFilter.push(value)
                                }
                            }
                            this.currentPage = 1
                            this.getCompanyList()
                        },

                        // 初始化轮播
                        initCarousel() {
                            var $panes = $('.recommend-company .pane')

                            // 获取每个分类的页数，赋予对应轮播时间
                            for (var i = 0; i < $panes.length; i++) {
                                let time = 0
                                let length = $panes.eq(i).find('.el-carousel__item').length

                                // 当分类的页数超过2页才显示轮播点
                                if (length > 1) {
                                    $panes.eq(i).find('.el-carousel__indicators').css('visibility', 'visible')
                                }

                                time = length * this.runTime
                                this.delayTime.push(time)
                            }
                        },

                        // 重置当前tab图片自动轮播状态
                        handleRecommend(e) {
                            const { nodeName } = e.target
                            const index = $(e.target).index()
                            const $recommend = $('.recommend-company .pane').eq(index)

                            if (nodeName === 'SPAN') {
                                $recommend.find('.el-carousel__indicator')[0].click()
                                this.running.fill(false)[$(e.target).index()] = true
                            }
                        },

                        handleShow(e) {
                            const $this = $(e.target)
                            const $parent = $this.parent()

                            $this.toggleClass('is-reverse')
                            $parent.toggleClass('is-show')
                        },

                        handleFilter(val, key) {
                            const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                            if (key !== 'page') {
                                query.page = 1
                            }
                            window.location.href = this.updQuery({ [key]: val })
                        },

                        handleSort(val) {
                            this.sort = val
                            this.handleFilter(val, 'sort')
                        },

                        handleClear() {
                            // 清空所有筛选条件，保留关键词和行业
                            this.areaFilter = []
                            this.natureFilter = []
                            this.typeFilter = []
                            this.welfareFilter = []
                            this.companyScaleType = ''
                            this.currentPage = 1

                            // 重新获取数据
                            this.getCompanyList()
                        }
                    }
                }

                Vue.createApp(component)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#component')
            })
        </script>

        <script src="./lib/swiper/swiper.min.js"></script>

        <script>
            $(function () {
                const companyArray = ['高等学院', '科研院校', '医疗卫生', '中小学校', '机关事业', '知名企业']

                new Swiper('.mySwiper', {
                    loop: true,
                    simulateTouch: false,
                    autoplay: {
                        pauseOnMouseEnter: true,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                        renderBullet: function (index, className) {
                            return `<span class="${className}">${companyArray[index]}</span>`
                        }
                    },
                    on: {
                        slideChangeTransitionStart: function () {
                            this.activeIndex = this.activeIndex > companyArray.length ? 1 : this.activeIndex
                            $('.tabs span')
                                .eq(this.activeIndex - 1)
                                .click()
                        }
                    }
                })
            })
        </script>
    </body>
</html>
