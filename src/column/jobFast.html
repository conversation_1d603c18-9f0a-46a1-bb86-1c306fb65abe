<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>求职快</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/jobFast.css?v=1.0.0" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message">
                        <!-- 有消息 -->
                        <el-badge :value="100" class="item"><i class="el-icon-bell"></i></el-badge>
                        <!-- 无消息 -->
                        <!-- <i class="el-icon-bell"></i> -->
                    </a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,
                                    type: '1',
                                    typeOptions: [
                                        {
                                            label: '职位',
                                            value: '1',
                                            path: '/job'
                                        },
                                        {
                                            label: '公告',
                                            value: '2',
                                            path: '/search'
                                        },
                                        {
                                            label: '单位',
                                            value: '3',
                                            path: '/company'
                                        },
                                        {
                                            label: '资讯',
                                            value: '4',
                                            path: '/search',
                                            query: 'type=2'
                                        }
                                    ],

                                    keyword: ''
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <a href="#package-wrapper" class="banner"></a>

            <div class="introduce-wrapper">
                <div class="main-wrapper">
                    <div class="wrapper-title">如果您也有以下求职困扰，建议了解「求职快」服务</div>
                    <div class="compare"></div>
                </div>
            </div>

            <div class="privilege-wrapper gray-bg">
                <div class="main-wrapper">
                    <div class="wrapper-title">三大特权</div>
                    <div class="privilege-content">
                        <div class="review">
                            <div class="swiper resume-top-swiper">
                                <div class="swiper-wrapper">
                                    <div :class="`swiper-slide slide${index}`" v-for="index in 3">
                                        <div class="effect"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-pagination resume-top-swiper-pagination"></div>
                        </div>
                        <div class="introduce">
                            <div class="title resume-top">简历置顶</div>
                            <div class="desc">简历置顶展示在高校人才网硕博人才库，从50W+人才中脱颖而出，加速提升简历曝光及求职效率。</div>
                            <p class="item">单位打开高校人才网人才库，<span class="primary">优先看到您</span></p>
                            <p class="item">单位搜索目标人选，您的简历<span class="primary">优先展示</span></p>
                            <p class="item">算法匹配全平台优质职位，简历<span class="primary">优先展示</span>于在招岗位匹配人才列表</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="privilege-wrapper">
                <div class="main-wrapper">
                    <div class="privilege-content reverse">
                        <div class="review">
                            <div class="swiper deliver-top-swiper">
                                <div class="swiper-wrapper">
                                    <div :class="`swiper-slide slide${index}`" v-for="index in 2">
                                        <div class="effect"></div>
                                        <div v-if="index === 2" class="effect-next"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-pagination deliver-top-swiper-pagination"></div>
                        </div>
                        <div class="introduce">
                            <div class="title deliver-top">投递置顶</div>
                            <div class="desc">您的投递信息将通过多渠道实时通知至单位端，并置顶展示在单位端投递列表，优先获取单位关注，便于单位第一时间查看及处理您的简历！</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="privilege-wrapper gray-bg">
                <div class="main-wrapper">
                    <div class="privilege-content">
                        <div class="review refresh-gif"></div>
                        <div class="introduce">
                            <div class="title refresh">简历刷新</div>
                            <div class="desc">刷新简历可提高简历在高校人才网硕博人才库中的排名，靠前的排名更有助于获得单位关注。</div>
                            <div class="desc">系统每天自动为您刷新简历，同时您也可以无限次主动刷新简历，让您的简历排名始终靠前！</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="evaluate-wrapper">
                <div class="main-wrapper">
                    <div class="wrapper-title">用户评价</div>
                    <div class="evaluate-content">
                        <div class="list">
                            <div class="avatar">
                                <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-1.png" alt="" />
                            </div>
                            <div class="name">陈*辉</div>
                            <div class="ask">华南理工大学丨硕士丨材料科学与工程</div>
                            <div class="content">很满意！使用了投递置顶和简历置顶功能后，投递回复率和单位邀约的数量有明显提升，真的很有效，希望可以尽快拿到好的offer。</div>
                            <div class="tag">
                                <span>效果明显</span>
                                <span>回复快</span>
                            </div>
                        </div>
                        <div class="list">
                            <div class="avatar">
                                <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-2.png" alt="" />
                            </div>
                            <div class="name">李*静</div>
                            <div class="ask">中南大学丨博士丨土木工程</div>
                            <div class="content">投递置顶非常好用。之前投递简历总是石沉大海杳无音信，使用了投递置顶后一周内基本都会收到回复，目前也拿到了2个offer。</div>
                            <div class="tag">
                                <span>效果明显</span>
                                <span>收到面试快</span>
                            </div>
                        </div>
                        <div class="list">
                            <div class="avatar">
                                <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-3.png" alt="" />
                            </div>
                            <div class="name">柴*</div>
                            <div class="ask">密歇根州立大学丨硕士丨经济学</div>
                            <div class="content">被毕业论文折磨的孩子……很需要自动刷新简历提升活跃度。在没有那么多时间主动找工作的情况下，也会有很多单位来联系我，就挺省心。</div>
                            <div class="tag">
                                <span>性价比高</span>
                                <span>体验好</span>
                            </div>
                        </div>
                        <div class="list">
                            <div class="avatar">
                                <img src="//img.gaoxiaojob.com/uploads/image/purchase/pc/fast/avatar-4.png" alt="" />
                            </div>
                            <div class="name">黄*樊</div>
                            <div class="ask">浙江师范大学丨博士丨教育学</div>
                            <div class="content">自我感觉自身条件在高校人才网硕博人才堆里不占优势，抱着试试看的态度购买了服务，近期已有几家单位邀请我去面试...</div>
                            <div class="tag">
                                <span>值得购买</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="package-wrapper" id="package-wrapper">
                <div class="main-wrapper">
                    <div class="wrapper-title">立即解锁高效求职特权</div>
                    <div class="package">
                        <div
                            :class="`list ${index === hoverPackageIndex ? 'active' : ''}`"
                            v-for="(item, index) in packageList"
                            :key="index"
                            @mouseenter="packageSelect(item, index)"
                            @click="openpayDialog(item.equityPackageCategoryId, index)"
                        >
                            <div v-if="item.buyTypeTxt" class="tag">{{item.buyTypeTxt}}</div>
                            <div class="name">{{item.name}}</div>
                            <div class="detail">
                                <div class="item" v-for="(equity, i) in item.equityList" :key="i">
                                    <div class="label">{{equity.name}}*<span>{{equity.description}}</span></div>
                                    <div class="value">{{equity.singlePrice}}</div>
                                </div>
                            </div>
                            <div class="price">
                                <div class="real">{{item.realAmount}}</div>
                                <div class="original">￥{{item.originalAmount}}</div>
                            </div>
                            <div class="avg-price">已优惠￥{{item.discountAmount}}</div>
                            <div class="open">立即开通</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="instructions-wrapper gray-bg">
                <div class="main-wrapper">
                    <div class="title">服务说明</div>
                    <div class="instructions-content">
                        <p>1、请在本页面通过官方支付方式——微信扫码支付，并向唯一官方收款方“高校人才网”完成服务费用的支付。请勿尝试任何私下转账方式。</p>
                        <p>2、服务将于付款成功后自动开通，服务过期则所有权益失效。购买后请合理安排时间并尽快使用。</p>
                        <p>3、竞争力分析&公告热度权益，在服务期内，按自然日，每项服务可使用的次数上限为10次，不累计。</p>
                        <p>4、使用简历置顶 & 简历刷新权益时，请务必确认您未“隐藏简历”，且在线简历完整度≥65%（以平台显示的完整度为准），否则无法提升简历曝光效果。</p>
                        <p>5、投递置顶权益限“报名方式”为“站内投递”的职位使用；您可在投递简历时选择是否使用该项服务。同一职位，30天内只能使用一次投递置顶权益.</p>
                        <p>6、您屏蔽的单位无法搜索到您的简历，请放心使用服务。</p>
                        <p>7、不同城市、职位类型下的曝光效果不同，实际曝光效果可能会存在波动。</p>
                        <p>8、本产品为虚拟服务，不支持退款，敬请谅解。</p>
                        <p>
                            9、购买即表示同意<a href="/agreement/value-added-services" target="_blank"><span>《高校人才网增值服务协议》</span></a
                            >。
                        </p>
                    </div>
                </div>
            </div>

            <div v-if="showFooterFixed" class="fixed-wrapper">
                <div class="main-wrapper">
                    <div class="buy-info">
                        <div class="real">{{selectPackage.realAmount}}</div>
                        元/{{selectPackage.days}}天
                        <div class="original">{{selectPackage.originalAmount}}元</div>
                    </div>
                    <button class="pay" @click="openVipDialog">立即开通</button>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            hoverPackageIndex: 1,
                            selectPackage: {
                                equityPackageCategoryId: 1,
                                realAmount: 98,
                                originalAmount: 128,
                                days: 30
                            },
                            packageList: [
                                {
                                    equityPackageId: '11',
                                    equityPackageCategoryId: '4',
                                    originalAmount: '48',
                                    realAmount: '28',
                                    name: '求职快(30天)',
                                    subname: '求职快',
                                    days: '30',
                                    buyDesc: '尝鲜体验丰富会员权益',
                                    buyType: '1',
                                    buyTypeTxt: '体验版',
                                    discountAmount: 20,
                                    equityList: [
                                        {
                                            id: '9',
                                            name: '简历刷新',
                                            description: '无限刷新'
                                        },
                                        {
                                            id: '10',
                                            name: '简历置顶',
                                            description: '12天'
                                        },
                                        {
                                            id: '11',
                                            name: '投递置顶',
                                            description: '9次'
                                        }
                                    ],
                                    isUpgrade: false,
                                    upgradeData: [],
                                    dailyAmount: 0.93
                                },
                                {
                                    equityPackageId: '12',
                                    equityPackageCategoryId: '4',
                                    originalAmount: '88',
                                    realAmount: '48',
                                    name: '求职快(90天)',
                                    subname: '求职快',
                                    days: '90',
                                    buyDesc: '省心畅享整个招聘季',
                                    buyType: '2',
                                    buyTypeTxt: '热销',
                                    discountAmount: 40,
                                    equityList: [
                                        {
                                            id: '9',
                                            name: '简历刷新',
                                            description: '无限刷新'
                                        },
                                        {
                                            id: '10',
                                            name: '简历置顶',
                                            description: '45天'
                                        },
                                        {
                                            id: '11',
                                            name: '投递置顶',
                                            description: '27次'
                                        }
                                    ],
                                    isUpgrade: false,
                                    upgradeData: [],
                                    dailyAmount: 0.53
                                },
                                {
                                    equityPackageId: '13',
                                    equityPackageCategoryId: '4',
                                    originalAmount: '168',
                                    realAmount: '68',
                                    name: '求职快(180天)',
                                    subname: '求职快',
                                    days: '180',
                                    buyDesc: '加赠价值¥318求职课程包',
                                    buyType: '0',
                                    buyTypeTxt: '',
                                    discountAmount: 100,
                                    equityList: [
                                        {
                                            id: '9',
                                            name: '简历刷新',
                                            description: '无限刷新'
                                        },
                                        {
                                            id: '10',
                                            name: '简历置顶',
                                            description: '90天'
                                        },
                                        {
                                            id: '11',
                                            name: '投递置顶',
                                            description: '54次'
                                        }
                                    ],
                                    isUpgrade: false,
                                    upgradeData: [],
                                    dailyAmount: 0.37
                                }
                            ],
                            showFooterFixed: false
                        }
                    },
                    methods: {
                        initSwiper() {
                            const swiperArray = ['resume-top-swiper', 'deliver-top-swiper']
                            swiperArray.map(function (item) {
                                new Swiper(`.${item}`, {
                                    autoplay: {
                                        delay: 5000,
                                        pauseOnMouseEnter: true,
                                        disableOnInteraction: false
                                    },
                                    loop: true,
                                    pagination: {
                                        el: `.${item}-pagination`,
                                        clickable: true
                                    }
                                })
                            })
                        },
                        scroll() {
                            const _this = this
                            window.addEventListener('scroll', function () {
                                const bannerHeight = 420
                                const bannerOffsetTop = bannerHeight + 68
                                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                                const pageHeight = document.documentElement.scrollHeight || document.body.scrollHeight
                                const creenHeight = document.body.clientHeight
                                _this.showFooterFixed = bannerOffsetTop < scrollTop && scrollTop < pageHeight - creenHeight * 2
                            })
                        },
                        packageSelect(item, index) {
                            this.hoverPackageIndex = index
                            this.selectPackage = item
                        },
                        openVipDialog() {
                            const {
                                selectPackage: { equityPackageCategoryId },
                                hoverPackageIndex
                            } = this
                            this.openpayDialog(equityPackageCategoryId, hoverPackageIndex, 2)
                        },
                        openpayDialog(id, index, position = 1) {
                            //position点击开通位置，1是页面套餐选择，2是底部开通按钮
                            //-------------用户付费转化数据埋点-开始---------------
                            // let productName = $('.list:eq('+index+') .name').text()
                            // let logData = {
                            //     params : { productId:id, productName:productName, clickPosition: position  },
                            //     actionType : '1',
                            //     actionId : '10030002'
                            // }
                            // this.payBuriedPoint(logData)
                            //-------------用户付费转化数据埋点-结束---------------

                            let api = "<?=$data['api_popup']?>"
                            let uuid = "<?=$data['uuid']?>"
                            window.globalComponents.PayDialogAlertComponent.show(api, id, index, uuid, position)
                        },
                        getPackage(type) {
                            // type 4求职快
                            httpGet('/api/person/resume-equity-package/get-buy-package-list?equityPackageCategoryId=' + type).then((r) => {
                                const { isDiamondVip, isGoldVip, list } = r
                                this.packageList = list
                                this.selectPackage = list[1]
                            })
                        },
                        payBuriedPoint(data) {
                            const jsonString = JSON.stringify(data)

                            let configKey = '123abcgaoxiaorencaiwang'
                            let code = CryptoJS.MD5(configKey).toString()
                            let iv = CryptoJS.enc.Utf8.parse(code.substring(0, 16))
                            let key = CryptoJS.enc.Utf8.parse(code.substring(16))

                            let encryptString = CryptoJS.AES.encrypt(jsonString, key, { mode: CryptoJS.mode.ECB, iv: iv })
                            let img = new Image()
                            img.src = `3.gif?data=` + encodeURIComponent(encryptString)
                        }
                    },
                    mounted() {
                        this.scroll()
                        this.initSwiper()
                        this.getPackage(4)
                        //-------------用户付费转化数据埋点-开始---------------
                        window.onbeforeunload = function () {
                            httpGet("/showcase-browse-log/update-job-fast-view-point-log?uuid=<?=$data['uuid']?>")
                        }
                        //-------------用户付费转化数据埋点-结束---------------
                    }
                }

                Vue.createApp(component).use(ElementPlus).mount('#component')
            })
        </script>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>
    </body>
</html>
