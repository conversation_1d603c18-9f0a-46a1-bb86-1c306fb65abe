@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'common/var' as *;

@include b(upload) {
    display: inline-block;
    text-align: center;
    cursor: pointer;
    outline: none;
    @include e(input) {
        display: none;
    }

    @include e(tip) {
        font-size: 12px;
        color: var(--el-text-color-regular);
        margin-top: 7px;
    }

    iframe {
        position: absolute;
        z-index: -1;
        top: 0;
        left: 0;
        opacity: 0;
        filter: alpha(opacity=0);
    }

    /* 照片墙模式 */
    @include m(picture-card) {
        background-color: #fbfdff;
        border: 1px dashed #c0ccda;
        border-radius: 6px;
        box-sizing: border-box;
        width: 148px;
        height: 148px;
        cursor: pointer;
        line-height: 146px;
        vertical-align: top;

        i {
            font-size: 28px;
            color: #8c939d;
        }

        &:hover {
            border-color: var(--el-color-primary);
            color: var(--el-color-primary);
        }
    }
    &:focus {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);

        .#{$namespace}-upload-dragger {
            border-color: var(--el-color-primary);
        }
    }
}

@include b(upload-dragger) {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width: 360px;
    height: 180px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    .#{$namespace}-icon-upload {
        font-size: 67px;
        color: var(--el-text-color-placeholder);
        margin: 40px 0 16px;
        line-height: 50px;
    }

    + .#{$namespace}-upload__tip {
        text-align: center;
    }

    ~ .#{$namespace}-upload__files {
        border-top: $border-base;
        margin-top: 7px;
        padding-top: 5px;
    }

    .#{$namespace}-upload__text {
        color: var(--el-text-color-regular);
        font-size: 14px;
        text-align: center;

        em {
            color: var(--el-color-primary);
            font-style: normal;
        }
    }

    &:hover {
        border-color: var(--el-color-primary);
    }

    @include when(dragover) {
        background-color: rgba(32, 159, 255, 0.06);
        border: 2px dashed var(--el-color-primary);
    }
}

@include b(upload-list) {
    margin: 0;
    padding: 0;
    list-style: none;

    @include e(item) {
        transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
        font-size: 14px;
        color: var(--el-text-color-regular);
        line-height: 1.8;
        margin-top: 5px;
        position: relative;
        box-sizing: border-box;
        border-radius: 4px;
        width: 100%;

        .#{$namespace}-progress {
            position: absolute;
            top: 20px;
            width: 100%;
        }

        .#{$namespace}-progress__text {
            position: absolute;
            right: 0;
            top: -13px;
        }

        .#{$namespace}-progress-bar {
            margin-right: 0;
            padding-right: 0;
        }

        &:first-child {
            margin-top: 10px;
        }

        & .#{$namespace}-icon-upload-success {
            color: var(--el-color-success);
        }

        .#{$namespace}-icon-close {
            display: none;
            position: absolute;
            top: 5px;
            right: 5px;
            cursor: pointer;
            opacity: 0.75;
            color: var(--el-text-color-regular);
            //transform: scale(.7);

            &:hover {
                opacity: 1;
            }
        }

        & .#{$namespace}-icon-close-tip {
            display: none;
            position: absolute;
            top: 5px;
            right: 5px;
            font-size: 12px;
            cursor: pointer;
            opacity: 1;
            color: var(--el-color-primary);
        }

        &:hover {
            background-color: $background-color-base;

            .#{$namespace}-icon-close {
                display: inline-block;
            }

            .#{$namespace}-progress__text {
                display: none;
            }
        }

        @include when(success) {
            .#{$namespace}-upload-list__item-status-label {
                display: block;
            }

            .#{$namespace}-upload-list__item-name:hover,
            .#{$namespace}-upload-list__item-name:focus {
                color: var(--el-color-primary);
                cursor: pointer;
            }

            &:focus:not(:hover) {
                /* 键盘focus */
                .#{$namespace}-icon-close-tip {
                    display: inline-block;
                }
            }

            &:not(.focusing):focus,
            &:active {
                /* click时 */
                outline-width: 0;
                .#{$namespace}-icon-close-tip {
                    display: none;
                }
            }

            &:hover,
            &:focus {
                .#{$namespace}-upload-list__item-status-label {
                    display: none;
                }
            }
        }
    }

    @include when(disabled) {
        .#{$namespace}-upload-list__item:hover .#{$namespace}-upload-list__item-status-label {
            display: block;
        }
    }

    @include e(item-name) {
        color: var(--el-text-color-regular);
        display: block;
        margin-right: 40px;
        overflow: hidden;
        padding-left: 4px;
        text-overflow: ellipsis;
        transition: color var(--el-transition-duration);
        white-space: nowrap;

        [class^='#{$namespace}-icon'] {
            height: 100%;
            margin-right: 7px;
            color: var(--el-text-color-secondary);
            line-height: inherit;
        }
    }

    @include e(item-status-label) {
        position: absolute;
        right: 5px;
        top: 0;
        line-height: inherit;
        display: none;
    }

    @include e(item-delete) {
        position: absolute;
        right: 10px;
        top: 0;
        font-size: 12px;
        color: var(--el-text-color-regular);
        display: none;

        &:hover {
            color: var(--el-color-primary);
        }
    }

    @include m(picture-card) {
        margin: 0;
        display: inline;
        vertical-align: top;

        .#{$namespace}-upload-list__item {
            overflow: hidden;
            background-color: #fff;
            border: 1px solid #c0ccda;
            border-radius: 6px;
            box-sizing: border-box;
            width: 148px;
            height: 148px;
            margin: 0 8px 8px 0;
            display: inline-block;

            .#{$namespace}-icon-check,
            .#{$namespace}-icon-circle-check {
                color: $color-white;
            }

            .#{$namespace}-icon-close {
                display: none;
            }
            &:hover {
                .#{$namespace}-upload-list__item-status-label {
                    display: none;
                }

                .#{$namespace}-progress__text {
                    display: block;
                }
            }
        }

        .#{$namespace}-upload-list__item-name {
            display: none;
        }

        .#{$namespace}-upload-list__item-thumbnail {
            width: 100%;
            height: 100%;
        }

        .#{$namespace}-upload-list__item-status-label {
            position: absolute;
            right: -15px;
            top: -6px;
            width: 40px;
            height: 24px;
            background: #13ce66;
            text-align: center;
            transform: rotate(45deg);
            box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);

            i {
                font-size: 12px;
                margin-top: 11px;
                transform: rotate(-45deg);
            }
        }

        .#{$namespace}-upload-list__item-actions {
            position: absolute;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            cursor: default;
            text-align: center;
            color: #fff;
            opacity: 0;
            font-size: 20px;
            background-color: rgba(0, 0, 0, 0.5);
            transition: opacity var(--el-transition-duration);
            &::after {
                display: inline-block;
                content: '';
                height: 100%;
                vertical-align: middle;
            }

            span {
                display: none;
                cursor: pointer;
            }

            span + span {
                margin-left: 15px;
            }

            .#{$namespace}-upload-list__item-delete {
                position: static;
                font-size: inherit;
                color: inherit;
            }

            &:hover {
                opacity: 1;
                span {
                    display: inline-block;
                }
            }
        }

        .#{$namespace}-progress {
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            bottom: auto;
            width: 126px;

            .#{$namespace}-progress__text {
                top: 50%;
            }
        }
    }

    @include m(picture) {
        .#{$namespace}-upload-list__item {
            overflow: hidden;
            z-index: 0;
            background-color: #fff;
            border: 1px solid #c0ccda;
            border-radius: 6px;
            box-sizing: border-box;
            margin-top: 10px;
            padding: 10px 10px 10px 90px;
            height: 92px;

            .#{$namespace}-icon-check,
            .#{$namespace}-icon-circle-check {
                color: $color-white;
            }

            &:hover {
                .#{$namespace}-upload-list__item-status-label {
                    background: transparent;
                    box-shadow: none;
                    top: -2px;
                    right: -12px;
                }

                .#{$namespace}-progress__text {
                    display: block;
                }
            }

            &.is-success {
                .#{$namespace}-upload-list__item-name {
                    line-height: 70px;
                    margin-top: 0;
                    i {
                        display: none;
                    }
                }
            }
        }

        .#{$namespace}-upload-list__item-thumbnail {
            vertical-align: middle;
            display: inline-block;
            width: 70px;
            height: 70px;
            float: left;
            position: relative;
            z-index: 1;
            margin-left: -80px;
            background-color: $color-white;
        }

        .#{$namespace}-upload-list__item-name {
            display: block;
            margin-top: 20px;

            i {
                font-size: 70px;
                line-height: 1;
                position: absolute;
                left: 9px;
                top: 10px;
            }
        }

        .#{$namespace}-upload-list__item-status-label {
            position: absolute;
            right: -17px;
            top: -7px;
            width: 46px;
            height: 26px;
            background: #13ce66;
            text-align: center;
            transform: rotate(45deg);
            box-shadow: 0 1px 1px #ccc;

            i {
                font-size: 12px;
                margin-top: 12px;
                transform: rotate(-45deg);
            }
        }

        .#{$namespace}-progress {
            position: relative;
            top: -7px;
        }
    }
}

@include b(upload-cover) {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 10;
    cursor: default;
    @include utils-vertical-center;

    img {
        display: block;
        width: 100%;
        height: 100%;
    }

    @include e(label) {
        position: absolute;
        right: -15px;
        top: -6px;
        width: 40px;
        height: 24px;
        background: #13ce66;
        text-align: center;
        transform: rotate(45deg);
        box-shadow: 0 0 1pc 1px rgba(0, 0, 0, 0.2);

        i {
            font-size: 12px;
            margin-top: 11px;
            transform: rotate(-45deg);
            color: #fff;
        }
    }

    @include e(progress) {
        display: inline-block;
        vertical-align: middle;
        position: static;
        width: 243px;

        + .#{$namespace}-upload__inner {
            opacity: 0;
        }
    }

    @include e(content) {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    @include e(interact) {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(#000, 0.72);
        text-align: center;

        .btn {
            display: inline-block;
            color: $color-white;
            font-size: 14px;
            cursor: pointer;
            vertical-align: middle;
            transition: var(--el-transition-md-fade);
            margin-top: 60px;

            i {
                margin-top: 0;
            }

            span {
                opacity: 0;
                transition: opacity 0.15s linear;
            }

            &:not(:first-child) {
                margin-left: 35px;
            }

            &:hover {
                transform: translateY(-13px);

                span {
                    opacity: 1;
                }
            }

            i {
                color: $color-white;
                display: block;
                font-size: 24px;
                line-height: inherit;
                margin: 0 auto 5px;
            }
        }
    }

    @include e(title) {
        position: absolute;
        bottom: 0;
        left: 0;
        background-color: $color-white;
        height: 36px;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: normal;
        text-align: left;
        padding: 0 10px;
        margin: 0;
        line-height: 36px;
        font-size: 14px;
        color: var(--el-text-color-primary);
    }

    + .#{$namespace}-upload__inner {
        opacity: 0;
        position: relative;
        z-index: 1;
    }
}
