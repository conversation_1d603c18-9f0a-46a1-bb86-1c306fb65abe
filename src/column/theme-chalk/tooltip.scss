@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

$positions: top, bottom, left, right;

$positionMap: (
    'top': 'bottom',
    'bottom': 'top',
    'left': 'right',
    'right': 'left'
);

@include b(tooltip) {
    @include set-component-css-var('tooltip', $tooltip);
}

@include b(tooltip) {
    &:focus:not(.focusing),
    &:focus:hover {
        outline-width: 0;
    }
    @include e(popper) {
        position: absolute;
        border-radius: 4px;
        padding: var(--el-tooltip-padding);
        z-index: var(--el-index-popper);
        font-size: var(--el-tooltip-font-size);
        line-height: 1.2;
        min-width: 10px;
        word-wrap: break-word;

        .popper__arrow,
        .popper__arrow::after {
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            border-color: transparent;
            border-style: solid;
        }

        .popper__arrow {
            border-width: var(--el-tooltip-arrow-size);
        }

        .popper__arrow::after {
            content: ' ';
            border-width: 5px;
        }

        @each $position in $positions {
            &[x-placement^='#{$position}'] {
                margin-#{map.get($positionMap, $position)}: calc(var(--el-tooltip-arrow-size) + 6px);
            }

            &[x-placement^='#{$position}'] .popper__arrow {
                #{map.get($positionMap, $position)}: calc(0px - var(--el-tooltip-arrow-size));
                border-#{$position}-color: var(--el-tooltip-border-color);
                border-#{map.get($positionMap, $position)}-width: 0;

                &::after {
                    #{map.get($positionMap, $position)}: 1px;
                    border-#{$position}-color: var(--el-tooltip-fill);
                    border-#{map.get($positionMap, $position)}-width: 0;
                }
            }
        }

        @include when(dark) {
            background: var(--el-tooltip-fill);
            color: var(--el-tooltip-color);
        }

        @include when(light) {
            background: var(--el-tooltip-color);
            border: 1px solid var(--el-tooltip-fill);

            @each $position in $positions {
                &[x-placement^='#{$position}'] .popper__arrow {
                    border-top-color: var(--el-tooltip-fill);
                    &::after {
                        border-top-color: var(--el-tooltip-color);
                    }
                }
            }
        }
    }
}
