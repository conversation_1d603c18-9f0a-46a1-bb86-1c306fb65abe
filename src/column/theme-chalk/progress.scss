@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'common/var' as *;

@include b(progress) {
    position: relative;
    line-height: 1;
    display: flex;
    align-items: center;

    @include e(text) {
        font-size: 14px;
        color: var(--el-text-color-regular);
        margin-left: 5px;
        min-width: 50px;
        line-height: 1;

        i {
            vertical-align: middle;
            display: block;
        }
    }

    @include m((circle, dashboard)) {
        display: inline-block;

        .#{$namespace}-progress__text {
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            text-align: center;
            margin: 0;
            transform: translate(0, -50%);

            i {
                vertical-align: middle;
                display: inline-block;
            }
        }
    }

    @include m(without-text) {
        .#{$namespace}-progress__text {
            display: none;
        }

        .#{$namespace}-progress-bar {
            padding-right: 0;
            margin-right: 0;
            display: block;
        }
    }

    @include m(text-inside) {
        .#{$namespace}-progress-bar {
            padding-right: 0;
            margin-right: 0;
        }
    }

    @include when(success) {
        .#{$namespace}-progress-bar__inner {
            background-color: var(--el-color-success);
        }

        .#{$namespace}-progress__text {
            color: var(--el-color-success);
        }
    }

    @include when(warning) {
        .#{$namespace}-progress-bar__inner {
            background-color: var(--el-color-warning);
        }

        .#{$namespace}-progress__text {
            color: var(--el-color-warning);
        }
    }

    @include when(exception) {
        .#{$namespace}-progress-bar__inner {
            background-color: var(--el-color-danger);
        }

        .#{$namespace}-progress__text {
            color: var(--el-color-danger);
        }
    }
}

@include b(progress-bar) {
    flex-grow: 1;
    box-sizing: border-box;

    @include e(outer) {
        height: 6px;
        border-radius: 100px;
        background-color: var(--el-border-color-lighter);
        overflow: hidden;
        position: relative;
        vertical-align: middle;
    }
    @include e(inner) {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background-color: var(--el-color-primary);
        text-align: right;
        border-radius: 100px;
        line-height: 1;
        white-space: nowrap;
        transition: width 0.6s ease;

        @include utils-vertical-center;

        @include m(indeterminate) {
            transform: translateZ(0);
            animation: indeterminate 3s infinite;
        }
    }

    @include e(innerText) {
        display: inline-block;
        vertical-align: middle;
        color: $color-white;
        font-size: 12px;
        margin: 0 5px;
    }
}

@keyframes progress {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: 32px 0;
    }
}

@keyframes indeterminate {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}
