@use 'mixins/mixins' as *;
@use 'common/var' as *;

%size {
    width: 100%;
    height: 100%;
}

@include b(image) {
    position: relative;
    display: inline-block;
    overflow: hidden;

    @include e(inner) {
        @extend %size;
        vertical-align: top;
    }

    @include e(placeholder) {
        @extend %size;
        background: $background-color-base;
    }

    @include e(error) {
        @extend %size;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;
        background: $background-color-base;
        color: var(--el-text-color-placeholder);
        vertical-align: middle;
    }

    @include e(preview) {
        cursor: pointer;
    }
}
