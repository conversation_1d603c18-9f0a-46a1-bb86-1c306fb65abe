@use '../mixins/mixins' as *;
@use '../common/var' as *;

@include b(picker-panel) {
    color: var(--el-text-color-regular);
    background: $color-white;
    border-radius: var(--el-border-radius-base);
    line-height: 30px;

    .#{$namespace}-time-panel {
        margin: 5px 0;
        border: solid 1px var(--el-datepicker-border-color);
        background-color: $color-white;
        box-shadow: var(--el-box-shadow-light);
    }

    @include e((body, body-wrapper)) {
        &::after {
            content: '';
            display: table;
            clear: both;
        }
    }

    @include e(content) {
        position: relative;
        margin: 15px;
    }

    @include e(footer) {
        border-top: 1px solid var(--el-datepicker-inner-border-color);
        padding: 4px;
        text-align: right;
        background-color: $color-white;
        position: relative;
        font-size: 0;
    }

    @include e(shortcut) {
        display: block;
        width: 100%;
        border: 0;
        background-color: transparent;
        line-height: 28px;
        font-size: 14px;
        color: var(--el-datepicker-font-color);
        padding-left: 12px;
        text-align: left;
        outline: none;
        cursor: pointer;

        &:hover {
            color: var(--el-datepicker-hover-font-color);
        }

        &.active {
            background-color: #e6f1fe;
            color: var(--el-datepicker-active-color);
        }
    }

    @include e(btn) {
        border: 1px solid #dcdcdc;
        color: var(--el-text-color-primary);
        line-height: 24px;
        border-radius: 2px;
        padding: 0 20px;
        cursor: pointer;
        background-color: transparent;
        outline: none;
        font-size: 12px;

        &[disabled] {
            color: #cccccc;
            cursor: not-allowed;
        }
    }

    @include e(icon-btn) {
        font-size: 12px;
        color: var(--el-datepicker-icon-color);
        border: 0;
        background: transparent;
        cursor: pointer;
        outline: none;
        margin-top: 8px;

        &:hover {
            color: var(--el-datepicker-hover-font-color);
        }

        @include when(disabled) {
            color: var(--el-font-color-disabled-base);

            &:hover {
                cursor: not-allowed;
            }
        }
    }

    @include e(link-btn) {
        vertical-align: middle;
    }
}

.#{$namespace}-picker-panel *[slot='sidebar'],
.#{$namespace}-picker-panel__sidebar {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 110px;
    border-right: 1px solid var(--el-datepicker-inner-border-color);
    box-sizing: border-box;
    padding-top: 6px;
    background-color: $color-white;
    overflow: auto;
}

.#{$namespace}-picker-panel *[slot='sidebar'] + .#{$namespace}-picker-panel__body,
.#{$namespace}-picker-panel__sidebar + .#{$namespace}-picker-panel__body {
    margin-left: 110px;
}
