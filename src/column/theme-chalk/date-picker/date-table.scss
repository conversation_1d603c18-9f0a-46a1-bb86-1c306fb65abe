@use '../common/var' as *;
@use '../mixins/mixins' as *;

@include b(date-table) {
    font-size: 12px;
    user-select: none;

    @include when(week-mode) {
        .#{$namespace}-date-table__row {
            &:hover {
                div {
                    background-color: var(--el-datepicker-inrange-background-color);
                }
                td.available:hover {
                    color: var(--el-datepicker-font-color);
                }
                td:first-child div {
                    margin-left: 5px;
                    border-top-left-radius: 15px;
                    border-bottom-left-radius: 15px;
                }
                td:last-child div {
                    margin-right: 5px;
                    border-top-right-radius: 15px;
                    border-bottom-right-radius: 15px;
                }
            }

            &.current div {
                background-color: var(--el-datepicker-inrange-background-color);
            }
        }
    }

    td {
        width: 32px;
        height: 30px;
        padding: 4px 0;
        box-sizing: border-box;
        text-align: center;
        cursor: pointer;
        position: relative;

        & div {
            height: 30px;
            padding: 3px 0;
            box-sizing: border-box;
        }

        & span {
            width: 24px;
            height: 24px;
            display: block;
            margin: 0 auto;
            line-height: 24px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 50%;
        }

        &.next-month,
        &.prev-month {
            color: var(--el-datepicker-off-font-color);
        }

        &.today {
            position: relative;
            span {
                color: var(--el-color-primary);
                font-weight: bold;
            }
            &.start-date span,
            &.end-date span {
                color: $color-white;
            }
        }

        &.available:hover {
            color: var(--el-datepicker-hover-font-color);
        }

        &.in-range div {
            background-color: var(--el-datepicker-inrange-background-color);
            &:hover {
                background-color: var(--el-datepicker-inrange-hover-background-color);
            }
        }

        &.current:not(.disabled) span {
            color: $color-white;
            background-color: var(--el-datepicker-active-color);
        }
        &.start-date div,
        &.end-date div {
            color: $color-white;
        }

        &.start-date span,
        &.end-date span {
            background-color: var(--el-datepicker-active-color);
        }

        &.start-date div {
            margin-left: 5px;
            border-top-left-radius: 15px;
            border-bottom-left-radius: 15px;
        }

        &.end-date div {
            margin-right: 5px;
            border-top-right-radius: 15px;
            border-bottom-right-radius: 15px;
        }

        &.disabled div {
            background-color: $background-color-base;
            opacity: 1;
            cursor: not-allowed;
            color: var(--el-text-color-placeholder);
        }

        &.selected div {
            margin-left: 5px;
            margin-right: 5px;
            background-color: var(--el-datepicker-inrange-background-color);
            border-radius: 15px;
            &:hover {
                background-color: var(--el-datepicker-inrange-hover-background-color);
            }
        }

        &.selected span {
            background-color: var(--el-datepicker-active-color);
            color: $color-white;
            border-radius: 15px;
        }

        &.week {
            font-size: 80%;
            color: var(--el-datepicker-header-font-color);
        }
    }

    th {
        padding: 5px;
        color: var(--el-datepicker-header-font-color);
        font-weight: 400;
        border-bottom: solid 1px var(--el-border-color-lighter);
    }
}
