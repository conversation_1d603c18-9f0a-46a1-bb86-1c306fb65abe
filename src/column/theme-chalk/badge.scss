@use 'sass:math';
@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(badge) {
    @include set-component-css-var('badge', $badge);

    position: relative;
    vertical-align: middle;
    display: inline-block;

    @include e(content) {
        background-color: var(--el-badge-background-color);
        border-radius: var(--el-badge-radius);
        color: var(--el-color-white);
        display: inline-block;
        font-size: var(--el-badge-font-size);
        height: var(--el-badge-size);
        line-height: var(--el-badge-size);
        padding: 0 var(--el-badge-padding);
        text-align: center;
        white-space: nowrap;
        border: 1px solid var(--el-color-white);

        @include when(fixed) {
            position: absolute;
            top: 0;
            right: calc(1px + var(--el-badge-size) / 2);
            transform: translateY(-50%) translateX(100%);

            @include when(dot) {
                right: 5px;
            }
        }

        @include when(dot) {
            height: 8px;
            width: 8px;
            padding: 0;
            right: 0;
            border-radius: 50%;
        }

        @each $type in (primary, success, warning, info, danger) {
            @include m($type) {
                background-color: var(--el-color-#{$type});
            }
        }
    }
}
