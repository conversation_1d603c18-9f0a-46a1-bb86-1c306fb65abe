@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(descriptions) {
    @include set-component-css-var('descriptions', $descriptions);

    box-sizing: border-box;
    font-size: var(--el-font-size-base);
    color: var(--el-text-color-primary);

    @include e(header) {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--el-descriptions-header-margin-bottom);

        @include e(title) {
            font-size: var(--el-descriptions-title-font-size);
            font-weight: bold;
        }
    }

    @include e(body) {
        color: var(--el-text-color-regular);
        background-color: $color-white;

        .#{$namespace}-descriptions__table {
            border-collapse: collapse;
            width: 100%;

            .#{$namespace}-descriptions__cell {
                box-sizing: border-box;
                text-align: left;
                font-weight: normal;
                line-height: 1.5;

                @include when(left) {
                    text-align: left;
                }

                @include when(center) {
                    text-align: center;
                }

                @include when(right) {
                    text-align: right;
                }
            }
        }
    }

    .is-bordered {
        .#{$namespace}-descriptions__cell {
            border: var(--el-descriptions-table-border);
            padding: 12px 10px;
        }
    }

    :not(.is-bordered) {
        .#{$namespace}-descriptions__cell {
            padding-bottom: 12px;
        }
    }

    @include m(medium) {
        &.is-bordered {
            .#{$namespace}-descriptions__cell {
                padding: 10px;
            }
        }

        &:not(.is-bordered) {
            .#{$namespace}-descriptions__cell {
                padding-bottom: 10px;
            }
        }
    }

    @include m(small) {
        font-size: 12px;

        &.is-bordered {
            .#{$namespace}-descriptions__cell {
                padding: 8px 10px;
            }
        }

        &:not(.is-bordered) {
            .#{$namespace}-descriptions__cell {
                padding-bottom: 8px;
            }
        }
    }

    @include m(mini) {
        font-size: 12px;

        &.is-bordered {
            .#{$namespace}-descriptions__cell {
                padding: 6px 10px;
            }
        }

        &:not(.is-bordered) {
            .#{$namespace}-descriptions__cell {
                padding-bottom: 6px;
            }
        }
    }
}
