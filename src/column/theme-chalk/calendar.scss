@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(calendar) {
    @include set-component-css-var('calendar', $calendar);

    background-color: #fff;

    @include e(header) {
        display: flex;
        justify-content: space-between;
        padding: 12px 20px;
        border-bottom: var(--el-calendar-header-border-bottom);
    }

    @include e(title) {
        color: #000000;
        align-self: center;
    }

    @include e(body) {
        padding: 12px 20px 35px;
    }
}

@include b(calendar-table) {
    table-layout: fixed;
    width: 100%;

    thead th {
        padding: 12px 0;
        color: var(--el-text-color-regular);
        font-weight: normal;
    }

    &:not(.is-range) {
        td.prev,
        td.next {
            color: var(--el-text-color-placeholder);
        }
    }

    td {
        border-bottom: var(--el-calendar-border);
        border-right: var(--el-calendar-border);
        vertical-align: top;
        transition: background-color var(--el-transition-duration-fast) ease;

        @include when(selected) {
            background-color: var(--el-calendar-selected-background-color);
        }

        @include when(today) {
            color: var(--el-color-primary);
        }
    }

    tr:first-child td {
        border-top: var(--el-calendar-border);
    }

    tr td:first-child {
        border-left: var(--el-calendar-border);
    }

    tr.#{$namespace}-calendar-table__row--hide-border td {
        border-top: none;
    }

    @include b(calendar-day) {
        box-sizing: border-box;
        padding: 8px;
        height: var(--el-calendar-cell-width);
        &:hover {
            cursor: pointer;
            background-color: var(--el-calendar-selected-background-color);
        }
    }
}
