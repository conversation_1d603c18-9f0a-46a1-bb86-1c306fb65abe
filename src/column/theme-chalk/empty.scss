@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(empty) {
    @include set-component-css-var('empty', $empty);

    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    text-align: center;
    box-sizing: border-box;
    padding: var(--el-empty-padding);

    @include e(image) {
        width: var(--el-empty-image-width);

        img {
            user-select: none;
            width: 100%;
            height: 100%;
            vertical-align: top;
            object-fit: contain;
        }

        svg {
            fill: var(--el-svg-monochrome-grey);
            width: 100%;
            height: 100%;
            vertical-align: top;
        }
    }

    @include e(description) {
        margin-top: var(--el-empty-description-margin-top);

        p {
            margin: 0;
            font-size: var(--el-font-size-base);
            color: var(--el-text-color-secondary);
        }
    }

    @include e(bottom) {
        margin-top: var(--el-empty-bottom-margin-top);
    }
}
