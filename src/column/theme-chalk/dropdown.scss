@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(dropdown) {
    @include set-component-css-var('dropdown', $dropdown);

    display: inline-block;
    position: relative;
    color: var(--el-text-color-regular);
    font-size: var(--el-font-size-base);
    line-height: 1;

    @include e(popper) {
        @include set-component-css-var('dropdown', $dropdown);

        // using attributes selector to override

        @include picker-popper($color-white, 1px solid var(--el-border-color-light), var(--el-dropdown-menu-box-shadow));

        .#{$namespace}-dropdown-menu {
            border: none;
        }

        #{& + '-selfdefine'} {
            outline: none;
        }

        @include b(scrollbar__bar) {
            z-index: calc(var(--el-dropdown-menu-index) + 1);
        }

        @include b(dropdown__list) {
            list-style: none;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }
    }

    .#{$namespace}-button-group {
        display: block;

        .#{$namespace}-button {
            float: none;
        }
    }

    & .#{$namespace}-dropdown__caret-button {
        padding-left: 5px;
        padding-right: 5px;
        position: relative;
        border-left: none;

        &::before {
            $gap: 5px;

            content: '';
            position: absolute;
            display: block;
            width: 1px;
            top: $gap;
            bottom: $gap;
            left: 0;
            background: mix(white, transparent, 50%);
        }

        &.#{$namespace}-button--default::before {
            background: var(--el-default-border-color);
            opacity: 0.5;
        }

        &:hover {
            &::before {
                top: 0;
                bottom: 0;
            }
        }

        & .#{$namespace}-dropdown__icon {
            padding-left: 0;
        }
    }
    @include e(icon) {
        font-size: 12px;
        margin: 0 3px;
    }

    .#{$namespace}-dropdown-selfdefine {
        // 自定义
        outline: none;
    }
}

@include b(dropdown-menu) {
    position: relative;
    top: 0;
    left: 0;
    z-index: var(--el-dropdown-menu-index);
    padding: 10px 0;
    margin: 0;
    background-color: $color-white;
    border: none;
    border-radius: var(--el-border-radius-base);
    box-shadow: none;

    @include e(item) {
        list-style: none;
        line-height: 36px;
        padding: 0 20px;
        margin: 0;
        font-size: var(--el-font-size-base);
        color: var(--el-text-color-regular);
        cursor: pointer;
        outline: none;
        &:not(.is-disabled):hover,
        &:focus {
            background-color: var(--el-dropdown-menuItem-hover-fill);
            color: var(--el-dropdown-menuItem-hover-color);
        }

        i {
            margin-right: 5px;
        }

        @include m(divided) {
            $divided-offset: 6px;

            position: relative;
            margin-top: $divided-offset;
            border-top: 1px solid var(--el-border-color-lighter);

            &:before {
                content: '';
                height: $divided-offset;
                display: block;
                margin: 0 -20px;
                background-color: $color-white;
            }
        }

        @include when(disabled) {
            cursor: not-allowed;
            color: var(--el-font-color-disabled-base);
        }
    }

    @include m(medium) {
        padding: 6px 0;

        @include e(item) {
            line-height: 30px;
            padding: 0 17px;
            font-size: 14px;

            &.#{$namespace}-dropdown-menu__item--divided {
                $divided-offset: 6px;
                margin-top: $divided-offset;

                &:before {
                    height: $divided-offset;
                    margin: 0 -17px;
                }
            }
        }
    }

    @include m(small) {
        padding: 6px 0;

        @include e(item) {
            line-height: 27px;
            padding: 0 15px;
            font-size: 13px;

            &.#{$namespace}-dropdown-menu__item--divided {
                $divided-offset: 4px;
                margin-top: $divided-offset;

                &:before {
                    height: $divided-offset;
                    margin: 0 -15px;
                }
            }
        }
    }

    @include m(mini) {
        padding: 3px 0;

        @include e(item) {
            line-height: 24px;
            padding: 0 10px;
            font-size: 12px;

            &.#{$namespace}-dropdown-menu__item--divided {
                $divided-offset: 3px;
                margin-top: $divided-offset;

                &:before {
                    height: $divided-offset;
                    margin: 0 -10px;
                }
            }
        }
    }
}
