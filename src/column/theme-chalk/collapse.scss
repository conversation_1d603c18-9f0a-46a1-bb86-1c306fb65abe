@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use 'common/transition';

@include b(collapse) {
    @include set-component-css-var('collapse', $collapse);

    border-top: 1px solid var(--el-collapse-border-color);
    border-bottom: 1px solid var(--el-collapse-border-color);
}

@include b(collapse-item) {
    @include when(disabled) {
        .#{$namespace}-collapse-item__header {
            color: var(--el-font-color-disabled-base);
            cursor: not-allowed;
        }
    }
    @include e(header) {
        display: flex;
        align-items: center;
        height: var(--el-collapse-header-height);
        line-height: var(--el-collapse-header-height);
        background-color: var(--el-collapse-header-background-color);
        color: var(--el-collapse-header-font-color);
        cursor: pointer;
        border-bottom: 1px solid var(--el-collapse-border-color);
        font-size: var(--el-collapse-header-font-size);
        font-weight: 500;
        transition: border-bottom-color var(--el-transition-duration);
        outline: none;
        @include e(arrow) {
            margin: 0 8px 0 auto;
            transition: transform var(--el-transition-duration);
            font-weight: 300;
            @include when(active) {
                transform: rotate(90deg);
            }
        }
        &.focusing:focus:not(:hover) {
            color: var(--el-color-primary);
        }
        @include when(active) {
            border-bottom-color: transparent;
        }
    }

    @include e(wrap) {
        will-change: height;
        background-color: var(--el-collapse-content-background-color);
        overflow: hidden;
        box-sizing: border-box;
        border-bottom: 1px solid var(--el-collapse-border-color);
    }

    @include e(content) {
        padding-bottom: 25px;
        font-size: var(--el-collapse-content-font-size);
        color: var(--el-collapse-content-font-color);
        line-height: 1.769230769230769;
    }

    &:last-child {
        margin-bottom: -1px;
    }
}
