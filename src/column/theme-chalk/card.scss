@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(card) {
    @include set-component-css-var('card', $card);
}

@include dark(card) {
    @include set-component-css-var('card', $dark-card);
}

@include b(card) {
    border-radius: var(--el-card-border-radius);
    border: 1px solid var(--el-card-border-color);
    background-color: var(--el-card-background-color);
    overflow: hidden;
    color: var(--el-text-color-primary);
    transition: var(--el-transition-duration);

    @include when(always-shadow) {
        box-shadow: var(--el-box-shadow-light);
    }

    @include when(hover-shadow) {
        &:hover,
        &:focus {
            box-shadow: var(--el-box-shadow-light);
        }
    }

    @include e(header) {
        padding: calc(var(--el-card-padding) - 2px) var(--el-card-padding);
        border-bottom: 1px solid var(--el-card-border-color);
        box-sizing: border-box;
    }

    @include e(body) {
        padding: var(--el-card-padding);
    }
}
