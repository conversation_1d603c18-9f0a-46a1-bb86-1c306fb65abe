@use 'mixins/mixins' as *;

@include b(time-spinner) {
    width: 100%;
    white-space: nowrap;
}

@include b(spinner) {
    display: inline-block;
    vertical-align: middle;
}
@include b(spinner-inner) {
    animation: rotate 2s linear infinite;
    width: 50px;
    height: 50px;

    & .path {
        stroke: #ececec;
        stroke-linecap: round;
        animation: dash 1.5s ease-in-out infinite;
    }
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dash {
    0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
    }
}
