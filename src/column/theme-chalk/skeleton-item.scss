@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(skeleton) {
    @each $size in (small, medium, large) {
        --el-skeleton-avatar-#{$size}-size: var(--el-avatar-#{$size}-size);
    }
}

@mixin circle-size($size) {
    width: $size;
    height: $size;
    line-height: $size;
}

@include b(skeleton) {
    @include e(item) {
        background: var(--el-skeleton-color);
        display: inline-block;
        height: 16px;
        border-radius: var(--el-border-radius-base);
        width: 100%;
    }

    @include e(circle) {
        border-radius: 50%;
        @include circle-size(var(--el-skeleton-avatar-medium-size));

        @include m(lg) {
            @include circle-size(var(--el-skeleton-avatar-large-size));
        }

        @include m(md) {
            @include circle-size(var(--el-skeleton-avatar-small-size));
        }
    }

    @include e(button) {
        height: 40px;
        width: 64px;
        border-radius: 4px;
    }

    @include e(p) {
        width: 100%;
        @include when(last) {
            width: 61%;
        }

        @include when(first) {
            width: 33%;
        }
    }

    @include e(text) {
        width: 100%;
        height: var(--el-font-size-small);
    }

    @include e(caption) {
        height: var(--el-font-size-extra-small);
    }

    @include e(h1) {
        height: var(--el-font-size-extra-large);
    }

    @include e(h3) {
        height: var(--el-font-size-large);
    }

    @include e(h5) {
        height: var(--el-font-size-medium);
    }

    @include e(image) {
        width: unset;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0;

        svg {
            fill: var(--el-svg-monochrome-grey);
            width: 22%;
            height: 22%;
        }
    }
}
