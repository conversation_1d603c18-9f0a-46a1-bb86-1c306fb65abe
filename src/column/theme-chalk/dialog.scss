@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use 'common/popup' as *;

@include b(dialog) {
    @include set-component-css-var('dialog', $dialog);

    position: relative;
    margin: var(--el-dialog-margin-top, 15vh) auto 50px;
    background: var(--el-dialog-background-color);
    border-radius: var(--el-border-radius-small);
    box-shadow: var(--el-dialog-box-shadow);
    box-sizing: border-box;
    width: var(--el-dialog-width, 50%);

    @include when(fullscreen) {
        --el-dialog-width: 100%;
        --el-dialog-margin-top: 0;
        margin-bottom: 0;
        height: 100%;
        overflow: auto;
    }

    @include e(wrapper) {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        overflow: auto;
        margin: 0;
    }

    @include e(header) {
        padding: var(--el-dialog-padding-primary);
        padding-bottom: 10px;
    }

    @include e(headerbtn) {
        position: absolute;
        top: var(--el-dialog-padding-primary);
        right: var(--el-dialog-padding-primary);
        padding: 0;
        background: transparent;
        border: none;
        outline: none;
        cursor: pointer;
        font-size: var(--el-message-close-size, map.get($message, 'close-size'));

        .#{$namespace}-dialog__close {
            color: var(--el-color-info);
        }

        &:focus,
        &:hover {
            .#{$namespace}-dialog__close {
                color: var(--el-color-primary);
            }
        }
    }

    @include e(title) {
        line-height: var(--el-dialog-font-line-height);
        font-size: var(--el-dialog-title-font-size);
        color: var(--el-text-color-primary);
    }

    @include e(body) {
        padding: calc(var(--el-dialog-padding-primary) + 10px) var(--el-dialog-padding-primary);
        color: var(--el-text-color-regular);
        font-size: var(--el-dialog-content-font-size);
        word-break: break-all;
    }

    @include e(footer) {
        padding: var(--el-dialog-padding-primary);
        padding-top: 10px;
        text-align: right;
        box-sizing: border-box;
    }

    // 内容居中布局
    @include m(center) {
        text-align: center;

        @include e(body) {
            text-align: initial;
            padding: 25px calc(var(--el-dialog-padding-primary) + 5px) 30px;
        }

        @include e(footer) {
            text-align: inherit;
        }
    }
}

.#{$namespace}-overlay-dialog {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: auto;
}

.dialog-fade-enter-active {
    animation: modal-fade-in var(--el-transition-duration);
    .#{$namespace}-overlay-dialog {
        animation: dialog-fade-in var(--el-transition-duration);
    }
}

.dialog-fade-leave-active {
    animation: modal-fade-out var(--el-transition-duration);
    .#{$namespace}-overlay-dialog {
        animation: dialog-fade-out var(--el-transition-duration);
    }
}

@keyframes dialog-fade-in {
    0% {
        transform: translate3d(0, -20px, 0);
        opacity: 0;
    }
    100% {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes dialog-fade-out {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    100% {
        transform: translate3d(0, -20px, 0);
        opacity: 0;
    }
}

@keyframes modal-fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes modal-fade-out {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
