@use 'sass:map';

@use 'common/var' as *;
@use 'mixins/button' as *;
@use 'mixins/mixins' as *;
@use 'mixins/utils' as *;
@use 'mixins/var' as *;

@include b(button) {
    @include set-component-css-var('button', $button);
}

@include b(button) {
    display: inline-block;
    line-height: 1;
    min-height: map.get($input-height, 'default');
    white-space: nowrap;
    cursor: pointer;
    background: var(--el-button-background-color, map.get($button, 'background-color'));
    border: var(--el-border-base);
    border-color: var(--el-button-border-color, map.get($button, 'border-color'));
    color: var(--el-button-font-color, map.get($button, 'font-color'));
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: var(--el-button-font-weight);
    @include utils-user-select(none);
    & + & {
        margin-left: 10px;
    }

    @include button-size(
        map.get($button-padding-vertical, 'default'),
        map.get($button-padding-horizontal, 'default'),
        map.get($button-font-size, 'default'),
        map.get($button-border-radius, 'default')
    );

    &:hover,
    &:focus {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary-light-7);
        background-color: var(--el-color-primary-light-9);
        outline: none;
    }

    &:active {
        color: mix($color-black, $color-primary, $button-active-shade-percent);
        border-color: mix($color-black, $color-primary, $button-active-shade-percent);
        outline: none;
    }

    &::-moz-focus-inner {
        border: 0;
    }

    & [class*='#{$namespace}-icon-'] {
        & + span {
            margin-left: 5px;
        }
    }

    @include when(plain) {
        &:hover,
        &:focus {
            background: var(--el-color-white);
            border-color: var(--el-color-primary);
            color: var(--el-color-primary);
        }

        &:active {
            background: var(--el-color-white);
            border-color: mix($color-black, $color-primary, $button-active-shade-percent);
            color: mix($color-black, $color-primary, $button-active-shade-percent);
            outline: none;
        }
    }

    @include when(active) {
        color: mix($color-black, $color-primary, $button-active-shade-percent);
        border-color: mix($color-black, $color-primary, $button-active-shade-percent);
    }

    @include when(disabled) {
        &,
        &:hover,
        &:focus {
            color: var(--el-button-disabled-font-color);
            cursor: not-allowed;
            background-image: none;
            background-color: var(--el-button-disabled-background-color);
            border-color: var(--el-button-disabled-border-color);
        }

        &.#{$namespace}-button--text {
            background-color: transparent;
        }

        &.is-plain {
            &,
            &:hover,
            &:focus {
                background-color: var(--el-color-white);
                border-color: var(--el-button-disabled-border-color);
                color: var(--el-button-disabled-font-color);
            }
        }
    }

    @include when(loading) {
        position: relative;
        pointer-events: none;

        &:before {
            pointer-events: none;
            content: '';
            position: absolute;
            left: -1px;
            top: -1px;
            right: -1px;
            bottom: -1px;
            border-radius: inherit;
            background-color: rgba(255, 255, 255, 0.35);
        }
    }
    @include when(round) {
        border-radius: var(--el-border-radius-round);
        padding: 12px 23px;
    }
    @include when(circle) {
        border-radius: 50%;
        padding: map.get($button-padding-vertical, 'default');
    }

    @each $type in (primary, success, warning, danger, info) {
        @include m($type) {
            --el-button-font-color: #{map.get($button-font-color, $type)};
            --el-button-background-color: #{map.get($button-background-color, $type)};
            --el-button-border-color: #{map.get($button-border-color, $type)};
            --el-button-hover-color: #{map.get($colors, $type, 'light-2')};
            --el-button-active-font-color: #{darken(map.get($button-font-color, $type), $button-active-shade-percent)};
            --el-button-active-background-color: #{darken(map.get($button-background-color, $type), $button-active-shade-percent)};
            --el-button-active-border-color: #{darken(map.get($button-border-color, $type), $button-active-shade-percent)};

            @include button-variant($type);
        }
    }

    @each $size in (medium, small, mini) {
        @include m($size) {
            min-height: map.get($input-height, $size);

            @include button-size(map.get($button-padding-vertical, $size), map.get($button-padding-horizontal, $size), map.get($button-font-size, $size), map.get($button-border-radius, $size));

            @include when(circle) {
                padding: map.get($button-padding-vertical, $size);
            }
        }
    }

    @include m(text) {
        border-color: transparent;
        color: var(--el-color-primary);
        background: transparent;
        padding-left: 0;
        padding-right: 0;

        &:hover,
        &:focus {
            color: var(--el-color-primary-light-2);
            border-color: transparent;
            background-color: transparent;
        }
        &:active {
            color: mix($color-black, $color-primary, $button-active-shade-percent);
            border-color: transparent;
            background-color: transparent;
        }

        &.is-disabled,
        &.is-disabled:hover,
        &.is-disabled:focus {
            border-color: transparent;
        }
    }
}
