@use 'sass:math';

@use '../common/var' as *;
@use './mixins' as *;

@mixin col-size($size) {
    @include res($size) {
        .#{$namespace}-col-#{$size}-0 {
            display: none;
            @include when(guttered) {
                display: none;
            }
        }
        @for $i from 0 through 24 {
            .#{$namespace}-col-#{$size}-#{$i} {
                @if $i != 0 {
                    display: block;
                }
                max-width: (math.div(1, 24) * $i * 100) * 1%;
                flex: 0 0 (math.div(1, 24) * $i * 100) * 1%;
            }

            .#{$namespace}-col-#{$size}-offset-#{$i} {
                margin-left: (math.div(1, 24) * $i * 100) * 1%;
            }

            .#{$namespace}-col-#{$size}-pull-#{$i} {
                position: relative;
                right: (math.div(1, 24) * $i * 100) * 1%;
            }

            .#{$namespace}-col-#{$size}-push-#{$i} {
                position: relative;
                left: (math.div(1, 24) * $i * 100) * 1%;
            }
        }
    }
}
