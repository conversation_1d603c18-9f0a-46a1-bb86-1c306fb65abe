@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(tag) {
    @include set-component-css-var('tag', $tag);
}

@mixin genTheme($backgroundColorWeight, $borderColorWeight, $fontColorWeight, $hoverColorWeight) {
    @if (unitless($backgroundColorWeight)) {
        $backgroundColorWeight: $backgroundColorWeight * 1%;
    }
    @if (unitless($fontColorWeight)) {
        $fontColorWeight: $fontColorWeight * 1%;
    }

    --el-tag-background-color: #{mix(map.get($tag-color, 'primary'), $color-white, $backgroundColorWeight)};
    --el-tag-border-color: #{mix(map.get($tag-color, 'primary'), $color-white, $borderColorWeight)};
    --el-tag-font-color: #{mix(map.get($tag-color, 'primary'), $color-white, $fontColorWeight)};
    --el-tag-hover-color: #{mix(map.get($tag-color, 'primary'), $color-white, $hoverColorWeight)};

    background-color: var(--el-tag-background-color);
    border-color: var(--el-tag-border-color);
    color: var(--el-tag-font-color);

    @include when(hit) {
        border-color: map.get($tag-color, 'primary');
    }

    .#{$namespace}-tag__close {
        color: var(--el-tag-font-color);
        &:hover {
            color: var(--el-color-white);
            background-color: var(--el-tag-hover-color);
        }
    }

    @each $type in $types {
        &.#{$namespace}-tag--#{$type} {
            --el-tag-background-color: #{mix(map.get($tag-color, $type), $color-white, $backgroundColorWeight)};
            --el-tag-border-color: #{mix(map.get($tag-color, $type), $color-white, $borderColorWeight)};
            --el-tag-font-color: #{mix(map.get($tag-color, $type), $color-white, $fontColorWeight)};
            --el-tag-hover-color: #{mix(map.get($tag-color, $type), $color-white, $hoverColorWeight)};

            @include when(hit) {
                border-color: map.get($tag-color, $type);
            }
        }
    }
}

@include b(tag) {
    @include genTheme(10%, 20%, 100%, 100%);
    display: inline-block;
    height: map.get($tag-height, 'default');
    padding: var(--el-tag-padding);
    line-height: map.get($tag-height, 'default') - 2px;
    font-size: var(--el-tag-font-size);
    border-width: 1px;
    border-style: solid;
    border-radius: var(--el-tag-border-radius);
    box-sizing: border-box;
    white-space: nowrap;

    .#{$namespace}-icon-close {
        border-radius: 50%;
        text-align: center;
        position: relative;
        cursor: pointer;
        font-size: 12px;
        height: 16px;
        width: 16px;
        line-height: 16px;
        vertical-align: middle;
        top: -1px;
        right: -5px;

        &::before {
            display: block;
        }
    }

    @include m(dark) {
        @include genTheme(100%, 100%, 0, 80%);
    }

    @include m(plain) {
        @include genTheme(0, 40%, 100%, 100%);
    }

    @each $size in (medium, small, mini) {
        @include m($size) {
            height: map.get($tag-height, $size);
            line-height: map.get($tag-height, $size) - 2px;
        }
    }

    @include m(medium) {
        .#{$namespace}-icon-close {
            transform: scale(0.8);
        }
    }

    @include m(small) {
        padding: 0 8px;
        .#{$namespace}-icon-close {
            transform: scale(0.8);
        }
    }

    @include m(mini) {
        padding: 0 5px;
        .#{$namespace}-icon-close {
            margin-left: -3px;
            transform: scale(0.7);
        }
    }
}
