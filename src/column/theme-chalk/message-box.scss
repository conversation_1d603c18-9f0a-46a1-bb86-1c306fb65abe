@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;
@use 'common/popup' as *;

@include b(message-box) {
    @include set-component-css-var('messagebox', $messagebox);
}

@include b(message-box) {
    display: inline-block;
    width: var(--el-messagebox-width);
    padding-bottom: 10px;
    vertical-align: middle;
    background-color: var(--el-color-white);
    border-radius: var(--el-messagebox-border-radius);
    border: 1px solid var(--el-border-color-lighter);
    font-size: var(--el-messagebox-font-size);
    box-shadow: var(--el-box-shadow-light);
    text-align: left;
    overflow: hidden;
    backface-visibility: hidden;

    @at-root .#{$namespace}-overlay.is-message-box {
        text-align: center;
        &::after {
            content: '';
            display: inline-block;
            height: 100%;
            width: 0;
            vertical-align: middle;
        }
    }

    @include e(header) {
        position: relative;
        padding: var(--el-messagebox-padding-primary);
        padding-bottom: 10px;
    }

    @include e(title) {
        padding-left: 0;
        margin-bottom: 0;
        font-size: var(--el-messagebox-font-size);
        line-height: 1;
        color: var(--el-messagebox-title-color);
    }

    @include e(headerbtn) {
        position: absolute;
        top: var(--el-messagebox-padding-primary);
        right: var(--el-messagebox-padding-primary);
        padding: 0;
        border: none;
        outline: none;
        background: transparent;
        font-size: var(--el-message-close-size, map.get($message, 'close-size'));
        cursor: pointer;

        .#{$namespace}-message-box__close {
            color: var(--el-color-info);
        }

        &:focus,
        &:hover {
            .#{$namespace}-message-box__close {
                color: var(--el-color-primary);
            }
        }
    }

    @include e(content) {
        padding: 10px var(--el-messagebox-padding-primary);
        color: var(--el-messagebox-content-color);
        font-size: var(--el-messagebox-content-font-size);
    }

    @include e(container) {
        position: relative;
    }

    @include e(input) {
        padding-top: 15px;

        & div.invalid > input {
            border-color: var(--el-color-error);
            &:focus {
                border-color: var(--el-color-error);
            }
        }
    }

    @include e(status) {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 24px !important;

        &::before {
            // 防止图标切割
            padding-left: 1px;
        }

        + .#{$namespace}-message-box__message {
            padding-left: 36px;
            padding-right: 12px;
            word-break: break-word;
        }

        @each $type in (success, info, warning, error) {
            &.#{$namespace}-icon-#{$type} {
                --el-messagebox-color: var(--el-color-#{$type});
                color: var(--el-messagebox-color);
            }
        }
    }

    @include e(message) {
        margin: 0;

        & p {
            margin: 0;
            line-height: 24px;
        }
    }

    @include e(errormsg) {
        color: var(--el-color-error);
        font-size: var(--el-messagebox-error-font-size);
        min-height: 18px;
        margin-top: 2px;
    }

    @include e(btns) {
        padding: 5px 15px 0;
        text-align: right;

        & button:nth-child(2) {
            margin-left: 10px;
        }
    }

    @include e(btns-reverse) {
        flex-direction: row-reverse;
    }

    // centerAlign 布局
    @include m(center) {
        padding-bottom: 30px;

        @include e(header) {
            padding-top: 30px;
        }

        @include e(title) {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @include e(status) {
            position: relative;
            top: auto;
            padding-right: 5px;
            text-align: center;
            transform: translateY(-1px);
        }

        @include e(message) {
            margin-left: 0;
        }

        @include e((btns, content)) {
            text-align: center;
        }

        @include e(content) {
            $padding-horizontal: calc(var(--el-messagebox-padding-primary) + 12px);

            padding-left: $padding-horizontal;
            padding-right: $padding-horizontal;
        }
    }
}

.fade-in-linear-enter-active {
    .#{$namespace}-message-box {
        animation: msgbox-fade-in var(--el-transition-duration);
    }
}

.fade-in-linear-leave-active {
    .#{$namespace}-message-box {
        animation: msgbox-fade-in var(--el-transition-duration) reverse;
    }
}

@keyframes msgbox-fade-in {
    0% {
        transform: translate3d(0, -20px, 0);
        opacity: 0;
    }
    100% {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@keyframes msgbox-fade-out {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
    100% {
        transform: translate3d(0, -20px, 0);
        opacity: 0;
    }
}
