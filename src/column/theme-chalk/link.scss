@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(link) {
    @include set-component-css-var('link', $link);
}

@include b(link) {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    position: relative;
    text-decoration: none;
    outline: none;
    cursor: pointer;
    padding: 0;
    font-size: var(--el-link-font-size);
    font-weight: var(--el-link-font-weight);

    @include when(underline) {
        &:hover:after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            height: 0;
            bottom: 0;
            border-bottom: 1px solid var(--el-link-default-active-color);
        }
    }

    @include when(disabled) {
        cursor: not-allowed;
    }

    & [class*='#{$namespace}-icon-'] {
        & + span {
            margin-left: 5px;
        }
    }

    &.#{$namespace}-link--default {
        color: var(--el-link-default-font-color);

        &:hover {
            color: var(--el-link-default-active-color);
        }

        &:after {
            border-color: var(--el-link-default-active-color);
        }

        @include when(disabled) {
            color: var(--el-link-disabled-font-color);
        }
    }

    @each $type in $types {
        &.#{$namespace}-link--#{$type} {
            --el-link-font-color: var(--el-color-#{$type});
            color: var(--el-link-font-color);

            &:hover {
                color: mix(map.get($link-font-color, $type), $color-white, 80%);
            }

            &:after {
                border-color: var(--el-link-font-color);
            }

            @include when(disabled) {
                color: mix(map.get($link-font-color, $type), $color-white, 50%);
            }
            @include when(underline) {
                &:hover:after {
                    border-color: var(--el-link-font-color);
                }
            }
        }
    }
}
