@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(select-dropdown) {
    @include e(option-item) {
        font-size: var(--el-select-font-size);
        // 20 as the padding of option item, 12 as the size of ✓ icon size
        padding: 0 #{20 + 12}px 0 20px;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: map.get($select-option, 'color');
        height: map.get($select-option, 'height');
        line-height: map.get($select-option, 'height');
        box-sizing: border-box;
        cursor: pointer;

        @include when(disabled) {
            color: map.get($select-option, 'disabled-color');
            cursor: not-allowed;

            &:hover {
                background-color: var(--el-color-white);
            }
        }

        @include when(selected) {
            background-color: map.get($select-option, 'hover-background');
            font-weight: 700;

            &:not(.is-multiple) {
                color: map.get($select-option, 'selected-font-color');
            }
        }

        &.hover {
            background-color: map.get($select-option, 'hover-background') !important;
        }

        &:hover {
            background-color: map.get($select-option, 'hover-background');
        }
    }

    @include when(multiple) {
        .el-select-dropdown__option-item {
            &.is-selected {
                color: map.get($select-option, 'selected-font-color');
                background-color: map.get($select-dropdown, 'background');
                font-weight: bold;
                &::after {
                    position: absolute;
                    right: 20px;
                    top: 0;
                    font-family: 'element-icons';
                    content: '\e6da';
                    font-size: 12px;
                    font-weight: bold;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                }
            }
        }
    }
}
