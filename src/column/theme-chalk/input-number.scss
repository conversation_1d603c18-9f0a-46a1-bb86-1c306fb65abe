@use 'sass:math';
@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'common/var' as *;

@include b(input-number) {
    position: relative;
    display: inline-block;
    width: 180px;
    line-height: #{map.get($input-height, 'default') - 2};

    .#{$namespace}-input {
        display: block;

        &__inner {
            -webkit-appearance: none;
            padding-left: #{map.get($input-height, 'default') + 10};
            padding-right: #{map.get($input-height, 'default') + 10};
            text-align: center;
        }
    }

    @include e((increase, decrease)) {
        position: absolute;
        z-index: 1;
        top: 1px;
        width: map.get($input-height, 'default');
        height: auto;
        text-align: center;
        background: $background-color-base;
        color: var(--el-text-color-regular);
        cursor: pointer;
        font-size: 13px;

        &:hover {
            color: var(--el-color-primary);

            & ~ .#{$namespace}-input:not(.is-disabled) .#{$namespace}-input__inner {
                border-color: var(--el-input-focus-border, map.get($input, 'focus-border'));
            }
        }

        &.is-disabled {
            color: var(--el-disabled-color-base);
            cursor: not-allowed;
        }
    }

    @include e(increase) {
        right: 1px;
        border-radius: 0 var(--el-border-radius-base) var(--el-border-radius-base) 0;
        border-left: var(--el-border-base);
    }

    @include e(decrease) {
        left: 1px;
        border-radius: var(--el-border-radius-base) 0 0 var(--el-border-radius-base);
        border-right: var(--el-border-base);
    }

    @include when(disabled) {
        @include e((increase, decrease)) {
            border-color: var(--el-disabled-border-base);
            color: var(--el-disabled-border-base);

            &:hover {
                color: var(--el-disabled-border-base);
                cursor: not-allowed;
            }
        }
    }

    @each $size in (medium, small, mini) {
        @include m($size) {
            width: map.get($input-number-width, $size);
            line-height: #{map.get($input-height, $size) - 2};

            @include e((increase, decrease)) {
                width: map.get($input-height, $size);
                font-size: map.get($input-font-size, $size);
            }

            .#{$namespace}-input__inner {
                padding-left: #{map.get($input-height, $size) + 7};
                padding-right: #{map.get($input-height, $size) + 7};
            }
        }
    }

    @include m(small) {
        @include e((increase, decrease)) {
            [class*='#{$namespace}-icon'] {
                transform: scale(0.9);
            }
        }
    }

    @include m(mini) {
        @include e((increase, decrease)) {
            [class*='#{$namespace}-icon'] {
                transform: scale(0.8);
            }
        }
    }

    @include when(without-controls) {
        .#{$namespace}-input__inner {
            padding-left: 15px;
            padding-right: 15px;
        }
    }

    @include when(controls-right) {
        .#{$namespace}-input__inner {
            padding-left: 15px;
            padding-right: #{map.get($input-height, 'default') + 10};
        }

        @include e((increase, decrease)) {
            height: auto;
            line-height: #{math.div(map.get($input-height, 'default') - 2, 2)};

            [class*='#{$namespace}-icon'] {
                transform: scale(0.8);
            }
        }

        @include e(increase) {
            border-radius: 0 var(--el-border-radius-base) 0 0;
            border-bottom: var(--el-border-base);
        }

        @include e(decrease) {
            right: 1px;
            bottom: 1px;
            top: auto;
            left: auto;
            border-right: none;
            border-left: var(--el-border-base);
            border-radius: 0 0 var(--el-border-radius-base) 0;
        }

        @each $size in (medium, small, mini) {
            &[class*='#{$size}'] {
                [class*='increase'],
                [class*='decrease'] {
                    line-height: #{math.div(map.get($input-height, $size) - 2, 2)};
                }
            }
        }
    }
}
