@use 'sass:map';

@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(textarea) {
    @include set-component-css-var('input', $input);
}

@include b(textarea) {
    position: relative;
    display: inline-block;
    width: 100%;
    vertical-align: bottom;
    font-size: var(--el-font-size-base);

    @include e(inner) {
        display: block;
        resize: vertical;
        padding: 5px 15px;
        line-height: 1.5;
        box-sizing: border-box;
        width: 100%;
        font-size: inherit;
        font-family: inherit;
        color: var(--el-input-font-color, map.get($input, 'font-color'));
        background-color: var(--el-input-background-color, map.get($input, 'background-color'));
        background-image: none;
        border: var(--el-input-border, map.get($input, 'border'));
        border-radius: var(--el-input-border-radius, map.get($input, 'border-radius'));
        transition: var(--el-transition-border);

        &::placeholder {
            color: var(--el-input-placeholder-color, map.get($input, 'placeholder-color'));
        }

        &:hover {
            border-color: var(--el-input-hover-border, map.get($input, 'hover-border'));
        }

        &:focus {
            outline: none;
            border-color: var(--el-input-focus-border, map.get($input, 'focus-border'));
        }
    }

    & .#{$namespace}-input__count {
        color: var(--el-color-info);
        background: var(--el-color-white);
        position: absolute;
        font-size: 12px;
        line-height: 14px;
        bottom: 5px;
        right: 10px;
    }

    @include when(disabled) {
        .#{$namespace}-textarea__inner {
            background-color: map.get($input-disabled, 'fill');
            border-color: map.get($input-disabled, 'border');
            color: map.get($input-disabled, 'color');
            cursor: not-allowed;

            &::placeholder {
                color: map.get($input-disabled, 'placeholder-color');
            }
        }
    }

    @include when(exceed) {
        .#{$namespace}-textarea__inner {
            border-color: var(--el-color-danger);
        }

        .#{$namespace}-input__count {
            color: var(--el-color-danger);
        }
    }
}

@include b(input) {
    @include set-component-css-var('input', $input);

    position: relative;
    font-size: var(--el-font-size-base);
    display: inline-block;
    width: 100%;
    line-height: map.get($input-height, 'default');
    @include scroll-bar;

    & .#{$namespace}-input__clear {
        color: var(--el-input-icon-color);
        font-size: map.get($input-font-size, 'default');
        cursor: pointer;
        transition: var(--el-transition-color);

        &:hover {
            color: var(--el-input-clear-hover-color);
        }
    }

    & .#{$namespace}-input__count {
        height: 100%;
        display: inline-flex;
        align-items: center;
        color: var(--el-color-info);
        font-size: 12px;

        .#{$namespace}-input__count-inner {
            background: $color-white;
            line-height: initial;
            display: inline-block;
            padding: 0 5px;
        }
    }

    @include e(inner) {
        -webkit-appearance: none;
        background-color: var(--el-input-background-color, map.get($input, 'background-color'));
        background-image: none;
        border-radius: var(--el-input-border-radius, map.get($input, 'border-radius'));
        border: var(--el-input-border, map.get($input, 'border'));
        box-sizing: border-box;
        color: var(--el-input-font-color, map.get($input, 'font-color'));
        display: inline-block;
        font-size: inherit;
        height: map.get($input-height, 'default');
        line-height: map.get($input-height, 'default');
        outline: none;
        padding: 0 15px;
        transition: var(--el-transition-border);
        width: 100%;

        &::placeholder {
            color: var(--el-input-placeholder-color, map.get($input, 'placeholder-color'));
        }

        &:hover {
            border-color: var(--el-input-hover-border, map.get($input, 'hover-border'));
        }

        &:focus {
            outline: none;
            border-color: var(--el-input-focus-border, map.get($input, 'focus-border'));
        }
    }

    @include e(suffix) {
        position: absolute;
        height: 100%;
        right: 5px;
        top: 0;
        text-align: center;
        color: var(--el-input-icon-color, map.get($input, 'icon-color'));
        transition: all var(--el-transition-duration);
        pointer-events: none;
    }

    @include e(suffix-inner) {
        pointer-events: all;
    }

    @include e(prefix) {
        position: absolute;
        height: 100%;
        left: 5px;
        top: 0;
        text-align: center;
        color: var(--el-input-icon-color, map.get($input, 'icon-color'));
        transition: all var(--el-transition-duration);
    }

    @include e(icon) {
        width: 25px;
        text-align: center;
        transition: all var(--el-transition-duration);
        line-height: map.get($input-height, 'default');

        &:after {
            content: '';
            height: 100%;
            width: 0;
            display: inline-block;
            vertical-align: middle;
        }
    }

    @include e(validateIcon) {
        pointer-events: none;
    }

    @include when(active) {
        .#{$namespace}-input__inner {
            outline: none;
            border-color: var(--el-input-focus-border, map.get($input, 'focus-color'));
        }
    }

    @include when(disabled) {
        .#{$namespace}-input__inner {
            background-color: map.get($input-disabled, 'fill');
            border-color: map.get($input-disabled, 'border');
            color: map.get($input-disabled, 'color');
            cursor: not-allowed;

            &::placeholder {
                color: map.get($input-disabled, 'placeholder-color');
            }
        }

        .#{$namespace}-input__icon {
            cursor: not-allowed;
        }
    }

    @include when(exceed) {
        .#{$namespace}-input__inner {
            border-color: var(--el-color-danger);
        }

        .#{$namespace}-input__suffix {
            .#{$namespace}-input__count {
                color: var(--el-color-danger);
            }
        }
    }

    @include m(suffix) {
        .#{$namespace}-input__inner {
            padding-right: 30px;
        }

        @include m(password-clear) {
            .#{$namespace}-input__inner {
                padding-right: 55px;
            }
        }
    }

    @include m(prefix) {
        .#{$namespace}-input__inner {
            padding-left: 30px;
        }
    }

    @each $size in (medium, small, mini) {
        @include m($size) {
            font-size: map.get($input-font-size, $size);
            line-height: map.get($input-height, $size);

            @include e(inner) {
                height: map.get($input-height, $size);
                line-height: map.get($input-height, $size);
            }

            .#{$namespace}-input__icon {
                line-height: map.get($input-height, $size);
            }
        }
    }
}

@include b(input-group) {
    line-height: normal;
    display: inline-table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;

    > .#{$namespace}-input__inner {
        vertical-align: middle;
        display: table-cell;
    }

    @include e((append, prepend)) {
        background-color: var(--el-background-color-base);
        color: var(--el-color-info);
        vertical-align: middle;
        display: table-cell;
        position: relative;
        border: $border-base;
        border-radius: var(--el-input-border-radius);
        padding: 0 20px;
        width: 1px;
        white-space: nowrap;

        &:focus {
            outline: none;
        }

        .#{$namespace}-select,
        .#{$namespace}-button {
            display: inline-block;
            margin: -10px -20px;
        }

        button.#{$namespace}-button,
        div.#{$namespace}-select .#{$namespace}-input__inner,
        div.#{$namespace}-select:hover .#{$namespace}-input__inner {
            border-color: transparent;
            background-color: transparent;
            color: inherit;
            border-top: 0;
            border-bottom: 0;
        }

        .#{$namespace}-button,
        .#{$namespace}-input {
            font-size: inherit;
        }
    }

    @include e(prepend) {
        border-right: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    @include e(append) {
        border-left: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    @include m(prepend) {
        .#{$namespace}-input__inner {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
        .#{$namespace}-select .#{$namespace}-input.is-focus .#{$namespace}-input__inner {
            border-color: transparent;
        }
    }

    @include m(append) {
        .#{$namespace}-input__inner {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
        .#{$namespace}-select .#{$namespace}-input.is-focus .#{$namespace}-input__inner {
            border-color: transparent;
        }
    }
}

/** disalbe default clear on IE */
.#{$namespace}-input__inner::-ms-clear {
    display: none;
    width: 0;
    height: 0;
}
