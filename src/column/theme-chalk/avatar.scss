@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(avatar) {
    @include set-component-css-var('avatar', $avatar);

    display: inline-block;
    box-sizing: border-box;
    text-align: center;
    overflow: hidden;
    color: var(--el-avatar-font-color);
    background: var(--el-avatar-background-color);
    width: var(--el-avatar-large-size);
    height: var(--el-avatar-large-size);
    line-height: var(--el-avatar-large-size);
    font-size: var(--el-avatar-text-font-size);

    > img {
        display: block;
        height: 100%;
        vertical-align: middle;
    }

    @include m(circle) {
        border-radius: 50%;
    }

    @include m(square) {
        border-radius: var(--el-avatar-border-radius);
    }

    @include m(icon) {
        font-size: var(--el-avatar-icon-font-size);
    }

    @each $size in (small, medium, large) {
        @include m($size) {
            width: var(--el-avatar-#{$size}-size);
            height: var(--el-avatar-#{$size}-size);
            line-height: var(--el-avatar-#{$size}-size);
        }
    }
}
