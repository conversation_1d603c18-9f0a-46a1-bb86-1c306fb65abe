@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'mixins/utils' as *;
@use 'common/var' as *;
@use 'common/transition';

@mixin menu-item {
    height: 56px;
    line-height: 56px;
    font-size: var(--el-menu-item-font-size);
    color: var(--el-menu-text-color);
    padding: 0 20px;
    list-style: none;
    cursor: pointer;
    position: relative;
    transition: border-color var(--el-transition-duration), background-color var(--el-transition-duration), color var(--el-transition-duration);
    box-sizing: border-box;
    white-space: nowrap;

    * {
        vertical-align: middle;
    }

    i {
        color: inherit;
    }

    &:hover,
    &:focus {
        outline: none;
    }

    &:hover {
        background-color: var(--el-menu-hover-background-color);
    }

    @include when(disabled) {
        opacity: 0.25;
        cursor: not-allowed;
        background: none !important;
    }
}

@include b(menu) {
    @include set-component-css-var('menu', $menu);

    border-right: solid 1px var(--el-menu-border-color);
    list-style: none;
    position: relative;
    margin: 0;
    padding-left: 0;
    background-color: var(--el-menu-background-color);
    box-sizing: border-box;

    @include m(horizontal) {
        display: flex;
        flex-wrap: nowrap;
        border-bottom: solid 1px var(--el-menu-border-color);
        border-right: none;

        & > .#{$namespace}-menu-item {
            display: inline-flex;
            justify-content: center;
            align-items: center;

            height: 60px;
            margin: 0;
            border-bottom: 2px solid transparent;
            color: var(--el-menu-text-color);

            a,
            a:hover {
                color: inherit;
            }

            &:not(.is-disabled):hover,
            &:not(.is-disabled):focus {
                background-color: #fff;
            }
        }
        & > .#{$namespace}-sub-menu {
            &:focus,
            &:hover {
                outline: none;
            }

            &:hover {
                .#{$namespace}-sub-menu__title {
                    color: var(--el-menu-hover-text-color);
                }
            }

            &.is-active {
                .#{$namespace}-sub-menu__title {
                    border-bottom: 2px solid var(--el-menu-active-color);
                    color: var(--el-menu-active-color);
                }
            }

            & .#{$namespace}-sub-menu__title {
                height: 60px;
                line-height: 60px;
                border-bottom: 2px solid transparent;
                color: var(--el-menu-text-color);

                &:hover {
                    background-color: #fff;
                }
            }

            & .#{$namespace}-sub-menu__icon-arrow {
                position: static;
                vertical-align: middle;
                margin-left: 8px;
                margin-top: -3px;
            }
        }
        & .#{$namespace}-menu {
            & .#{$namespace}-menu-item,
            & .#{$namespace}-sub-menu__title {
                background-color: var(--el-menu-background-color);
                display: flex;
                align-items: center;
                height: 36px;
                padding: 0 10px;
                color: var(--el-menu-text-color);
            }

            & .#{$namespace}-menu-item.is-active,
            & .#{$namespace}-sub-menu.is-active > .#{$namespace}-sub-menu__title {
                color: var(--el-menu-active-color);
            }
        }
        & .#{$namespace}-menu-item:not(.is-disabled):hover,
        & .#{$namespace}-menu-item:not(.is-disabled):focus {
            outline: none;
            color: var(--el-menu-hover-text-color);
            background-color: var(--el-menu-hover-background-color);
        }
        & > .#{$namespace}-menu-item.is-active {
            border-bottom: 2px solid var(--el-menu-active-color);
            color: var(--el-menu-active-color) !important;
        }
    }
    @include m(collapse) {
        width: 64px;

        > .#{$namespace}-menu-item,
        > .#{$namespace}-sub-menu > .#{$namespace}-sub-menu__title {
            [class^='#{$namespace}-icon-'] {
                margin: 0;
                vertical-align: middle;
                width: 24px;
                text-align: center;
            }

            .#{$namespace}-sub-menu__icon-arrow {
                display: none;
            }

            span {
                height: 0;
                width: 0;
                overflow: hidden;
                visibility: hidden;
                display: inline-block;
            }
        }

        > .#{$namespace}-menu-item.is-active i {
            color: inherit;
        }

        .#{$namespace}-menu .#{$namespace}-sub-menu {
            min-width: 200px;
        }

        .#{$namespace}-sub-menu {
            position: relative;

            & .#{$namespace}-menu {
                position: absolute;
                margin-left: 5px;
                top: 0;
                left: 100%;
                z-index: 10;
                border: 1px solid var(--el-border-color-light);
                border-radius: var(--el-border-radius-small);
                box-shadow: var(--el-box-shadow-light);
            }

            &.is-opened {
                > .#{$namespace}-sub-menu__title .#{$namespace}-sub-menu__icon-arrow {
                    transform: none;
                }
            }

            &.is-active i {
                color: inherit;
            }
        }
    }
    @include m(popup) {
        z-index: 100;
        min-width: 200px;
        border: none;
        padding: 5px 0;
        border-radius: var(--el-border-radius-small);
        box-shadow: var(--el-box-shadow-light);
    }
}

@include b(menu-item) {
    @include menu-item;

    & [class^='#{$namespace}-icon-'] {
        margin-right: 5px;
        width: 24px;
        text-align: center;
        font-size: 18px;
        vertical-align: middle;
    }
    @include when(active) {
        color: var(--el-menu-active-color);
        i {
            color: inherit;
        }
    }
}

@include b(sub-menu) {
    list-style: none;
    margin: 0;
    padding-left: 0;

    @include e(title) {
        @include menu-item;

        &:hover {
            background-color: var(--el-menu-hover-background-color);
        }
    }
    & .#{$namespace}-menu {
        border: none;
    }
    & .#{$namespace}-menu-item {
        height: 50px;
        line-height: 50px;
        padding: 0 45px;
        min-width: 200px;
    }
    @include e(hide-arrow) {
        .#{$namespace}-sub-menu__icon-arrow {
            display: none !important;
        }
    }
    @include e(icon-more) {
        margin-right: 0 !important;
    }
    @include e(icon-arrow) {
        position: absolute;
        top: 50%;
        right: 20px;
        margin-top: -7px;
        transition: transform var(--el-transition-duration);
        font-size: 12px;
    }
    @include when(active) {
        .#{$namespace}-sub-menu__title {
            border-bottom-color: var(--el-menu-active-color);
        }
    }
    @include when(opened) {
        > .#{$namespace}-sub-menu__title .#{$namespace}-sub-menu__icon-arrow {
            transform: rotateZ(180deg);
        }
    }
    @include when(disabled) {
        .#{$namespace}-sub-menu__title,
        .#{$namespace}-menu-item {
            opacity: 0.25;
            cursor: not-allowed;
            background: none !important;
        }
    }
    [class^='#{$namespace}-icon-'] {
        vertical-align: middle;
        margin-right: 5px;
        width: 24px;
        text-align: center;
        font-size: 18px;
    }
}

@include b(menu-item-group) {
    > ul {
        padding: 0;
    }
    @include e(title) {
        padding: 7px 0 7px 20px;
        line-height: normal;
        font-size: 12px;
        color: var(--el-text-color-secondary);
    }
}

.horizontal-collapse-transition .#{$namespace}-sub-menu__title .#{$namespace}-sub-menu__icon-arrow {
    transition: var(--el-transition-duration-fast);
    opacity: 0;
}
