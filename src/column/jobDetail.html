<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>职位详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/detail.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main">
                <div class="detail-container">
                    <div class="detail-header">
                        <div class="detail-header-container">
                            <div class="breadcrumb">
                                位置：
                                <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞ <a>北京大学珠海分校2021年度招聘公告</a>＞ <a>职位列表</a>＞ 职位详情
                            </div>

                            <div class="main">
                                <section>
                                    <div class="title">
                                        <h1>
                                            东师范大学医学与健康研究院诚聘助理研究员东师范大学医学与健康研究院诚聘助理研究员
                                            <div class="job-tag-cell">
                                                <span class="help-wanted">急聘</span>
                                                <span class="establishment">编制</span>
                                                <span class="fast-feedback">反馈快</span>
                                            </div>
                                        </h1>

                                        <span class="color-salary">15K-25K/月</span>
                                    </div>

                                    <div class="tips">
                                        <div class="job-tag-cell">
                                            <span class="help-wanted">急聘</span>
                                            <span class="establishment">编制</span>
                                            <span class="fast-feedback">反馈快</span>
                                        </div>

                                        <span>昆明</span>
                                        <span>硕士研究生</span>
                                        <span>5-10年</span>
                                        <span>若干人</span>
                                        <span>05-09发布</span>

                                        <a class="view-relation" href="">查看公告详情</a>
                                    </div>

                                    <div id="boonsTemplate" class="boons">
                                        <span class="boon">全勤奖</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">五险一金</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">绩效奖金</span>

                                        <el-popover placement="bottom" :width="330" trigger="hover" v-cloak>
                                            <template #reference>
                                                <i class="el-icon boon-more">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                        <path
                                                            fill="currentColor"
                                                            d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                        ></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <!-- <span class="boon">全勤奖</span> -->
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">五险一金</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">绩效奖金</span>
                                        </el-popover>
                                    </div>
                                </section>

                                <aside>
                                    <div class="emit">
                                        <!-- <button class="el-button el-button--collect">
                                            <span>收藏</span>
                                        </button> -->
                                        <div class="detail-button">
                                            <div class="share-mini-code-container">
                                                <div class="share-mini-code-trigger share-mini-code-trigger--primary">分享</div>

                                                <div class="share-mini-code-popup">
                                                    <div class="share-mini-code">
                                                        <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                    </div>
                                                    <div class="share-mini-code-title">微信扫一扫</div>
                                                    <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                                </div>
                                            </div>
                                            <div class="el-button--collect">收藏</div>
                                            <!-- <div class="el-button--collect collected">已收藏</div> -->
                                            <div class="el-button--analyse see-analysis">竞争力分析</div>
                                        </div>
                                        <!-- <div class="el-button--collect">收藏</div> -->
                                        <!-- <button class="el-button el-button--collect collected">
                                        <span>已收藏</span>
                                    </button> -->

                                        <div class="button-group">
                                            <button class="el-button chat-button">
                                                <span>聊一聊</span>
                                            </button>

                                            <button class="el-button el-button--primary job-apply-button">
                                                <span>立即投递</span>
                                            </button>
                                            <!-- <button class="el-button el-button--primary is-disabled" disabled>
                                        <span>已投递</span>
                                    </button>
                                    <button class="el-button el-button--info is-plain is-disabled" disabled>
                                        <span>已下线</span>
                                    </button> -->
                                        </div>
                                    </div>
                                </aside>

                                <a class="el-button el-button--primary view-button" href="">
                                    <span>查看公告详情</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <section>
                            <div class="tips">
                                声明：本站部分公告与职位内容由本站根据官方招聘公告进行整理编辑。由于用人单位需求专业、学历学位、资格条件、职位编制、备注内容等内容情况复杂且有变化可能，是否符合招聘条件以用人单位公告为准或请联系用人单位确认。本站整理编辑的职位信息仅供求职者参考，如因此造成的损失本站不承担任何责任！
                            </div>

                            <div class="detail-subtitle is-job">职位详情</div>

                            <div class="detail-list">
                                <div class="detail-item">
                                    <div class="detail-title">基本信息</div>
                                    <div class="detail-content el-row">
                                        <div class="el-col">职位名称：东师范大学医学与健康研究院诚聘助理研究...</div>
                                        <div class="el-col">
                                            职位类型：
                                            <a class="color-primary">高校/科研机构中层党政部门负责人</a>
                                        </div>

                                        <div class="el-col is-complete">工作地点：江苏省江苏市湘府路189号天堂大街178号25栋603号房间</div>
                                        <div class="el-col">招聘人数：3</div>

                                        <div class="el-col is-complete">报名方式：电子邮件，网上系统，现场报名，电话报名，传真，邮寄，其他</div>
                                        <div class="el-col">截止时间：2018-08-10</div>

                                        <div class="el-col is-complete">
                                            用人部门：
                                            <a class="color-primary">如果我的用人部门名字很长，我就需要展示完整，不然就一行展示</a>
                                        </div>
                                        <div class="el-col">工作性质：全职</div>
                                        <div class="el-col disinblock is-complete establishment-explain">
                                            职位编制 ： 有编制/事业编制/{编制类型}
                                            <!-- <a class="color-primary">说明</a> -->
                                            <el-popover placement="bottom" :width="300" trigger="hover" content="职位编制内容仅供参考！请以用人单位发布的公告&职位信息内容为准，或联系用人单位确认。">
                                                <template #reference>
                                                    <p class="color-primary">说明</p>
                                                </template>
                                            </el-popover>
                                        </div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-title">其他要求</div>
                                    <div class="detail-content el-row">
                                        <div class="el-col">学历要求：博士及以上</div>
                                        <div class="el-col">职称要求：不限</div>
                                        <div class="el-col">工作经验：不限</div>
                                        <div class="el-col is-complete">年龄要求：不限不限不限不限不限不限不限不限不限不限不限不限不限不限不限</div>
                                        <div class="el-col">性别要求：不限</div>
                                        <div class="el-col">海外经历：不限</div>
                                        <div class="el-row">
                                            <div class="el-col">政治面貌：不限</div>
                                        </div>
                                        <div class="el-row">
                                            <div class="el-col show-complete">
                                                <span class="label">需求专业：</span>
                                                <div class="value">
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                    <a href="" class="color-primary" target="_blank">应用经济学, </a>
                                                </div>
                                            </div>

                                            <div class="job-detail-major-tips">
                                                <span>该需求专业仅展示一级学科</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-title">岗位职责</div>
                                    <div class="detail-content single">
                                        <p>1、本科及以上学历，正高或副高职称，相关证件齐全；</p>
                                        <p>2、三甲级医院护理管理工作经验多年；</p>
                                        <p>3、具有深厚的专业理论知识、丰富的临床经验和科研等工作能力；</p>
                                        <p>5、爱岗敬业，具备良好的沟通能力和组织协调管理能力，团队意识强；</p>
                                        <p>6、同时欢迎离退休专家及享受国务院津贴等教授加入我们。</p>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-title">任职要求</div>
                                    <div class="detail-content single">
                                        <p>1、本科及以上学历，正高或副高职称，相关证件齐全；</p>
                                        <p>2、三甲级医院护理管理工作经验多年；</p>
                                        <p>3、具有深厚的专业理论知识、丰富的临床经验和科研等工作能力；</p>
                                        <p>5、爱岗敬业，具备良好的沟通能力和组织协调管理能力，团队意识强；</p>
                                        <p>6、同时欢迎离退休专家及享受国务院津贴等教授加入我们。</p>
                                    </div>
                                </div>

                                <div class="detail-item">
                                    <div class="detail-title">其他说明</div>
                                    <div class="detail-content single">
                                        <p>1、本科及以上学历，正高或副高职称，相关证件齐全；</p>
                                        <p>2、三甲级医院护理管理工作经验多年；</p>
                                        <p>3、具有深厚的专业理论知识、丰富的临床经验和科研等工作能力；</p>
                                        <p>5、爱岗敬业，具备良好的沟通能力和组织协调管理能力，团队意识强；</p>
                                        <p>6、同时欢迎离退休专家及享受国务院津贴等教授加入我们。</p>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-subtitle is-file">附件下载</div>

                            <div class="file-list">
                                <el-popover placement="bottom" :width="300" trigger="hover" content="word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx">
                                    <template #reference>
                                        <a href="" download="" class="file" target="_blank">
                                            <div class="word"></div>
                                            <div class="file-name">word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx</div>
                                        </a>
                                    </template>
                                </el-popover>
                                <el-popover placement="bottom" :width="300" trigger="hover" content="excel文件名称.xlsx">
                                    <template #reference>
                                        <a href="" download="" class="file" target="_blank">
                                            <div class="excel"></div>
                                            <div class="file-name">excel文件名称.xlsx</div>
                                        </a>
                                    </template>
                                </el-popover>
                                <a href="" download="" class="file" target="_blank">
                                    <div class="pdf"></div>
                                    <div class="file-name">pdf文件名称.pdf</div>
                                </a>

                                <a href="" download="" class="file" target="_blank">
                                    <div class="common"></div>
                                    <div class="file-name">txt文件名称.txt</div>
                                </a>
                            </div>

                            <!-- 未登录 start -->
                            <div class="see-analysis">
                                <div class="detail-subtitle is-analyse analyse">
                                    <div>竞争力分析</div>
                                    <div class="open-analysis">解锁详细分析</div>
                                </div>

                                <div class="power-analyse">
                                    <div class="text-rate">您与该职位匹配度: <span class="active">非常匹配</span>，已超过了<span class="active">56%</span>的竞争者</div>
                                    <div class="not-login">您在 ？位置</div>
                                    <div class="matching-rate">
                                        <div class="lower">
                                            <div></div>
                                            <span></span>
                                            较低
                                        </div>
                                        <div class="general">
                                            <div></div>
                                            <span></span>
                                            一般
                                        </div>
                                        <div class="matching">
                                            <div></div>
                                            <span></span>
                                            比较匹配
                                        </div>
                                        <div class="closely-match">
                                            <div></div>
                                            <span></span>
                                            非常匹配
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 未登录 end -->

                            <!-- 已登录 start -->
                            <div class="detail-subtitle is-analyse analyse">
                                <div>竞争力分析</div>
                                <div class="open-analysis">查看详细分析</div>
                            </div>

                            <div class="power-analyse">
                                <div class="text-rate">您与该职位匹配度: <span>非常匹配</span>，已超过了<span>56%</span>的竞争者</div>
                                <div class="matching-rate">
                                    <div class="lower">
                                        <div class="active"></div>
                                        <span></span>
                                        较低
                                    </div>
                                    <div class="general">
                                        <div class="active"></div>
                                        <span></span>
                                        一般
                                    </div>
                                    <div class="matching">
                                        <div class="active"></div>
                                        <span></span>
                                        比较匹配
                                    </div>
                                    <div class="closely-match">
                                        <div class="active"></div>
                                        <span></span>
                                        非常匹配
                                    </div>
                                </div>
                            </div>
                            <!-- 已登录 end -->

                            <div class="detail-emit">
                                <a class="el-button el-button--primary" href=""><span>查看其他职位</span></a>
                                <a class="el-button el-button--primary" href=""><span>查看公告详情</span></a>

                                <button class="el-button el-button--primary job-apply-button">
                                    <span>立即投递</span>
                                </button>
                                <!-- <button class="el-button el-button--primary is-disabled" disabled>
                                <span>已投递</span>
                            </button>
                            <button class="el-button el-button--info is-plain is-disabled" disabled>
                                <span>已下线</span>
                            </button> -->
                            </div>

                            <div class="qr-code">
                                <div class="qr-tips">更多热点资讯！欢迎扫描下方二维码关注高校人才网官方微信（硕博QQ交流群：676044887）。</div>
                                <img class="code" src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" />
                            </div>

                            <div class="detail-tips">
                                重要风险提示：如招聘单位在招聘过程中向求职者提出收取押金、保证金、体检费、材料费、成本费，或指定医院体检等，求职者有权要求招聘单位出具物价部门批准的收费许可证明材料，若无法提供相关证明，请求职者提高警惕，有可能属于诈骗或违规行为。
                            </div>

                            <!-- <div class="share-custom">
                                <div class="sina-weibo">
                                    <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24"> </wb:follow-button>
                                </div>
                                <div>
                                    <div class="bshare-custom">
                                        <a title="分享到微信" class="bshare-weixin"></a>
                                        <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>
                                        <a title="分享到QQ空间" class="bshare-qzone"></a>
                                        <a title="分享到Facebook" class="bshare-facebook"></a>
                                        <a title="分享到Twitter" class="bshare-twitter"></a>
                                        <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn"> </a>
                                        <span class="BSHARE_COUNT bshare-share-count">0</span>
                                    </div>
                                    <script src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>
                                    <script src="http://static.bshare.cn/b/bshareC0.js"></script>
                                </div>
                            </div> -->
                        </section>

                        <aside>
                            <div class="unit">
                                <a class="unit-name" href="" target="_blank">
                                    <img class="logo" src="http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png" alt="" />

                                    <div class="name">
                                        <h3>湖南师范大学</h3>
                                        <h5>Hunan Normal University</h5>
                                    </div>
                                </a>

                                <div class="unit-tips category">教育/培训/院校</div>
                                <div class="unit-tips number">1000人以上</div>
                                <div class="unit-tips type">科研院所</div>

                                <div class="unit-data el-row">
                                    <a class="el-col el-col-8" href="" target="_blank">
                                        <strong class="color-primary">100</strong>
                                        <span>在招公告</span>
                                    </a>
                                    <a class="el-col el-col-8" href="" target="_blank">
                                        <strong class="color-primary">66</strong>
                                        <span>在招职位</span>
                                    </a>
                                    <span class="el-col el-col-8 to-home">
                                        <a href="" target="_blank">
                                            <span>单位主页</span>
                                        </a>
                                    </span>
                                </div>
                            </div>

                            <div class="contact">
                                <div class="title">职位联系人</div>

                                <div class="intro">
                                    <div class="avatar-content">
                                        <img src="http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png" alt="" class="avatar" />

                                        <span class="online-status"></span>
                                    </div>

                                    <div class="box">
                                        <div class="top">
                                            <div class="name">李主任李主任李主任李主任李主任李主任李主任李主任李主任李主任李主任李主任</div>
                                            <span class="active-time">3天内活跃</span>
                                        </div>
                                        <div class="desc">经济管理与国际贸易学院经济管理与国际贸易学院</div>
                                    </div>
                                </div>

                                <div class="chat chat-button">
                                    <span>聊一聊</span>
                                </div>
                            </div>

                            <div id="recommendContainer">
                                <!-- <div class="recommend">
                                <div class="title">
                                    <h5>专享推荐</h5>
                                </div>

                                <div id="detailRecommend" class="swiper">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="recommend-list">
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>150-200K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>150-200K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>15-20K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>15-20K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="recommend-list">
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>15-20K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>15-20K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>15-20K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                                <a class="recommend-item" href="" target="_blank">
                                                    <div class="recommend-item-title">
                                                        <span class="name">助理辅导员助理辅导助理辅导助助理辅导员助理辅导助理辅导助</span>
                                                        <span>15-20K</span>
                                                    </div>

                                                    <div class="recommend-item-tips">
                                                        <span>经验不限</span>
                                                        <span>硕士研究生</span>
                                                        <span>若干人</span>
                                                    </div>

                                                    <div class="recommend-item-data">
                                                        <h6>清华大学清华清华大学清华清华大学清华清华大学清华</h6>
                                                        <span>呼和浩特呼和浩特</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="swiper-pagination"></div>
                                </div>
                            </div>
                            <script>
                                $(function () {
                                    new Swiper("#detailRecommend", {
                                        autoplay: {
                                            delay: 7000,
                                            pauseOnMouseEnter: true,
                                            disableOnInteraction: false
                                        },
                                        delay: 7000,
                                        loop: true,
                                        pagination: {
                                            el: ".swiper-pagination",
                                            clickable: true
                                        }
                                    })
                                })
                            </script> -->
                            </div>
                        </aside>
                    </div>
                </div>
            </div>
        </div>

        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                <a href="/" target="_blank">资质证明</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                    <a href="/" target="_blank">粤ICP备13048400号-2</a>
                    粤公网安备：
                    <a href="/" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="/" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <script src="http://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
        <script src="./js/detailService.js"></script>
        <script>
            $(function () {
                Vue.createApp({}).use(ElementPlus).mount('#boonsTemplate')
                Vue.createApp({}).use(ElementPlus).mount('.establishment-explain')
                Vue.createApp({}).use(ElementPlus).mount('.file-list')

                function getQueryVariable(variable) {
                    var query = window.location.search.substring(1)
                    var vars = query.split('&')
                    for (var i = 0; i < vars.length; i++) {
                        var pair = vars[i].split('=')
                        if (pair[0] == variable) {
                            return pair[1]
                        }
                    }
                }

                var jobId = "<?= $info['jobId'] ?>"
                var $applyButtons = $('.job-apply-button')
                var $chatButtons = $('.chat-button')
                var $collectButton = $('.el-button--collect')
                var isFromEmail = !!getQueryVariable('from')
                var $recommendContainer = $('#recommendContainer')

                httpPost('/api/person/job/get-recommend-list', { id: jobId }).then(function (data) {
                    if (data.html) {
                        $recommendContainer.html(data.html)
                    }
                })

                $applyButtons.on('click', function () {
                    window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                        $applyButtons.prop('disabled', true).addClass('is-disabled').find('span').text('已投递')
                    })
                })

                $chatButtons.on('click', function () {
                    window.globalComponents.ChatDialogComponent.create(jobId, function (resp) {
                        const { chatButtonText = '聊一聊' } = resp
                        $chatButtons.html(`<span>${chatButtonText}</span>`)
                    })
                })

                if (isFromEmail) {
                    $applyButtons.click()
                }

                $collectButton.on('click', function () {
                    var $this = $(this)
                    var isCollected = $this.hasClass('collected')
                    httpPost('/api/person/job/collect', { jobId }).then(function () {
                        $this.toggleClass('collected').text(isCollected ? '收藏' : '已收藏')
                    })
                })

                const PayDialogAlertComponent = Vue.createApp(payDialogAlertOptions).use(ElementPlus).mount('#payDialogAlert')

                var seeAnalysis = $('.see-analysis')
                var params = {
                    apiPull: '/api/person/job/check-generate-report',
                    apiCreate: '/api/person/job/create-report',
                    param: { jobId: '<?=$info["id"]?>' }
                }
                seeAnalysis.on('click', function () {
                    window.globalComponents.PromptDialogComponent.pull(params)
                })
            })
        </script>
    </body>
</html>
