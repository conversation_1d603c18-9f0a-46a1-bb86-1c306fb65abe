<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>博后活动</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/commonPostdoctor.css" />
        <link rel="stylesheet" href="./css/postdoctorActivity.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="./lib/swiper/swiper.min.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <div class="postdoctor-container">
            <header class="postdoctor-header-container">
                <div class="postdoctor-header-content">
                    <div class="postdoctor-menu-content">
                        <a href="/" class="logo">
                            <span class="logo-text">高才博士后</span>

                            <div class="collect-tips"></div>
                        </a>
                        <a href="/" class="postdoctor-menu-item">首页</a>
                        <a href="/" class="postdoctor-menu-item">博后公告&职位</a>
                        <a href="/" class="postdoctor-menu-item">招收单位& PI大厅</a>
                        <a href="/" class="postdoctor-menu-item active">博后活动</a>
                    </div>
                    <div class="postdoctor-nav-content">
                        <a href="" class="postdoctor-nav-item">需求发布</a>
                        <a href="" class="postdoctor-nav-item" target="_blank">高才官网</a>
                        <a href="" class="postdoctor-nav-item" target="_blank">单位登录</a>

                        <button class="postdoctor-login-btn open-login-dialog">人才登录</button>
                        <!-- <a class="person-avatar" href="" target="_blank"></a>
                            <img src="https://picsum.photos/seed/picsum/36/36" alt="" />
                        </a> -->
                    </div>
                </div>

                <script>
                    $(function () {
                        var $header = $('.postdoctor-header-container')
                        var $logo = $header.find('.logo')
                        var $logoTips = $header.find('.collect-tips')
                        var isCollectShow = false

                        function showCollect() {
                            if (isCollectShow) return

                            isCollectShow = true
                            var time = new Date().getTime()

                            $logoTips.fadeIn(0).css('background', `url(./assets/postdoctor/common/collect-tips.gif?v=${time}) no-repeat center/100% auto`)

                            setTimeout(() => {
                                $logoTips.fadeOut(1000)

                                setTimeout(() => {
                                    isCollectShow = false
                                }, 1000)
                            }, 6000)
                        }

                        var envHrefMap = [
                            'https://boshihou.gaoxiaojob.com/',
                            'https://boshihou.test.gcjob.jugaocai.com/',
                            'https://boshihou.dev.gcjob.jugaocai.com/',
                            'https://boshihou.pre.gcjob.jugaocai.com/',
                            'https://boshihou.gray.gcjob.jugaocai.com/'
                        ]
                        var href = window.location.href

                        var isPostdoctorHome = envHrefMap.includes(href)

                        if (isPostdoctorHome) {
                            showCollect()
                        }

                        $logo.mouseenter(showCollect)
                    })
                </script>
            </header>

            <div class="main-container">
                <div class="swiper-wrapper">
                    <div class="swiper swiper-t1">
                        <div class="swiper-wrapper">
                            <a class="swiper-slide" href="" target="_blank">
                                <img src="https://picsum.photos/id/1/642/170" alt="" />
                            </a>
                            <a class="swiper-slide" href="" target="_blank">
                                <img src="https://picsum.photos/id/2/642/170" alt="" />
                            </a>
                            <a class="swiper-slide" href="" target="_blank">
                                <img src="https://picsum.photos/id/3/642/170" alt="" />
                            </a>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
                <!-- 推荐活动 -->
                <div class="activity recommend-wrapper">
                    <div class="list-header">
                        <span class="title-fill">推荐</span>
                        <span class="title-suffix">活动</span>
                        <span class="title-english">Featured Activities</span>
                    </div>
                    <div class="list-content">
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="" nofollow="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>
                                            活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2行
                                        </p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn">立即参与</div>
                            </div>
                        </div>
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="" nofollow="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2行…</p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn">立即参与</div>
                            </div>
                        </div>
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="" nofollow="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2行…</p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex" data-url="http://www.baidu.com">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn">立即参与</div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 最新活动 -->
                <div class="activity newest-wrapper">
                    <div class="list-header">
                        <span class="title-fill">最新</span>
                        <span class="title-suffix">活动</span>
                        <span class="title-english">Recent Activities</span>
                    </div>
                    <div class="list-content">
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>活动标题活动标题活动标题活动标题</p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn">立即参与</div>
                            </div>
                        </div>
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="" nofollow="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2行…</p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn disabled">已结束</div>
                            </div>
                        </div>
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="" nofollow="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2行…</p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn disabled">已结束</div>
                            </div>
                        </div>
                        <div class="list animation-mouseover">
                            <div class="hd">
                                <a href="" class="" target="_blank" title="" nofollow="">
                                    <div class="pic-box">
                                        <img src="https://picsum.photos/seed/picsum/100/100" alt="" />
                                        <p class="tag">北京,上海,广州…北京,上海,广州…/</p>
                                    </div>
                                    <div class="tit">
                                        <p>活动标题活动标题活动标题活动标题活动标题活动标题超2行超2行超2行超2行超2行超2行…</p>
                                    </div>
                                </a>
                            </div>
                            <div class="ft pointer flex" data-url="http://xxx.com">
                                <div class="aside flex1">
                                    <div class="total">活动日期:<span>2024.07.15 ～ 2024.07.17</span></div>
                                    <div class="total">报名截止:<span>2024.05.10</span></div>
                                </div>
                                <div class="btn disabled">已结束</div>
                            </div>
                        </div>
                    </div>
                    <div id="paginationComponent" class="pagination">
                        <el-pagination background layout="prev, pager, next" @current-change="handlePageChange" :default-page-size="pageSize" :total="total" v-model:current-page="page" />
                    </div>

                    <div class="empty-container">暂无相关活动，敬请期待～</div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar-container">
                <div class="guild-part">
                    <a href="/member/person/home" target="_blank" class="tools-link is-home"> 主页 </a>

                    <a href="/member/person/resume" target="_blank" class="tools-link is-resume">简历</a>

                    <a href="/member/person/invite" target="_blank" class="tools-link is-invite">
                        <span>邀约</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/delivery" target="_blank" class="tools-link is-delivery">
                        <span>投递</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <a href="/member/person/chat" target="_blank" class="tools-link is-chat">
                        <span>直聊</span>
                        <sup class="badge">99+</sup>
                    </a>

                    <!-- <a href="/member/person/collection" target="_blank"
                                        class="tools-link is-collect">收藏</a> -->

                    <!-- <a href="/member/person/message" target="_blank" class="tools-link is-news">消息</a> -->
                </div>

                <div class="status-part" id="statusTemplate">
                    <a href="javascript:;" class="link-icon miniapp">
                        <span>小程序</span>
                        <span class="miniapp-hover">扫码进入小程序</span>
                    </a>

                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高才博士后公众号</span>
                    </a>

                    <a href="javascript:;" class="link-icon mobile">
                        <span>移动端</span>
                        <span class="mobile-hover">查看高校人才网移动端</span>
                    </a>

                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <a href="javascript:;" class="link-icon feedback-link">
                                <span>
                                    咨询<br />
                                    反馈
                                </span>
                                <span class="mobile-hover">查看高校人才网移动端</span>
                            </a>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>平台功能体验等问题请联系：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                        <p>求职会员服务问题请咨询：</p>
                                        <p><strong>微信：</strong>gzgxrcw14</p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>

                    <!-- <a href="//weibo.com/jobgd" target="_blank" class="link-icon weibo">
                        <span>微博</span>
                        <span class="weibo-hover">关注高校人才网官方微博</span>
                    </a> -->
                </div>
            </div>

            <!-- 返回顶部 start -->
            <div id="backtopTemplate">
                <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                    <span class="backtop-button"></span>
                    <div class="top">TOP</div>
                </el-backtop>
            </div>

            <script>
                $(function () {
                    const backtopOptions = {
                        computed: {
                            viewportHeight() {
                                return window.innerHeight / 2
                            }
                        }
                    }

                    Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
                    Vue.createApp({}).use(ElementPlus).mount('#statusTemplate')
                })
            </script>
            <!-- 返回顶部 end -->

            <footer class="page-footer-container">
                <div class="site-foot-menu">
                    <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                    <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                    <a href="/" target="_blank">资质证明</a>
                </div>

                <div class="site-foot-copyright">
                    <p>
                        Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                        <a href="/" target="_blank">粤ICP备13048400号-2</a>
                        粤公网安备：
                        <a href="/" target="_blank">44010602004138号</a>
                    </p>
                    <p>本站由广州高才信息科技有限公司运营</p>
                    <p>
                        中华人民共和国增值电信业务经营许可证：
                        <a href="/" target="_blank">粤B2-20180648</a>
                    </p>
                    <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                    <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                    <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                    <p>本平台由广东同福律师事务所提供法律支持服务</p>
                </div>
            </footer>
        </div>

        <script type="module">
            import { handleIntoView } from './lib/common/index.js'
            $(function () {
                var swiper = new Swiper('.swiper-t1', {
                    loop: true,
                    autoplay: {
                        delay: 5000,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: '.swiper-t1 .swiper-pagination',
                        clickable: true
                    }
                })
                // 点击热区使用js跳转
                $('.activity').on('click', '.list .ft', (event) => {
                    let url = event.currentTarget.dataset.url
                    // console.log(url);
                    let targetUrl = url || window.location.href
                    window.open(targetUrl, '_blank')
                })
            })
            $(function () {
                const paginationComponent = {
                    data() {
                        return {
                            page: 1,
                            pageSize: 9,
                            total: 100
                        }
                    },
                    methods: {
                        // 获取 最新活动 列表
                        async getNewestList() {
                            const res = await httpGet('/activity/get-list', { page: this.page })
                            // console.log(res);
                            if (!res.list) return
                            $('.newest-wrapper .list-content').html(res.list)
                        },
                        // 分页方法
                        handlePageChange(page) {
                            // console.log(page);
                            handleIntoView('.newest-wrapper', 60)
                            this.getNewestList()
                        }
                    },
                    mounted() {}
                }
                const paginationVue = Vue.createApp(paginationComponent).use(ElementPlus).mount('#paginationComponent')
            })
        </script>
    </body>
</html>
