<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Company Page Test</title>
    <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .company-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .company-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .company-logo {
            width: 60px;
            height: 60px;
            margin-right: 15px;
            border-radius: 5px;
        }
        .company-info {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .collect-btn {
            margin-left: auto;
            padding: 5px 15px;
            border: 1px solid #409eff;
            background: white;
            color: #409eff;
            border-radius: 3px;
            cursor: pointer;
        }
        .collect-btn.collected {
            background: #409eff;
            color: white;
        }
        .loading {
            text-align: center;
            padding: 40px;
        }
        .empty {
            text-align: center;
            padding: 40px;
            color: #999;
        }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <h1>Company Page - 前后分离测试</h1>
        
        <div class="search-section">
            <el-input 
                v-model="keyword" 
                placeholder="搜索公司名称"
                @keydown.enter="handleSearch"
                style="width: 300px; margin-right: 10px;">
            </el-input>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleClear">清空</el-button>
        </div>

        <div v-loading="loading" style="min-height: 200px; margin-top: 20px;">
            <div v-if="!loading && companyList.length === 0" class="empty">
                暂无公司数据
            </div>
            
            <div v-for="company in companyList" :key="company.companyId" class="company-item">
                <div class="company-header">
                    <img :src="company.logo" :alt="company.name" class="company-logo">
                    <div>
                        <h3>{{ company.name }}</h3>
                        <span>{{ company.nature }}</span>
                    </div>
                    <button 
                        class="collect-btn"
                        :class="{ collected: company.isCollected }"
                        @click="handleCollect(company.companyId)">
                        {{ company.isCollected ? '已收藏' : '收藏' }}
                    </button>
                </div>
                <div class="company-info">
                    <span><strong>{{ company.announcementCount }}</strong> 招聘公告</span>
                    <span><strong>{{ company.jobCount }}</strong> 招聘职位</span>
                    <span><strong>{{ company.activeTime }}</strong> 活跃时间</span>
                </div>
            </div>
        </div>

        <el-pagination
            v-if="!loading && companyList.length > 0"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="currentPage"
            :page-sizes="[5, 10, 20]"
            :page-size="pageSize"
            :total="total"
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
            style="margin-top: 20px; text-align: center;">
        </el-pagination>
    </div>

    <script src="./lib/vue/vue.min.js"></script>
    <script src="./lib/element-plus/index.min.js"></script>
    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    loading: false,
                    keyword: '',
                    currentPage: 1,
                    pageSize: 5,
                    total: 0,
                    companyList: []
                }
            },
            
            mounted() {
                this.loadMockData();
            },
            
            methods: {
                loadMockData() {
                    this.loading = true;
                    
                    // 模拟API调用延迟
                    setTimeout(() => {
                        const mockCompanies = [
                            {
                                companyId: 1,
                                name: '北京协和医学院医学中心',
                                nature: '科研院所',
                                logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png',
                                announcementCount: '999+',
                                jobCount: '999+',
                                activeTime: '近30天',
                                isCollected: false
                            },
                            {
                                companyId: 2,
                                name: '清华大学',
                                nature: '双一流院校',
                                logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw311.png',
                                announcementCount: '500+',
                                jobCount: '800+',
                                activeTime: '近7天',
                                isCollected: true
                            },
                            {
                                companyId: 3,
                                name: '北京大学',
                                nature: '双一流院校',
                                logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw312.png',
                                announcementCount: '600+',
                                jobCount: '900+',
                                activeTime: '近3天',
                                isCollected: false
                            },
                            {
                                companyId: 4,
                                name: '中科院计算技术研究所',
                                nature: '科研院所',
                                logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw313.png',
                                announcementCount: '200+',
                                jobCount: '300+',
                                activeTime: '近15天',
                                isCollected: false
                            },
                            {
                                companyId: 5,
                                name: '华为技术有限公司',
                                nature: '知名企业',
                                logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw314.png',
                                announcementCount: '1000+',
                                jobCount: '2000+',
                                activeTime: '近1天',
                                isCollected: true
                            }
                        ];
                        
                        // 根据关键词过滤
                        let filteredCompanies = mockCompanies;
                        if (this.keyword) {
                            filteredCompanies = mockCompanies.filter(company => 
                                company.name.includes(this.keyword)
                            );
                        }
                        
                        // 分页处理
                        const start = (this.currentPage - 1) * this.pageSize;
                        const end = start + this.pageSize;
                        
                        this.companyList = filteredCompanies.slice(start, end);
                        this.total = filteredCompanies.length;
                        this.loading = false;
                    }, 1000);
                },
                
                handleSearch() {
                    this.currentPage = 1;
                    this.loadMockData();
                },
                
                handleClear() {
                    this.keyword = '';
                    this.currentPage = 1;
                    this.loadMockData();
                },
                
                handlePageSizeChange(val) {
                    this.pageSize = val;
                    this.currentPage = 1;
                    this.loadMockData();
                },
                
                handleCurrentPageChange(val) {
                    this.currentPage = val;
                    this.loadMockData();
                },
                
                handleCollect(companyId) {
                    const company = this.companyList.find(item => item.companyId === companyId);
                    if (company) {
                        company.isCollected = !company.isCollected;
                        ElementPlus.ElMessage.success(
                            company.isCollected ? '收藏成功' : '取消收藏成功'
                        );
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
