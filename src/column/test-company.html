<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Company Page Test</title>
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
            }
            .test-container {
                max-width: 800px;
                margin: 0 auto;
            }
            .company-item {
                border: 1px solid #ddd;
                padding: 15px;
                margin: 10px 0;
                border-radius: 5px;
            }
            .company-header {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
            }
            .company-logo {
                width: 60px;
                height: 60px;
                margin-right: 15px;
                border-radius: 5px;
            }
            .company-info {
                display: flex;
                gap: 20px;
                margin-top: 10px;
            }
            .collect-btn {
                margin-left: auto;
                padding: 5px 15px;
                border: 1px solid #409eff;
                background: white;
                color: #409eff;
                border-radius: 3px;
                cursor: pointer;
            }
            .collect-btn.collected {
                background: #409eff;
                color: white;
            }
            .loading {
                text-align: center;
                padding: 40px;
            }
            .empty {
                text-align: center;
                padding: 40px;
                color: #999;
            }
            .filter-tag {
                transition: all 0.3s ease;
            }
            .filter-tag:hover {
                background-color: #f0f0f0;
            }
            .filter-tag.active {
                background-color: #409eff !important;
                color: white !important;
                border-color: #409eff !important;
            }
        </style>
    </head>
    <body>
        <div id="app" class="test-container">
            <h1>Company Page - 前后分离测试</h1>

            <div class="search-section">
                <el-input v-model="keyword" placeholder="搜索公司名称" @keydown.enter="handleSearch" style="width: 300px; margin-right: 10px"> </el-input>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="handleClear">清空</el-button>
            </div>

            <!-- 热门搜索 -->
            <div class="hot-search-section" style="margin-top: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px" v-if="hotSearchList.length">
                <strong>热门搜索：</strong>
                <span
                    v-for="item in hotSearchList"
                    :key="item.id"
                    class="hot-search-tag"
                    @click="handleHotSearchClick(item.text)"
                    style="margin-right: 10px; padding: 3px 8px; background-color: #e9ecef; border-radius: 3px; cursor: pointer; display: inline-block; margin-bottom: 5px; transition: all 0.3s ease"
                    @mouseover="$event.target.style.backgroundColor = '#409eff'; $event.target.style.color = 'white'"
                    @mouseout="$event.target.style.backgroundColor = '#e9ecef'; $event.target.style.color = 'inherit'"
                >
                    {{ item.text }}
                </span>
            </div>

            <!-- 筛选区域 -->
            <div class="filter-section" style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px">
                <div style="margin-bottom: 10px">
                    <strong>地区筛选：</strong>
                    <span
                        v-for="area in areaOptions"
                        :key="area.value"
                        class="filter-tag"
                        :class="{ active: areaFilter.includes(area.value) }"
                        @click="handleAreaFilter(area.value)"
                        style="margin-right: 10px; padding: 5px 10px; border: 1px solid #ddd; cursor: pointer; border-radius: 3px; display: inline-block; margin-bottom: 5px"
                    >
                        {{ area.label }}
                    </span>
                </div>

                <div style="margin-bottom: 10px">
                    <strong>单位性质：</strong>
                    <span
                        v-for="nature in natureOptions"
                        :key="nature.value"
                        class="filter-tag"
                        :class="{ active: natureFilter.includes(nature.value) }"
                        @click="handleNatureFilter(nature.value)"
                        style="margin-right: 10px; padding: 5px 10px; border: 1px solid #ddd; cursor: pointer; border-radius: 3px; display: inline-block; margin-bottom: 5px"
                    >
                        {{ nature.label }}
                    </span>
                </div>

                <div>
                    <strong>已选筛选条件：</strong>
                    <span v-if="areaFilter.length === 0 && natureFilter.length === 0" style="color: #999">无</span>
                    <el-tag v-for="area in areaFilter" :key="'area-' + area" closable @close="handleAreaFilter(area)" style="margin-right: 5px"> 地区: {{ getAreaLabel(area) }} </el-tag>
                    <el-tag v-for="nature in natureFilter" :key="'nature-' + nature" closable @close="handleNatureFilter(nature)" style="margin-right: 5px">
                        性质: {{ getNatureLabel(nature) }}
                    </el-tag>
                </div>
            </div>

            <div v-loading="loading" style="min-height: 200px; margin-top: 20px">
                <div v-if="!loading && companyList.length === 0" class="empty">暂无公司数据</div>

                <div v-for="company in companyList" :key="company.companyId" class="company-item">
                    <div class="company-header">
                        <img :src="company.logo" :alt="company.name" class="company-logo" />
                        <div>
                            <h3>{{ company.name }}</h3>
                            <span>{{ company.nature }}</span>
                        </div>
                        <button class="collect-btn" :class="{ collected: company.isCollected }" @click="handleCollect(company.companyId)">{{ company.isCollected ? '已收藏' : '收藏' }}</button>
                    </div>
                    <div class="company-info">
                        <span><strong>{{ company.announcementCount }}</strong> 招聘公告</span>
                        <span><strong>{{ company.jobCount }}</strong> 招聘职位</span>
                        <span><strong>{{ company.activeTime }}</strong> 活跃时间</span>
                    </div>
                </div>
            </div>

            <el-pagination
                v-if="!loading && companyList.length > 0"
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="currentPage"
                :page-sizes="[5, 10, 20]"
                :page-size="pageSize"
                :total="total"
                @size-change="handlePageSizeChange"
                @current-change="handleCurrentPageChange"
                style="margin-top: 20px; text-align: center"
            >
            </el-pagination>
        </div>

        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script>
            const { createApp } = Vue

            createApp({
                data() {
                    return {
                        loading: false,
                        keyword: '',
                        currentPage: 1,
                        pageSize: 5,
                        total: 0,
                        companyList: [],

                        // 筛选条件
                        areaFilter: [],
                        natureFilter: [],

                        // 筛选选项
                        areaOptions: [
                            { label: '北京', value: 'beijing' },
                            { label: '上海', value: 'shanghai' },
                            { label: '广州', value: 'guangzhou' },
                            { label: '深圳', value: 'shenzhen' },
                            { label: '杭州', value: 'hangzhou' },
                            { label: '南京', value: 'nanjing' },
                            { label: '武汉', value: 'wuhan' },
                            { label: '成都', value: 'chengdu' },
                            { label: '西安', value: 'xian' },
                            { label: '天津', value: 'tianjin' },
                            { label: '重庆', value: 'chongqing' },
                            { label: '苏州', value: 'suzhou' },
                            { label: '青岛', value: 'qingdao' },
                            { label: '大连', value: 'dalian' },
                            { label: '宁波', value: 'ningbo' },
                            { label: '厦门', value: 'xiamen' },
                            { label: '福州', value: 'fuzhou' },
                            { label: '济南', value: 'jinan' },
                            { label: '郑州', value: 'zhengzhou' },
                            { label: '长沙', value: 'changsha' }
                        ],

                        // 热门搜索数据
                        hotSearchList: [
                            { id: 1, text: '教授' },
                            { id: 2, text: '副教授' },
                            { id: 3, text: '博士后' },
                            { id: 4, text: '研究员' },
                            { id: 5, text: '讲师' },
                            { id: 6, text: '助理教授' },
                            { id: 7, text: '科研人员' },
                            { id: 8, text: '实验员' }
                        ],
                        natureOptions: [
                            { label: '双一流院校', value: 'top_university' },
                            { label: '科研院所', value: 'research_institute' },
                            { label: '知名企业', value: 'famous_company' },
                            { label: '事业单位', value: 'institution' }
                        ]
                    }
                },

                mounted() {
                    this.loadMockData()
                },

                methods: {
                    loadMockData() {
                        this.loading = true

                        // 模拟API调用延迟
                        setTimeout(() => {
                            const mockCompanies = [
                                {
                                    companyId: 1,
                                    name: '北京协和医学院医学中心',
                                    nature: '科研院所',
                                    area: 'beijing',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw310.png',
                                    announcementCount: '999+',
                                    jobCount: '999+',
                                    activeTime: '近30天',
                                    isCollected: false
                                },
                                {
                                    companyId: 2,
                                    name: '清华大学',
                                    nature: '双一流院校',
                                    area: 'beijing',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw311.png',
                                    announcementCount: '500+',
                                    jobCount: '800+',
                                    activeTime: '近7天',
                                    isCollected: true
                                },
                                {
                                    companyId: 3,
                                    name: '北京大学',
                                    nature: '双一流院校',
                                    area: 'beijing',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw312.png',
                                    announcementCount: '600+',
                                    jobCount: '900+',
                                    activeTime: '近3天',
                                    isCollected: false
                                },
                                {
                                    companyId: 4,
                                    name: '中科院计算技术研究所',
                                    nature: '科研院所',
                                    area: 'beijing',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw313.png',
                                    announcementCount: '200+',
                                    jobCount: '300+',
                                    activeTime: '近15天',
                                    isCollected: false
                                },
                                {
                                    companyId: 5,
                                    name: '华为技术有限公司',
                                    nature: '知名企业',
                                    area: 'shenzhen',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw314.png',
                                    announcementCount: '1000+',
                                    jobCount: '2000+',
                                    activeTime: '近1天',
                                    isCollected: true
                                },
                                {
                                    companyId: 6,
                                    name: '上海交通大学',
                                    nature: '双一流院校',
                                    area: 'shanghai',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw315.png',
                                    announcementCount: '400+',
                                    jobCount: '600+',
                                    activeTime: '近5天',
                                    isCollected: false
                                },
                                {
                                    companyId: 7,
                                    name: '阿里巴巴集团',
                                    nature: '知名企业',
                                    area: 'hangzhou',
                                    logo: 'http://img.gaoxiaojob.com/zhaopinhui2002/chdw316.png',
                                    announcementCount: '800+',
                                    jobCount: '1200+',
                                    activeTime: '近2天',
                                    isCollected: false
                                }
                            ]

                            // 根据筛选条件过滤
                            let filteredCompanies = mockCompanies

                            // 关键词过滤
                            if (this.keyword) {
                                filteredCompanies = filteredCompanies.filter((company) => company.name.includes(this.keyword))
                            }

                            // 地区过滤
                            if (this.areaFilter.length > 0) {
                                filteredCompanies = filteredCompanies.filter((company) => this.areaFilter.includes(company.area))
                            }

                            // 性质过滤
                            if (this.natureFilter.length > 0) {
                                const natureMap = {
                                    top_university: '双一流院校',
                                    research_institute: '科研院所',
                                    famous_company: '知名企业',
                                    institution: '事业单位'
                                }
                                const selectedNatures = this.natureFilter.map((value) => natureMap[value]).filter(Boolean)
                                filteredCompanies = filteredCompanies.filter((company) => selectedNatures.includes(company.nature))
                            }

                            // 分页处理
                            const start = (this.currentPage - 1) * this.pageSize
                            const end = start + this.pageSize

                            this.companyList = filteredCompanies.slice(start, end)
                            this.total = filteredCompanies.length
                            this.loading = false
                        }, 1000)
                    },

                    handleSearch() {
                        this.currentPage = 1
                        this.loadMockData()
                    },

                    handleClear() {
                        this.keyword = ''
                        this.areaFilter = []
                        this.natureFilter = []
                        this.currentPage = 1
                        this.loadMockData()
                    },

                    // 热门搜索点击处理
                    handleHotSearchClick(text) {
                        this.keyword = text
                        this.currentPage = 1
                        this.loadMockData()
                        ElementPlus.ElMessage.success(`搜索关键词：${text}`)
                    },

                    // 地区筛选处理
                    handleAreaFilter(value) {
                        const index = this.areaFilter.indexOf(value)
                        if (index > -1) {
                            this.areaFilter.splice(index, 1)
                        } else {
                            this.areaFilter.push(value)
                        }
                        this.currentPage = 1
                        this.loadMockData()
                    },

                    // 性质筛选处理
                    handleNatureFilter(value) {
                        const index = this.natureFilter.indexOf(value)
                        if (index > -1) {
                            this.natureFilter.splice(index, 1)
                        } else {
                            this.natureFilter.push(value)
                        }
                        this.currentPage = 1
                        this.loadMockData()
                    },

                    // 获取地区标签
                    getAreaLabel(value) {
                        const area = this.areaOptions.find((item) => item.value === value)
                        return area ? area.label : value
                    },

                    // 获取性质标签
                    getNatureLabel(value) {
                        const nature = this.natureOptions.find((item) => item.value === value)
                        return nature ? nature.label : value
                    },

                    handlePageSizeChange(val) {
                        this.pageSize = val
                        this.currentPage = 1
                        this.loadMockData()
                    },

                    handleCurrentPageChange(val) {
                        this.currentPage = val
                        this.loadMockData()
                    },

                    handleCollect(companyId) {
                        const company = this.companyList.find((item) => item.companyId === companyId)
                        if (company) {
                            company.isCollected = !company.isCollected
                            ElementPlus.ElMessage.success(company.isCollected ? '收藏成功' : '取消收藏成功')
                        }
                    }
                }
            })
                .use(ElementPlus)
                .mount('#app')
        </script>
    </body>
</html>
