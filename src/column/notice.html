<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>公告详情</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/notice.css" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/public.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component">
            <div class="el-main">
                <div class="detail-container">
                    <!-- 自定义背景图需添加 is-custom-background 类名 -->
                    <div class="detail-header">
                        <div class="detail-header-container">
                            <div class="breadcrumb">
                                位置：
                                <a>高校人才网</a>＞ <a>高校招聘</a>＞ <a>高校教学科研人才招聘</a>＞
                                <a>北京大学珠海分校2021年度招聘公告</a>
                            </div>

                            <div class="main">
                                <section>
                                    <h1 class="title">北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学北京大学珠海分校2021年度招聘公告北京大学北学学</h1>

                                    <div class="info">
                                        <span class="establishment-tag superior">部分编制</span>
                                        <div>共计<span class="color-primary">4</span>个岗位，招 <span class="color-primary">29</span>人</div>

                                        <ul>
                                            <li>发布时间：2021-06-25</li>
                                            <li>截止时间：详见正文</li>
                                            <li>工作地点：四川省成都市</li>
                                        </ul>

                                        <a class="view-relation" href="">查看此公告的职位列表</a>
                                    </div>

                                    <div class="tips" id="tipsTemplate">
                                        <span class="establishment-tag superior">部分编制</span>
                                        <span class="boon">全勤奖</span>
                                        <span class="boon">带薪年假</span>
                                        <span class="boon">五险一金</span>
                                        <span class="boon">节日福利</span>
                                        <span class="boon">绩效奖金</span>

                                        <el-popover placement="bottom" :width="330" trigger="hover" v-cloak>
                                            <template #reference>
                                                <i class="el-icon boon-more">
                                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                                                        <path
                                                            fill="currentColor"
                                                            d="M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224zm336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224z"
                                                        ></path>
                                                    </svg>
                                                </i>
                                            </template>
                                            <!-- <span class="boon">全勤奖</span> -->
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">五险一金</span>
                                            <span class="boon">带薪年假</span>
                                            <span class="boon">绩效奖金</span>
                                        </el-popover>
                                    </div>
                                </section>

                                <aside>
                                    <div class="detail-button">
                                        <div class="share-mini-code-container">
                                            <div class="share-mini-code-trigger">分享</div>

                                            <div class="share-mini-code-popup">
                                                <div class="share-mini-code">
                                                    <img src="//img.gaoxiaojob.com/uploads/static/image/newMedia/qrcode/12.png" alt="" class="share-mini-code-img" />
                                                </div>
                                                <div class="share-mini-code-title">微信扫一扫</div>
                                                <div class="share-mini-code-tips">分享给你的朋友吧</div>
                                            </div>
                                        </div>

                                        <!-- 已收藏添加 collected -->
                                        <div class="el-button--collect">收藏</div>
                                        <div class="el-button--analyse see-heat">公告热度</div>
                                    </div>
                                    <div class="aside-bottom">
                                        <button class="offline">已下线</button>
                                        <a href="www.baidu.com" target="_blank" class="company-home el-button el-button--primary">
                                            <span>单位主页</span>
                                        </a>
                                    </div>
                                </aside>

                                <a class="el-button el-button--primary view-button" href="">
                                    <span>查看此公告的职位列表</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="detail-main">
                        <h2 class="detail-title"><span>学校概况</span></h2>
                        <p>
                            成都纺织高等专科学校是四川省人民政府举办、直属于四川省教育厅的公办全日制普通高等学校，前身为1939年创办的国立中央技艺专科学校。现为国家示范（骨干）高职院校、国家优质高等职业院校、国家“双高计划”建设单位。
                        </p>
                        <p>
                            学校地处四川省成都市，占地1153亩，拥有犀浦校区（613亩）和邛崃产教园区（540亩）。犀浦校区座落在成都市郫都区犀浦，成都四环路（绕城高速）北段内侧，成灌高铁、成都地铁2号线、6号线可直达；邛崃产教园区位于邛崃市高铁新城规划区，紧邻天新邛快速路和成蒲高铁邛崃站。
                        </p>
                        <p>
                            <img src="https://img.gaoxiaojob.com/uploads/announcement/detail-header-1.png" />
                        </p>
                        <ul class="detail-images">
                            <li>
                                <img src="https://img.gaoxiaojob.com/uploads/announcement/detail-header-1.png" />
                                <!-- <img src="assets/icon/female.png" /> -->
                            </li>
                            <li>
                                <!-- <img src="assets/icon/female.png" /> -->
                                <img src="https://img.gaoxiaojob.com/uploads/announcement/detail-header-1.png" />
                            </li>
                        </ul>
                        <p>
                            学校现有教职工871人，其中教授、研究员等正高职称45名，副教授、高级工程师、高级实验师等副高职称154名，国务院政府特殊津贴获得者3名，全国优秀教师3名，四川省劳动模范1名，四川省有突出贡献的优秀专家6名，四川省学术和技术带头人1名，2018“天府万人计划”天府名师1名，黄炎培职业教育“杰出教师奖”2人，省教学名师4名，省师德标兵2名，省教书育人名师3人。
                        </p>
                        <p>
                            学校现有材料与环保学院、纺织工程学院、建筑工程学院、电气信息工程学院、机械工程学院、经济管理学院、外国语学院、艺术学院、服装学院9个二级学院和基础教学部、体育工作部、思想政治理论课教学部3个教学部，学校专业以工为主，以服务纺织服装产业为特色，面向现代制造业和现代服务业发展，主要涉及轻工纺织、装备制造、电子信息、土木建筑、材料与环保、财经商贸、文化艺术、艺术设计传媒、旅游等专业大类。
                        </p>

                        <h2 class="detail-title"><span>招聘学科</span></h2>
                        <p>
                            材料科学与工程、高分子材料工程、纺织、服装、染整、机械工程、会计、财务管理、应用经济学、管理科学与工程、计算机科学与技术、软件工程、大数据科学与技术、电子电气、建筑科学与工程、食品科学与工程、环境科学与工程、艺术、旅游、思想政治教育、英语、日语、数学、物理、化学、教育学、心理学、汉语言文学等相关研究的学科或专业。
                        </p>

                        <h2 class="detail-title"><span>招聘岗位及要求</span></h2>
                        <p>学校诚邀各类高层次人才加入，主要面向上述11个教学单位相关专业领域，具体岗位及要求详见下方表格。</p>

                        <table>
                            <tr>
                                <th>序号</th>
                                <th>招聘部门</th>
                                <th>岗位名称</th>
                                <th>专业要求</th>
                                <th>学历或职位要求</th>
                                <th>联系人</th>
                            </tr>
                            <tr>
                                <td>1</td>
                                <td>机械技术学院</td>
                                <td>专任教师</td>
                                <td>机械工程、机电控制类</td>
                                <td>博士研究生</td>
                                <td>
                                    王老师<br /><br />
                                    0510-81838711<br /><br />
                                    <EMAIL>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>控制技术学院</td>
                                <td>专任教师</td>
                                <td>机电控制、控制工程、软件工程类、电子信息（大类）类</td>
                                <td>博士研究生、硕士研究生且具有副<br /><br />高职称</td>
                                <td>
                                    刘老师<br /><br />
                                    0510-81838726<br /><br />
                                    <EMAIL>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>物联网技术学院</td>
                                <td>专任教师</td>
                                <td>计算机（打类）类、软件工程类、计算机（网络管理）、物联网工程</td>
                                <td>博士研究生、硕士研究生且具有副<br /><br />高职称</td>
                                <td>
                                    蔡老师<br /><br />
                                    0510-81838751<br /><br />
                                    <EMAIL>
                                </td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>汽车与交通学院</td>
                                <td>专任教师</td>
                                <td>车辆工程、交通运输工程、载运工具运通工程、机械工程类、动力工程及工程热<br /><br />物理、物体立学、航空工程、航空宇航科学与技术</td>
                                <td>博士研究生、硕士研究生且具有副<br /><br />高职称</td>
                                <td>
                                    邹老师<br /><br />
                                    <EMAIL>
                                </td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>管理学院</td>
                                <td>专任老师</td>
                                <td>工商管理（市场营销、电子商务及相关专业方向）或管理科学与工程（物流管理<br /><br />及相关专业方向）</td>
                                <td>博士研究生</td>
                                <td>
                                    俞老师<br /><br />
                                    0510-81838501<br /><br />
                                    <EMAIL>
                                </td>
                            </tr>
                        </table>

                        <h2 class="detail-title"><span>引进待遇</span></h2>
                        <p>1. 博士</p>
                        <p>(1) 安家费：30-40万元</p>
                        <p>(2) 科研启动费：10-20万元</p>
                        <p>(3) 引进博士无副高级专业技术职务的，5年内享受学校副高级专业技术职务待遇。</p>
                        <p>(4) 紧缺专业的博士、特别优秀的博士，待遇可另行协商。</p>

                        <p>2. 技术技能大师</p>
                        <p>(1) 第一层次安家费：60-90万元，设立技能大师工作室。</p>
                        <p>(2) 第二层次安家费：30-50万元，设立技能大师工作室。</p>

                        <h2 class="detail-title"><span>联系方式</span></h2>
                        <p>
                            1.应聘者将报名表（见附件）、个人简历及相关证明材料（最高学历、学位、职称等证书、代表性论文著作、科研成果、获奖及被SCI、EI、SSCI收录和引用证明文件）扫描件发送至邮箱：
                            <span class="detail-hidden-email-address">（点击查看）</span>
                            。 邮件标题名称为：应聘XX类别+最高学历学位+本人姓名+高校人才网
                        </p>
                        <p>2.联系地址：四川省成都市郫都区犀浦泰山南街186号，成都纺织高等专科学校人事处。</p>
                        <p>3.联系人：李老师、唐老师</p>
                        <p>联系电话：13666209291，028-87843597</p>
                        <p>附件：<span>成都纺织高等专科学校高层次人才招聘报名表</span></p>

                        <h2 class="detail-title"><span>附件下载</span></h2>

                        <div class="file-list">
                            <el-popover placement="bottom" :width="300" trigger="hover" content="word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx">
                                <template #reference>
                                    <a href="" download="" class="file" target="_blank">
                                        <div class="word"></div>
                                        <div class="file-name">word文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docxword文件名称.docx</div>
                                    </a>
                                </template>
                            </el-popover>
                            <el-popover placement="bottom" :width="300" trigger="hover" content="excel文件名称.xlsx">
                                <template #reference>
                                    <a href="" download="" class="file" target="_blank">
                                        <div class="excel"></div>
                                        <div class="file-name">excel文件名称.xlsx</div>
                                    </a>
                                </template>
                            </el-popover>
                            <a href="" download="" class="file" target="_blank">
                                <div class="pdf"></div>
                                <div class="file-name">pdf文件名称.pdf</div>
                            </a>

                            <a href="" download="" class="file" target="_blank">
                                <div class="common"></div>
                                <div class="file-name">txt文件名称.txt</div>
                            </a>
                        </div>

                        <!-- 未登录 start -->
                        <div>
                            <div class="notice-heat see-heat">
                                <div class="top">
                                    <div class="left">公告热度</div>
                                    <div class="right">解锁详细分析</div>
                                </div>
                                <div class="text-heat">该公告在同类公告中的热度为 <span class="active">火爆</span> ，目前已有 <span class="active">1000人</span> 对其非常感兴趣</div>
                            </div>
                        </div>

                        <!-- 未登录 start -->

                        <!-- 已登录 start -->
                        <div class="notice-heat">
                            <div class="top">
                                <div class="left">公告热度</div>
                                <div class="right">查看详细分析</div>
                            </div>
                            <div class="text-heat">该公告在同类公告中的热度为 <span>火爆</span> ，目前已有 <span>1000人</span> 对其非常感兴趣</div>
                        </div>
                        <!-- 已登录 start -->

                        <div class="detail-emit">
                            <a class="el-button el-button--primary" href="">
                                <span>查看此公告的职位列表</span>
                            </a>
                            <button class="announcement el-button el-button--primary el-button--apply">
                                <span>立即投递</span>
                            </button>
                        </div>

                        <div class="share-custom">
                            <div class="sina-weibo">
                                <wb:follow-button uid="3702192203" type="red_3" width="100%" height="24"> </wb:follow-button>
                            </div>
                            <div>
                                <div class="bshare-custom">
                                    <a title="分享到微信" class="bshare-weixin"></a>
                                    <a title="分享到新浪微博" class="bshare-sinaminiblog"></a>
                                    <a title="分享到QQ空间" class="bshare-qzone"></a>
                                    <a title="分享到Facebook" class="bshare-facebook"></a>
                                    <a title="分享到Twitter" class="bshare-twitter"></a>
                                    <a title="更多平台" onclick="javascript:bShare.more(event);return false;" class="custom-more-btn"> </a>
                                    <span class="BSHARE_COUNT bshare-share-count">0</span>
                                </div>
                                <script src="http://static.bshare.cn/b/buttonLite.js#style=-1&amp;uuid=40cb6f46-7685-42c6-8cf8-7e18be117d11&amp;pophcol=1&amp;lang=zh"></script>
                                <script src="http://static.bshare.cn/b/bshareC0.js"></script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>

        <footer class="page-footer-container">
            <div class="site-foot-menu">
                <a href="/" target="_blank">关于我们</a>| <a href="/" target="_blank">产品服务</a>| <a href="/" target="_blank">媒体矩阵</a>| <a href="/" target="_blank">加入我们</a>|
                <a href="/" target="_blank">联系我们</a>| <a href="/" target="_blank">免责声明</a>|
                <a href="/" target="_blank">资质证明</a>
            </div>

            <div class="site-foot-copyright">
                <p>
                    Copyright © 2007-2021 高校人才网 版权所有 网站备案信息：
                    <a href="/" target="_blank">粤ICP备13048400号-2</a>
                    粤公网安备：
                    <a href="/" target="_blank">44010602004138号</a>
                </p>
                <p>本站由广州高才信息科技有限公司运营</p>
                <p>
                    中华人民共和国增值电信业务经营许可证：
                    <a href="/" target="_blank">粤B2-20180648</a>
                </p>
                <p>人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
                <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
                <p>本平台由广东同福律师事务所提供法律支持服务</p>
            </div>
        </footer>

        <script src="http://tjs.sjs.sinajs.cn/open/api/js/wb.js" type="text/javascript" charset="utf-8"></script>
        <script src="./js/detailService.js"></script>
        <script>
            $(function () {
                Vue.createApp({}).use(ElementPlus).mount('#tipsTemplate')
                Vue.createApp({}).use(ElementPlus).mount('.file-list')

                var id = '<?=$info["id"]?>'
                var $collectButton = $('.el-button--collect')
                var $jobApplyButton = $('.el-button--apply')

                $collectButton.on('click', function () {
                    var $this = $(this)
                    var isCollected = $this.hasClass('collected')

                    httpPost('/api/person/announcement/collect', { id }).then(function () {
                        $this
                            .toggleClass('collected')
                            .find('span')
                            .text(isCollected ? '收藏' : '已收藏')
                    })
                })

                $jobApplyButton.on('click', function () {
                    window.globalComponents.applyDialogComponent.announcementApply(id)
                })

                var seeHeat = $('.see-heat')
                var params = {
                    apiPull: '/api/person/announcement/check-generate-report',
                    apiCreate: '/api/person/announcement/create-report',
                    param: { announcementId: '<?=$info["id"]?>' }
                }
                seeHeat.on('click', function () {
                    window.globalComponents.PromptDialogComponent.pull(params)
                })
            })
        </script>
    </body>
</html>
