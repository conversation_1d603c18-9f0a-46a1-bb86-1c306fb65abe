<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>职位中心</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/job.css" />
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message"><i class="el-icon-bell"></i></a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,

                                    type: '1',

                                    typeOptions: [
                                        { label: '职位', value: '1', path: '/job' },
                                        { label: '公告', value: '2', path: '/search' },
                                        { label: '单位', value: '3', path: '/company' },
                                        { label: '资讯', value: '4', path: '/search', query: 'type=2' }
                                    ],

                                    keyword: '',
                                    isVip: false,
                                    vipInfo: {}
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <div class="el-main">
                <div class="job-container">
                    <div class="search-container">
                        <div class="search-content">
                            <div class="search-main">
                                <div class="el-input el-input-group el-input--prefix el-input-group--append">
                                    <input
                                        v-model.trim="keyword"
                                        class="search-input el-input__inner"
                                        :class="recordVisible ? 'active' : ''"
                                        type="text"
                                        autocomplete="off"
                                        placeholder="请输入搜索关键词"
                                        @focus="recordVisible = true"
                                        @blur="handleRecord"
                                        @keydown.enter="handleSearch"
                                    />

                                    <span class="search-options el-input__prefix">
                                        <span class="el-input__suffix-inner">
                                            <!-- <el-select class="search-type" v-model="searchType">
                                            <el-option v-for="{ label, value } in typeOptions" :key="value"
                                                :label="label" :value="value">
                                            </el-option>
                                        </el-select>

                                        <span class="search-line"></span> -->

                                            <!-- <el-cascader class="search-job-type" v-model="jobType" :options="jobOptions"
                                            placeholder="职位类型" :clearable="true" :show-all-levels="false"
                                            :props="{ checkStrictly: false, emitPath: false, expandTrigger: 'hover' }" /> -->

                                            <div @mouseenter="handleJobTypeMouseenter" @mouseleave="handleJobTypeMouseleave" @click="handleOpenCascader('jobType')" class="search-job-type-content">
                                                <span class="job-type-label">{{jobTypeLabel}}</span>

                                                <span class="el-input__suffix">
                                                    <span class="el-input__suffix-inner">
                                                        <i v-show="isJobTypeClearable" @click="clearJobType" class="el-input__icon el-icon-circle-close"></i>
                                                        <i v-show="!isJobTypeClearable" class="el-input__icon el-icon-arrow-down"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </span>
                                    </span>

                                    <div class="el-input-group__append">
                                        <button class="search-button el-button el-button--primary" @click="handleSearch"></button>
                                    </div>

                                    <div class="search-record" :class="recordVisible ? 'active' : ''">
                                        <ul class="list">
                                            <li class="tips" v-show="searchRecord.length">历史搜索记录</li>

                                            <li class="item" v-for="(item, index) in searchRecord" :key="index" @click="keyword = item">
                                                <span>{{ item }}</span>
                                                <i class="el-icon el-icon-close" @click.stop="removeRecord(item)"></i>
                                            </li>

                                            <li class="item" @click="keyword = '高才信息科技有限公司'">
                                                <span>高才信息科技有限公司</span>
                                                <i>热门推荐</i>
                                            </li>

                                            <li class="remove" v-show="searchRecord.length">
                                                <span @click="removeRecord([])">删除全部历史</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="search-hot">
                                    热门搜索：
                                    <a>大学教师</a>
                                    <a>高校辅导员</a>
                                    <a>博士后</a>
                                    <a>科研人员</a>
                                    <a>领军人才</a>
                                    <a>高校教</a>
                                </div>
                            </div>

                            <div class="swiper mySwiper recommend-company" v-cloak>
                                <div class="swiper-wrapper company-pane">
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[0]">
                                        <el-carousel :height="paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[0]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[1]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[1]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[2]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[2]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[3]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[3]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[4]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[4]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                    <div class="swiper-slide pane" :data-swiper-autoplay="delayTime[5]">
                                        <el-carousel :height=" paneHeight" trigger="click" arrow="never" pause-on-hover="true" indicator-position="outside" :interval="runTime" :autoplay="running[5]">
                                            <el-carousel-item>
                                                <a href="#"><img src="assets/home/<USER>/banner1.jpg" alt="" /></a>
                                                <a href="#"><img src="assets/home/<USER>/banner2.png" alt="" /></a>
                                                <a href="#"><img src="https://file.suliaolian.com/GetFileHandler.aspx?FID=AE856EF9-1569-481C-B7D1-E5B0574356E5" alt="" /></a>
                                            </el-carousel-item>
                                        </el-carousel>
                                    </div>
                                </div>

                                <div class="recommend-tabs">
                                    <div class="label"><span>HOT</span>推荐单位：</div>
                                    <div class="swiper-pagination tabs" @click="handleRecommend"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="job-content">
                        <div class="search-filter">
                            <div class="filter-pane area">
                                <h6 class="filter-label">工作地点</h6>
                                <div class="filter-value">
                                    <a href="" class="el-tag active"> 全部<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 北京<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 上海<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 广州<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 西安<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 杭州<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 南京<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 武汉<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 成都<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 江苏<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 河北<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                </div>
                                <span class="filter-more" @click="handleShow">更多</span>
                            </div>

                            <div class="filter-pane nature">
                                <h6 class="filter-label">单位性质</h6>
                                <div class="filter-value">
                                    <a href="" class="el-tag active"> 全部<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 公立（国有）<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 民营（私营）<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 公私混合<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 中外合资（合营）<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 外企（外商）独资<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 非盈利组织及其他<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                </div>
                            </div>

                            <div class="filter-pane type">
                                <h6 class="filter-label">单位类型</h6>
                                <div class="filter-value">
                                    <a href="" class="el-tag active"> 全部<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 双一流院校<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 普通本科院校<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 高职高专院校<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 党校与行政学院<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 行政机关<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 军队武警<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 事业单位<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 政府国有企业<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 知名企业<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 中小成长型企业<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 银行、信用社等金融机构<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 教育系统<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 中小学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 中专&职业中学&技师学院<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 幼儿园<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 中科院系统<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 人文社科研究机构<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 自然与应用科研机构<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 企业研发机构<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 卫健委<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 医院<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 其他医疗机构<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                </div>
                            </div>

                            <div class="filter-pane major">
                                <h6 class="filter-label">学科分类</h6>
                                <div class="filter-value">
                                    <a href="" class="el-tag active"> 全部<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag is-level is-show" @click.prevent="handleFilter(12, 'majorId')">上级</a>
                                    <a href="" class="el-tag"> 哲学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 经济学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 法学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 教育学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 文学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 历史学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 理学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 工学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 农学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 医学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 军事学<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                </div>
                                <span class="filter-multiple" :class="{active: majorId.length>0}" @click="handleOpenCascader('major')">{{majorId.length ? '选项·'+majorId.length:'多选'}} </span>
                            </div>

                            <div class="filter-pane weflare special">
                                <h6 class="filter-label">职位福利</h6>
                                <div class="filter-value">
                                    <a href="" class="el-tag active"> 全部<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 安家费<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 科研启动费<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 科研奖励<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 提供住房公寓<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 住房补贴<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 五险一金<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 带薪年假<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 节日福利<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 周末双休<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 寒暑假<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                    <a href="" class="el-tag"> 绩效奖金<i class="el-icon el-icon-close el-tag__close" @click.prevent></i> </a>
                                </div>
                                <span class="filter-more" @click="handleShow">更多</span>
                            </div>

                            <div class="filter-pane filter-pane-more">
                                <h6 class="filter-label">更多筛选</h6>
                                <div class="filter-value">
                                    <el-select v-model="educationType" class="education" multiple collapse-tags placeholder="学历要求" @change="(val) => handleFilter(val, 'educationType')">
                                        <el-option v-for="{label, value} in educationOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>

                                    <el-cascader
                                        v-model="industryId"
                                        :options="industryOptions"
                                        placeholder="行业类别"
                                        :clearable="true"
                                        :show-all-levels="false"
                                        :props="{ checkStrictly: true, emitPath: false, expandTrigger: 'hover' }"
                                        @change="(val) => handleFilter(val, 'industryId')"
                                    >
                                    </el-cascader>

                                    <el-select v-model="experienceType" placeholder="工作年限" clearable="true" @change="(val) => handleFilter(val, 'experienceType')">
                                        <el-option v-for="{label, value} in experienceOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>

                                    <el-select v-model="releaseTimeType" placeholder="发布时间" clearable="true" @change="(val) => handleFilter(val, 'releaseTimeType')">
                                        <el-option v-for="{label, value} in releaseOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>

                                    <el-select v-model="wageId" placeholder="薪资范围" clearable="true" @change="(val) => handleFilter(val, 'wageId')">
                                        <el-option v-for="{label, value} in wageOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>

                                    <el-select v-model="companyScaleType" placeholder="单位规模" clearable="true" @change="(val) => handleFilter(val, 'companyScaleType')">
                                        <el-option v-for="{label, value} in scaleOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>

                                    <el-select v-model="natureType" placeholder="职位性质" clearable="true" @change="(val) => handleFilter(val, 'natureType')">
                                        <el-option v-for="{label, value} in natureOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>

                                    <el-select v-model="titleType" placeholder="职称类型" clearable="true" @change="(val) => handleFilter(val, 'titleType')">
                                        <el-option v-for="{label, value} in titleOptions" :key="value" :label="label" :value="value"> </el-option>
                                    </el-select>
                                </div>
                                <span class="filter-clear" @click="handleClear">清空筛选条件</span>
                            </div>
                        </div>

                        <div class="search-result">
                            <div class="result-header">
                                <h4 class="result-title">职位列表</h4>

                                <div class="result-sort">
                                    <span :class="sort === 'default' ? 'active' : ''" @click="handleSort('default')">综合排序</span>
                                    <span :class="sort === 'recommend' ? 'active' : ''" @click="handleSort('recommend')">推荐职位</span>
                                </div>
                            </div>

                            <div class="result-list">
                                <div class="result-item">
                                    <div class="job-data">
                                        <div class="title el-row">
                                            <h3>
                                                <a href="" title="湖北商贸学院招聘教师湖北商贸学院招聘教师">湖北商贸学院招聘教师湖北商贸学院招聘教师</a>
                                            </h3>
                                            <span>07-06 发布</span>
                                        </div>

                                        <div class="basic el-row">
                                            <span class="salary">1000W-10000W/<i>月</i></span>
                                            <span class="area">内蒙古-内蒙古内蒙古内蒙古</span>
                                            <span>10年以上</span>
                                            <span>博士研究生</span>
                                            <span>300人</span>
                                        </div>

                                        <div class="tips el-row">
                                            <a href="">北京大学珠海分校2021年公开招聘公告北京大学珠海分校2021年公开招聘公告</a>
                                        </div>
                                    </div>

                                    <div class="unit-data">
                                        <div class="title el-row">
                                            <h3>
                                                <a href="" title="中国科学院上海微系统与信息技术中国科学院上海微系统与信息技术">中国科学院上海微系统与信息技术中国科学院上海微系统与信息技术</a>
                                            </h3>
                                        </div>

                                        <div class="basic el-row">科研院所/大学教师</div>

                                        <div class="tips el-row">
                                            <span>全勤奖</span>
                                            <span>带薪年假</span>
                                            <span>五险一金</span>
                                            <span>节日福利</span>
                                            <span>绩效奖金</span>
                                        </div>
                                    </div>

                                    <div class="operate">
                                        <button class="el-button el-button--primary job-apply-button" data-id="12">
                                            <span>申请职位</span>
                                        </button>
                                        <!-- <button class="el-button el-button--primary is-disabled" disabled="disabled">
                                        <span>已申请</span>
                                    </button>
                                    <button class="el-button el-button--info is-plain is-disabled" disabled="disabled">
                                        <span>已下线</span>
                                    </button> -->
                                    </div>
                                </div>
                            </div>

                            <el-pagination
                                background
                                :layout="'sizes, prev, pager, next, jumper'"
                                :current-page="page"
                                :page-size="pageSize"
                                :total="count"
                                @size-change="(val) => handleFilter(val, 'pageSize')"
                                @current-change="(val) => handleFilter(val, 'page')"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            keyword: '',
                            searchType: 1,
                            typeOptions: [
                                { label: '职位', value: 1 },
                                { label: '公告', value: 2 }
                            ],
                            jobType: '5',
                            jobTypeLabel: '职位类型',
                            isJobTypeClearable: false,
                            jobOptions: [
                                {
                                    label: '技术',
                                    value: 1,
                                    children: [
                                        {
                                            label: '前端',
                                            value: 11
                                        },
                                        {
                                            label: '后端',
                                            value: 12
                                        }
                                    ]
                                }
                            ],
                            recordVisible: false,
                            searchRecord: [],
                            paneHeight: '70px',
                            runTime: 3000,
                            running: [true, false, false, false, false, false],
                            delayTime: [],
                            /* filter */
                            educationType: [],
                            educationOptions: [
                                { label: '不限', value: 0 },
                                { label: '大专', value: 1 },
                                { label: '本科', value: 2 },
                                { label: '硕士研究生', value: 3 },
                                { label: '博士研究生', value: 4 },
                                { label: '其他', value: 5 }
                            ],
                            industryId: '',
                            industryOptions: [
                                {
                                    label: '技术',
                                    value: 1,
                                    children: [
                                        {
                                            label: '前端',
                                            value: 11
                                        },
                                        {
                                            label: '后端',
                                            value: 12
                                        }
                                    ]
                                }
                            ],
                            experienceType: '',
                            experienceOptions: [
                                { label: '不限', value: 0 },
                                { label: '应届生', value: 1 },
                                { label: '1-3年', value: 2 },
                                { label: '3-5年', value: 3 },
                                { label: '5-10年', value: 4 },
                                { label: '10年以上', value: 5 }
                            ],
                            releaseTimeType: '',
                            releaseOptions: [
                                { label: '不限', value: 0 },
                                { label: '24小时内', value: 1 },
                                { label: '近三天', value: 2 },
                                { label: '近一周', value: 3 },
                                { label: '近一个月', value: 4 }
                            ],
                            wageId: '',
                            wageOptions: [
                                { label: '不限', value: 0 },
                                { label: '面议', value: 1 },
                                { label: '3k以下', value: 2 },
                                { label: '3-5k', value: 3 },
                                { label: '5-10k', value: 4 },
                                { label: '10-15k', value: 5 },
                                { label: '15-20k', value: 6 },
                                { label: '20-40k', value: 7 },
                                { label: '40-60k', value: 8 }
                            ],
                            companyScaleType: '',
                            scaleOptions: [
                                { label: '不限', value: 0 },
                                { label: '0-199人', value: 1 },
                                { label: '200-499人', value: 2 },
                                { label: '500-999人', value: 3 },
                                { label: '1000-1999人', value: 4 },
                                { label: '2000-4999人', value: 5 },
                                { label: '5000-9999人', value: 6 },
                                { label: '10000人以上', value: 7 }
                            ],
                            natureType: '',
                            natureOptions: [
                                { label: '不限', value: 0 },
                                { label: '全职', value: 1 },
                                { label: '兼职', value: 2 },
                                { label: '实习全职', value: 3 },
                                { label: '实习兼职', value: 4 },
                                { label: '返聘', value: 5 }
                            ],
                            titleType: '',
                            titleOptions: [
                                { label: '不限', value: 0 },
                                { label: '高层次人才', value: 1 },
                                { label: '科研人才', value: 2 }
                            ],
                            sort: 'default',
                            page: 1,
                            pageSize: 20,
                            count: 1000,

                            popover: '', // 1多选，2其他
                            majorId: []
                        }
                    },

                    mounted() {
                        const search = window.location.search
                        const [mark, query] = search.split('?')
                        const getParams = (key, feature = '') => {
                            if (new RegExp(`${key}=([^&]*)`).test(query)) {
                                const value = decodeURIComponent(RegExp.$1)
                                const toNumber = (val) => (/^-?\d+$/.test(val) ? val * 1 : val)
                                if (feature) {
                                    this[key] = value.split(feature).map((item) => toNumber(item))
                                } else {
                                    this[key] = toNumber(value)
                                }
                            }
                        }

                        if (query) {
                            getParams('keyword')
                            getParams('searchType')
                            getParams('jobType')
                            getParams('educationType', '_')
                            getParams('industryId')
                            getParams('experienceType')
                            getParams('releaseTimeType')
                            getParams('wageId')
                            getParams('companyScaleType')
                            getParams('natureType')
                            getParams('titleType')
                            getParams('sort')
                            getParams('page')
                            getParams('pageSize')
                            getParams('popover')
                            if (this.popover === 1) {
                                getParams('majorId', '_')
                            }
                        }

                        this.searchRecord = this.getRecord()

                        var $panes = $('.recommend-company .pane')

                        // 获取每个分类的页数，赋予对应轮播时间
                        for (var i = 0; i < $panes.length; i++) {
                            let time = 0
                            let length = $panes.eq(i).find('.el-carousel__item').length

                            // 当分类的页数超过2页才显示轮播点
                            if (length > 1) {
                                $panes.eq(i).find('.el-carousel__indicators').css('visibility', 'visible')
                            }

                            time = length * this.runTime
                            this.delayTime.push(time)
                        }
                    },

                    methods: {
                        getRecord() {
                            return JSON.parse(window.localStorage.getItem('searchRecord')) || []
                        },

                        setRecord(val) {
                            window.localStorage.setItem('searchRecord', JSON.stringify(val))
                        },

                        updRecord(val) {
                            const record = this.getRecord()
                            record.unshift(val)

                            const result = Array.from(new Set(record)).slice(0, 5)

                            this.searchRecord = result
                            this.setRecord(result)
                        },

                        updQuery(data) {
                            const base = window.location.href
                            const hasParams = base.indexOf('?') > -1
                            const baseUrl = base + (hasParams ? '' : '?')
                            const keys = Object.keys(data)

                            const result = keys.reduce((previous, current) => {
                                const value = data[current]
                                const isValid = value === null ? false : value !== ''
                                const isExist = new RegExp(`(${current}=[^&]*)`).test(previous)
                                const keyValue = isExist ? RegExp.$1 : ''

                                if (isValid) {
                                    if (isExist) {
                                        previous = previous.replace(keyValue, `${current}=${value}`)
                                    } else {
                                        previous += `&${current}=${encodeURIComponent(value)}`
                                    }
                                } else {
                                    previous = previous.replace(new RegExp(`&?${keyValue}`), '')
                                }

                                return previous.replace(/\?&/, '?')
                            }, baseUrl)

                            return result.replace(/\?$/, '')
                        },

                        handleSearch() {
                            const { keyword, searchType, jobType } = this

                            this.recordVisible = false
                            if ((keyword + '').trim().length) {
                                this.updRecord(keyword)
                            }
                            window.location.href = this.updQuery({ keyword, searchType, jobType, page: 1 })
                        },

                        handleRecord() {
                            setTimeout(() => {
                                this.recordVisible = false
                            }, 320)
                        },

                        removeRecord(val) {
                            if (Array.isArray(val)) {
                                this.searchRecord = []
                                this.setRecord([])
                            } else {
                                const index = this.searchRecord.findIndex((item) => item === val)

                                this.searchRecord.splice(index, 1)
                                this.setRecord(this.searchRecord)
                            }
                            this.recordVisible = false
                        },

                        // 重置当前tab图片自动轮播状态
                        handleRecommend(e) {
                            const { nodeName } = e.target
                            const index = $(e.target).index()
                            const $recommend = $('.recommend-company .pane').eq(index)

                            if (nodeName === 'SPAN') {
                                $recommend.find('.el-carousel__indicator')[0].click()
                                this.running.fill(false)[$(e.target).index()] = true
                            }
                        },

                        handleShow(e) {
                            const $this = $(e.target)
                            const $parent = $this.parent()

                            $this.toggleClass('is-reverse')
                            $parent.toggleClass('is-show')
                        },

                        handleFilter(val, key) {
                            const query = { [key]: Array.isArray(val) ? val.join('_') : val }
                            if (key !== 'page') {
                                query.page = 1
                            }
                            window.location.href = this.updQuery(query)
                        },

                        handleSort(val) {
                            this.sort = val
                            this.handleFilter(val, 'sort')
                        },

                        handleClear() {
                            const { keyword, searchType, jobType } = this
                            window.location.href = `/job?keyword=${encodeURIComponent(keyword)}&searchType=${searchType}&jobType=${jobType}`
                        },

                        handleOpenCascader(type) {
                            if (type === 'major') {
                                window.globalComponents.cascaderDialog.open('41', function (value) {})
                            }
                            if (type === 'jobType') {
                                window.globalComponents.cascaderDialog.open('41', function (value) {})
                            }
                        },

                        handleJobTypeMouseenter() {
                            this.isJobTypeClearable = !!this.jobType
                        },

                        handleJobTypeMouseleave() {
                            this.isJobTypeClearable = false
                        },

                        clearJobType(event) {
                            event.stopPropagation()
                            this.jobType = ''
                            this.jobTypeLabel = '职位类型'
                        }
                    }
                }

                Vue.createApp(component)
                    .use(ElementPlus, {
                        locale: {
                            name: 'zh-cn',
                            el: {
                                pagination: {
                                    goto: '前往',
                                    pagesize: '条/页',
                                    total: '共 {total} 条',
                                    pageClassifier: '页',
                                    deprecationWarning: '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档'
                                }
                            }
                        }
                    })
                    .mount('#component')

                var $applyButtons = $('.job-apply-button')

                $applyButtons.on('click', function () {
                    var $this = $(this)
                    var jobId = $this.attr('data-id')

                    window.globalComponents.applyDialogComponent.beforeApply(jobId, function () {
                        $this.prop('disabled', true).addClass('is-disabled').find('span').text('已申请')
                    })
                })
            })
        </script>

        <script src="./lib/swiper/swiper.min.js"></script>

        <script>
            $(function () {
                const companyArray = ['高等学院', '科研院校', '医疗卫生', '中小学校', '机关事业', '知名企业']

                new Swiper('.mySwiper', {
                    loop: true,
                    simulateTouch: false,
                    autoplay: {
                        pauseOnMouseEnter: true,
                        disableOnInteraction: false
                    },
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                        renderBullet: function (index, className) {
                            return `<span class="${className}">${companyArray[index]}</span>`
                        }
                    },
                    on: {
                        slideChangeTransitionStart: function () {
                            this.activeIndex = this.activeIndex > companyArray.length ? 1 : this.activeIndex
                            $('.tabs span')
                                .eq(this.activeIndex - 1)
                                .click()
                        }
                    }
                })
            })
        </script>
    </body>
</html>
