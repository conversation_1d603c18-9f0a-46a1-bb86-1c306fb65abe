<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>VIP</title>
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/element-plus/index.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/common.css" />
        <link rel="stylesheet" href="./css/vipIntroduce.css?v=1.0.0" />
        <link rel="stylesheet" href="./css/feedback.css" />
        <link rel="stylesheet" href="./css/selectDialog.css" />
        <script src="./lib/dialog/selectDialog.js"></script>
        <script src="./lib/vue/vue.min.js"></script>
        <script src="./lib/axios/axios.min.js"></script>
        <script src="./lib/element-plus/index.min.js"></script>
        <script src="./lib/qs/qs.min.js"></script>
        <script src="./lib/jquery/jquery.min.js"></script>
        <script src="./js/config.js"></script>
        <script src="./js/request.js"></script>
        <script src="https://img.gaoxiaojob.com/uploads/static/lib/crypto/index.js"></script>
    </head>

    <body>
        <header class="el-header">
            <div class="header-container">
                <nav class="header-nav">
                    <a href="/" class="header-logo">
                        <img src="//img.gaoxiaojob.com/uploads/static/image/logo/logo_column.png" alt="" />
                    </a>

                    <a href="/" class="nav-link">首页</a>

                    <div class="header-notice-container">
                        <span class="nav-link">公告&amp;简章</span>

                        <div class="notice-open-part is-open">
                            <div class="notice-content">
                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>栏目导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">人才专场</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">高校招聘</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">科研人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">政府与事业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中小学校</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">医学人才</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">企业单位</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">博士后</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">海归人才</a>
                                        </li>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>省区导航</p>
                                    </div>
                                    <ul class="nav-container">
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">全国</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">内蒙古</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">黑龙江</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">广东</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">台湾</a>
                                        </li>
                                        <a class="more" href="/" target="_blank">更多</a>
                                    </ul>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>城市导航</p>
                                    </div>
                                    <div class="nav-container">
                                        <ul class="nav-container">
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <li>
                                                <a href="/" target="_blank">北京</a>
                                            </li>
                                            <a href="/" target="_blank" class="more">更多</a>
                                        </ul>
                                    </div>
                                </div>

                                <div class="nav-list">
                                    <div class="nav-title">
                                        <p>学科导航</p>
                                    </div>
                                    <ul class="is-nav-container nav-container">
                                        <li>
                                            <a href="/" target="_blank">计算机科学与技术</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">生物学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">管理科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">临床医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">电子信息</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">基础医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">经济学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">马克思主义理论</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">化学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">材料科学与工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">机械工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">信息与通信工程</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">公共卫生与预防医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中医学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">教育学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">数学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">中国语言文学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">药学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">物理学</a>
                                        </li>
                                        <li>
                                            <a href="/" target="_blank">外国语言文学</a>
                                        </li>
                                        <a href="/" target="_blank" class="more">更多</a>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <a href="/job" class="nav-link">找职位</a>
                    <a href="/company" class="nav-link">找单位</a>
                    <a href="/vip.html" class="nav-link">VIP<span class="gaocai-vip">升级</span></a>
                </nav>

                <div id="headerTemplate" class="header-main" v-cloak>
                    <div class="header-search">
                        <el-input class="search-input" v-model="keyword" @keydown.enter="handleSearch">
                            <template #prefix>
                                <el-select class="search-type" v-model="type">
                                    <el-option v-for="{ label, value } in typeOptions" :key="value" :label="label" :value="value"> </el-option>
                                </el-select>
                            </template>

                            <template #suffix>
                                <i class="el-icon-search pointer" @click="handleSearch"></i>
                            </template>
                        </el-input>
                    </div>

                    <!-- 已登录 start -->
                    <a href="/member/person/message" class="message">
                        <!-- 有消息 -->
                        <el-badge :value="100" class="item"><i class="el-icon-bell"></i></el-badge>
                        <!-- 无消息 -->
                        <!-- <i class="el-icon-bell"></i> -->
                    </a>

                    <el-dropdown popper-class="header-dropdown-popper">
                        <div class="header-dropdown">
                            <el-avatar :size="28" :src="avatar"></el-avatar>
                            <div class="vip-logo"></div>
                            <span>{{ username }}</span>
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </div>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item @click="() => openVip('/vip.html')" v-if="!isVip">
                                    <div class="dropdown-item-user"></div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=service')" v-else>
                                    <div class="dropdown-item-vip">
                                        <span>有效期至{{ vipInfo.vipExpireDate }}</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/home')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">个人中心<i class="icon"></i></span>
                                        <span class="tips">智能匹配职位、求职管理</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/resume')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            我的简历
                                            <span class="complete" :class="{ 'is-special': resumeComplete >= 75 }"> {{ resumeComplete }}% </span>
                                        </span>
                                        <span class="tips">完整度达75%可投全站职位</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/delivery')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">投递反馈</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/view')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">谁看过我</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/job?tab=tool')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">
                                            求职工具
                                            <span class="complete"> NEW </span>
                                        </span>
                                        <span class="tips">求职无压力，实用工具助你赢在起跑线</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="() => handleRoute('/setting')">
                                    <div class="dropdown-item-cell">
                                        <span class="name">账号设置</span>
                                        <span class="tips">管理账号、屏蔽单位和简历公开程度</span>
                                    </div>
                                </el-dropdown-item>

                                <el-dropdown-item @click="handleLogout">
                                    <div class="dropdown-item-cell is-logout">
                                        <span class="name">退出登录</span>
                                    </div>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <!-- 已登录 end -->
                    <!-- 未登录 start -->
                    <!-- <div class="login-register-container">
                        <a :href="`${basePath}/login`" target="_blank" class="login">求职者登录</a>
                        <span class="line">|</span>
                        <a :href="`${basePath}/registry`" target="_blank" class="register">注册</a>
                    </div> -->
                    <!-- 未登录 end -->
                </div>

                <script>
                    $(function () {
                        const headerOptions = {
                            data() {
                                return {
                                    basePath: '/member/person',
                                    avatar: 'https://img.gaoxiaojob.com/uploads/static/image/defaultMemberAvatarFemale.png?imageView2/1/w/200/h/200/q/75',
                                    username: '木子',
                                    resumeComplete: 70,
                                    type: '1',
                                    typeOptions: [
                                        {
                                            label: '职位',
                                            value: '1',
                                            path: '/job'
                                        },
                                        {
                                            label: '公告',
                                            value: '2',
                                            path: '/search'
                                        },
                                        {
                                            label: '单位',
                                            value: '3',
                                            path: '/company'
                                        },
                                        {
                                            label: '资讯',
                                            value: '4',
                                            path: '/search',
                                            query: 'type=2'
                                        }
                                    ],

                                    keyword: ''
                                }
                            },

                            methods: {
                                handleSearch() {
                                    const { type, typeOptions, keyword } = this
                                    const { path, query } = typeOptions.find((item) => item.value === type) || {
                                        path: 'search'
                                    }
                                    window.location.href = `${path}?keyword=${keyword}${query ? `&${query}` : ''}`
                                },

                                handleRoute(path) {
                                    window.location.href = '/member/person' + path
                                },

                                openVip(url) {
                                    window.open(url, '_blank')
                                },

                                handleLogout() {
                                    this.$confirm('确定退出登录?', '提示', {
                                        buttonSize: 'large',
                                        confirmButtonText: '确定',
                                        cancelButtonText: '取消'
                                    })
                                        .then(() => {
                                            httpGet('/api/member/logout').then(() => {
                                                window.localStorage.clear()
                                                window.sessionStorage.clear()
                                                removeToken()
                                                window.location.reload()
                                            })
                                        })
                                        .catch(() => {})
                                }
                            }
                        }
                        Vue.createApp(headerOptions).use(ElementPlus).mount('#headerTemplate')
                    })
                </script>
            </div>
        </header>

        <div id="component" v-cloak>
            <div class="banner"></div>

            <div class="tabs-wrapper" id="tabs-wrapper">
                <div class="main-wrapper">
                    <div class="tabs">
                        <a class="item" :class="{'active': vipType === 1}" @click="changeVipType(1)" href="javascript:;">
                            <span class="tag">更多人选择</span>
                            尊享<span class="amount">11+</span>求职权益
                        </a>
                        <a class="item" :class="{'active': vipType === 2}" @click="changeVipType(2)" href="javascript:;">尊享<span class="amount">8+</span>求职权益</a>
                    </div>
                    <!--
                        forbidden-status :不支出开通黄金会员
                        update-status :黄金升钻石
                     -->
                    <div class="tab-panes" :class="{'forbidden-status': openType === 3, 'update-status': openType === 2}">
                        <div v-if="vipType === 1" class="pane diamond">
                            <div class="list" :class="{'active': index === hoverPackageIndex}" v-for="(item, index) in diamondList" :key="index" @mouseenter="packageSelect(item, index)">
                                <span v-if="item.buyTypeTxt" class="tag">{{item.buyTypeTxt}}</span>
                                <div class="title">钻石VIP∙{{item.days}}天</div>
                                <div class="desc">{{item.buyDesc}}</div>
                                <div class="price">
                                    <span class="real-price">{{item.realAmount}}</span>
                                    <span class="original-price">¥{{item.originalAmount}}</span>
                                </div>
                                <div class="avg-price">已优惠￥{{item.discountAmount}}</div>
                                <div class="deduction-content">
                                    <div class="deduction">
                                        <span class="label">升级抵扣</span>
                                        <span class="money">¥{{item.upgradeData.convertPrice}}</span>
                                        <el-tooltip
                                            effect="dark"
                                            content="升级钻石VIP服务：黄金VIP会员可按剩余时长天数，折算抵扣对应金额升级为钻石VIP会员，享受更多求职权益！（最高抵扣金额不得高于所升级套餐金额）"
                                            placement="top"
                                        >
                                            <span slot="default" class="what"></span>
                                        </el-tooltip>
                                    </div>
                                </div>
                                <button class="vip-status" @click="openpayDialog(item.equityPackageCategoryId, index)">
                                    <div class="open">立即开通</div>
                                    <div class="update">实付<span class="money">{{item.upgradeData.realAmount}}</span>立即升级</div>
                                </button>
                                <div class="detail">
                                    <div class="detail-item" v-for="(equity, i) in item.equityList" :key="i">
                                        <div class="label">{{equity.name}}</div>
                                        <div class="value">
                                            <div class="text" v-html="equity.description"></div>
                                            <i v-if="equity.icon == 1" class="icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="vipType === 2" class="pane gold">
                            <div class="list" :class="{'active': index === hoverPackageIndex}" v-for="(item, index) in goldList" @mouseenter="packageSelect(item, index)">
                                <span v-if="item.buyTypeTxt" class="tag">{{item.buyTypeTxt}}</span>
                                <div class="title">黄金VIP∙{{item.days}}天</div>
                                <div class="desc">{{item.buyDesc}}</div>
                                <div class="price">
                                    <span class="real-price">{{item.realAmount}}</span>
                                    <span class="original-price">¥{{item.originalAmount}}</span>
                                </div>
                                <div class="avg-price">{{item.dailyAmount}}元/天</div>
                                <button class="vip-status" @click="openpayDialog(item.equityPackageCategoryId, index)" :disabled="openType === 3">
                                    <div class="open">立即开通</div>
                                    <div class="forbidden">
                                        不支持开通
                                        <div class="reason">钻石VIP服务生效中</div>
                                    </div>
                                </button>
                                <div class="detail">
                                    <div class="detail-item" v-for="(equity, i) in item.equityList" :key="i">
                                        <div class="label">{{equity.name}}</div>
                                        <div class="value">
                                            <div class="text" v-html="equity.description"></div>
                                            <i v-if="equity.icon == 1" class="icon"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="agreement">* 购买即表示同意 <a href="/agreement/value-added-services" target="_blank">《高校人才网增值服务协议》</a></div>
                </div>
            </div>

            <div class="contrast-wrapper" id="contrast-wrapper">
                <div class="main-wrapper" :class="{'show': showLimit}">
                    <div class="wrapper-title">VIP 会员权益对比</div>
                    <div class="subheading">海量招聘信息，心仪岗位轻松get，从此快速便捷找工作</div>
                    <div class="detail" :class="[vipType === 2 ? 'gold' : 'diamond']">
                        <span class="hot">推荐</span>
                        <table>
                            <thead>
                                <tr>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="name">简历置顶</div>
                                        <div class="instructions">您的简历将在单位搜索时优先置顶展示，让单位一眼可见</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">投递置顶</div>
                                        <div class="instructions">您投递的简历将在单位的应聘列表中置顶展示，便于单位快速查看</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">简历刷新</div>
                                        <div class="instructions">每天9:00自动刷新简历+无限次手动刷新，有效提升简历搜索排名</div>
                                    </td>
                                    <td>1次/天</td>
                                    <td>1次/天</td>
                                    <td>无限次</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">竞争力分析</div>
                                        <div class="instructions">查看职位热度、人岗匹配度、简历竞争力，实时了解自身竞争优势</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>10次/天</td>
                                    <td>10次/天</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">公告热度</div>
                                        <div class="instructions">查看招聘公告的热度数据及投递者的画像分布</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>10次/天</td>
                                    <td>10次/天</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">
                                            高级筛选
                                            <span class="privilege-escalation-tag">权益升级</span>
                                        </div>
                                        <div class="instructions">一键筛选有编制、低热度岗位，“上岸”快人一步</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">简历模板</div>
                                        <div class="instructions">多款高层次人才专属简历模板，精美简历一键生成</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">浏览足迹</div>
                                        <div class="instructions">支持查看近90天的公告&职位浏览记录，优质岗位不再错过</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">收藏查看</div>
                                        <div class="instructions">心仪岗位可一键收藏，可查看全部收藏的职位&公告信息</div>
                                    </td>
                                    <td>限50条</td>
                                    <td>无限制</td>
                                    <td>无限制</td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">求职资源</div>
                                        <div class="instructions">免费获取硕博求职资源干货包，包括年度招聘报告</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">专属标识</div>
                                        <div class="instructions">拥有会员专属身份标识，彰显会员尊贵权益</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                    <td>
                                        <span class="contain"></span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="name">高才优课</div>
                                        <div class="instructions">海量硕博人才精选课程，求职路上时刻“充电”</div>
                                    </td>
                                    <td>
                                        <span class="error"></span>
                                    </td>
                                    <td>开通180天会员可加赠</td>
                                    <td>开通180天会员可加赠</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="show-more">
                        <button class="more" @click="showLimit = !showLimit">{{showLimit ? '收起': '查看更多'}}</button>
                    </div>
                </div>
            </div>

            <div>
                <div class="introduce-wrapper">
                    <div class="main-wrapper">
                        <div class="wrapper-title">权益介绍</div>
                        <div>
                            <div class="list">
                                <div class="title diamond-vip">简历置顶</div>
                                <div class="desc">
                                    想让用人单位第一眼就看到你的简历？使用「简历置顶」功能，能够在用人单位搜索人才时，让你的简历优先展示在搜索结果顶部，有效提升你的简历曝光度，提高进面概率！
                                </div>
                            </div>
                            <div class="list">
                                <div class="title diamond-vip">投递置顶</div>
                                <div class="desc">同一岗位有大量求职者，不想自己的简历被刷下去？快试试「投递置顶」功能！投递后，你的简历将在用人单位的应聘列表中置顶展示，更快速被单位浏览和处理。</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="introduce-wrapper reverse">
                    <div class="main-wrapper">
                        <div class="list">
                            <div class="title diamond-vip">简历刷新</div>
                            <div class="desc">
                                简历总是石沉大海，可能是因为你没有使用「简历刷新」功能。「简历刷新」功能，每天9:00自动帮你刷新简历，让你的简历在用人单位搜索时排名更靠前，被更多单位第一时间看到！
                            </div>
                        </div>
                        <div class="list">
                            <div class="title">
                                高级筛选
                                <span class="privilege-escalation-tag">权益升级</span>
                            </div>
                            <div class="desc">
                                在海量招聘信息中，一键筛选有编制、低热度的职位&公告，助力选岗提质增效。无论你是想上岸编制岗，捡漏冷门岗，还是跟投热门岗，都能快速把握，省时省力更省心！
                            </div>
                        </div>
                    </div>
                </div>
                <div class="introduce-wrapper">
                    <div class="main-wrapper">
                        <div class="list">
                            <div class="title">竞争力分析</div>
                            <div class="desc">基于智能匹配算法进行人岗适配分析，多维度查看职位热度数据&趋势变化；同时根据竞争者的画像数据分析，直观了解自身竞争优劣势，帮助你更有针对性地投递。</div>
                        </div>
                        <div class="list">
                            <div class="title">公告热度</div>
                            <div class="desc">根据公告感兴趣人群、投递数据和同类公告热度对比等条件综合分析得出，不仅可及时掌握热度趋势变化，还可以获取竞争者的群体特征，知己知彼，让求职更有把握！</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="more-contrast-wrapper">
                <div class="main-wrapper">
                    <div class="title"></div>
                    <div class="content">
                        <span
                            class="pre"
                            :class="{'disabled': moreContrastScrollIndex === 0}"
                            @click="moreContrastScrollIndex = moreContrastScrollIndex = moreContrastScrollIndex - 1 >=0 ? moreContrastScrollIndex - 1 : 0"
                        ></span>
                        <div class="scroll-content">
                            <div class="contrast" :style="scrollStyle">
                                <div class="item">
                                    <div class="name">简历模板</div>
                                    <div class="sub">高层次人才专属模板 一键应用专业吸睛</div>
                                </div>
                                <div class="item">
                                    <div class="name">浏览足迹</div>
                                    <div class="sub">查看近90天浏览记录 历史信息快速查找</div>
                                </div>
                                <div class="item">
                                    <div class="name">收藏查看</div>
                                    <div class="sub">查看全部收藏记录 心仪岗位不再错过</div>
                                </div>
                                <div class="item">
                                    <div class="name">求职资源</div>
                                    <div class="sub">免费获取硕博求职资源 干货包<span>(需前往服务号领取)</span></div>
                                </div>
                                <div class="item">
                                    <div class="name">专属标识</div>
                                    <div class="sub">拥有会员专属身份标识 彰显会员尊贵权益</div>
                                </div>
                                <div class="item">
                                    <div class="name">高才优课</div>
                                    <div class="sub">免费获取硕博人才精选 课程包<span>(需前往服务号领取)</span></div>
                                </div>
                            </div>
                        </div>
                        <span
                            class="next"
                            :class="{'disabled': moreContrastScrollIndex === 2}"
                            @click="moreContrastScrollIndex = moreContrastScrollIndex + 1 > 2 ? 2 : moreContrastScrollIndex + 1"
                        ></span>
                    </div>
                </div>
            </div>

            <div class="user-wrapper">
                <div class="main-wrapper">
                    <div class="wrapper-title">用户评价</div>
                    <div class="user-description">
                        <div class="right-content">
                            <div class="right-catalogue" v-for="(item, index) in userList" :key="item.Id">
                                <div class="head">
                                    <div class="avatar">
                                        <img :src="item.img" alt="" />
                                        <div :class="item.sex"></div>
                                    </div>
                                    <div class="user-content">
                                        <div class="name">{{item.name}}</div>
                                        <div class="category">
                                            <div class="icon">{{item.icon1}}</div>
                                            <div class="icon">{{item.icon2}}</div>
                                            <div class="icon">{{item.icon3}}</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text">{{item.text}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="search-content">
                        <div class="search-title">服务说明</div>
                        <div class="text-content">
                            <div class="left-text">
                                <p>1、请在本页面通过官方支付方式——微信扫码支付，并向唯一官方收款方“高校人才网”完成服务费用的支付。请勿尝试任何私下转账方式。</p>
                                <p>2、服务将于付款成功后自动开通，服务过期则所有权益失效。购买后请合理安排时间并尽快使用。</p>
                                <p>3、竞争力分析&公告热度权益，在服务期内，按自然日，每项服务可使用的次数上限为10次，不累计。</p>
                                <p>4、使用简历置顶 & 简历刷新权益时，请务必确认您未“隐藏简历”，且在线简历完整度≥65%（以平台显示的完整度为准），否则无法提升简历曝光效果。</p>
                                <p>5、投递置顶权益限“报名方式”为“站内投递”的职位使用；您可在投递简历时选择是否使用该项服务。同一职位，30天内只能使用一次投递置顶权益.</p>
                                <p>6、您屏蔽的单位无法搜索到您的简历，请放心使用服务。</p>
                                <p>7、不同城市、职位类型下的曝光效果不同，实际曝光效果可能会存在波动。</p>
                                <p>8、本产品为虚拟服务，不支持退款，敬请谅解。</p>
                                <p>
                                    9、购买即表示同意<a href="/agreement/value-added-services" target="_blank"><span>《高校人才网增值服务协议》</span></a
                                    >。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="showFooterFixed" class="fixed-wrapper">
                <!--
                    forbidden-status :不支出开通黄金会员
                    update-status :黄金升钻石
                    -->
                <div class="main-wrapper" :class="{'forbidden-status': openType === 3 && vipType === 2, 'update-status': openType === 2 && vipType === 1}">
                    <div class="place">
                        <a href="#contrast-wrapper">权益对比</a>
                        |
                        <a href="#tabs-wrapper">服务套餐</a>
                    </div>
                    <div class="buy-info">
                        {{selectPackage.name}}：
                        <span class="price">
                            <span class="tag">￥</span>
                            {{selectPackage.realAmount}}
                        </span>
                        <button class="pay" :disabled="openType === 3 && vipType === 2" @click="openVipDialog">
                            <div class="open">立即开通</div>
                            <div class="update">
                                <div class="deduction">立减¥{{selectPackage.upgradeData.convertPrice}}</div>
                                实付<span class="real">{{selectPackage.upgradeData.realAmount}}</span>立即升级
                            </div>
                            <div class="forbidden">
                                不支持开通
                                <div class="reason">钻石VIP服务生效中</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <script>
            $(function () {
                const component = {
                    data() {
                        return {
                            // 1钻石、2黄金
                            vipType: 2,
                            // 开通类型 1开通，2升级，3不可购买黄金
                            openType: 1,
                            hoverPackageIndex: 1,
                            diamondList: [],
                            goldList: [],
                            selectPackage: {
                                equityPackageCategoryId: 1,
                                upgradeData: {}
                            },
                            showLimit: false,
                            moreContrastScrollIndex: 0,
                            userList: [
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/user-avatar.png',
                                    name: '陈**',
                                    sex: 'male',
                                    id: '1',
                                    text: '求职焦虑期购买了服务。我主要使用投递置顶和简历置顶功能，使用后简历查看率有明显提升，这半个月已经拿到了2个不错的offer！ 总体来说，超值！好评！',
                                    icon1: '硕士',
                                    icon2: '会计学',
                                    icon3: '湖南大学'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/female-avatar.png',
                                    name: '林**',
                                    sex: 'female',
                                    id: '1',
                                    text: '试了下那个竞争力分析，竟然可以跟同岗位的求职者对比，看到自己是什么水平，有点意思，就是这结果确实很卷……',
                                    icon1: '硕士',
                                    icon2: '教育学',
                                    icon3: '北京师范大学'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar2.png',
                                    name: '王**  ',
                                    sex: 'male',
                                    id: '1',
                                    text: '开通了钻石VIP后，我最常用的就是“简历置顶”，用了之后职位邀约明显增加了，很多单位联系我，最近已经开始在面试了，比我预想的还要快！',
                                    icon1: '博士',
                                    icon2: '机械工程',
                                    icon3: '华南理工大学'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar3.png',
                                    name: '郑**  ',
                                    sex: 'male',
                                    id: '1',
                                    text: '“高级筛选”太实用了，不仅能查热度，还能查编制岗，对于想入编的人来说很方便！而且还能一键置顶投递，让招聘单位优先看到我，反馈真的快很多。',
                                    icon1: '博士',
                                    icon2: '有机化学',
                                    icon3: '南京大学'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar4.png',
                                    name: '黄**  ',
                                    sex: 'female',
                                    id: '1',
                                    text: '开通了会员后，简历模板就可以无限次使用了。这些模板精美且专业，还可以一键套用样式，不用重新调整排版设计，很方便！适合我这种懒人哈哈。',
                                    icon1: '博士',
                                    icon2: '海洋生物学',
                                    icon3: '中国海洋大学'
                                },
                                {
                                    img: '//img.gaoxiaojob.com/uploads/image/purchase/pc/vip/male-avatar5.png',
                                    name: '孙*  ',
                                    sex: 'female',
                                    id: '1',
                                    text: '投递简历前，查一下公告热度还蛮有用的，我主要用来看公告热度趋势和投递竞争比例，了解哪公告的受欢迎程，哪些单位受求职者追捧，竞争情况怎么样。',
                                    icon1: '博士',
                                    icon2: '计算机应用技术',
                                    icon3: '复旦大学'
                                }
                            ],
                            showFooterFixed: false
                        }
                    },
                    computed: {
                        scrollStyle() {
                            const x = -238 * this.moreContrastScrollIndex
                            return `transform:translateX(${x}px)`
                        }
                    },
                    methods: {
                        scroll() {
                            const _this = this
                            window.addEventListener('scroll', function () {
                                const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
                                const showHeight = document.body.clientHeight
                                _this.showFooterFixed = scrollTop > showHeight * 2
                            })
                        },
                        packageSelect(item, index) {
                            this.hoverPackageIndex = index
                            this.selectPackage = item
                        },
                        changeVipType(type) {
                            //-------------用户付费转化数据埋点-开始---------------
                            // let tabName = type === 1 ? '钻石VIP' : '黄金VIP'
                            // let logData = {
                            //     params : { tabName },
                            //     actionType : '1',
                            //     actionId : '10010002'
                            // }
                            // this.payBuriedPoint(logData)
                            httpGet("/showcase-browse-log/vip-tab-point-log?uuid=<?=$data['uuid']?>&type=" + type)
                            //-------------用户付费转化数据埋点-结束---------------

                            const { vipType } = this
                            if (type === vipType) return
                            this.vipType = type
                            this.hoverPackageIndex = 1
                            this.showLimit = false

                            this.getPackage(type === 1 ? 3 : 1)
                        },
                        openVipDialog() {
                            const {
                                selectPackage: { equityPackageCategoryId },
                                hoverPackageIndex
                            } = this
                            this.openpayDialog(equityPackageCategoryId, hoverPackageIndex, 2)
                        },
                        openpayDialog(id, index, position = 1) {
                            //-------------用户付费转化数据埋点-开始---------------
                            // let productName = $('.list:eq('+index+') .title').text()
                            // let logData = {
                            //     params : { productId:id, productName:productName, clickPosition:position },
                            //     actionType : '1',
                            //     actionId : '10010003'
                            // }
                            // this.payBuriedPoint(logData)
                            //-------------用户付费转化数据埋点-结束---------------

                            let api = "<?=$data['api_popup']?>"
                            let uuid = "<?=$data['uuid']?>"
                            window.globalComponents.PayDialogAlertComponent.show(api, id, index, uuid, position)
                        },
                        getPackage(type) {
                            // type 1黄金 3钻石
                            httpGet('/api/person/resume-equity-package/get-buy-package-list?equityPackageCategoryId=' + type).then((r) => {
                                const { isDiamondVip, isGoldVip, list } = r
                                this[type === 1 ? 'goldList' : 'diamondList'] = list
                                this.selectPackage = list[1]

                                let openType = 1
                                if (isGoldVip) {
                                    openType = 2
                                }
                                if (isDiamondVip) {
                                    openType = 3
                                }
                                this.openType = openType
                            })
                        },
                        payBuriedPoint(data) {
                            const jsonString = JSON.stringify(data)

                            let configKey = '123abcgaoxiaorencaiwang'
                            let code = CryptoJS.MD5(configKey).toString()
                            let iv = CryptoJS.enc.Utf8.parse(code.substring(0, 16))
                            let key = CryptoJS.enc.Utf8.parse(code.substring(16))

                            let encryptString = CryptoJS.AES.encrypt(jsonString, key, { mode: CryptoJS.mode.ECB, iv: iv })
                            let img = new Image()
                            img.src = `3.gif?data=` + encodeURIComponent(encryptString)
                        }
                    },
                    mounted() {
                        let that = this
                        this.scroll()
                        this.getPackage(1)

                        //-------------用户付费转化数据埋点-开始---------------
                        window.onbeforeunload = function () {
                            httpGet("/showcase-browse-log/update-vip-view-point-log?uuid=<?=$data['uuid']?>")
                        }
                        //-------------用户付费转化数据埋点-结束---------------
                    }
                }

                Vue.createApp(component).use(ElementPlus).mount('#component')
            })
        </script>

        <script>
            $(function () {
                const backtopOptions = {
                    computed: {
                        viewportHeight() {
                            return window.innerHeight
                        }
                    }
                }
                Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
            })
        </script>

        <div id="backtopTemplate">
            <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
                <div class="feedback" @click.stop>
                    <el-popover :popper-class="'feedback-popover'" placement="left" :width="290" :offset="36" trigger="hover">
                        <template #reference>
                            <p class="feedback-link">咨询反馈</p>
                        </template>

                        <template #default>
                            <div class="feedback-detail">
                                <a href="/member/company/applyCooperation" target="_blank" class="business-cooperation">
                                    <h6>商务合作</h6>
                                    <p>点击填写您的业务诉求，专属商务会尽快联系您</p>
                                </a>

                                <a href="//wj.qq.com/s2/10430873/e75f" target="_blank" class="opinion-feedback">
                                    <h6>意见反馈</h6>
                                    <p>点击填写内容快捷反馈问题，会有运营人员为您提供帮助</p>
                                </a>

                                <div class="customer-service">
                                    <h6>联系客服</h6>
                                    <div>
                                        <p>更多咨询，也可通过以下方式联系我们：</p>
                                        <p><strong>电话：</strong>020-85611139 ***********</p>
                                        <p><strong>微信：</strong>***********</p>
                                        <p><strong>QQ：</strong><a href="//wpa.qq.com/msgrd?v=3&uin=2881224205&site=qq&menu=yes&jumpflag=1" target="_blank">2881224205</a></p>
                                        <p><strong>邮箱：</strong><a href="mailto:<EMAIL>" target="_blank"><EMAIL></a></p>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-popover>
                    <a href="javascript:;" class="link-icon weixin">
                        <span>公众号</span>
                        <span class="weixin-hover">关注高校人才网V公众号</span>
                    </a>
                </div>
                <span class="backtop-button"></span>
            </el-backtop>
        </div>
    </body>
</html>
