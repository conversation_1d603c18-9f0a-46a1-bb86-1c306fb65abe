// 加入我们
@import './variables';
@import './common/pageContent';

body,
html {
    position: relative;
}
.page-container {
    .propaganda {
        margin-top: 180px;

        .subtitle {
            padding-bottom: 32px;
            font-size: 20px;
        }

        .title {
            padding-bottom: 40px;
            font-size: 56px;
        }

        .post-link {
            margin: 0 auto;
            width: 150px;
            height: 40px;
            text-align: center;
            line-height: 40px;
            font-size: 20px;
            border: 1px solid $color-white;
        }
    }
}

.join-us {
    background-image: url('../assets/bg3.jpg');
}

.future {
    margin-top: 54px;

    .list-container {
        display: flex;
        margin: 40px 0 64px;
    }

    .list {
        margin-right: 75px;
        width: calc(100% / 3);

        &:last-child {
            margin-right: 0;
        }
    }

    .top-title {
        display: flex;
        padding-bottom: 18px;
        border-bottom: 2px solid $color-primary;

        img {
            margin-right: 8px;
            width: 49px;
            height: 54px;
        }

        span {
            padding-top: 28px;
            font-size: 22px;
            font-weight: bold;
        }
    }

    .bottom-text {
        padding-top: 20px;
        line-height: 1.5;
    }
}

.job-container {
    padding-top: 53px;
    background-color: $color-white;
    color: $color-white;

    .job-classify {
        display: flex;
        padding: 30px 0 60px;
    }

    .post {
        width: calc(100% / 3);
    }

    .job-description {
        padding: 5px 0 0 20px;
        width: 355px;
        line-height: 1.8;
        font-size: 12px;
    }

    .modular {
        padding: 20px 0 0 20px;
        width: 100%;
        height: 183px;
        box-sizing: border-box;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        font-size: 22px;
        font-weight: bold;
    }

    .post-title {
        padding: 20px 0 0 20px;
    }

    .product-manager {
        position: relative;
        background-image: url('../assets/job-list1.png');

        .hover-post {
            position: absolute;
            width: 100%;
            height: 0;
            top: 0;
            left: 0;
            z-index: 1;
            background-color: $color-primary;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
            transition: all ease 0.5s;
            overflow: hidden;
        }

        &:hover {
            .hover-post {
                height: 366px;
            }
        }
    }

    .creative-director {
        position: relative;
        background-image: url('../assets/job-list2.png');

        .hover-post {
            position: absolute;
            width: 100%;
            height: 0;
            top: -183px;
            left: 0;
            background-color: #4d4d4d;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;
            transition: all ease 0.5s;
            overflow: hidden;
        }

        &:hover {
            .hover-post {
                height: 366px;
            }
        }
    }

    .sales-consultant {
        position: relative;
        background-image: url('../assets/job-list3.png');

        .hover-post {
            position: absolute;
            width: 100%;
            height: 0;
            top: 0;
            left: 0;
            background-color: #004eff;
            z-index: 1;
            transition: all ease 0.5s;
            overflow: hidden;
        }

        &:hover {
            .hover-post {
                height: 366px;
            }
        }
    }

    .website-operation {
        position: relative;
        background-image: url('../assets/job-list4.png');

        .hover-post {
            position: absolute;
            width: 100%;
            height: 0;
            top: -183px;
            left: 0;
            background-color: #495771;
            transition: all ease 0.5s;
            overflow: hidden;
        }

        &:hover {
            .hover-post {
                height: 366px;
            }
        }
    }

    .content-operation {
        position: relative;
        background-image: url('../assets/job-list5.png');

        .hover-post {
            position: absolute;
            width: 100%;
            height: 0;
            top: 0;
            left: 0;
            background-color: #55d6bf;
            z-index: 1;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
            transition: all ease 0.5s;
            overflow: hidden;
        }

        &:hover {
            .hover-post {
                height: 366px;
            }
        }
    }

    .copywriting-planning {
        position: relative;
        background-image: url('../assets/job-list6.png');

        .hover-post {
            position: absolute;
            width: 100%;
            height: 0;
            top: -183px;
            left: 0;
            background-color: #c85527;
            border-top-right-radius: 5px;
            border-bottom-right-radius: 5px;
            transition: all ease 0.5s;
            overflow: hidden;
        }

        &:hover {
            .hover-post {
                height: 366px;
            }
        }
    }
}

.team-container {
    margin-top: 47px;

    .is-title {
        margin: 0 auto;
        width: 1200px;
    }

    .team-situation {
        display: flex;
        margin-top: 40px;
        width: 100%;
    }

    .propaganda-content {
        width: 50%;
        color: $color-white;
        line-height: 1.5;

        p {
            padding-top: 50px;
            font-size: 24px;
            font-weight: 800;
        }
    }

    .left-content {
        text-align: right;
        box-sizing: border-box;
    }

    .right-content {
        padding-left: 50px;
        box-sizing: border-box;
        background: url('../assets/team-picture3.png') no-repeat center / cover;
    }

    .top-content,
    .bottom-content {
        padding-right: 50px;
        height: 300px;
    }

    .top-content {
        background: url('../assets/team-picture1.png') no-repeat center / cover;
    }

    .bottom-content {
        background: url('../assets/team-picture2.jpg') no-repeat center / cover;
    }
}

.grow-up-container {
    padding: 48px 0 60px;
    background-color: $color-white;

    .company-picture {
        display: flex;
        justify-content: space-between;
        margin-top: 26px;

        img {
            transition: all ease 0.3s;
            border: 1px solid #dfdfdf;
            border-radius: 4px;
            box-sizing: border-box;

            &:hover {
                transform: scale(1.1);
            }
        }
    }
}

@media screen and (max-width: 1200px) {
    .page-container {
        .propaganda {
            .title {
                font-size: 40px;
            }
        }
    }

    .future {
        .list-container {
            display: block;
            padding: 0 10px;
        }

        .list {
            margin-bottom: 20px;
            width: 100%;
        }
    }

    .job-container {
        .job-classify {
            display: block;
        }

        .post {
            width: 100%;
        }

        .product-manager {
            border-top-right-radius: 9px;
        }

        .creative-director {
            .hover-post {
                top: 0;
                z-index: 1;
            }
        }

        .website-operation {
            .hover-post {
                top: 0;
                z-index: 1;
            }
        }

        .copywriting-planning {
            border-bottom-left-radius: 9px;
        }
    }

    .grow-up-container {
        padding: 40px 5px;

        .company-picture {
            display: block;

            img {
                margin-bottom: 20px;
                width: 100%;

                &:hover {
                    transform: scale(1);
                }
            }
        }
    }

    .team-container {
        .is-title {
            width: 95%;
        }

        .team-situation {
            display: block;
        }

        .propaganda-content {
            width: 100%;

            p {
                padding-top: 20px;
                font-size: 20px;
            }
        }

        .left-content {
            text-align: left;
        }

        .right-content {
            padding: 0 10px 20px 10px;
        }

        .top-content,
        .bottom-content {
            padding: 0 10px;
        }
    }
}

.swiper {
    width: 100%;
    height: 100%;
    padding: 24px;
    overflow: hidden;
    user-select: none;
    box-sizing: border-box;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    -khtml-user-select: none;
}

.swiper-button-next,
.swiper-button-prev {
    width: 14px;
    color: rgba(51, 51, 51, 0.3);
}

.swiper-button-next {
    right: 0;
}

.swiper-button-prev {
    left: 0;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 24px;
    font-weight: bold;
}
