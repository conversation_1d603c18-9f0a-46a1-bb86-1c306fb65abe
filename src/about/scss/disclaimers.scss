// 免责声明
@import './variables';
@import './common/pageContent';

.disclaimers {
    background-image: url('../assets/bg5.jpg');
}

.disclaimers-container {
    padding: 100px 0;

    .statement-content {
        display: flex;
        margin-bottom: 40px;
        padding-left: 40px;

        &:last-child {
            margin-bottom: 0;
        }

        span {
            display: block;
        }
    }

    .number {
        margin: 4px 3px 0 0;
        width: 18px;
        height: 18px;
        text-align: center;
        line-height: 18px;
        background-color: $color-primary;
        color: $color-white;
        border-radius: 50%;
    }

    .tips {
        line-height: 1.8;
        letter-spacing: 1;
    }
}

@media screen and (max-width: 1200px) {
    .disclaimers-container {
        .statement-content {
            padding-left: 0;
        }

        .tips {
            flex: 1;
        }
    }
}
