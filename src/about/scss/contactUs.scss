// 联系我们
@import './variables';
@import './common/pageContent';

.contact-us {
    background-image: url('../assets/bg4.png');
}

.cooperation-mode {
    margin-top: 53px;

    .contact-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 40px 0 56px;

        li {
            margin-top: 90px;
            width: 33%;
        }

        h4 {
            padding-left: 33px;
            line-height: 30px;
            font-size: 20px;
            background: url('../assets/phone.png');
            background-repeat: no-repeat;
            background-position: left center;
            background-size: 26px;
        }

        .fax {
            background-image: url('../assets/fax.png');
        }

        .qq {
            background-image: url('../assets/qq.png');
        }

        .weixin {
            background-image: url('../assets/weixin.png');
        }

        .mail {
            background-image: url('../assets/mail.png');
        }

        .code {
            background-image: url('../assets/code.png');
        }

        span {
            display: block;
            padding: 15px 0 0 33px;
            font-size: 14px;
        }

        @for $i from 1 through 3 {
            li:nth-of-type(#{$i}) {
                margin-top: 0;
            }
        }
    }
}

.contact-information {
    background-color: $color-white;
}

.current-container {
    padding: 43px 0 50px;

    .current-content {
        padding: 18px 0 0 20px;

        p {
            line-height: 2;
            font-size: 14px;
        }

        .is-current {
            position: relative;
            padding-left: 10px;

            &::after {
                content: '';
                position: absolute;
                width: 5px;
                height: 5px;
                background-color: #495771;
                border-radius: 50%;
                left: 0;
                top: 12px;
            }
        }
    }
}

.map-container {
    padding: 42px 0 51px;
    background-color: $color-white;

    .map-picture {
        margin-top: 30px;
        width: 100%;
        height: 300px;
        background: url('../assets/map.jpg') no-repeat center / cover;
    }
}

@media screen and (max-width: 1200px) {
    .cooperation-mode {
        .contact-list {
            li {
                margin-top: 30px;
                width: 50%;
                text-align: center;
            }

            li:nth-of-type(3) {
                margin-top: 30px;
            }

            h4 {
                background-position: 20% center;
            }

            span {
                padding-left: 0;
            }
        }
    }

    .map-container {
        .map-picture {
            height: 160px;
        }
    }
}
