// 产品服务
@import './variables';
@import './common/pageContent';

.product-service {
    background-image: url('../assets/bg1.jpg');
}

.advantages {
    padding: 42px 0 57px;

    .card {
        display: flex;
        justify-content: space-between;
        padding-top: 30px;
    }

    .card-list {
        position: relative;
        padding: 35px 0 30px;
        width: 360px;
        text-align: center;
        background: url('../assets/card-bg.png') no-repeat center / cover;
        border-radius: 10px;
        transition: all 0.3s;

        &::after {
            display: none;
            content: '';
            position: absolute;
            width: 100%;
            height: 5px;
            left: 0;
            bottom: 0;
            background-color: $color-primary;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
        }
    }

    .card-list:hover {
        box-shadow: 0px 0px 26px 0px rgba(73, 87, 113, 0.1);

        &::after {
            display: block;
        }
    }

    .picture {
        margin-bottom: 30px;
        width: 93;
        height: 93px;
    }

    .title {
        padding-bottom: 9px;
        font-size: 24px;
        color: #343434;
    }

    .subtitle {
        display: inline-block;
        margin-bottom: 30px;
        padding-left: 4px;
        font-size: 16px;
        background: url('../assets/rectangle.png') no-repeat bottom center / 246px;
    }

    .text {
        padding: 0 34px;
        text-align: left;
        line-height: 1.8;
        font-size: inherit;
    }
}

.service-mode {
    margin-bottom: 62px;

    .mode-title {
        margin-bottom: 20px;
    }

    .mode {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 65px;
        padding: 20px;
        background-color: $color-white;
        border-radius: 10px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .picture-content {
        width: 515px;
        height: 260px;
        border-radius: 10px;
    }

    .text-content {
        width: 607px;
    }

    .title {
        position: relative;
        margin-bottom: 13px;
        padding-left: 10px;
        font-size: 22px;
        color: #343434;

        &::after {
            content: '';
            position: absolute;
            width: 5px;
            height: 21px;
            top: 50%;
            transform: translateY(-50%);
            left: 0;
            background-color: $color-primary;
        }
    }

    .subtitle {
        display: block;
        margin-bottom: 10px;
        line-height: 2;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-box-orient: vertical;
        // -webkit-line-clamp: 2;
    }

    .process {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 100%;

        &.row{
            li{
                width: 100%;
            }
        }

        li {
            margin-top: 17px;
            padding-left: 23px;
            width: 46%;
            font-size: 14px;
            position: relative;
            @include utils-ellipsis;

            &::after, &::before{
                content: '';
                position: absolute;
                width: 17px;
                height: 17px;
                top: 50%;
                transform: translateY(-50%);
                left: 0;
                background-color: rgba($color-primary, 0.2);
                border-radius: 50%;
            }

            &::before{
                width: 10px;
                height: 10px;
                top: 50%;
                left: 4px;
                background-color: $color-primary;
            }

            &:nth-of-type(4n - 1), &:nth-of-type(4n) {
                &::after {
                    background-color: rgba(#80c269, 0.2);
                }

                &::before {
                    background-color: #80c269;
                }
            }
        }
    }
}

.partner {
    margin-bottom: 60px;

    &:nth-last-of-type(1) .job-container {
        margin-top: 20px;
    }

    .job-container {
        margin-bottom: 40px;
        background-color: $color-white;
        border-radius: 10px;
    }

    .job-container:last-child {
        margin-bottom: 0;
    }

    .job-title {
        padding: 15px 0 12px 30px;
        font-size: 20px;
        color: #343434;
        border-bottom: 2px solid $color-primary;
    }

    .job-logo {
        display: flex;
        flex-wrap: wrap;
        padding: 25px 20px 0 20px;

        li {
            margin: 0 32px 20px 0;
            width: 202px;
            height: 52px;
            border: 2px solid #f7f7f7;
        }

        li:nth-of-type(5n) {
            margin-right: 0;
        }
    }

    .show-colleges-content,
    .show-scientific-content,
    .show-medical-content,
    .advantages-enterprise-content {
        padding-top: 0;
    }

    .arrow {
        padding-bottom: 15px;
    }

    .open,
    .hide {
        margin: 0 auto;
        width: 53px;
        height: 14px;
        cursor: pointer;
    }

    .open {
        background: url('../assets/down-arrow.png') no-repeat center / cover;
    }

    .hide {
        display: none;
        background: url('../assets/up-arrow.png') no-repeat center / cover;
    }
}

@media screen and (max-width: 1200px) {
    .advantages {
        .card {
            display: block;
        }

        .card-list {
            margin-bottom: 20px;
            width: 100%;
        }
    }

    .service-mode {
        .mode {
            margin-bottom: 20px;
            display: block;
        }

        .picture-content {
            width: 100%;
        }

        .text-content {
            width: 100%;
        }
    }

    .partner {
        .job-logo {
            li {
                margin: 0 auto 10px;
                width: 100%;
            }
        }
    }
}
