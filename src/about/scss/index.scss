// 关于我们
@import './variables';
@import './common/pageContent';

.about-us {
    padding: 42px 0 77px;

    .information {
        padding: 20px 0 0 20px;
        line-height: 2;

        p{
            font-size: inherit;
            text-indent: 2em;
        }
    }
}

.vision-bg {
    width: 100%;
    height: 220px;
    background: url('../assets/vision.png') no-repeat center / cover;
}

.event-container {
    padding: 53px 0 70px;
    background-color: $color-white;

    .information-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 56px 0 65px;
    }

    .left-picture {
        width: 546px;
        height: 281px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
    }

    .establish-picture {
        background-image: url('../assets/establish-picture.png');
    }

    .transformation-picture {
        background-image: url('../assets/transformation-picture.png');
    }

    .innovate-picture {
        background-image: url('../assets/innovate-picture.png');
    }

    .right-text {
        width: 597px;
    }

    .so-far {
        padding-bottom: 20px;
        font-size: 23px;
        font-weight: 800;
    }

    .honor {
        line-height: 2;
    }

    .time {
        color: $color-primary;
    }

    .process {
        position: relative;
        display: flex;
        justify-content: space-between;
        padding: 0 60px;

        &::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 1px;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background-color: #eee;
        }

        span {
            display: block;
        }
    }

    .experience {
        margin-bottom: 15px;
        text-align: center;
        color: #8690a4;
    }

    .circle {
        position: relative;
        margin: 0 auto 15px;
        width: 12px;
        height: 12px;
        background-color: #c9c9c9;
        border-radius: 50%;
        z-index: 1;
        cursor: pointer;
    }

    .current {
        background-color: $color-primary;
    }

    .experience-time {
        font-size: 16px;
    }

    .is-experience {
        margin: 0 auto 5px;
        width: 14px;
        height: 26px;
        background: url('../assets/triangle.png') no-repeat center / cover;
    }
}

.enterprise-honor {
    padding: 53px 0 0;

    // 电脑端
    .honor-part {
        position: relative;
        padding: 50px 60px 63px;
    }

    .list {
        width: 323px;
    }

    .top-picture {
        width: 100%;
        height: 217px;
    }

    .bottom-text {
        display: block;
        padding-top: 24px;
        text-align: center;
    }

    .swiper-button-next,
    .swiper-button-prev {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 44px;
        height: 44px;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
    }

    .swiper-button-next {
        background-image: url('../assets/right.png');
    }

    .swiper-button-prev {
        background-image: url('../assets/left.png');
        left: 0;
    }

    .swiper-button-prev:after,
    .swiper-rtl .swiper-button-next:after,
    .swiper-button-next:after,
    .swiper-rtl .swiper-button-prev:after {
        display: none;
    }

    // 手机端
    .honor-content {
        display: none;
    }
}

.enterprise-culture-container {
    padding: 48px 0 27px;
    background-color: $color-white;

    .enterprise-culture-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 41px 0 0 20px;

        li {
            margin-bottom: 50px;
            width: 50%;
        }

        .title {
            padding-bottom: 16px;
            font-size: 20px;
        }
    }
}

@media screen and (max-width: 1200px) {
    .about-us {
        padding: 20px 0;

        .information {
            padding: 20px;
        }
    }

    .vision-bg {
        height: 90px;
    }

    .event-container {
        .information-content {
            display: flow-root;
            padding: 30px 10px;
        }

        .left-picture,
        .right-text {
            margin-bottom: 10px;
            width: 100%;
        }
    }

    .enterprise-culture-container {
        padding: 20px 0;

        .enterprise-culture-list {
            display: block;
            padding: 20px;

            li {
                width: 100%;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }

    .enterprise-honor {
        // 电脑端
        .honor-part {
            display: none;
        }

        // 手机端
        .honor-content {
            display: block;
            padding: 30px 0;

            .list {
                margin: 0 auto 20px;
            }
        }
    }
}
