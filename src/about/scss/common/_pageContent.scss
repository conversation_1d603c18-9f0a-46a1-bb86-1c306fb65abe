// 关于我们公共头部
@import '../variables';
@import './main';

.page-container {
    position: relative;
    height: 600px;

    // 视频
    .video {
        position: absolute;
        display: block;
        width: 100%;
        height: 100%;
        z-index: -1;
        object-fit: cover;
    }

    // 背景图片
    .bg-picture {
        position: absolute;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        z-index: -1;
    }

    .header-container {
        position: relative;
        z-index: 99;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 0 auto;
        height: 70px;

        .logo-link {
            width: 121px;
            height: 34px;
            background: url($logo-column) no-repeat center / cover;
        }

        nav {
            display: flex;
            line-height: 70px;
        }

        .nav-link {
            position: relative;
            margin: 0 28px;
            color: $color-white;

            &::before {
                content: '';
                position: absolute;
                width: 0;
                height: 1px;
                left: 0;
                bottom: 13px;
                display: block;
                transition: all ease 1s;
                background-color: $color-white;
            }

            &:hover {
                &::before {
                    width: 100%;
                }
            }
        }

        .nav-link:last-child {
            position: relative;
            margin-right: 0;

            &::after {
                content: '';
                position: absolute;
                width: 2px;
                height: 30px;
                left: -28px;
                top: 50%;
                transform: translateY(-50%);
                background-color: $color-white;
            }
        }
    }

    .propaganda {
        margin: 155px auto 0;
        color: $color-white;

        .title {
            padding-bottom: 30px;
            text-align: center;
            font-size: 40px;
        }

        .subtitle {
            padding-bottom: 86px;
            text-align: center;
            font-size: 18px;
        }

        .data-content {
            display: flex;
            padding: 40px 0;
            text-align: center;
            height: 153px;
            background: #f3f3f3;
            box-shadow: 0px 8px 18px 2px rgba(55, 99, 170, 0.1);
            opacity: 0.8;
            color: #495771;
            border-radius: 5px;
            box-sizing: border-box;
        }

        .data {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 16.666%;
            height: 100%;
            border-right: 1px solid rgba(73, 87, 113, 0.3);

            &:last-child {
                border-right: none;
            }
        }

        .quantity {
            padding-bottom: 20px;
            line-height: 1;
            font-size: 20px;
        }

        .number,
        .symbol {
            font-size: 45px;
            font-family: Bahnschrift;
        }

        .information {
            font-size: 16px;
        }
    }

    .is-propaganda {
        margin-top: 190px;

        .is-title {
            padding-bottom: 26px;
            text-align: left;
            font-size: 54px;
        }

        .is-subtitle {
            width: 574px;
            text-align: left;
            line-height: 2;
            font-size: inherit;
        }
    }
}

.is-page-container {
    &::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        opacity: 0.3;
        background-color: #495771;
    }
}

@media screen and (max-width: 320px) {
    .page-container {
        .header {
            .nav-link {
                margin: 0 !important;
                font-size: 12px;
            }
        }

        .propaganda .title {
            font-size: 18px !important;
        }

        .subtitle {
            font-size: 14px !important;
        }

        .number,
        .symbol,
        .quantity {
            font-size: 14px !important;
        }

        .information {
            font-size: 12px !important;
        }
    }
}

@media screen and (max-width: 1200px) {
    .page-container {
        .header {
            .logo-link {
                width: 110px;
                height: 24px;
            }

            .nav-link {
                margin: 0 2px;
            }

            .nav-link:last-child {
                &::after {
                    display: none;
                }
            }
        }

        .propaganda {
            .title {
                padding-left: 0;
                font-size: 20px;
            }

            .subtitle {
                font-size: 16px;
            }

            .quantity {
                font-size: 16px;
            }

            .number,
            .symbol {
                font-size: 22px;
            }

            .information {
                font-size: 14px;
            }
        }

        .is-propaganda {
            padding: 0 10px;

            .is-subtitle {
                width: 100%;
            }
        }
    }
}
