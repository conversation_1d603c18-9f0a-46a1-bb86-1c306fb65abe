// 关于我们公共scss
@import '../variables';
@import './footer';

html {
    height: 100%;
    box-sizing: border-box;
    scroll-behavior: smooth;
}

body {
    font-size: 14px;
    color: #495771;
    background-color: #f7f8f9;
    font-family: 'pingfang sc', 微软雅黑;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
ul {
    margin: 0;
    padding: 0;
    font-size: 12px;
}

ul {
    list-style: none;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

.wrapper {
    position: relative;
    width: 100%;

    a {
        display: block;
    }

    img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}

// 宽度1200px
.view-width {
    margin: 0 auto;
    width: $view-width;
}

.tab-container {
    position: sticky;
    top: 0;
    width: 100%;
    background-color: $color-white;
    z-index: 9999;

    .tab {
        display: flex;
    }

    .tab-link {
        padding: 18px 40px;

        &:not(&.active):hover {
            color: #495771;
            background-color: #eee;
            border-radius: 5px;
        }
    }

    .active {
        color: $color-white;
        background-color: $color-primary;
        border-radius: 5px;
    }
}

// 公共标题
.is-title {
    h2 {
        position: relative;
        padding-left: 20px;
        font-size: 28px;
        color: #1d1d1d;

        &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background-color: $color-primary;
            border-radius: 3px;
        }
    }
}

@media screen and (max-width: 1200px) {
    .view-width {
        width: 100%;
    }

    .tab-container {
        .tab-link {
            padding: 13px 2px;
            font-size: 12px;
        }
    }

    // 公共标题
    .is-title {
        padding-left: 20px;

        h2 {
            font-size: 24px;
        }
    }

    .fair-footer {
        display: none;
    }
}
