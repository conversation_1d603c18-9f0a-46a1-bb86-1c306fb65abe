// 底部
.fair-footer {
    width: 100%;
    height: 230px;
    overflow: hidden;
    background-color: #1f2329;
    display: flex;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-justify-content: center;
    -webkit-align-items: center;
}

.footer-content-box {
    width: 1300px;
    height: 170px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
    overflow: hidden;
}

.footer-nav {
    padding-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
}

.footer-nav li a {
    color: #fff;
    font-size: 14px;
}

.footer-left p {
    font-size: 14px;
    color: #838687;
    line-height: 30px;
    text-align: left;
}

.footer-left .num-one {
    margin-top: 30px;

    a {
        display: inline-block;
    }
}

.sp {
    display: inline-block;
}

.footer-right {
    height: 150px;
    width: 200px;
    overflow: hidden;
    border-left: 1px solid #fff;
    text-align: left;

    background: url($logo-column) no-repeat 50px center/150px auto;
}

.footer-right p {
    color: #838687;
    font-size: 14px;
    line-height: 40px;
}

.footer-enroll {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
}

.footer-enroll li img {
    width: 50px;
    height: 50px;
}
