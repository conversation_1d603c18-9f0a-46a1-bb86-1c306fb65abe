// 媒体矩阵
@import './variables';
@import './common/pageContent';

.media-matrix {
    background-image: url('../assets/bg2.jpg');
}

.qr-code-container {
    margin-top: 54px;

    .public-list {
        display: flex;
        justify-content: space-between;
        margin: 27px 0 72px;

        li {
            position: relative;
            padding: 20px 15px;
            width: 270px;
            height: 350px;
            text-align: center;
            background-color: $color-white;
            border-radius: 10px;
            box-sizing: border-box;
            transition: all 0.3s;

            &:hover {
                box-shadow: 0px 8px 18px 2px rgba(55, 99, 170, 0.1);
            }
        }

        .public-picture {
            position: relative;
            margin: 0 auto 20px;
            width: 79px;
            height: 68px;

            &::after {
                content: '';
                position: absolute;
                width: 153px;
                height: 33px;
                left: 50%;
                transform: translateX(-50%);
                bottom: -10px;
                background: url('../assets/shadow.png') no-repeat center / cover;
            }

            img {
                position: relative;
                z-index: 99;
            }
        }

        .public-title {
            margin-bottom: 11px;
            font-size: 22px;
        }

        .public-subtitle {
            margin-bottom: 23px;
            padding-bottom: 17px;
            width: 100%;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            @include utils-ellipsis;
        }

        .text {
            display: inline-block;
            margin-bottom: 20px;
            padding: 0 8px;
            font-size: 12px;
            background: url('../assets/media-rectangle.png') no-repeat bottom center / contain;
        }

        .is-text {
            margin: 0;
        }

        .see {
            position: absolute;
            width: 100px;
            height: 25px;
            text-align: center;
            line-height: 25px;
            left: 50%;
            transform: translateX(-50%);
            bottom: 20px;
            background-color: $color-primary;
            border-radius: 5px;
            font-size: 12px;
            color: $color-white;
            cursor: pointer;

            &:hover {
                background-color: #ffba45;
            }
        }

        .show-content {
            display: none;
        }

        .qr-code {
            margin-top: 28px;
            width: 123px;
            height: 123px;

            &::after {
                bottom: -24px;
            }
        }

        .follow {
            position: relative;
            margin: 50px 0 10px;
            font-size: 14px;
        }

        .left-rectangle {
            position: absolute;
            margin-left: -21px;
            width: 15px;
            height: 7px;
            top: 5px;
            background: $color-primary;
            opacity: 0.5;
            border-radius: 4px;
        }

        .right-rectangle {
            position: absolute;
            margin-left: 5px;
            width: 15px;
            height: 7px;
            top: 5px;
            background: $color-primary;
            opacity: 0.5;
            border-radius: 4px;
        }

        .name {
            font-size: 14px;
            font-weight: 300;
            color: $color-primary;
        }
    }
}

// 点击(客服&社群)展开全部二维码样式
.community-content {
    display: none;
    margin: 27px 0 72px;
    padding: 42px 0 27px;
    background-color: $color-white;
    box-shadow: 0px 8px 18px 2px rgba(55, 99, 170, 0.1);
    border-radius: 10px;

    .community {
        display: flex;
    }

    .code-content {
        width: 50%;
    }

    .community-code {
        position: relative;
        margin: 0 auto 26px;
        width: 123px;
        height: 123px;

        &::after {
            content: '';
            position: absolute;
            width: 153px;
            height: 33px;
            left: 50%;
            transform: translateX(-50%);
            bottom: -15px;
            background-color: #313131;
            opacity: 0.05;
            border-radius: 50%;
        }
    }

    .contact-information {
        padding-bottom: 30px;
        text-align: center;
        font-size: 12px;
    }

    .tips-content {
        margin: 0 auto;
        padding-top: 25px;
        width: 850px;
        text-align: center;
        border-top: 1px solid #eee;
    }

    .tips {
        position: relative;
        margin-bottom: 23px;
        font-size: 12px;
    }

    .left-rectangle {
        position: absolute;
        margin-left: -21px;
        width: 15px;
        height: 7px;
        top: 4px;
        background-color: $color-primary;
        opacity: 0.5;
        border-radius: 4px;
    }

    .right-rectangle {
        position: absolute;
        margin-left: 7px;
        width: 15px;
        height: 7px;
        top: 4px;
        background-color: $color-primary;
        opacity: 0.5;
        border-radius: 4px;
    }

    .revert {
        margin: 0 auto;
        width: 100px;
        height: 25px;
        text-align: center;
        line-height: 25px;
        background: $color-primary;
        border-radius: 5px;
        color: $color-white;
        cursor: pointer;
    }
}

.other-container {
    padding: 43px 0 84px;
    background-color: $color-white;

    .other-platforms {
        display: flex;
        justify-content: space-between;
        margin-top: 42px;

        li {
            padding: 25px 28px 13px;
            width: calc(100% / 9);
            height: 200px;
            text-align: center;
            box-sizing: border-box;

            &:hover {
                background-color: #f7f8f9;

                .see-link {
                    display: block;
                }
            }
        }

        .top-picture {
            margin-bottom: 12px;
            width: 77px;
            height: 77px;
        }

        .bottom-text {
            display: block;
            margin-bottom: 10px;
            font-size: 20px;
            white-space: nowrap;
        }

        .see-link {
            display: none;
            margin: 0 auto;
            width: 85px;
            height: 25px;
            text-align: center;
            line-height: 25px;
            background-color: $color-primary;
            border-radius: 5px;
            color: $color-white;
        }

        .tiktok {
            position: relative;
        }

        .tiktok-code {
            display: none;
            position: absolute;
            top: 15px;
            left: 50%;
            transform: translateX(-50%);
        }

        .is-picture {
            margin-bottom: 0;
            width: 115px;
            height: 115px;
        }
    }
}

@media screen and (max-width: 1200px) {
    .qr-code-container {
        .public-list {
            display: block;

            li {
                margin-bottom: 10px;
                width: 100%;
            }
        }
    }

    .community-content {
        padding: 30px 5px;

        .community {
            display: block;
        }

        .code-content {
            margin: 0 auto;
        }

        .tips-content {
            width: 100%;
        }

        .left-rectangle,
        .right-rectangle {
            display: none;
        }
    }

    .other-container {
        padding-bottom: 30px;

        .other-platforms {
            display: block;

            li {
                width: 100%;
            }

            .top-picture {
                margin: 0 auto 5px;
            }
        }
    }
}
