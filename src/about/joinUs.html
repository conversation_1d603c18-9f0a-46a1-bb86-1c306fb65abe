<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="shortcut icon" href="//img.gaoxiaojob.com/uploads/static/favicon.ico" type="image/x-icon" />
        <title>加入高才_高校人才网|硕士博士高层次人才招聘服务平台</title>
        <script>
            if (/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i.test(navigator.userAgent)) {
                const url = window.location.href
                if (url.includes('www.gaoxiaojob.com')) {
                    window.location.href = url.replace('www', 'm')
                }
            }
        </script>
        <link rel="stylesheet" href="./lib/normalize/normalize.min.css" />
        <link rel="stylesheet" href="./lib/swiper/swiper.min.css" />
        <link rel="stylesheet" href="./css/joinUs.css" />
    </head>

    <body>
        <div class="wrapper">
            <div class="page-container">
                <div class="bg-picture join-us"></div>

                <div class="header-container view-width">
                    <header class="header">
                        <div class="logo">
                            <a href="/" target="_blank" class="logo-link"></a>
                        </div>

                        <nav>
                            <a href="/" target="_blank" class="nav-link">首页</a>
                            <a href="/job" target="_blank" class="nav-link">职位</a>
                            <a href="/company" target="_blank" class="nav-link">单位</a>
                            <a href="/column/10.html" target="_blank" class="nav-link">资讯</a>
                            <a href="/member/person/login" target="_blank" class="nav-link">登录/注册</a>
                        </nav>
                    </header>

                    <div class="propaganda">
                        <p class="subtitle">高才科技-<span class="now-years">2023</span></p>
                        <h2 class="title">让世界发现你的热爱</h2>
                        <a href="#bottom" class="post-link">查看岗位</a>
                    </div>
                </div>
            </div>

            <div class="tab-container">
                <div class="tab view-width">
                    <a href="./index.html" class="tab-link">关于我们</a>
                    <a href="./productService.html" class="tab-link">产品服务</a>
                    <a href="./mediaMatrix.html" class="tab-link">媒体矩阵</a>
                    <a href="./joinUs.html" class="tab-link active">加入高才</a>
                    <a href="./contactUs.html" class="tab-link">联系我们</a>
                    <a href="./disclaimers.html" class="tab-link">免责声明</a>
                </div>
            </div>

            <div class="future view-width fixed-nav">
                <div class="is-title">
                    <h2>当你在高才</h2>
                </div>

                <div class="list-container">
                    <div class="list">
                        <div class="top-title">
                            <img src="assets/future-list1.png" alt="" />
                            <span>互助成长，共生未来</span>
                        </div>
                        <div class="bottom-text">
                            加入高才，你的优秀才华会被赏识，你的努力付出会有回报，你的创新想法会被真诚对待；加入高才，你将遇见一群与你亦师亦友，并肩作战的伙伴，我们一起成长，一起进步，一起探索更多发展的可能性。
                        </div>
                    </div>

                    <div class="list">
                        <div class="top-title">
                            <img src="assets/future-list2.png" alt="" />
                            <span>多元文化，连接你我</span>
                        </div>
                        <div class="bottom-text">
                            自由包容的文化氛围，舒适精致的办公环境，富有挑战的工作内容，年轻活力的团队伙伴，融洽有爱的工作氛围，丰富多彩的员工活动……高才竭力为你提供一个安心工作、开心生活的发展平台。
                        </div>
                    </div>

                    <div class="list">
                        <div class="top-title">
                            <img src="assets/future-list3.png" alt="" />
                            <span>福利关怀，坚强后盾</span>
                        </div>
                        <div class="bottom-text">
                            我们重视员工的发展和利益，提供极具竞争力的薪酬体系，完善的晋升机制及优厚的福利保障：享双休、全勤奖、补贴、五险一金、法定节假日、年休假、健康假、下午茶、团建旅游等；提供岗前及上岗后技能技巧培训。
                        </div>
                    </div>
                </div>
            </div>

            <a name="bottom"></a>

            <div class="job-container">
                <div class="job view-width">
                    <div class="is-title">
                        <h2>开启你的光芒</h2>
                    </div>

                    <div class="job-classify">
                        <div class="post">
                            <div class="modular sales-consultant">
                                <span>销售顾问</span>

                                <div class="hover-post">
                                    <div class="post-title">销售顾问</div>
                                    <div class="job-description">
                                        1.负责通过电话、QQ、微信以及邮件等方式开发新客户，介绍公司产品促成客户签单；<br />
                                        2.负责跟进潜在客户保持与客户的沟通，为客户解答产品相关的咨询问题；<br />
                                        3.负责维护已开发的客户，促进与客户的后续合作；<br />
                                        4. 主动维护新老客户关系，快速有效解决客户的咨询、疑问、投诉、建议及反馈，提高客户满意度；<br />
                                        5. 挖掘自身潜力，收集一线营销信息、用户意见、市场信息、竞争方信息；<br />
                                        6. 执行公司的销售策略及政策，积极向公司提出参考意见，维护企业形象。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="job-classify">
                    <div class="post">
                        <div class="modular product-manager">
                            <span>高级市场专员</span>

                            <div class="hover-post">
                                <div class="post-title">高级市场专员</div>
                                <div class="job-description">
                                    1.负责B端市场活动的策划、统筹、执行落地、媒体传播（B端客户沙龙、政府性展会、培训会议和颁奖盛典等）；<br>
                                    2.负责市场异业合作资源的商务洽谈，包括但不仅限于行业协会、政府、学校等相关渠道的开发和维护；<br>
                                    3.与政府园区协会、人力资源媒体、TOB服务商等组织协调建立合作关系，输出行业观点及报告，达到B端品牌专业度及行业影响力的提升；<br>
                                    4.整合公司自有资源，参与品宣平台的搭建和运营，策划话题活动与异业品牌跨界合作，通过双方渠道推广完成品牌覆盖，从0到1搭建线上线下精准的专业线曝光渠道，打造更高品牌知名度；<br>
                                    5.通过紧抓行业时事热点，联合行业优秀讲师，联合当地政府，以及联合各大媒体报道宣传；<br>
                                    6.每季度进行市场品牌调研，根据行业或竞商动态，完成行业动态分析报告，为公司决策提供依据。
                                </div>
                            </div>
                        </div>

                        <div class="modular creative-director">
                            <span>用户运营专员</span>

                            <div class="hover-post">
                                <div class="post-title">用户运营专员</div>
                                <div class="job-description">
                                    1. 负责自有产品平台（网站、小程序等）运营工作，根据产品周期及特点，协助制定并执行运营策略；<br>
                                    2. 负责平台各业务数据监控和分析，参与产品功能设计、测试等工作，合理设置数据埋点，分析数据和用户行为，收集用户反馈的问题；<br>
                                    3.对用户画像及行为进行研究分析，从用户角度出发，协助产品、研发部门优化产品功能，提升用户体验，实现用户持续增长、内容页面访问、用户注册、简历完善、投递等各项业务核心运营指标；<br>
                                    4. 根据平台运营策略和指标要求，制定运营激励活动方案，进行线上线下活动创意、策划，活动方案撰写及执行，并对活动数据进行跟踪和分析；<br>
                                    5.根据活动效果推进活动改进及策略优化，提高平台用户留存和持续增长。
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="post">
                        <div class="modular sales-consultant">
                            <span>销售顾问</span>

                            <div class="hover-post">
                                <div class="post-title">销售顾问</div>
                                <div class="job-description">
                                    1.负责通过电话、QQ、微信以及邮件等方式开发新客户，介绍公司产品促成客户签单；<br>
                                    2.负责跟进潜在客户保持与客户的沟通，为客户解答产品相关的咨询问题；<br>
                                    3.负责维护已开发的客户，促进与客户的后续合作；<br>
                                    4. 主动维护新老客户关系，快速有效解决客户的咨询、疑问、投诉、建议及反馈，提高客户满意度；<br>
                                    5. 挖掘自身潜力，收集一线营销信息、用户意见、市场信息、竞争方信息；<br>
                                    6. 执行公司的销售策略及政策，积极向公司提出参考意见，维护企业形象。
                                </div>
                            </div>
                        </div>

                        <div class="modular website-operation">
                            <span>网站编辑</span>

                            <div class="hover-post">
                                <div class="post-title">网站编辑</div>
                                <div class="job-description">
                                    1、负责网站信息内容的收集工作；<br>
                                    2、按照相关规则及要求，对特定信息内容进行精细化拆分、优化；<br>
                                    3、 负责网站信息的发布及审核工作；<br>
                                    4、按照上级领导安排，完成其他编辑相关工作。
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="post">
                        <div class="modular content-operation">
                            <span>商务主管</span>

                            <div class="hover-post">
                                <div class="post-title">商务主管</div>
                                <div class="job-description">
                                    1、负责公司的广告位价格管理、数据管理、合同管理、资源管理、招投标管理、售前方案管理等日常商务管理统筹工作；<br>
                                    2、做好商务支撑工作，负责协同、协调相关部门快速响应各部门提出的支撑需求，解决各部门提出的各种疑难杂症；<br>
                                    3、负责组织建立各类业务操作流程，确保业务流程执行通畅；<br>
                                    4、负责对所属下级的各项工作的检查、督促，并做好协调工作；<br>
                                    5、做好部门团队建设及人才培养储备工作；<br>
                                    6、监督业绩完成情况，确保全年目标的完成、项目目标的完成；<br>
                                    7、关注同行竞品市场情况，组织输出竞品报告；<br>
                                    8、做好销售风险管控工作，规避和控制公司商务风险，对异常客户及时预警跟进；<br>
                                    9、及时、准确传达上级指示并贯彻执行，向上级领导提供合理化建议。
                                </div>
                            </div>
                        </div>

                        <div class="modular copywriting-planning">
                            <span>SEO运营专员</span>

                            <div class="hover-post">
                                <div class="post-title">SEO运营专员</div>
                                <div class="job-description">
                                    1.负责公司网站SEO工作，制定全面的搜索引擎优化策略，包括但不局限于站内外优化、长尾关键词挖掘等操作，对SEO效果负责；提升网站排名和权重；<br>
                                    2.熟练掌握软文、交换链接、邮件推广、论坛推广及其它特殊的推广方式，不断开拓网站的外部链接，制定各种流量推广的策划、执行与跟踪，分析提升网站的有效流量；<br>
                                    3.关注业内及竞品变化，不断探索网站新的流量增长玩法，制定阶段性流量增长策略和方案，准确快速地落地，达成流量增长目标；<br>
                                    4.参与网站内容、活动策划工作，有效达成网站平台的访问量、注册量等相关运营指标，并不断探索新的运营思路和推广方法；<br>
                                    5.负责网站流量数据监控和数据统计，对网站进行持续分析和改进；以及日常网站的维护。
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->
                </div>
            </div>

            <div class="team-container">
                <div class="is-title">
                    <h2>我们的团队</h2>
                </div>

                <div class="team-situation">
                    <div class="left-content propaganda-content">
                        <div class="top-content">
                            <p>我们年轻朝气，力争上游</p>
                            <span>在这里，一群以90、95后居多的后浪，为梦想奔涌，闪闪发光。</span>
                        </div>

                        <div class="bottom-content">
                            <p>我们个性鲜明，不拘一格</p>
                            <span>
                                在这里，汇集了追星族、黑胶发<br />
                                烧友、梗王、零食Girl、表情包Queen等特色各异的年轻人，乔装上阵，玩转职场。
                            </span>
                        </div>
                    </div>

                    <div class="right-content propaganda-content">
                        <p>我们一起快乐，一起成长</p>
                        <span>
                            在这里，我们工作有“六乐”：乐于尝试、乐于思考、乐于交流、乐于协作、乐于分享、乐于学习;<br />
                            在这里，工作之余的我们精力Fun浪：下午茶、团建、旅游、部门联谊等应接不暇，还有来自匿名<br />
                            Boss时不时的美食投喂~<br />
                            在这里，我们收获快乐，互助成长；成就自我，循梦前行。
                        </span>
                        <p>在这里，有一分热，便发一分光！来加入高才，做你热爱的事吧！</p>
                    </div>
                </div>
            </div>

            <div class="grow-up-container">
                <div class="grow-up view-width">
                    <div class="is-title">
                        <h2>快乐成长之地</h2>
                    </div>

                    <div class="company-picture swiper companySwiper">
                        <div class="swiper-wrapper">
                            <img src="assets/company-pictures1.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                            <img src="assets/company-pictures2.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                            <img src="assets/company-pictures3.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                            <img src="assets/company-pictures4.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                            <img src="assets/company-pictures5.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                            <img src="assets/company-pictures6.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                            <img src="assets/company-pictures7.jpg" class="swiper-slide" alt="高校人才网团建活动" />
                        </div>
                        <div class="swiper-button-prev"></div>
                        <div class="swiper-button-next"></div>
                    </div>
                </div>
            </div>

            <div class="fair-footer">
                <div class="footer-content-box">
                    <div class="footer-left">
                        <ul class="footer-nav">
                            <li><a href="./index.html">关于我们</a></li>
                            <li><a href="./productService.html">产品服务</a></li>
                            <li><a href="./mediaMatrix.html">媒体矩阵</a></li>
                            <li><a href="./joinUs.html">加入我们</a></li>
                            <li><a href="./contactUs.html">联系我们</a></li>
                            <li><a href="./disclaimers.html">免责声明</a></li>
                        </ul>
                        <p class="num-one">Copyright © 2007-2022 高校人才网 粤ICP备13048400号-2 粤公网安备：44010602004138号 本站由广州高才信息科技有限公司运营</p>
                        <p>中华人民共和国增值电信业务经营许可证：粤B2-20180648 人力资源服务许可证编号：440106160023 企业统一社会信用代码：91440106MA59BTXW56</p>
                        <p>
                            高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台<span class="sp" id="busuanzi_container_page_pv"> -SP-<b id="busuanzi_value_page_pv"></b></span>
                        </p>
                    </div>
                    <div class="footer-right"></div>
                </div>
            </div>
        </div>
    </body>
    <script src="/static/js/tongji.js"></script>
    <script src="./lib/swiper/swiper.min.js"></script>
    <script src="./lib/jquery/jquery.min.js"></script>

    <script>
        $(function () {
            var windowWidth = 0
            var changing = false
            var timer = 0
            var swiper = null

            function setSwiper(showImages, mouseIn) {
                return new Swiper('.companySwiper', {
                    slidesPerView: showImages,
                    spaceBetween: 24,
                    loop: true,
                    autoplay: {
                        delay: 1500,
                        pauseOnMouseEnter: mouseIn,
                        disableOnInteraction: false
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev'
                    }
                })
            }

            function isWidth() {
                windowWidth = window.innerWidth

                if (windowWidth < 675) {
                    swiper = setSwiper(1, false)
                } else if (windowWidth < 1200) {
                    swiper = setSwiper(2, false)
                } else {
                    swiper = setSwiper(3, true)
                }
            }

            isWidth()

            !(function () {
                $(window).resize(function () {
                    if (!changing) {
                        changing = true
                        clearTimeout(timer)
                        timer = setTimeout(function () {
                            swiper.destroy()
                            isWidth()
                            changing = false
                        }, 800)
                    }
                })
            })()

            const nowYears = new Date().getFullYear()
            $('.now-years').html(nowYears)
        })
    </script>
</html>
