<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>chat</title>
    <!-- 引入jquery -->
    <script src="https://cdn.bootcss.com/jquery/3.4.1/jquery.min.js"></script>
</head>
<style>
    @media (max-width: 992px) {
        .hidden-sm {
            display: none;
        }
    }

</style>

<script type="text/javascript">
    if (typeof console == "undefined") {
        this.console = {
            log: function (msg) {}
        };
    }
    // 如果浏览器不支持websocket，会使用这个flash自动模拟websocket协议，此过程对开发者透明
    WEB_SOCKET_SWF_LOCATION = "/swf/WebSocketMain.swf";
    // 开启flash的websocket debug
    WEB_SOCKET_DEBUG = true;
    var ws, name, client_list = {},
        room_id, clientId;

    room_id = getQueryString('room_id') ? getQuerString('room_id') : 1;


    function random() {
        // 随机生成数字
        var d = new Date().getTime();
        var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = (d + Math.random() * 16) % 16 | 0;
            d = Math.floor(d / 16);
            return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        });
        return uuid;
    }

    function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    // 连接服务端
    function connect() {
        // 创建websocket
        // ws = new WebSocket("wss://gray.wss.jugaocai.com");
        ws = new WebSocket("ws://127.0.0.1:7272");
        // 当socket连接打开时，输入用户名
        ws.onopen = onopen;
        // 当有消息时根据消息类型显示不同信息
        ws.onmessage = onmessage;
        ws.onclose = function () {
            console.log("连接关闭，定时重连");
            connect();
        };
        ws.onerror = function () {
            console.log("出现错误");

            // 发现服务器有问题
        };
    }

    // 连接建立时发送登录信息
    function onopen() {
        if (!name) {
            show_prompt();
        }
        // 登录
        var login_data = '{"type":"login","token":"' + name.replace(/"/g, '\\"') + '","room_id":' + room_id + '}';
        // var login_data = '{"type":"login","memberId":"' + name.replace(/"/g, '\\"') + '","room_id":' + room_id + '}';
        console.log("websocket握手成功，发送登录数据:" + login_data);
        ws.send(login_data);
    }

    // 服务端发来消息时
    function onmessage(e) {
        console.log(e.data);
        var data = JSON.parse(e.data);
        switch (data['type']) {
            // 服务端ping客户端

            // 登录 更新用户列表
            case 'login':
                var clientName = data['clientName'];
                if (data['client_list']) {
                    clientId = data['clientId'];
                    clientName = '你';
                    client_list = data['client_list'];
                } else {
                    client_list[data['clientId']] = data['clientName'];
                }

                for (i in data['log_list']) {
                    say(data['log_list'][i]['clientId'], data['log_list'][i]['clientName'], data['log_list'][i][
                        'content'
                    ], data['log_list'][i]['time']);
                }

                say(data['clientId'], data['clientName'], clientName + ' 加入了聊天室', data['time']);

                flush_client_list();
                console.log(data['clientName'] + "登录成功");
                break;
                // 发言
            case 'say':
                //{"type":"say","from_clientId":xxx,"to_clientId":"all/clientId","content":"xxx","time":"xxx"}
                say(data['from_clientId'], data['from_clientName'], data['content'], data['time']);
                break;
                // 用户退出 更新用户列表
            case 'join':
                //{"type":"say","from_clientId":xxx,"to_clientId":"all/clientId","content":"xxx","time":"xxx"}
                console.log(data)
                say(data['clientId'], data['clientName'], '加入了房间' + data['chatId'], data['time']);
                break;
                // 用户退出 更新用户列表
            case 'logout':
                //{"type":"logout","clientId":xxx,"time":"xxx"}
                say(data['from_clientId'], data['from_clientName'], data['from_clientName'] + ' 退出了', data['time']);
                delete client_list[data['from_clientId']];
                flush_client_list();
            case 'error':
                // 重新加载页面



        }
    }

    // 输入姓名
    function show_prompt() {
        name = prompt('输入你的memberId：', '');
        if (!name || name == 'null') {
            name = '游客';
        }
    }

    // 提交对话
    function onSubmit() {
        var input = document.getElementById("textarea");
        var to_clientId = $("#client_list option:selected").attr("value");
        var to_clientName = $("#client_list option:selected").text();
        var chatId = $("#chatIdList option:selected").attr("value");
        ws.send('{"type":"say","chatId":"' + chatId + '","content":"' + input.value.replace(/"/g, '\\"').replace(/\n/g,
            '\\n').replace(/\r/g, '\\r') + '"}');
        input.value = "";
        input.focus();
    }

    // 刷新用户列表框
    function flush_client_list() {
        var userlist_window = $("#userlist");
        var client_list_slelect = $("#client_list");
        userlist_window.empty();
        client_list_slelect.empty();
        userlist_window.append('<h4>在线用户</h4><ul>');
        client_list_slelect.append('<option value="all" id="cli_all">所有人</option>');
        for (var p in client_list) {
            userlist_window.append('<li id="' + p + '">' + client_list[p] + '</li>');
            if (p != clientId) {
                client_list_slelect.append('<option value="' + p + '">' + client_list[p] + '</option>');
            }
        }
        $("#client_list").val(select_clientId);
        userlist_window.append('</ul>');
    }

    // 发言
    function say(from_clientId, from_clientName, content, time) {


        $("#dialog").append('<div class="speech_item">' + from_clientName + ' <br> ' + time +
            '<div style="clear:both;"></div><p class="triangle-isosceles top">' + content + '</p> </div>')
    }

    function join(chatId) {
        ws.send('{"type":"join","chatId":"' + chatId + '"}');
    }

    $(function () {
        select_clientId = 'all';
        $("#client_list").change(function () {
            select_clientId = $("#client_list option:selected").attr("value");
        });
        $('.face').click(function (event) {
            $(this).sinaEmotion();
            event.stopPropagation();
        });
    });

</script>

<script type="text/javascript">
    // 动态自适应屏幕
    document.write('<meta name="viewport" content="width=device-width,initial-scale=1">');
    $("textarea").on("keydown", function (e) {
        // 按enter键自动提交
        if (e.keyCode === 13 && !e.ctrlKey) {
            e.preventDefault();
            $('form').submit();
            return false;
        }

        // 按ctrl+enter组合键换行
        if (e.keyCode === 13 && e.ctrlKey) {
            $(this).val(function (i, val) {
                return val + "\n";
            });
        }
    });

</script>

<body>
    <div class="row clearfix mt-5">
        <div class="col-md-1 column">
        </div>
        <div class="col-md-6 column">
            <div class="thumbnail">
                <div class="caption" id="dialog"></div>
            </div>
            <form onsubmit="onSubmit(); return false;">
                <select style="margin-bottom:8px" id="client_list">
                    <option value="all">所有人</option>
                </select>
                <select style="margin-bottom:8px" id="chatIdList">
                    <option value="all">所有房间</option>
                    <option value="1">房间1</option>
                    <option value="2">房间2</option>
                    <option value="3">房间3</option>
                </select>
                <textarea class="textarea thumbnail" id="textarea"></textarea>
                <div class="say-btn">
                    <input type="button" class="btn btn-info face pull-left" value="表情" />
                    <input type="submit" class="btn btn-success" value="发表" />
                    <input type="button" class="btn btn-join" onclick="join(1)" value="加入1号房间" />
                    <input type="button" class="btn btn-join" onclick="join(2)" value="加入2号房间" />
                    <input type="button" class="btn btn-join" onclick="join(3)" value="加入3号房间" />
                </div>
            </form>
            <div>
                &nbsp;&nbsp;&nbsp;&nbsp;<b>房间列表:</b>（当前在&nbsp;房间<script>
                    document.write(room_id)

                </script>）<br>
                &nbsp;&nbsp;&nbsp;&nbsp;<a href="/?room_id=1">房间1</a>&nbsp;&nbsp;&nbsp;&nbsp;<a
                    href="/?room_id=2">房间2</a>&nbsp;&nbsp;&nbsp;&nbsp;<a
                    href="/?room_id=3">房间3</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="/?room_id=4">房间4</a>
                <br><br>
            </div>
            <p class="cp">PHP多进程+Websocket(HTML5/Flash)+PHP Socket实时推送技术&nbsp;&nbsp;&nbsp;&nbsp;Powered by <a
                    href="https://github.com/funson86/yii2-websocket" target="_blank">Funboot yii2-websocket</a> & <a
                    href="http://www.workerman.net/workerman-chat" target="_blank">workerman-chat</a></p>
        </div>
        <div class="col-md-3 column">
            <div class="thumbnail">
                <div class="caption" id="userlist"></div>
            </div>
        </div>
    </div>
</body>

</html>




<script>
    $(document).ready(function () {
        connect();
    });

</script>
