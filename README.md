# ts-vue-person-static

- src/variables.scss -> 项目 scss 变量

## column

```textplain
column
├── assets                          静态资源
├── css
├── js
├── lib
├── scss
├── theme-chalk                     Element-plus 主题
├── home.html                       首页
├── column.html                     栏目页模版A
├── columnB.html                    栏目页模板B
├── columnProvince.html             省区栏目
├── columnSecond.html               二级栏目
├── columnGovernment.html           政府与事业单位年度招聘
├── columnPostdoctor.html           博士后
├── region.html                     地区选择
├── news.html                       资讯列表
├── newsDetail.html                 资讯详情
├── daily.html                      每日汇总
├── dailyDetail.html                每日汇总详情页
├── contentSearch.html              内容搜索
├── siteMap.html                    网站地图
├── bindMobile.html                 微信扫码绑定手机号
├──
├── job.html                        职位列表
├── jobList.html                    职位列表（新） [header-container 只在此更新]
├── jobDetail.html                  职位详情
├── announcement.html               非合作单位公告
├── company.html                    单位列表
├── companyDetail.html              单位详情
├── companyDetailNoticeList.html    合作单位公告列表
├── unCooperationCompanyDetail.html非合作单位详情
├── notice.html                     合作单位公告详情
├── noticeJobList.html              单位公告职位列表
├── application.html                合作申请
├── rczhaopin.html                  职位列表（用于SEO）
├── rckeywords.html                 关键词职位列表（用于SEO）
├── dbMeetingApply.html             招聘会报名表（双会）
├── dbMeetingApplySucceed.html      招聘会报名成功（双会）
├──
├── guideCard.html                  引导组件
├── loginDialog.html                登录弹窗
├── loginAside.html                 登录侧边
├── applyDialog.html                职位申请
├── cascaderDialog.html             选择弹窗
├── subscribeJobDialog.html         职位订阅弹窗
├── filterDialog.html               筛选弹窗（用于SEO页面）
├── successDialogAlert.html         投递成功弹窗
├── payDialog.html                  支付弹窗
├── checkGenerateReportDialog.html  职位，公告详情解锁分析功能弹窗
├── paymentSuccessDialog.html       下单成功弹窗
├──
├── vipIntroduce.html               VIP介绍页
├── jobFast.html                    求职快介绍页
├── competitivePower.html           竞争力分析介绍页
├── activityVipIntroduce.html       VIP介绍页(活动)
├──
├── superiorDialog.html              高级筛选组件
├──
├── echarts.html                    echarts
├── jobReport.html                  职位竞争力分析报告
├── noticeReport.html               公告热度分析报告
├──
├──noticeTwo.html                   高级公告模板二
├──noticeThree.html                 高级公告模板三
├──
├── postdoctorHome.html             博士后栏目-首页
├── postdoctorAnnouncement.html     博士后栏目-博后公告
├── postdoctorJob.html              博士后栏目-博后职位
├── postdoctorCompany.html          博士后栏目-招收单位&PI大厅
├── postdoctorActivity.html         博士后栏目-博后活动
├── postdoctorPublish.html          博士后栏目-需求发布
├── postdoctorGuide.html            博士后全局引流底部悬浮栏
├──
├── footerLink.html                 底部内链组件（栏目页、非合作单位公告）
├──
├── mini-code-popup.html            职位、公告分享小程序码
├──
├── activitySpecial.html            专场活动
├── activitySingleDetail.html       单场活动详情
├──
├── 404.html                        404
```

## about

```textplain
about
├── assets                          静态资源
├── css
├── js
├── lib
├── scss
├── index.html                      关于我们
├── productService.html             产品服务
├── mediaMatrix.html                媒体矩阵
├── joinUs.html                     加入我们
├── contactUs.html                  联系我们
├── disclaimers.html                免责声明
```

## email

```textplain
email
├── assets                          静态资源
├── css
├── js
├── lib
├── scss
├── invite.html                邀请面试邮箱模板
├── auditFailed.html           职位审核不通过邮箱模板
├── concernedNotice.html       关注反馈邮箱模板
├── deliverNoticeA.html        单位A账号投递通知模板
├── deliverNoticeB.html        单位B账号投递通知模板
├── deliverSucceeded.html      求职者投递成功通知模板
├── deliverFeedback.html       求职者投递反馈-不合适
├── jobInvite.html             职位邀约
```
