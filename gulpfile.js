import del from 'del'
import gulp from 'gulp'
import sass from 'sass'
import gulpSass from 'gulp-sass'
import gulpRename from 'gulp-rename'
import gulpPlumber from 'gulp-plumber'

const { dest, src, watch } = gulp

const gulpScss = gulpSass(sass)

class ScssCompile {
    constructor(src, dest) {
        this.src = src
        this.dest = dest
        this.renameOptions = { suffix: '.min' }

        this.clearable = /\*.scss$/.test(this.src)

        if (this.clearable) {
            this.clear()
            this.renameOptions.suffix = ''
        }
    }

    static gulpSassOptions = {
        outputStyle: 'compressed'
    }

    clear() {
        del(`${this.dest}/*`)
    }

    compiler() {
        return src(this.src).pipe(gulpPlumber()).pipe(gulpScss(ScssCompile.gulpSassOptions)).pipe(gulpRename(this.renameOptions)).pipe(dest(this.dest))
    }

    watcher() {
        watch(this.src, (callback) => this.execute(callback))
    }

    execute(callback) {
        this.compiler()
        callback()
    }
}

const isBuild = process.argv.indexOf('build') !== -1

const columnPath = './src/column'

const elementSourcePath = columnPath + '/theme-chalk/index.scss'
const elementOutputPath = columnPath + '/lib/element-plus/'
const elementCompiler = new ScssCompile(elementSourcePath, elementOutputPath)

const columnSourcePath = columnPath + '/scss/**/*.scss'
const columnOutputPath = columnPath + '/css/'
const columnCompiler = new ScssCompile(columnSourcePath, columnOutputPath)

const aboutPath = './src/about'
const aboutSourcePath = aboutPath + '/scss/**/*.scss'
const aboutOutputPath = aboutPath + '/css/'
const aboutCompiler = new ScssCompile(aboutSourcePath, aboutOutputPath)

const emailPath = './src/email'
const emailSourcePath = emailPath + '/scss/**/*.scss'
const emailOutputPath = emailPath + '/css/'
const emailCompiler = new ScssCompile(emailSourcePath, emailOutputPath)

const compiler = [elementCompiler, columnCompiler, aboutCompiler, emailCompiler]

const dev = (callback) => {
    compiler.forEach((compiler) => compiler.execute(callback))
}

const build = (callback) => {
    dev(callback)
}

if (!isBuild) {
    compiler.forEach((compiler) => compiler.watcher())
}

export { build }
export default dev
