{"name": "ts-vue-person-static", "version": "1.0.0", "description": "", "type": "module", "scripts": {"dev": "gulp", "build": "gulp build"}, "author": "Babel <<EMAIL>>", "license": "ISC", "devDependencies": {"axios": "^0.21.1", "del": "^6.1.1", "element-plus": "^1.1.0-beta.17", "gulp": "^4.0.2", "gulp-plumber": "^1.2.1", "gulp-rename": "^2.0.0", "gulp-sass": "5.1.0", "jquery": "^3.6.0", "prettier": "^2.7.1", "qs": "^6.10.1", "sass": "1.43.4", "swiper": "^7.0.5", "vue": "^3.2.4"}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}